using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;

namespace Levelbuild.Domain.Storage.Db.QueryParser.SqlExpression;

/// <inheritdoc cref="SqlExpression"/>
public class AggregateExpression : SqlExpression
{
	/// <summary>
	/// The aggregate function of this expression
	/// </summary>
	private DataStoreColumnAggregationFunction Function { get; }
	
	/// <summary>
	/// The expression which is aggregated
	/// </summary>
	private SqlExpression InnerExpression { get; }

	/// <inheritdoc/>
	public AggregateExpression(Db db, DataStoreColumnAggregationFunction function, SqlExpression innerExpression): base(db)
	{
		Function = function;
		InnerExpression = innerExpression;
	}

	/// <inheritdoc/>
	protected internal override List<ColumnExpression> GetColumnExpressions()
	{
		return InnerExpression.GetColumnExpressions();
	}

	/// <inheritdoc/>
	public override string AsString() => $"{Function}({InnerExpression.AsString()})";

	/// <inheritdoc/>
	protected internal override void DetermineType()
	{
		InnerExpression.DetermineType();

		if (InnerExpression.AggregateFunction != null)
		{
			throw new DataStoreQueryException($"Cannot nest aggregate functions: '{AsString()}'");
		}
		
		MultiValue = InnerExpression.MultiValue;
		Type = DataStoreFieldType.Double;
		Nullable = false;
		DefaultValue = 0;
		AggregateFunction = Function;
	}
}