using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Domain.StorageEntities.Features.Dto;

namespace Levelbuild.Domain.Storage.Db.QueryParser.SqlExpression;

/// <inheritdoc/>
public class ColumnExpression : SqlExpression
{
	/// <summary>
	/// The table for the column
	/// </summary>
	public string TableName { get; set; }
	
	/// <summary>
	/// The column itself
	/// </summary>
	public string ColumnName { get; set; }
	
	/// <summary>
	/// Whether the ColumnName is a complex query and is used without the TableName
	/// </summary>
	private bool isQuery = false;

	/// <inheritdoc/>
	public ColumnExpression(Db db, string columnName): base(db)
	{
		ColumnName = columnName;
	}

	/// <summary>
	/// The field this ColumnExpression represents. This will be used to set the properties Type, MultiValue, Nullable, DefaultValue and AggregateFunction.
	/// </summary>
	/// <param name="storageFieldDefinitionOrm"></param>
	public void ApplyField(StorageFieldDefinitionOrm storageFieldDefinitionOrm)
	{
		Type = storageFieldDefinitionOrm.Type;
		MultiValue = storageFieldDefinitionOrm.MultiValue;
		Nullable = storageFieldDefinitionOrm.Nullable;
		DefaultValue = storageFieldDefinitionOrm.DefaultValue;
		AggregateFunction = null;
	}

	/// <inheritdoc/>
	protected internal override List<ColumnExpression> GetColumnExpressions()
	{
		return [this];
	}

	/// <inheritdoc/>
	public override string AsString() => isQuery ? ColumnName : $"[{TableName}].[{ColumnName}]";

	/// <summary>
	/// Sets the ColumnName to a query and sets isQuery to true
	/// </summary>
	/// <param name="query"></param>
	public void AsQuery(string query)
	{
		isQuery = true;
		ColumnName = query;
	}

	/// <inheritdoc/>
	protected internal override void DetermineType() {}
}