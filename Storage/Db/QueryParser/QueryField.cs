using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Domain.Storage.Db.QueryParser.SqlExpression;
using SqlKata;

namespace Levelbuild.Domain.Storage.Db.QueryParser;

/// <summary>
/// Representation of a single sql entity that can be selected
/// </summary>
public class QueryField
{
	private DataStoreQueryField _dataStoreQueryField;

	/// <summary>
	/// The name of the DataStoreQueryField
	/// </summary>
	public string Name = "";
	
	/// <summary>
	/// The alias of the DataStoreQueryField
	/// </summary>
	private string _alias;
	
	/// <summary>
	/// Whether the expression results in multi-value
	/// </summary>
	public bool MultiValue;
	
	/// <summary>
	/// The type of the expression result
	/// </summary>
	public DataStoreFieldType Type;
	
	/// <summary>
	/// Whether the expression result is nullable
	/// </summary>
	public bool Nullable;
	
	/// <summary>
	/// The default value
	/// </summary>
	public object? DefaultValue;
	
	/// <summary>
	/// The aggregation function used by the expression
	/// </summary>
	public DataStoreColumnAggregationFunction? AggregateFunction = null;
	private readonly bool _allowAggregateFunctions;

	/// <summary>
	/// Whether this field should be rendered with its alias
	/// </summary>
	public bool WithAlias { get; set; }
	
	/// <summary>
	/// Whether this field is of type query or string
	/// </summary>
	public bool IsQuery = false;

	/// <summary>
	/// The sql expression
	/// </summary>
	public SqlExpression.SqlExpression Expression;
	private Db _db;
	
	/// <summary>
	/// The Joins that need to be added to the query in order to use this QueryField
	/// </summary>
	public Dictionary<string, Join> PreconditionJoins { get; set; } = new ();
	private Query SubSelect { get; set; }

	private QueryField(DataStoreQueryField dataStoreQueryField, string name, string alias, bool multiValue, DataStoreFieldType type, bool nullable, object? defaultValue, DataStoreColumnAggregationFunction? aggregateFunction, bool allowAggregateFunctions, bool isQuery, SqlExpression.SqlExpression expression, Db db, bool withAlias, Query subSelect)
	{
		_dataStoreQueryField = dataStoreQueryField;
		Name = name;
		_alias = alias;
		MultiValue = multiValue;
		Type = type;
		Nullable = nullable;
		DefaultValue = defaultValue;
		AggregateFunction = aggregateFunction;
		_allowAggregateFunctions = allowAggregateFunctions;
		IsQuery = isQuery;
		Expression = expression;
		_db = db;
		WithAlias = withAlias;
		SubSelect = subSelect;
	}

	public QueryField(Db db, DataStoreQueryField dataStoreQueryField, bool withAlias, bool allowAggregateFunctions, Dictionary<string, string>? changedFieldAliases)
	{
		_db = db;
		_dataStoreQueryField = dataStoreQueryField;
		Name = _dataStoreQueryField.Name;
		_alias = FilterParser.ShortenAlias(_dataStoreQueryField.Alias ?? _dataStoreQueryField.Name, changedFieldAliases);
		WithAlias = withAlias;
		_allowAggregateFunctions = allowAggregateFunctions;
		
		if (!string.IsNullOrEmpty(Name))
		{
			Expression = new SqlExpressionParser(db, Name, allowAggregateFunctions).Parse();
		}
	}

	public QueryField(Db db, Query subSelect, string? alias, bool withAlias, QueryField backingField, Dictionary<string, string>? changedFieldAliases)
	{
		_db = db;
		_alias = "";
		WithAlias = withAlias;
		IsQuery = true;
		
		if (withAlias)
		{
			if (alias == null)
				throw new DataStoreOperationException("Alias not provided");		
			_alias = FilterParser.ShortenAlias(alias, changedFieldAliases);
		}

		SubSelect = subSelect;

		MultiValue = backingField.MultiValue;
		Type = backingField.Type;
		Nullable = backingField.Nullable;
		DefaultValue = backingField.DefaultValue;
		AggregateFunction = backingField.AggregateFunction;
	}

	public string AsQueryString()
	{
		if (IsQuery)
			throw new DataStoreOperationException("Cannot use query field as string");
		return $"{Expression.AsString()}{(WithAlias ? $" AS [{_alias}]" : "")}";
	}

	public Query AsQuery()
	{
		if (!IsQuery)
			throw new DataStoreOperationException("Cannot use query field as query");
		return SubSelect;
	}

	public void DetermineType()
	{
		Expression.DetermineType();
		MultiValue = Expression.MultiValue;
		Type = Expression.Type;
		Nullable = Expression.Nullable;
		DefaultValue = Expression.DefaultValue;
		AggregateFunction = Expression.AggregateFunction;
	}

	public QueryField CastTo(DataStoreFieldType newFieldType)
	{
		if (IsQuery)
		{
			WrapColumn(col => _db.DataHelper.CastRaw(col, newFieldType));
		}
		else
		{
			Expression = new CastExpression(_db, Expression, newFieldType);
		}
		Type = newFieldType;
		return this;
	}

	public QueryField FilterNotNull()
	{
		if (!IsQuery)
			throw new DataStoreOperationException("FilterNotNull can only be used if the QueryField is a SubSelect");
		string select = GetSelect();
		SubSelect = SubSelect.WhereRaw(select + " is not null");
		return this;
	}
	
	private void WrapColumn(Func<string, string> fun)
	{
		var str = GetSelect();

		SubSelect.ClearComponent("select");
		SubSelect.SelectRaw(fun(str));
	}
	
	private string GetSelect()
	{
		if (!IsQuery)
			return Expression.AsString();

		var abstractCol = (AbstractColumn)SubSelect.GetOneComponent("select");
		var str = "";
		if (abstractCol is Column column)
		{
			str = $"[{column.Name}]";
		}
		else if (abstractCol is RawColumn rawColumn)
		{
			str = rawColumn.Expression;
		}
		else if (abstractCol is AggregatedColumn)
		{
			str = _db.ConnectionHelper.GetCompiler().CompileColumn(new SqlResult(), abstractCol);
		}

		return str;
	}

	/// <summary>
	/// Duplicate this QueryField
	/// </summary>
	/// <returns></returns>
	public QueryField Clone()
	{
		return new QueryField(_dataStoreQueryField, Name, _alias, MultiValue, Type, Nullable, DefaultValue, AggregateFunction, _allowAggregateFunctions, IsQuery, Expression, _db, WithAlias, SubSelect);
	}
}