using System.Text.RegularExpressions;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Domain.Storage.Db.QueryParser.SqlExpression;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using SqlKata;

namespace Levelbuild.Domain.Storage.Db.QueryParser;

/// <summary>
/// Parser for fields
/// </summary>
public class FieldParser
{
	private readonly Db _db;
	private readonly Func<string, StorageIndexDefinition?> _getStorageIndexDefinition;
	private readonly string _lang;
	private readonly string _defaultLang;
	private readonly StorageIndexDefinition _indexDefinition;
	private readonly Dictionary<string, string> _changedFieldAliases;

	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="db"></param>
	/// <param name="getStorageIndexDefinition"></param>
	/// <param name="lang"></param>
	/// <param name="defaultLang"></param>
	/// <param name="indexDefinition"></param>
	/// <param name="changedFieldAliases"></param>
	public FieldParser(Db db, Func<string, StorageIndexDefinition?> getStorageIndexDefinition, string lang, string defaultLang,
					   StorageIndexDefinition indexDefinition, Dictionary<string, string> changedFieldAliases)
	{
		_db = db;
		_getStorageIndexDefinition = getStorageIndexDefinition;
		_lang = lang;
		_defaultLang = defaultLang;
		_indexDefinition = indexDefinition;
		_changedFieldAliases = changedFieldAliases;
	}

	/// <summary>
	/// Parses the dataStoreQueryField into a QueryField representative
	/// </summary>
	/// <param name="dataStoreQueryField"></param>
	/// <param name="withAlias"></param>
	/// <param name="allowAggregateFunctions"></param>
	/// <returns></returns>
	public QueryField Parse(DataStoreQueryField dataStoreQueryField, bool withAlias = false, bool allowAggregateFunctions = true)
	{
		var queryField = new QueryField(_db, dataStoreQueryField, withAlias, allowAggregateFunctions, _changedFieldAliases);
		ParseFields(_indexDefinition, queryField);
		queryField.DetermineType();
		return queryField;
	}

	private void ParseFields(StorageIndexDefinition sid, QueryField queryField)
	{
		foreach (var columnExpression in queryField.Expression.GetColumnExpressions())
		{
			ParseField(sid, queryField, columnExpression);
		}
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="sid"></param>
	/// <param name="queryField"></param>
	/// <param name="columnExpression"></param>
	/// <exception cref="DataStoreQueryException"></exception>
	private void ParseField(StorageIndexDefinition sid, QueryField queryField,
							ColumnExpression columnExpression)
	{
		var cleanedName = columnExpression.ColumnName;
		var splitted = cleanedName.Split(".");
		
		if (splitted.Length == 1)
		{
			var field = sid.Fields.FirstOrDefault(f => f.Name == cleanedName);
			if (field == null)
				throw new DataStoreQueryException(
					$"Datasource '{sid.Name}' does not contain any field named '{cleanedName}' ");
			
			columnExpression.ApplyField(field);

			if (field is { MultiValue: true, LookupSource: not null })
			{
				columnExpression.AsQuery(GetMultivalueLookupField(sid, null, field));
				return;
			}

			columnExpression.TableName = sid.Name;
			columnExpression.AsQuery(GetTranslatableField(sid, null, field, columnExpression));
			return;
		}

		ParseStackedFields(sid, sid.Name, queryField, columnExpression, splitted, cleanedName);
	}

	private string GetTranslatableField(StorageIndexDefinition definition, string? definitionAs, StorageFieldDefinitionOrm field, ColumnExpression columnExpression)
	{
		columnExpression.TableName = definitionAs ?? definition.Name;
		columnExpression.ApplyField(field);
		
		var fieldName = field.Name;
		if (field.Translatable)
		{
			string defaultName = $"{fieldName}__{_defaultLang}";
			string currentLanguageFieldName = $"{fieldName}__" + _lang;
			
			columnExpression.ColumnName = defaultName;
			
			if (defaultName != currentLanguageFieldName)
			{
				var currentLanguageField = definition.Fields.FirstOrDefault(it => it.Name == currentLanguageFieldName);
				if (currentLanguageField == null)
					return columnExpression.AsString();
				
				var currentLanguageFieldExpression = new ColumnExpression(_db, currentLanguageFieldName)
				{
					TableName = columnExpression.TableName
				};
				currentLanguageFieldExpression.ApplyField(currentLanguageField);
				return _db.DataHelper.IsNullRaw(currentLanguageFieldExpression.AsString(),
												columnExpression.AsString());
			}
			
			return columnExpression.AsString();
		}
		columnExpression.ColumnName = field.Name;
		return columnExpression.MultiValue
				   ? _db.DataHelper.IsNullRaw(columnExpression.AsString(), @$"ARRAY\[\]::{_db.DataHelper.GetColumnType(columnExpression.Type)}\[\]")
				   : columnExpression.AsString();
	}

	private void ParseStackedFields(StorageIndexDefinition sid, string definitionAlias, QueryField queryField,
									ColumnExpression columnExpression, string[] splitted, string cleanedName)
	{
		var currentName = splitted[0];

		var definition = sid;
		StorageFieldDefinitionOrm lastField = sid.Fields.FirstOrDefault(it => it.Name == currentName) ??
											   throw new DataStoreQueryException(
												   $"Datasource '{sid.Name}' does not contain any field named '{currentName}' for '{cleanedName}'.");
		
		var currentDefinitionAlias = currentName;
		var nextDefinitionAlias = currentDefinitionAlias + "." + splitted[0];
		StorageFieldDefinitionOrm? multivalueLookupField = null;
		string? multivalueDefinitionAlias = null;
		var currentQueryField = queryField;
		Query? multiValueFieldQuery = null; 
		for (int i = 1; i < splitted.Length; i++)
		{
			if (lastField.MultiValue && lastField.LookupSource != null)
			{
				if (multivalueLookupField != null)
					throw new DataStoreQueryException(
						$"Stacked virtual field '{cleanedName}' contained multiple multivalue fields. Only one multivalue field is allowed. First multivalue field: '{multivalueLookupField.Name}', second: '{lastField.Name}'");
				multivalueLookupField = lastField;
				multivalueDefinitionAlias = i == 1
													? multivalueLookupField.LookupSourceMappingTable!
													: FilterParser.GetTableAlias(multivalueLookupField.LookupSourceMappingTable!);
				multiValueFieldQuery = new Query($"{multivalueLookupField.LookupSourceMappingTable} AS {multivalueDefinitionAlias}");
				multiValueFieldQuery.WhereColumns($"{(i == 1 ? definitionAlias : FilterParser.GetTableAlias(currentDefinitionAlias))}.{StorageSystemField.Id}", "=", $"{multivalueDefinitionAlias}.{definition.Name}");
				currentQueryField = new QueryField(_db, new DataStoreQueryField(""), false, false, null);
				currentDefinitionAlias = multivalueLookupField.LookupSourceMappingTable;
				definitionAlias = multivalueDefinitionAlias;
				lastField = new StorageFieldDefinitionOrm(multivalueLookupField.LookupSource)
				{
					LookupSource = multivalueLookupField.LookupSource
				};
			}
			
			lastField = ParseStacked(cleanedName, definition, i == 1 ? definitionAlias : FilterParser.GetTableAlias(currentDefinitionAlias!),
									 FilterParser.GetTableAlias(nextDefinitionAlias), currentQueryField, lastField, splitted[i], out definition);
			currentDefinitionAlias = nextDefinitionAlias;
			nextDefinitionAlias = currentDefinitionAlias + "." + splitted[i];
		}

		columnExpression.ApplyField(lastField);
		
		if (lastField is { LookupSourceMappingTable: not null, MultiValue: true })
		{
			if (multivalueLookupField != null)
				throw new DataStoreQueryException(
					$"Stacked virtual field '{cleanedName}' contained multiple multivalue fields. Only one multivalue field is allowed. First multivalue field: '{multivalueLookupField.Name}', second: '{lastField.Name}'");
			columnExpression.AsQuery(GetMultivalueLookupField(definition, FilterParser.GetTableAlias(currentDefinitionAlias), lastField));
		}
		else if (multivalueLookupField == null)
		{
			columnExpression.AsQuery(GetTranslatableField(definition, FilterParser.GetTableAlias(currentDefinitionAlias), lastField, columnExpression));
		}
		else
		{
			FilterParser.AddJoins(multiValueFieldQuery!, currentQueryField, new Dictionary<string, string>());
			multiValueFieldQuery!.SelectRaw(_db.DataHelper.IsNullRaw(_db.DataHelper.ArrayAggregateRaw(
																		 $"[{FilterParser.GetTableAlias(currentDefinitionAlias)}].[{lastField.Name}] ORDER BY [{multivalueDefinitionAlias}].[{StorageSystemField.SysOrderBy}]"),
																	 @$"ARRAY\[\]::{_db.DataHelper.GetColumnType(lastField.Type)}\[\]"));
			multiValueFieldQuery.WhereRaw($"[{multivalueDefinitionAlias}].[{StorageSystemField.StorageFieldDefinitionId.ToString()}] = {multivalueLookupField.Id}");
			var result = _db.ConnectionHelper.GetCompiler().Compile(multiValueFieldQuery).RawSql;
			var sql = Regex.Replace(result, @"([\[\]{}])", @"\$1");
			columnExpression.AsQuery(_db.DataHelper.CastRaw("(" + sql + ")", lastField.Type, true));
			columnExpression.MultiValue = true;
		}
	}

	private StorageFieldDefinitionOrm ParseStacked(string fullStack, StorageIndexDefinition sid, string definitionAlias, string nextDefinitionAlias, QueryField queryField,
												   StorageFieldDefinitionOrm lastField, string nextFieldName, out StorageIndexDefinition nextDefinition)
	{
		if (lastField.LookupSource == null)
			throw new DataStoreQueryException($"Field {lastField.Name} is not a lookup field in datasource {sid.Name} for '{fullStack}'.");

		// if (lastField.MultiValue)
		// 	throw new DataStoreQueryException($"Field {lastField.Name} is a multivalue field in datasource {sid.Name} for '{fullStack}'.");
		
		nextDefinition = _getStorageIndexDefinition(lastField.LookupSource)!;
		var nextField = nextDefinition.Fields.FirstOrDefault(it => it.Name == nextFieldName);

		if (nextField == null)
			throw new DataStoreQueryException(
				$"Datasource '{nextDefinition.Name}' does not contain any field named '{nextFieldName}' in datasource {sid.Name} for '{fullStack}'.");

		queryField.PreconditionJoins.TryAdd(nextDefinitionAlias,
											new Join(nextDefinition.Name, nextDefinitionAlias, $"{definitionAlias}.{lastField.Name}",
													 $"{nextDefinitionAlias}.{StorageSystemField.Id}")); 
		
		return nextField;
	}

	private string GetMultivalueLookupField(StorageIndexDefinition sid, string? tableAlias, StorageFieldDefinitionOrm lookupField)
	{
		Query query = new Query(lookupField.LookupSourceMappingTable)
			.Join(lookupField.LookupSource, $"{lookupField.LookupSourceMappingTable}.{lookupField.LookupSource}",
				  $"{lookupField.LookupSource}.{StorageSystemField.Id}")
			.WhereColumns($"{tableAlias ?? sid.Name}.{StorageSystemField.Id}", "=", $"{lookupField.LookupSourceMappingTable}.{sid.Name}")
			.WhereRaw($"[{lookupField.LookupSourceMappingTable}].[{StorageSystemField.StorageFieldDefinitionId}] = {lookupField.Id}")
			.SelectRaw(_db.DataHelper.IsNullRaw(_db.DataHelper.ArrayAggregateRaw(
													$"[{lookupField.LookupSource}].[{StorageSystemField.Id}] ORDER BY [{lookupField.LookupSourceMappingTable}].[{StorageSystemField.SysOrderBy}]"),
												@$"ARRAY\[\]::{_db.DataHelper.GetColumnType(lookupField.Type)}\[\]"));
		return "(" + Regex.Replace(_db.ConnectionHelper.GetCompiler().Compile(query).RawSql, @"([\[\]{}])", @"\$1") + ")";
	}
}