using System.Collections;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;
using Elastic.Clients.Elasticsearch;
using FluentMigrator;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Domain.Storage.Db.QueryParser;
using Levelbuild.Domain.Storage.Helper;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using SharpCompress;
using SqlKata;
using SqlKata.Compilers;
using SqlKata.Execution;

namespace Levelbuild.Domain.Storage.Db;

/// <summary>
/// Contains helper functions for CRUD data
/// </summary>
public abstract class SqlDataHelper
{
	protected readonly ILogger Logger;
	protected readonly Db Db;
	private readonly HashSet<string> _failedMigrationTables;

	public SqlDataHelper(Db db, HashSet<string> failedMigrationTables, ILogger logger)
	{
		Db = db;
		_failedMigrationTables = failedMigrationTables;
		Logger = logger;
	}

	/// <summary>
	/// Expresses if this Database supports array column types
	/// </summary>
	protected internal virtual bool InlineMultiValueFields => false;

	/// <summary>
	/// Check if the field is null and replace null occurrences with <paramref name="replaceWith"/> e.g. COALESCE(field, 0)
	/// </summary>
	/// <param name="field"></param>
	/// <param name="replaceWith"></param>
	/// <returns></returns>
	public abstract string IsNullRaw(string field, object replaceWith);

	public abstract string StringAggregateRaw(string field, string separator, string? orderBy = null);

	public abstract string ArrayAggregateRaw(string field, string? orderBy = null);

	/// <summary>
	/// Cast the <paramref name="column"/> to the given field type
	/// </summary>
	/// <param name="column"></param>
	/// <param name="castToType"></param>
	/// <param name="array"></param>
	/// <returns></returns>
	public virtual string CastRaw(string column, DataStoreFieldType castToType, bool array = false)
	{
		return $"CAST({column} AS {GetColumnType(castToType)}{(array ? " ARRAY" : "")})";
	}

	public abstract string LimitStringLength(string field, int i);

	///  <summary>
	///  Is only being called if <see cref="InlineMultiValueFields"/> == true.
	///  Should be overwritten by database specific implementation if supported.
	///  Premises:
	///  <list type="bullet">
	/// 		<item>Arrays contain unique values</item>
	/// 		<item>Values within arrays are ordered</item>
	/// 		<item>The comparison should ignore the order of values within arrays. e.g: [1, 3, 2] == [1, 2, 3] -&gt; true</item>
	///  </list>
	///  </summary>
	///  <param name="query"></param>
	///  <param name="field"></param>
	///  <param name="param"></param>
	///  <exception cref="NotImplementedException"></exception>
	public virtual void ArrayEqual(Query query, QueryField field, IList param)
	{
		throw new NotSupportedException();
	}

	/// <inheritdoc cref="ArrayEqual(Query,QueryField,IList)"/>
	public virtual void ArrayNotEqual(Query query, QueryField field, IList param)
	{
		throw new NotSupportedException();
	}

	public virtual void ArrayEqualColumns(Query query, string fieldRaw, string compareFieldRaw)
	{
		throw new NotSupportedException();
	}

	public virtual void ArrayNotEqualColumns(Query query, string fieldRaw, string compareFieldRaw)
	{
		throw new NotSupportedException();
	}

	/// <inheritdoc cref="ArrayEqual(Query,QueryField,IList)"/>
	public virtual void ArrayLike(Query query, QueryField field, string param)
	{
		throw new NotSupportedException();
	}

	/// <inheritdoc cref="ArrayEqual(Query,QueryField,IList)"/>
	public virtual void ArrayNotLike(Query query, QueryField field, string param)
	{
		throw new NotSupportedException();
	}

	/// <inheritdoc cref="ArrayEqual(Query,QueryField,IList)"/>
	public virtual void ArrayLike(Query query, QueryField field, QueryField param)
	{
		throw new NotSupportedException();
	}

	/// <inheritdoc cref="ArrayEqual(Query,QueryField,IList)"/>
	public virtual void ArrayNotLike(Query query, QueryField field, QueryField param)
	{
		throw new NotSupportedException();
	}

	/// <inheritdoc cref="ArrayEqual(Query,QueryField,IList)"/>
	public virtual void ArrayLike(Query query, string param, QueryField field)
	{
		throw new NotSupportedException();
	}

	/// <inheritdoc cref="ArrayEqual(Query,QueryField,IList)"/>
	public virtual void ArrayNotLike(Query query, string param, QueryField field)
	{
		throw new NotSupportedException();
	}

	/// <inheritdoc cref="ArrayEqual(Query,QueryField,IList)"/>
	public virtual void ArrayContains(Query query, QueryField field, object param)
	{
		throw new NotSupportedException();
	}

	/// <inheritdoc cref="ArrayEqual(Query,QueryField,IList)"/>
	public virtual void ArrayNotContains(Query query, QueryField field, object param)
	{
		throw new NotSupportedException();
	}

	/// <inheritdoc cref="ArrayEqual(Query,QueryField,IList)"/>
	public virtual void ArrayOverlap(Query query, QueryField field, IList param)
	{
		throw new NotSupportedException();
	}

	public virtual void ArrayAggregate(Query query, Compiler compiler, QueryField field,
									   DataStoreColumnAggregationFunction columnAggregationFunction,
									   string compOperation, string compareTo, List<object> bindings)
	{
		throw new NotSupportedException();
	}

	/// <summary>
	/// In this method, the following compare operations should be supported: In, NotIn, Like, NotLike
	/// </summary>
	/// <param name="query"></param>
	/// <param name="field"></param>
	/// <param name="fieldRaw"></param>
	/// <param name="filterOperator">one of In, NotIn, Like, NotLike</param>
	/// <param name="compareOperation">corresponding to <paramref name="filterOperator"/>, one of in, not in, like, not like</param>
	/// <param name="arrayFieldRaw"></param>
	/// <exception cref="NotSupportedException"></exception>
	public virtual void ColumnToArrayColumn(Query query, string fieldRaw, QueryFilterOperator filterOperator,
											string compareOperation, string arrayFieldRaw)
	{
		throw new NotSupportedException();
	}

	public virtual void FulltextQuery(Query query, StorageIndexDefinition storageIndexDefinition, string compareValue)
	{
		throw new NotSupportedException();
	}

	public IDictionary<string, object?> Insert(StorageIndexDefinition sid, IDictionary<string, object?> dict,
											   Func<string, StorageIndexDefinition?> getStorageIndexDefinition)
	{
		return Db.ConnectionHelper.WithQueryFactory(it => Insert(it, sid, dict, getStorageIndexDefinition));
	}

	public IDictionary<string, object?> Insert(QueryFactory queryFactory, StorageIndexDefinition sid, IDictionary<string, object?> dict,
											   Func<string, StorageIndexDefinition?> getStorageIndexDefinition, string language = "en",
											   IList<DataStoreQueryField>? lookupFields = null)
	{
		var fields = sid.Fields.ToDictionary(field => field.Name);
		dict = dict.Select(keyValuePair =>
							   new KeyValuePair<string, object?>(keyValuePair.Key, ToType(keyValuePair.Value, fields[keyValuePair.Key])))
			.ToDictionary();

		var mvfLookupData = BuildMvfLookupData(sid, dict);

		var result = InsertWithConnection(queryFactory, sid, dict, mvfLookupData, language, getStorageIndexDefinition, lookupFields);
		result = CleanupDbResponse(sid, lookupFields, getStorageIndexDefinition, result)!;

		if (sid.FulltextSearch && Db.ElasticClient != null)
			Db.ElasticClient.IndexAsync(new IndexRequestDescriptor<IDictionary<string, object?>>(
												result.Where(it => fields.ContainsKey(it.Key) && ElasticFieldInclude(fields[it.Key])).ToDictionary(),
												sid.ElasticName(Db.CustomerContext!.ToDto()), (string)result[MultiValueFieldDefinition.Id]!)
											.Version(long.Parse(result[StorageSystemField.SysCurrentRevision.ToString()] + ""))
											.VersionType(VersionType.External));

		return result;
	}

	private static Dictionary<string, List<IDictionary<string, object?>>> BuildMvfLookupData(StorageIndexDefinition sid, IDictionary<string, object?> dict)
	{
		var mvfLookupFields = sid.Fields.Where(field => field.LookupSourceMappingTable != null).ToList();
		var mvfLookupData = new Dictionary<string, List<IDictionary<string, object?>>>();
		foreach (var mvfLookupField in mvfLookupFields)
		{
			if (!dict.Remove(mvfLookupField.Name, out var value) || value == null)
				continue;
			if (!value.IsArray())
				throw new DataStoreQueryException($"Data for multivalue field '{mvfLookupField.Name}' must be an array");
			var array = value.ToArray().ToList();

			var lookupEntries = new List<IDictionary<string, object?>>();
			mvfLookupData[mvfLookupField.LookupSourceMappingTable!] = lookupEntries;

			for (int i = 0; i < array.Count; i++)
			{
				var element = array[i];
				var lookupEntry = new Dictionary<string, object?>();
				lookupEntries.Add(lookupEntry);
				lookupEntry[sid.Name] = new Query(sid.Name).Select(StorageSystemField.Id.ToString());
				lookupEntry[StorageSystemField.StorageFieldDefinitionId.ToString()] = mvfLookupField.Id;
				lookupEntry[StorageSystemField.SysOrderBy.ToString()] = i;
				lookupEntry[mvfLookupField.LookupSource!] = element;
			}
		}

		return mvfLookupData;
	}

	protected abstract IDictionary<string, object?> InsertWithConnection(QueryFactory queryFactory, StorageIndexDefinition sid,
																		 IDictionary<string, object?> dict,
																		 IDictionary<string, List<IDictionary<string, object?>>> multiValueLookupData,
																		 string language,
																		 Func<string, StorageIndexDefinition?> getStorageIndexDefinition,
																		 IList<DataStoreQueryField>? lookupFields);

	/// <summary>
	/// Batch insert
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="sid"></param>
	/// <param name="dictList"></param>
	/// <returns></returns>
	protected abstract IList<Guid> InsertMultiWithConnection(QueryFactory queryFactory, StorageIndexDefinition sid,
															   IList<IDictionary<string, object?>> dictList);

	internal void SaveMvfValues(QueryFactory it, StorageIndexDefinition sid, IDictionary<string, object> dict,
								IEnumerable<string> mvfFieldNames, string id)
	{
		var cols = new[]
		{
			MultiValueFieldDefinition.ElementId, MultiValueFieldDefinition.FieldId, MultiValueFieldDefinition.FieldValue
		};
		List<List<object>> entries = new();

		// insert MVF data sets
		foreach (var mvfFieldName in mvfFieldNames)
		{
			if (!dict.TryGetValue(mvfFieldName, out object? values))
				continue;
			if (!(values is not string and (IEnumerable or Array)))
				throw new ArgumentException("multi value field is not an array");
			var mvfFieldValues = (IEnumerable)dict[mvfFieldName];
			var sidField = sid.ToDictionary()[mvfFieldName];
			foreach (var mvfFieldValue in mvfFieldValues)
			{
				List<object> entry = new List<object>()
				{
					id,
					sidField.Id,
					mvfFieldValue.ToString()!
				};
				entries.Add(entry);
			}
		}

		if (entries.Count > 0)
			it.Query(sid.MvfTable).Insert(cols, entries);
	}


	public IDictionary<string, object?> Update(StorageIndexDefinition sid, string id, IDictionary<string, object?> dict,
											   Func<string, StorageIndexDefinition?> getStorageIndexDefinition)
	{
		return Db.ConnectionHelper.WithTransaction(it => Update(it, sid, id, dict, getStorageIndexDefinition));
	}

	public IDictionary<string, object?> Update(QueryFactory queryFactory, StorageIndexDefinition sid, string id,
											   IDictionary<string, object?> dict, Func<string, StorageIndexDefinition?> getStorageIndexDefinition,
											   string language = "en",
											   IList<DataStoreQueryField>? lookupFields = null)
	{
		var fields = sid.Fields.ToDictionary(field => field.Name);
		dict = dict.Select(keyValuePair =>
							   new KeyValuePair<string, object?>(keyValuePair.Key, ToType(keyValuePair.Value, fields[keyValuePair.Key])))
			.ToDictionary();

		var mvfLookupData = BuildMvfLookupData(sid, dict);
		
		if (!Guid.TryParse(id, out var guid))
			throw new DataStoreQueryException($"string '{id}' is not a valid guid");

		var result = UpdateWithConnection(queryFactory, sid, guid, dict, mvfLookupData, language, getStorageIndexDefinition, lookupFields);
		result = CleanupDbResponse(sid, lookupFields, getStorageIndexDefinition, result);

		if (sid.FulltextSearch && Db.ElasticClient != null)
			Db.ElasticClient.IndexAsync(new IndexRequestDescriptor<IDictionary<string, object>>(
												result!.Where(it => fields.ContainsKey(it.Key) && ElasticFieldInclude(fields[it.Key])).ToDictionary()!,
												sid.ElasticName(Db.CustomerContext!.ToDto()), (string)result![MultiValueFieldDefinition.Id]!)
											.Version(long.Parse(result[StorageSystemField.SysCurrentRevision.ToString()] + ""))
											.VersionType(VersionType.External));

		return result!;
	}

	protected abstract IDictionary<string, object?> UpdateWithConnection(QueryFactory queryFactory, StorageIndexDefinition sid,
																		 Guid id, IDictionary<string, object?> dict,
																		 IDictionary<string, List<IDictionary<string, object?>>> multiValueLookupData,
																		 string language, Func<string, StorageIndexDefinition?> getStorageIndexDefinition,
																		 IList<DataStoreQueryField>? lookupFields);

	protected virtual IEnumerable<IDictionary<string, object?>> GetWithTransaction(QueryFactory queryFactory, StorageIndexDefinition sid, Query query,
																				   IList<DataStoreQueryField>? dataStoreQueryFields,
																				   Func<string, StorageIndexDefinition?>? getStorageIndexDefinition = null)
	{
		var rawRes = DoSelect(query, queryFactory);
		var list = new List<IDictionary<string, object?>>();
		foreach (var dictionary in rawRes)
		{
			IDictionary<string, object?>? response = CleanupDbResponse(sid, dictionary, true);
			if (response != null)
				list.Add(response);
		}

		return list;
	}

	public static IEnumerable<IDictionary<string, object?>> DoSelect(
		Query query,
		QueryFactory db,
		IDbTransaction? transaction = null,
		int? timeout = null)
	{
		Dictionary<string, object> bindings = new();
		var sql = CompileQuery(query, db, bindings);

		return ((IList)db.Select<dynamic>(
					   sql,
					   bindings,
					   transaction,
					   timeout
				   )).Cast<IDictionary<string, object?>>();
	}

	private static string CompileQuery(Query query, QueryFactory db, Dictionary<string, object> bindings)
	{
		var compiledQuery = db.Compiler.Compile(db.FromQuery(query));
		var sql = compiledQuery.RawSql;

		for (int i = 0; i < compiledQuery.NamedBindings.Count; i++)
		{
			var binding = compiledQuery.NamedBindings[$"@p{i}"];
			if (binding is Query subSelect)
			{
				var subSql = CompileQuery(subSelect, db, bindings);
				sql = sql.ReplaceFirst("?", subSql);
			}
			else
			{
				string placeholder = $"@p{bindings.Count}";
				sql = sql.ReplaceFirst("?", placeholder);
				bindings[placeholder] = binding;
			}
		}

		return sql;
	}

	/// <summary>
	/// Delete all MVF values of a certain element
	/// </summary>
	/// <param name="it"></param>
	/// <param name="mvfTableName"></param>
	/// <param name="elementId"></param>
	/// <param name="mvfFieldIds"></param>
	internal void DeleteMvfValues(QueryFactory it, string mvfTableName, string elementId, IEnumerable<long> mvfFieldIds)
	{
		it.Query(mvfTableName).Where(MultiValueFieldDefinition.ElementId, "=", elementId).WhereIn(MultiValueFieldDefinition.FieldId, mvfFieldIds).Delete();
	}

	public IDictionary<string, object?>? GetById(StorageIndexDefinition sid, string id, string language,
												 Func<string, StorageIndexDefinition?> getStorageIndexDefinition,
												 IList<DataStoreQueryField>? lookupFields = null, bool withDeleted = false,
												 bool withInactive = false, bool withHidden = false)
	{
		var result = Db.ConnectionHelper.WithQueryFactory(it =>
		{
			var query = GetPlainByIdQuery(sid, id, language, lookupFields, getStorageIndexDefinition, withDeleted, withInactive, withHidden, it,
										  out var changedFieldAliases);

			var dictRes1Task = query.FirstOrDefault();
			var dictRes1 = (IDictionary<string, object?>)dictRes1Task;

			if (dictRes1 != null)
			{
				foreach (var changedFieldAlias in changedFieldAliases)
				{
					if (dictRes1.Remove(changedFieldAlias.Key, out var value))
						dictRes1[changedFieldAlias.Value] = value;
				}
			}

			return dictRes1;
		});

		return CleanupDbResponse(sid, lookupFields, getStorageIndexDefinition, result);
	}

	protected IDictionary<string, object?>? CleanupDbResponse(StorageIndexDefinition sid,
															  IDictionary<string, object?>? result, bool ignoreRedundantFields)
	{
		return CleanupDbResponse(sid, null, null, result, ignoreRedundantFields);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="sid"></param>
	/// <param name="lookupFields"></param>
	/// <param name="getStorageIndexDefinition"></param>
	/// <param name="result"></param>
	/// <param name="ignoreRedundantFields"></param>
	/// <returns></returns>
	[return: NotNullIfNotNull(nameof(result))]
	protected IDictionary<string, object?>? CleanupDbResponse(StorageIndexDefinition sid, IList<DataStoreQueryField>? lookupFields,
															  Func<string, StorageIndexDefinition?>? getStorageIndexDefinition,
															  IDictionary<string, object?>? result, bool ignoreRedundantFields = false)
	{
		if (result == null)
			return null;

		if (!result.IsNullOrEmpty() && !InlineMultiValueFields && sid.HasMultiValueFields())
		{
			var pkField = sid.Fields.First(it => it.PrimaryKey);
			var pk = FromDb(result[StorageSystemField.Id.ToString()], pkField)!;
			var result1 = result;
			result = Db.ConnectionHelper.WithQueryFactory(it => GetMvfsForEntry(result1, sid, it, pk.ToString()!));
		}

		var fields = sid.Fields.ToDictionary(it => it.Name);
		var tables = new Dictionary<string, Dictionary<string, StorageFieldDefinitionOrm>>();
		if (lookupFields != null)
		{
			foreach (var lookupField in lookupFields)
			{
				var arr = lookupField.Name.Split(".");
				var table = fields[arr[0]].LookupSource;
				if (table != null)
				{
					if (!tables.ContainsKey(table))
					{
						tables[table] = getStorageIndexDefinition!(table)!.Fields.ToDictionary(it => it.Name);
					}

					var field = tables[table][arr[1]];
					fields[lookupField.Alias ?? lookupField.Name] = field;
				}
			}
		}

		StorageFieldDefinitionOrm isFavouriteField = new StorageFieldDefinitionOrm(StorageSystemField.SysIsFavourite.ToString(), DataStoreFieldType.Boolean);
		fields.Add(StorageSystemField.SysIsFavourite.ToString(), isFavouriteField);

		if (!ignoreRedundantFields)
			result = result.Where(it => fields.ContainsKey(it.Key)).ToDictionary();

		return result.Select(
				it =>
				{
					fields.TryGetValue(it.Key, out var field);
					return new KeyValuePair<string, object?>(it.Key, FromDb(it.Value, field));
				})
			.ToDictionary();
	}

	/// <summary>
	/// This method parses the high level filters to a database query.
	/// </summary>
	/// <param name="sid"></param>
	/// <param name="id">if null, no filter will be added (for create &amp; update only)</param>
	/// <param name="language"></param>
	/// <param name="lookupFields"></param>
	/// <param name="getStorageIndexDefinition"></param>
	/// <param name="withDeleted"></param>
	/// <param name="it"></param>
	/// <param name="changedFieldAliases"></param>
	/// <returns></returns>
	protected Query GetPlainByIdQuery(StorageIndexDefinition sid, string? id, string language, IList<DataStoreQueryField>? lookupFields,
									  Func<string, StorageIndexDefinition?> getStorageIndexDefinition, bool withDeleted, bool withInactive, bool withHidden,
									  QueryFactory it, out Dictionary<string, string> changedFieldAliases)
	{
		var filter = new FilterParser(Db, getStorageIndexDefinition, language, Db.CustomerContext!.Language);
		var storageQuery = new StorageQuery(sid.Name, new List<DataStoreQueryField>(), null);

		if (id != null)
		{
			var queryFilterGroup = new QueryFilterGroup()
				.AddFilter(new EqualsFilter(new QueryFilterField(StorageSystemField.Id.ToString()), id));
			storageQuery.WithFilter(queryFilterGroup);
		}

		if (lookupFields != null && lookupFields.Count > 0)
		{
			sid.Fields.Select(iter => new DataStoreQueryField(iter.Name)).ForEach(qff => storageQuery.Fields!.Add(qff));
			lookupFields.ForEach(lf => storageQuery.Fields!.Add(lf));
		}

		var query = it.FromQuery(filter.ParseQuery(sid, storageQuery, out var usedDefinitions, out changedFieldAliases, false, withDeleted, withInactive,
												   withHidden));
		var subSet = _failedMigrationTables.Intersect(usedDefinitions).ToList();
		if (subSet.Count != 0)
			throw new DataStoreOperationException(
				$"The following tables have failed migrations and can currently not be used: {string.Join(", ", _failedMigrationTables)}. The following tables are requested: {string.Join(", ", subSet)}");
		return query;
	}

	private IDictionary<string, object?> GetMvfsForEntry(IDictionary<string, object?> result, StorageIndexDefinition sid,
														 QueryFactory it, string id)
	{
		var mvfFieldById = sid.Fields.Where(iter => iter.MultiValue).ToDictionary(iter => iter.Id);
		//var allFields = sid.ToDictionary();

		var dictResMvfTask = it.Query(sid.MvfTable).Where(MultiValueFieldDefinition.ElementId, id)
			.OrderBy(MultiValueFieldDefinition.Id).Get().ToList();
		foreach (var mvfEntryDyn in dictResMvfTask)
		{
			IDictionary<string, object?> mvfEntry = (IDictionary<string, object?>)mvfEntryDyn;

			if (mvfEntry[MultiValueFieldDefinition.FieldId] == null)
				continue;

			var fieldId = (long)mvfEntry[MultiValueFieldDefinition.FieldId]!;
			var fieldValue = mvfEntry[MultiValueFieldDefinition.FieldValue];

			if (fieldValue == null)
				continue;

			var sidField = mvfFieldById[fieldId];

			var mvfList = new List<object>();
			if (result[sidField.Name] != null)
				mvfList = (List<object>)result[sidField.Name]!;

			var str = fieldValue.ToString();
			if (str == null)
				continue;
			mvfList.Add(fieldValue);
			result[sidField.Name] = mvfList;
		}

		foreach (var mvf in mvfFieldById.Values)
		{
			if (!result.TryGetValue(mvf.Name, out object? value) || value == null)
				result[mvf.Name] = new List<object>();
		}

		return result;
	}

	public IEnumerable<IDictionary<string, object?>> Get(StorageIndexDefinition sid, Query query,
														 IList<DataStoreQueryField>? dataStoreQueryFields,
														 Func<string, StorageIndexDefinition?>? getStorageIndexDefinition = null)
	{
		var res = Db.ConnectionHelper.WithQueryFactory(it => { return GetWithTransaction(it, sid, query, dataStoreQueryFields, getStorageIndexDefinition); });
		IEnumerable<IDictionary<string, object?>> enumerable = res.ToList();
		return enumerable;
	}


	/// <summary>
	/// 
	/// </summary>
	/// <param name="sid"></param>
	/// <param name="idCol"></param>
	/// <param name="dataStoreQueryFields"></param>
	/// <param name="rawRes"></param>
	/// <returns></returns>
	private IEnumerable<IDictionary<string, object>> AddMvfs(StorageIndexDefinition sid, string idCol,
															 IList<DataStoreQueryField>? dataStoreQueryFields, IEnumerable<IDictionary<string, object>> rawRes)
	{
		if ((dataStoreQueryFields == null) || (dataStoreQueryFields.Count == 0)
										   || (dataStoreQueryFields.Count(it =>
																			  !it.Name.Contains("(")
																			  && !it.Name.Contains(".")
																			  && sid.ToDictionary().ContainsKey(it.Name)
																			  && sid.ToDictionary()[it.Name].MultiValue) > 0))
		{
			var mvfFieldById = sid.Fields.Where(it => it.MultiValue).ToDictionary(it => it.Id);
			if ((dataStoreQueryFields != null) && (dataStoreQueryFields.Count > 0))
			{
				var fieldsToSelect = dataStoreQueryFields.Select(it =>
																	 Regex.Replace(it.Name, "([a-zA-Z]+)\\(", "").Replace(")", ""));

				mvfFieldById = sid.Fields.Where(it => fieldsToSelect.Contains(it.Name) && it.MultiValue).ToDictionary(it => it.Id);
			}

			if (mvfFieldById.Count > 0)
			{
				var idList = rawRes.Select(it => it[idCol]).ToList();
				//var mvfRes = _dataStore.Get(new Query(sid.MvfTable).WhereIn(MultiValueFieldDefinition.ElementId, idList));
				var mvfRes = Db.ConnectionHelper.WithQueryFactory(
					it => ((IList)it.Query(sid.MvfTable).WhereIn(MultiValueFieldDefinition.ElementId, idList).Get())
						.Cast<IDictionary<string, object>>()
				);

				foreach (var result in rawRes)
				{
					var mvfResForElement = mvfRes.Where(
						it => it[MultiValueFieldDefinition.ElementId].Equals(result[StorageSystemField.Id.ToString()]));
					foreach (var mvfEntryDyn in mvfResForElement)
					{
						IDictionary<string, object> mvfEntry = (IDictionary<string, object>)mvfEntryDyn;
						var fieldId = (long)mvfEntry[MultiValueFieldDefinition.FieldId];
						var fieldValue = mvfEntry[MultiValueFieldDefinition.FieldValue];

						if (fieldValue == null)
							continue;

						var sidField = mvfFieldById[fieldId];

						var mvfList = new List<object>();
						if (result[sidField.Name] != null)
							mvfList = (List<object>)result[sidField.Name];

						var str = fieldValue.ToString();
						if (str == null)
							continue;
						mvfList.Add(fieldValue);
						result[sidField.Name] = mvfList;
					}
				}
			}
		}

		return rawRes;
	}


	/// <summary>
	/// Delete a record from the defined DataSource
	/// </summary>
	/// <param name="sid">The DataSource</param>
	/// <param name="elementId">The ElementId within the DataSource</param>
	/// <returns>if the element has been found and deleted</returns>
	public bool Delete(StorageIndexDefinition sid, string elementId)
	{
		if (!Guid.TryParse(elementId, out var guid))
			throw new DataStoreQueryException($"string '{elementId}' is not a valid guid");
		var result = 1 == Db.ConnectionHelper.WithQueryFactory(it =>
		{
			return it.Query(sid.Name)
				.Where(StorageSystemField.Id.ToString(),
					   "=", guid).Delete();
		});

		if (result && sid.FulltextSearch && Db.ElasticClient != null)
			Db.ElasticClient.DeleteAsync(new DeleteRequestDescriptor(sid.ElasticName(Db.CustomerContext!.ToDto()), elementId)
											 // max possible version to ensure it will be deleted. Will be held in cache for one minute in elastic and then physically deleted
											 .Version(long.MaxValue - 1)
											 .VersionType(VersionType.External));

		return result;
	}

	private static QueryField AsQueryField(StorageFieldDefinitionOrm field)
	{
		return new QueryField(null!, new DataStoreQueryField(""), false, false, null)
		{
			MultiValue = field.MultiValue,
			DefaultValue = field.DefaultValue,
			Type = field.Type,
			Nullable = field.Nullable,
			Name = field.Name
		};
	}

	/// <inheritdoc cref="ToType(object?,QueryField)"/>
	public static object? ToType(object? value, StorageFieldDefinitionOrm field)
	{
		return ToType(value, AsQueryField(field));
	}

	/// <summary>
	/// parse the value so that it can be processed by the database
	/// </summary>
	/// <param name="value"></param>
	/// <param name="field"></param>
	/// <returns></returns>
	/// <exception cref="ArgumentException"></exception>
	public static object? ToType(object? value, QueryField field)
	{
		if (value is Enum)
			return value;

		if (field.MultiValue)
		{
			IList list = field.Type switch
			{
				DataStoreFieldType.Integer => new List<int>(),
				DataStoreFieldType.Long => new List<long>(),
				DataStoreFieldType.Double => new List<double>(),
				DataStoreFieldType.String or DataStoreFieldType.Text => new List<string>(),
				DataStoreFieldType.Date or DataStoreFieldType.Time or DataStoreFieldType.DateTime => new List<DateTime>(),
				DataStoreFieldType.Boolean => new List<bool>(),
				DataStoreFieldType.Guid => new List<Guid>(),
				_ => throw new ArgumentOutOfRangeException(nameof(field.Type), field.Type, null)
			};
			if (value == null)
				return null;

			if (!value.IsArray())
			{
				throw new ArgumentException($"data for multi value field '{field.Name}' is not enumerable: {value}");
			}

			foreach (var element in value.ToArray())
			{
				var elementString = element.ToString();
				if (elementString != null)
					list.Add(ToType(element, field.Type, elementString));
			}

			return list;
		}

		if (!field.Nullable && (value == null || value.ToString() == null))
		{
			if (field.DefaultValue != null)
				return GetDefaultValue(field);
			return null;
		}

		var str = value?.ToString();
		if (str == null)
			return null;

		// field translations need to be null for coalesce to work
		if (field.Name.Contains("__") && (string.IsNullOrWhiteSpace(str)))
			return null;

		return ToType(value!, field.Type, str);
	}

	/// <inheritdoc cref="GetDefaultValue(QueryField)"/>
	public static object? GetDefaultValue(StorageFieldDefinitionOrm field)
	{
		return GetDefaultValue(AsQueryField(field));
	}

	/// <summary>
	/// get the default value for the field definition
	/// </summary>
	/// <param name="field"></param>
	/// <returns>the default value for this field</returns>
	public static object? GetDefaultValue(QueryField field)
	{
		if (field.DefaultValue == null)
			return null;
		SystemMethods method;
		if (field.DefaultValue.ToString() != null
			&& Enum.IsDefined(typeof(SystemMethods), field.DefaultValue.ToString()!)
			&& Enum.TryParse(field.DefaultValue.ToString(), out method))
			return MethodToString(method);
		return ToType(field.DefaultValue, field);
	}

	/// <summary>
	/// Get the string representation of the method for the specified database
	/// </summary>
	/// <param name="method"></param>
	/// <returns></returns>
	protected static object MethodToString(SystemMethods method)
	{
		return method switch
		{
			SystemMethods.NewGuid => Guid.NewGuid().ToString(),
			SystemMethods.CurrentDateTime => ToType(DateTime.UtcNow, DataStoreFieldType.DateTime, ""),
			SystemMethods.CurrentUTCDateTime => ToType(DateTime.UtcNow, DataStoreFieldType.DateTime, ""),
			_ => throw new ArgumentOutOfRangeException(nameof(method), method, "not supported")
		};
	}

	private static object ToType(object value, DataStoreFieldType type, string str)
	{
		switch (type)
		{
			case DataStoreFieldType.String:
			case DataStoreFieldType.Text:
				return str;
			case DataStoreFieldType.Boolean:
				str = str.Trim().ToLower();
				return str is "true" or "t" or "y" or "yes" or "1" or "on";
			case DataStoreFieldType.Date:
				// drop timezone and remove time; set timezone to UTC
				if (value is DateTime dateTime)
					return DateTime.SpecifyKind(dateTime.ToUniversalTime().Date, DateTimeKind.Utc);
				return DateTime.SpecifyKind(DateTime.Parse(str).ToUniversalTime().Date, DateTimeKind.Utc);
			case DataStoreFieldType.Time:
				// drop timezone and remove day (1970-01-01); set timezone to UTC
				if (value is DateTime time)
					return DateTime.SpecifyKind(DateTime.UnixEpoch + time.ToUniversalTime().TimeOfDay, DateTimeKind.Utc);
				return DateTime.SpecifyKind(DateTime.UnixEpoch + DateTime.Parse(str).ToUniversalTime().TimeOfDay, DateTimeKind.Utc);
			case DataStoreFieldType.DateTime:
				if (value is DateTime dt)
				{
					// this should be an ArgumentException in the future
					if (dt.Kind == DateTimeKind.Unspecified)
						Log.Warning($"@MKU: DateTimeKind for DateTime {dt} is {nameof(DateTimeKind.Unspecified)}. Please use UTC instead.");
					return dt.ToUniversalTime();
				}
				return DateTime.Parse(str).ToUniversalTime();
			case DataStoreFieldType.Double:
				return Double.Parse(str);
			case DataStoreFieldType.Integer:
				return Int32.Parse(str);
			case DataStoreFieldType.Long:
				return Int64.Parse(str);
			case DataStoreFieldType.Guid:
				// an empty compare string shouldn't lead to an error
				if (string.IsNullOrEmpty(str))
					return Guid.Empty;
				if (!Guid.TryParse(str, out var guid))
					throw new DataStoreQueryException($"string '{str}' is not a valid guid");
				return guid;
			default:
				return value;
		}
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="value"></param>
	/// <param name="field"></param>
	/// <returns></returns>
	/// <exception cref="ArgumentException"></exception>
	protected virtual object? FromDb(object? value, StorageFieldDefinitionOrm? field)
	{
		if (value is Enum)
			return value;

		if (field == null)
		{
			if (value == null)
				return value;

			// guess the correct type
			DataStoreFieldType? type = value switch
			{
				Decimal => DataStoreFieldType.Double,
				_ => null
			};

			if (!type.HasValue)
				return value;

			return FromDb(value, type.Value);
		}

		if (field.MultiValue)
		{
			if (value == null)
				return Array.Empty<object>();

			if (!value.IsArray())
			{
				throw new ArgumentException($"data for multi value field '{field.Name}' is not enumerable: {value}");
			}

			List<object> list = new List<object>();
			foreach (var element in value.ToArray())
			{
				var elementString = element.ToString();
				if (elementString != null)
					list.Add(FromDb(element, field.Type));
			}

			return list;
		}

		if (value == null)
			return value;
		return FromDb(value, field.Type);
	}

	private static object FromDb(object value, DataStoreFieldType type)
	{
		switch (type)
		{
			case DataStoreFieldType.Double:
				if (value is string strDouble)
					return Double.Parse(strDouble);
				if (value is Decimal decDouble)
					return Decimal.ToDouble(decDouble);
				return value;
			case DataStoreFieldType.Date:
			case DataStoreFieldType.Time:
			case DataStoreFieldType.DateTime:
				if (value is TimeSpan ts)
					return DateTime.SpecifyKind(DateTime.UnixEpoch + ts, DateTimeKind.Utc);
				if (value is DateTime dt)
					return DateTime.SpecifyKind(dt, DateTimeKind.Utc);
				if (value is string str)
					return DateTime.SpecifyKind(DateTime.Parse(str), DateTimeKind.Utc);
				return value;
			case DataStoreFieldType.Guid:
				if (value is Guid guid)
					return guid.ToString();
				return value;
			default:
				return value;
		}
	}


	public string GetColumnType(DataStoreFieldType fieldType)
	{
		return fieldType switch
		{
			DataStoreFieldType.Boolean => GetBoolCol(),
			DataStoreFieldType.String => GetStringCol(),
			DataStoreFieldType.Text => GetTextCol(),
			DataStoreFieldType.Integer => GetIntCol(),
			DataStoreFieldType.Long => GetLongCol(),
			DataStoreFieldType.Double => GetDoubleCol(),
			DataStoreFieldType.Date => GetDateCol(),
			DataStoreFieldType.Time => GetTimeCol(),
			DataStoreFieldType.DateTime => GetDateTimeCol(),
			DataStoreFieldType.Guid => GetGuidCol(),
			_ => throw new ArgumentOutOfRangeException(nameof(fieldType), fieldType, null)
		};
	}

	protected virtual string GetBoolCol()
	{
		return "BOOLEAN";
	}

	protected virtual string GetStringCol()
	{
		return "VARCHAR";
	}

	protected virtual string GetTextCol()
	{
		return "TEXT";
	}

	protected virtual string GetIntCol()
	{
		return "INTEGER";
	}

	protected virtual string GetLongCol()
	{
		return "BIGINT";
	}

	protected virtual string GetDoubleCol()
	{
		return "DOUBLE PRECISION";
	}

	protected virtual string GetDateCol()
	{
		return "DATE";
	}

	protected virtual string GetTimeCol()
	{
		return "TIME";
	}

	protected virtual string GetDateTimeCol()
	{
		return "DATETIME";
	}

	protected virtual string GetGuidCol()
	{
		return "UUID";
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementDictionaryList"></param>
	/// <returns></returns>
	public IList<Guid> InsertMulti(QueryFactory queryFactory, StorageIndexDefinition storageIndexDefinition,
									 IList<IDictionary<string, object?>> elementDictionaryList)
	{
		/* maybe later...
		var fields = storageIndexDefinition.Fields.ToDictionary(field => field.Name);
		var unknown = dict.Keys.FirstOrDefault(it => !fields.ContainsKey(it));
		if (unknown != null)
			throw new DataStoreOperationException($"Field '{unknown}' does not exists in table {storageIndexDefinition.Name}");
		dict = dict.Select(keyValuePair =>
							   new KeyValuePair<string, object>(keyValuePair.Key, ToType(keyValuePair.Value, fields[keyValuePair.Key])))
			.ToDictionary();
		*/
		return InsertMultiWithConnection(queryFactory, storageIndexDefinition, elementDictionaryList);
	}

	internal static bool ElasticFieldInclude(StorageFieldDefinitionOrm fieldDefinitionOrm)
	{
		return fieldDefinitionOrm.Name == StorageSystemField.Id.ToString()
			   ||fieldDefinitionOrm.Name == StorageSystemField.SysGroups.ToString()
			   || fieldDefinitionOrm.Name == StorageSystemField.SysCurrentRevision.ToString()
			   || (!fieldDefinitionOrm.ExcludeFromFulltext
				   && (fieldDefinitionOrm.Type == DataStoreFieldType.String || fieldDefinitionOrm.Type == DataStoreFieldType.Text));
	}
}

public static class SqlKataQueryExtensionFunctions
{
	/// <summary>
	/// Adds the provided column with the aggregation function to the query as select
	/// </summary>
	/// <param name="query"></param>
	/// <param name="columnName"></param>
	/// <param name="columnAggregationFunction"></param>
	/// <param name="alias"></param>
	/// <returns></returns>
	public static Query SelectAggregate(this Query query, string columnName, DataStoreColumnAggregationFunction columnAggregationFunction, string? alias = null)
	{
		if (!columnName.Contains(".") && columnName.StartsWith("["))
			columnName = columnName.ReplaceFirst("[", "").ReplaceFirst("]", "");
		else if ((columnAggregationFunction != DataStoreColumnAggregationFunction.Avg) &&
				 (columnName.StartsWith("["))) // not for average function! Need brackets in cast!
			columnName = columnName.Replace("[", "").Replace("]", "");

		switch (columnAggregationFunction)
		{
			case DataStoreColumnAggregationFunction.Avg:
				if (columnName.Contains("."))
				{
					query.SelectRaw($"avg(cast(" + columnName + " as float))" + ((alias != null) ? " as [" + alias + "]" : ""));
				}
				else
				{
					query.SelectRaw($"avg(cast([" + columnName + "] as float))" + ((alias != null) ? " as [" + alias + "]" : ""));
				}

				break;
			case DataStoreColumnAggregationFunction.Sum:
				query.SelectSum((alias != null) ? (columnName + " as " + alias) : columnName);
				break;
			case DataStoreColumnAggregationFunction.Min:
				query.SelectMin((alias != null) ? (columnName + " as " + alias) : columnName);
				break;
			case DataStoreColumnAggregationFunction.Max:
				query.SelectMax((alias != null) ? (columnName + " as " + alias) : columnName);
				break;
			case DataStoreColumnAggregationFunction.Count:
				query.SelectCount((alias != null) ? (columnName + " as " + alias) : columnName);
				break;
		}

		return query;
	}

	public static Query Select(this Query query, QueryField field)
	{
		return query.SelectRaw(field.AsQueryString());
	}

	public static Query WithSelectGroupByPrimaryKey(this Query query, StorageIndexDefinition sid)
	{
		return query
			.Select($"{sid.Name}.{StorageSystemField.Id.ToString()}")
			.GroupBy($"{sid.Name}.{StorageSystemField.Id.ToString()}");
	}

	public static Query WhereRaw(this Query query, Compiler compiler, Query subSelect, string compareOperation, string compareToRaw, List<object> value)
	{
		var result = compiler.Compile(subSelect);
		var sql = Regex.Replace(result.RawSql, @"([\[\]{}])", @"\$1");
		List<object> bindings = new List<object>();
		bindings.AddRange(result.Bindings);
		bindings.AddRange(value);
		return query.WhereRaw($"(({sql}) {compareOperation} {compareToRaw})", bindings.ToArray());
	}

	public static Query WhereRaw(this Query query, Compiler compiler, object value, string compareOperation, Query subSelect)
	{
		var result = compiler.Compile(subSelect);
		var sql = Regex.Replace(result.RawSql, @"([\[\]{}])", @"\$1");
		List<object> bindings = new List<object>();
		bindings.Add(value);
		bindings.AddRange(result.Bindings);
		return query.WhereRaw($"(? {compareOperation} ({sql}))", bindings.ToArray());
	}

	public static Query WhereRawColumnToSubselect(this Query query, string column, string compareOperation, Query subSelect, Compiler compiler)
	{
		var result = compiler.Compile(subSelect);
		var sql = Regex.Replace(result.RawSql, @"([\[\]{}])", @"\$1");
		List<object> bindings = new List<object>();
		bindings.AddRange(result.Bindings);
		return query.WhereRaw($"({column} {compareOperation} ({sql}))", bindings.ToArray());
	}

	public static Query WhereExists(this Query query, object value, string compareOperator, Query subSelect, Compiler compiler)
	{
		var result = compiler.Compile(subSelect);
		var sql = Regex.Replace(result.RawSql, @"([\[\]{}])", @"\$1");
		List<object> bindings = new List<object>();
		bindings.Add(value);
		bindings.AddRange(result.Bindings);
		return query.WhereRaw($"EXISTS (? {compareOperator} ({sql}))", bindings.ToArray());
	}

	public static Query WhereIn(this Query query, object value, Query subSelect, Compiler compiler)
	{
		var result = compiler.Compile(subSelect);
		var sql = Regex.Replace(result.RawSql, @"([\[\]{}])", @"\$1");
		List<object> bindings = new List<object>();
		bindings.Add(value);
		bindings.AddRange(result.Bindings);
		return query.WhereRaw($"? in ({sql})", bindings.ToArray());
	}

	public static Query WhereNotIn(this Query query, object value, Query subSelect, Compiler compiler)
	{
		var result = compiler.Compile(subSelect);
		List<object> bindings = new List<object>();
		var sql = Regex.Replace(result.RawSql, @"([\[\]{}])", @"\$1");
		bindings.Add(value);
		bindings.AddRange(result.Bindings);
		return query.WhereRaw($"? not in ({sql})", bindings.ToArray());
	}

	public static Query WhereLike(this Query query, string column, Query subSelect, Compiler compiler)
	{
		var result = compiler.Compile(subSelect);
		var sql = Regex.Replace(result.RawSql, @"([\[\]{}])", @"\$1");
		List<object> bindings = new List<object>();
		bindings.AddRange(result.Bindings);
		return query.WhereRaw($"{column} like ({sql})", bindings.ToArray());
	}

	public static Query GetMultiValueFieldJoin(this Query query, StorageIndexDefinition sid, long multiValueFieldId)
	{
		string multiValueFieldDefinitionName = sid.GetMvfDefinition()!.Name;
		return query.LeftJoin(multiValueFieldDefinitionName,
							  j => j.On($"{sid.Name}.{StorageSystemField.Id.ToString()}",
										$"{multiValueFieldDefinitionName}.{MultiValueFieldDefinition.ElementId}")
								  .Where($"{multiValueFieldDefinitionName}.{MultiValueFieldDefinition.FieldId}", multiValueFieldId));
	}
}

public static class KataHelper
{
	/// <summary>
	///     A string extension method that replace first occurence.
	/// </summary>
	/// <param name="this">The @this to act on.</param>
	/// <param name="oldValue">The old value.</param>
	/// <param name="newValue">The new value.</param>
	/// <returns>The string with the first occurence of old value replace by new value.</returns>
	public static string ReplaceFirst(this string @this, string oldValue, string newValue)
	{
		int startindex = @this.IndexOf(oldValue);

		if (startindex == -1)
		{
			return @this;
		}

		return @this.Remove(startindex, oldValue.Length).Insert(startindex, newValue);
	}

	/// <summary>
	///     A string extension method that replace first number of occurences.
	/// </summary>
	/// <param name="this">The @this to act on.</param>
	/// <param name="number">Number of.</param>
	/// <param name="oldValue">The old value.</param>
	/// <param name="newValue">The new value.</param>
	/// <returns>The string with the numbers of occurences of old value replace by new value.</returns>
	public static string ReplaceFirst(this string @this, int number, string oldValue, string newValue)
	{
		List<string> list = @this.Split(oldValue).ToList();
		int old = number + 1;
		IEnumerable<string> listStart = list.Take(old);
		IEnumerable<string> listEnd = list.Skip(old);

		return string.Join(newValue, listStart) +
			   (listEnd.Any() ? oldValue : "") +
			   string.Join(oldValue, listEnd);
	}
}