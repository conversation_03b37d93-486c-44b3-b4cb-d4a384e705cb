using System.Data;
using System.Data.Common;
using System.Transactions;
using SqlKata;
using Serilog;
using SqlKata.Compilers;
using SqlKata.Execution;

namespace Levelbuild.Domain.Storage.Db;

public abstract class SqlConnectionHelper
{
	private readonly string _connectionString;
	protected readonly ILogger _logger;

	public SqlConnectionHelper(string connectionString, ILogger logger)
	{
		_connectionString = connectionString;
		_logger = logger;
	}

	protected abstract DbConnection GetConnection(string connectionString);

	public abstract DbDataReader GetReader(string sql, DbConnection connection,
										   CommandBehavior? commandBehavior = null);

	public void CreateDatabase(string databaseName)
	{
		using (var connection = GetConnection(_connectionString))
		{
			connection.Open();
			CreateDatabase(databaseName, connection);
		}
	}

	public string GetDatabaseName()
	{
		using (var connection = GetConnection(_connectionString))
		{
			return connection.Database;
		}
	}


	public void WithConnection(Action<DbConnection> func)
	{
		using (var connection = GetConnection(_connectionString))
		{
			connection.Open();
			func(connection);
		}
	}

	public T WithConnection<T>(Func<DbConnection, T> func)
	{
		using (var connection = GetConnection(_connectionString))
		{
			connection.Open();
			return func(connection);
		}
	}

	public void WithQueryFactory(Action<QueryFactory> func)
	{
		WithConnection(connection => { WithQueryFactory(connection, func); });
	}

	public void WithQueryFactory(IDbConnection connection, Action<QueryFactory> func)
	{
		var compiler = GetCompiler();
		var db = new QueryFactory(connection, compiler);
		db.Logger = compiled => { LogSql(compiled); };
		func(db);
	}

	public T WithQueryFactory<T>(Func<QueryFactory, T> func)
	{
		return WithConnection(connection =>
		{
			var compiler = GetCompiler();
			var db = new QueryFactory(connection, compiler);
			db.Logger = compiled => { LogSql(compiled); };
			return func(db);
		});
	}

	public T WithTransaction<T>(Func<QueryFactory, T> func)
	{
		return WithConnection(connection =>
		{
			using var transaction = connection.BeginTransaction();
			var compiler = GetCompiler();
			var db = new QueryFactory(connection, compiler);
			db.Logger = compiled => { LogSql(compiled); };
			var ret = func(db);
			transaction.Commit();
			return ret;
		});
	}

	private void LogSql(SqlResult compiled)
	{
		if (compiled.Query.Method != "insert")
		{
			_logger.Verbose(compiled.ToString());
		}
		else
		{
			// TODO
			// don't know why, but Substring function causes every filter test to fail
			//Console.WriteLine(compiled.Sql.Substring(0, 255));
			_logger.Verbose(compiled.Sql);
		}
	}


	protected internal abstract Compiler GetCompiler();

	/// <summary>
	/// 
	/// </summary>
	/// <param name="databaseName"></param>
	/// <param name="connection"></param>
	/// <returns>if the database was created</returns>
	protected internal abstract void CreateDatabase(string databaseName, DbConnection connection);

	/// <summary>
	/// Checks if the selected language is supported by the database. Returns the closest supported language (case insensitive check, but the return value corrects the case).
	/// </summary>
	/// <param name="configLanguage">The language which should be checked.</param>
	/// <returns>The language, case corrected.</returns>
	/// <exception cref="KeyNotFoundException">If the provided language is not supported by the database.</exception>
	public string ValidateLanguage(string configLanguage)
	{
		var langs = GetSupportedLanguages();
		var found = langs.Where(it => it.Equals(configLanguage)).FirstOrDefault();
		if (found != null)
			return found;
		found = langs.Where(it => it.Equals(configLanguage, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
		if (found != null)
			return found;

		throw new KeyNotFoundException(
			$"Language '{configLanguage}' is currently not supported by the database. Supported values are: '{string.Join(", ", langs)}'");
	}

	protected abstract string[] GetSupportedLanguages();
}