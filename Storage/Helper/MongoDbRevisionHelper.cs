using System.Diagnostics;
using System.Text.Json;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Domain.Storage.Db;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;
using Newtonsoft.Json;
using Serilog;
using SqlKata.Execution;

namespace Levelbuild.Domain.Storage.Helper;

public class MongoDbRevisionHelper : IRevisionHelper
{
	private readonly Db.Db _db;
	private IMongoDatabase _mongoDb;
	private readonly IFileStoreHelper _fileStore;
	private readonly StorageConnection _storageConnection;
	private readonly ILogger _logger;

	public const string Fields = "Fields";
	public const string MongoId = "_id";
	public const int MongoDbNameMaxLength = 38;

	/// <summary>
	/// Constructor to initialize RevisionHelper
	/// </summary>
	/// <param name="storageConnection"></param>
	/// <param name="db"></param>
	/// <param name="fileStoreHelper"></param>
	/// <param name="logger"></param>
	public MongoDbRevisionHelper(StorageConnection storageConnection, Db.Db db, IFileStoreHelper fileStoreHelper, ILogger logger)
	{
		_storageConnection = storageConnection;
		_db = db;
		_fileStore = fileStoreHelper;
		_logger = logger;
		_mongoDb = _db.MongoDbClient!.GetDatabase(FilterParser.ShortenAlias(_db.ConnectionHelper.GetDatabaseName(), null, MongoDbNameMaxLength));
	}

	/// <summary>
	/// Delete all revisions under given elementId in database and physical subfolder
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	public void DeleteRevisionsForElement(StorageIndexDefinition storageIndexDefinition, string elementId)
	{
		var collection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias(storageIndexDefinition.Name));
		var filter = Builders<BsonDocument>.Filter.Eq(RevisionDefinition.ElementId, elementId);
		collection.DeleteMany(filter);
	}

	/// <summary>
	/// Delete one certain revision in database and on disk
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionId"></param>
	public void DeleteRevision(StorageIndexDefinition storageIndexDefinition, string elementId, string revisionId)
	{
		var collection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias(storageIndexDefinition.Name));
		var filter = Builders<BsonDocument>.Filter.Eq(MongoId, ObjectId.Parse(revisionId));
		collection.DeleteOne(filter);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="values"></param>
	/// <param name="oldValues"></param>
	/// <param name="origin"></param>
	/// <param name="hasFile"></param>
	/// <param name="newRevNumber"></param>
	public void AddRevision(QueryFactory queryFactory, StorageIndexDefinition storageIndexDefinition, string elementId,
							IDictionary<string, object?> values, IDictionary<string, object?>? oldValues, DataStoreOperationOrigin origin,
							bool hasFile, long newRevNumber)
	{
		// No file required in MongoDB
		CreateRevision(queryFactory, storageIndexDefinition, elementId, values, origin, hasFile, DataStoreOperationType.Update, newRevNumber, oldValues);
	}

	/// <summary>
	/// create revision entry and create revision file on storage
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="values"></param>
	/// <param name="origin"></param>
	/// <param name="hasFile"></param>
	/// <param name="operationType"></param>
	/// <param name="newRevNumber"></param>
	/// <param name="oldValues"></param>
	/// <returns></returns>
	public void CreateRevision(QueryFactory? queryFactory, StorageIndexDefinition storageIndexDefinition, string elementId,
							   IDictionary<string, object?> values, DataStoreOperationOrigin origin, bool hasFile,
							   DataStoreOperationType operationType, long newRevNumber, IDictionary<string, object?>? oldValues = null)
	{
		RemoveFavourites(values, oldValues);

		// compare revisions values
		IDictionary<string, IDictionary<string, object?>> checkedFields = TransformAndCheck(values, oldValues, storageIndexDefinition.ToDictionary());

		// create revision entry in database
		IDictionary<string, object?> dict =
			GetDictionaryForRevisionData(storageIndexDefinition.GetRevisionDefinition()!, storageIndexDefinition.CurrentStoragePath!.Id, elementId,
										 newRevNumber, origin, hasFile, operationType);

		dict[RevisionDefinition.ChangedFields] = checkedFields.Where(it =>
																		 bool.Parse(it.Value[RevisionFieldValue.REVISION_FIELD_CHANGED]!.ToString()!))
			.Select(it => it.Value[RevisionFieldValue.REVISION_FIELD_ID]).ToList();

		if (storageIndexDefinition.CurrentStoragePath!.Path.Contains("##"))
		{
			string extendedRevisionPath = _fileStore.GetCurrentSavePath(storageIndexDefinition, values, elementId, newRevNumber);
			dict.Add(RevisionDefinition.ExtendedPath, extendedRevisionPath);
		}
		else
			dict.Add(RevisionDefinition.ExtendedPath, null);

		long newRevisionNumber = long.Parse(dict[RevisionDefinition.Revision]!.ToString()!);

		// firstly store revision meta data
		var collection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias(storageIndexDefinition.Name));
		BsonDocument document = new BsonDocument(dict);
		collection.InsertOne(document);

		// reset and store values & fields
		dict = new Dictionary<string, object?>();
		dict[RevisionDefinition.ElementId] = elementId;
		dict[RevisionDefinition.Revision] = newRevisionNumber;
		dict[Fields] = checkedFields;

		var valuesCollection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias($"{storageIndexDefinition.Name}_{Fields}"));
		BsonDocument valuesDocument = new BsonDocument(dict);
		valuesCollection.InsertOne(valuesDocument);

		// and save stored revision number
		_ = SaveRevisionFileAsync(storageIndexDefinition, elementId, newRevisionNumber);
	}

	/// <summary>
	/// Remove SysFavourites Field in revision data
	/// </summary>
	/// <param name="values"></param>
	/// <param name="oldValues"></param>
	private void RemoveFavourites(IDictionary<string, object?> values, IDictionary<string, object?>? oldValues)
	{
		// remove SysFavourites
		if (values.ContainsKey(StorageSystemField.SysFavourites.ToString()))
			values.Remove(StorageSystemField.SysFavourites.ToString());
		// remove SysFavourites
		if (oldValues != null && oldValues.ContainsKey(StorageSystemField.SysFavourites.ToString()))
			oldValues.Remove(StorageSystemField.SysFavourites.ToString());
	}

	/// <summary>
	/// Create revisions for batch insert
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementDatas"></param>
	/// <param name="origin"></param>
	/// <param name="hasFile"></param>
	/// <param name="operationType"></param>
	public void CreateMultipleRevisionsAfterInsert(QueryFactory queryFactory, StorageIndexDefinition storageIndexDefinition,
												   DataStoreResultSet<DataStoreElement> elementDatas,
												   DataStoreOperationOrigin origin, bool hasFile, DataStoreOperationType operationType)
	{
		IList<IDictionary<string, object?>> multiInserts = new List<IDictionary<string, object?>>();
		IList<BsonDocument> multiInsertsBson = new List<BsonDocument>();
		IList<BsonDocument> multiFieldInsertsBson = new List<BsonDocument>();
		foreach (DataStoreElement dataStoreElement in elementDatas)
		{
			IDictionary<string, object?> values = dataStoreElement.Values;
			// compare revisions values
			IDictionary<string, IDictionary<string, object?>> checkedFields = TransformAndCheck(values, null, storageIndexDefinition.ToDictionary());

			IDictionary<string, object?> dict =
				GetDictionaryForRevisionData(storageIndexDefinition.GetRevisionDefinition()!, storageIndexDefinition.CurrentStoragePath!.Id,
											 values[StorageSystemField.Id.ToString()]!.ToString()!, 1, origin, hasFile, operationType);

			dict[RevisionDefinition.ChangedFields] = checkedFields.Where(it => bool.Parse(it.Value[RevisionFieldValue.REVISION_FIELD_CHANGED]!.ToString()!))
				.Select(it => it.Value[RevisionFieldValue.REVISION_FIELD_ID]).ToList();


			if (storageIndexDefinition.CurrentStoragePath!.Path.Contains("##"))
			{
				string extendedRevisionPath = _fileStore.GetCurrentSavePath(storageIndexDefinition, values, dataStoreElement.Id,
																			Int64.Parse(dict[RevisionDefinition.Revision]!.ToString()!));
				dict.Add(RevisionDefinition.ExtendedPath, extendedRevisionPath);
			}

			// firstly store revision meta data
			multiInserts.Add(dict);
			multiInsertsBson.Add(new BsonDocument(dict));

			// reset and store values & fields
			IDictionary<string, object?> fieldsDictionary = new Dictionary<string, object?>();
			long newRevisionNumber = long.Parse(dict[RevisionDefinition.Revision]!.ToString()!);
			fieldsDictionary[RevisionDefinition.ElementId] = dataStoreElement.Id;
			fieldsDictionary[RevisionDefinition.Revision] = newRevisionNumber;
			fieldsDictionary[Fields] = checkedFields;
			multiFieldInsertsBson.Add(new BsonDocument(fieldsDictionary));
		}

		var collection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias(storageIndexDefinition.Name));
		collection.InsertMany(multiInsertsBson);

		var valuesCollection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias($"{storageIndexDefinition.Name}_{Fields}"));
		valuesCollection.InsertMany(multiFieldInsertsBson);

		foreach (IDictionary<string, object?> revisionEntry in multiInserts)
		{
			long newRevisionNumber = long.Parse(revisionEntry[RevisionDefinition.Revision]!.ToString()!);
			DataStoreElement element = elementDatas.First(entry => entry.Id == revisionEntry[RevisionDefinition.ElementId]!.ToString());

			// and save changes to file
			_ = SaveRevisionFileAsync(storageIndexDefinition, element.Id, newRevisionNumber);
		}
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="newRevId"></param>
	public async Task SaveRevisionFileAsync(StorageIndexDefinition storageIndexDefinition, string elementId, long newRevId)
	{
		await SaveRevisionFile(storageIndexDefinition, elementId, newRevId);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="newRevId"></param>
	/// <returns></returns>
	private Task SaveRevisionFile(StorageIndexDefinition storageIndexDefinition, string elementId, long newRevId)
	{
		return Task.Run(() =>
		{
			try
			{
				// set stored revision
				_db.DataHelper.Update(storageIndexDefinition, elementId,
									  new Dictionary<string, object?>() { { StorageSystemField.SysStoredRevision.ToString(), newRevId } }, _ => null);
			}
			catch (Exception e)
			{
				_logger.Error(e, "SysStoredRevision could not be set");
			}
		});
	}

	/// <summary>
	/// standard search functions for revisions with filter option
	/// </summary>
	/// <param name="dataStoreRevisionQuery"></param>
	/// <returns></returns>
	public DataStoreResultSet<DataStoreRevisionInfo> GetRevisions(DataStoreRevisionQuery dataStoreRevisionQuery)
	{
		var storageIndexDefinition = _storageConnection.WithDbContext(db =>
																		  db.StorageIndexDefinition
																			  .Include(it => it.Fields)
																			  .FirstOrDefault(it => it.Name == dataStoreRevisionQuery.DataSourceName));

		if (storageIndexDefinition == null)
			throw new DataStoreOperationException("Data source not found");

		if (!storageIndexDefinition.StoreRevisions)
			return new DataStoreResultSet<DataStoreRevisionInfo>(0);

		var collection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias(dataStoreRevisionQuery.DataSourceName));

		var builder = Builders<BsonDocument>.Filter;
		var filterList = GetFilters(builder, dataStoreRevisionQuery.Filters);
		filterList.Add(Builders<BsonDocument>.Filter.Eq(RevisionDefinition.ElementId, dataStoreRevisionQuery.ElementId));
		var filter = builder.And(filterList);

		var sortBuilder = Builders<BsonDocument>.Sort;

		List<BsonDocument> result;
		//var result = collection.Find(filter).Project(Builders<BsonDocument>.Projection.Exclude(revision => Values).Exclude(hrevision => Fields));
		Stopwatch watch = new Stopwatch();
		watch.Start();

		if (dataStoreRevisionQuery.Filters.Count > 0 || dataStoreRevisionQuery.OrderBy.Count > 0)
			result = collection.Find(filter)
				.Sort(GetSorter(sortBuilder, dataStoreRevisionQuery.OrderBy))
				.Skip(dataStoreRevisionQuery.Offset).Limit(dataStoreRevisionQuery.Limit).ToList();
		else
			result = collection.Find(filter)
				.Skip(dataStoreRevisionQuery.Offset).Limit(dataStoreRevisionQuery.Limit).ToList();

		watch.Stop();

		DataStoreResultSet<DataStoreRevisionInfo> set = new DataStoreResultSet<DataStoreRevisionInfo>(result.Count());
		foreach (var revisionEntry in result)
			set.Add(ToDataStoreRevisionInfo(revisionEntry));

		return set;
	}

	private SortDefinition<BsonDocument> GetSorter(SortDefinitionBuilder<BsonDocument> sortBuilder, IList<DataStoreRevisionSort> orderBy)
	{
		SortDefinition<BsonDocument> res = sortBuilder.Ascending(RevisionDefinition.Revision);
		if (orderBy.Count > 0)
		{
			foreach (DataStoreRevisionSort dataStoreRevisionSort in orderBy)
			{
				switch (dataStoreRevisionSort.Property)
				{
					case DataStoreRevisionProperty.Comment:
						res = GetSorterByIncoming(sortBuilder, res, dataStoreRevisionSort, RevisionDefinition.Comment);
						break;
					case DataStoreRevisionProperty.Date:
						res = GetSorterByIncoming(sortBuilder, res, dataStoreRevisionSort, RevisionDefinition.DateTime);
						break;
					case DataStoreRevisionProperty.Username:
						res = GetSorterByIncoming(sortBuilder, res, dataStoreRevisionSort, RevisionDefinition.UserName);
						break;
					case DataStoreRevisionProperty.OriginType:
						res = GetSorterByIncoming(sortBuilder, res, dataStoreRevisionSort, RevisionDefinition.OperationOriginType);
						break;
					case DataStoreRevisionProperty.Type:
						res = GetSorterByIncoming(sortBuilder, res, dataStoreRevisionSort, RevisionDefinition.OperationType);
						break;
				}
			}
		}

		return res;
	}

	private SortDefinition<BsonDocument> GetSorterByIncoming(SortDefinitionBuilder<BsonDocument> sortBuilder, SortDefinition<BsonDocument>? res,
															 DataStoreRevisionSort dataStoreRevisionSort, string fieldName)
	{
		if (res == null)
		{
			if (dataStoreRevisionSort.SortDirection == DataStoreElementSortDirection.Asc)
				res = sortBuilder.Ascending(fieldName);
			else
				res = sortBuilder.Descending(fieldName);
		}
		else
		{
			if (dataStoreRevisionSort.SortDirection == DataStoreElementSortDirection.Asc)
				res = res.Ascending(fieldName);
			else
				res = res.Descending(fieldName);
		}

		return res;
	}

	/// <summary>
	/// Gets last revision id having file
	/// </summary>
	/// <param name="definition"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public long GetCurrentRevisionNumberWithFile(StorageIndexDefinition definition, string elementId)
	{
		var collection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias(definition.Name));

		FilterDefinition<BsonDocument> filter = Builders<BsonDocument>.Filter.Where(document =>
																						document[RevisionDefinition.ElementId] == elementId
																						&& document[RevisionDefinition.HasFile] == true);

		SortDefinition<BsonDocument> sorter = Builders<BsonDocument>.Sort.Descending(RevisionDefinition.Revision);
		List<BsonDocument> result = collection.Find(filter).Sort(sorter).ToList();

		// return -1, than throw exception. Same as in RevisionHelper for Postgres. 
		if (result.Count == 0)
			return -1;

		long revisionNumber = long.Parse(result.First()[RevisionDefinition.Revision].ToString()!);
		return revisionNumber;
	}

	/// <summary>
	/// search for revisions using names of changed fields
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="fieldName"></param>
	/// <returns></returns>
	public DataStoreResultSet<DataStoreRevisionInfo> GetRevisionsByField(StorageIndexDefinition storageIndexDefinition, string elementId,
																		 string fieldName)
	{
		if (!storageIndexDefinition.StoreRevisions)
			return new DataStoreResultSet<DataStoreRevisionInfo>(0);

		var field = storageIndexDefinition.Fields.FirstOrDefault(it => it.Name == fieldName);
		if (field == null)
			throw new DataStoreOperationException($"Field {fieldName} not found in definition {FilterParser.ShortenAlias(storageIndexDefinition.Name)}.");

		var collection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias(storageIndexDefinition.Name));

		var builder = Builders<BsonDocument>.Filter;
		var filter = builder.And(builder.Eq(RevisionDefinition.ElementId, elementId),
								 builder.Eq(document => document[RevisionDefinition.ChangedFields], field.Id));

		List<BsonDocument> result = collection.Find(filter).SortBy(o => o[RevisionDefinition.Revision]).ToList();
		DataStoreResultSet<DataStoreRevisionInfo> set = new DataStoreResultSet<DataStoreRevisionInfo>(result.Count());
		foreach (var revisionEntry in result)
			set.Add(ToDataStoreRevisionInfo(revisionEntry));

		return set;
	}

	/// <summary>
	/// load a specific revision by its id
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionId"></param>
	/// <returns></returns>
	public DataStoreRevisionData? GetRevision(StorageIndexDefinition storageIndexDefinition, string elementId, string revisionId)
	{
		var collection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias(storageIndexDefinition.Name));
		var valuesCollection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias($"{storageIndexDefinition.Name}_{Fields}"));

		var filter = Builders<BsonDocument>.Filter.Eq(MongoId, ObjectId.Parse(revisionId));
		List<BsonDocument> result = collection.Find(filter).ToList();
		BsonDocument revision = result.First();

		filter = Builders<BsonDocument>.Filter.Where(document =>
														 document[RevisionDefinition.ElementId] == elementId
														 && document[RevisionDefinition.Revision] == revision[RevisionDefinition.Revision]);
		List<BsonDocument> valuesResult = valuesCollection.Find(filter).ToList();

		if (result.Count == 0)
			return null;

		RevisionFile revisionJson = GetRevisionJson(revision, valuesResult.First());
		return revisionJson.ToDto(ToDataStoreRevisionInfo(revision));
	}

	/// <summary>
	/// load a specific revision by its id
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <exception cref="ElementNotFoundException">If the revision is not found</exception>
	/// <returns></returns>
	public DataStoreRevisionData GetRevision(StorageIndexDefinition storageIndexDefinition, string elementId, long revisionNumber)
	{
		var collection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias(storageIndexDefinition.Name));
		var valuesCollection = _mongoDb.GetCollection<BsonDocument>(FilterParser.ShortenAlias($"{storageIndexDefinition.Name}_{Fields}"));
		var filter = Builders<BsonDocument>.Filter.Where(document =>
															 document[RevisionDefinition.ElementId] == elementId
															 && document[RevisionDefinition.Revision] == revisionNumber);

		List<BsonDocument> result = collection.Find(filter).ToList();
		List<BsonDocument> valuesResult = valuesCollection.Find(filter).ToList();

		if (result.Count == 0)
			throw new ElementNotFoundException($"Revision {revisionNumber} not found for elementId {elementId}");

		RevisionFile revisionJson = GetRevisionJson(result.First(), valuesResult.First());
		return revisionJson.ToDto(ToDataStoreRevisionInfo(result.First())) ??
			   throw new DataStoreOperationException($"Could not load revisionJson for revision {revisionNumber} and elementId {elementId}.");
	}

	private RevisionFile GetRevisionJson(BsonDocument revisionEntry, BsonDocument revisionValues)
	{
		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(
			(DataStoreOperationOriginType)revisionEntry[RevisionDefinition.OperationOriginType].AsInt32,
			Int64.Parse(revisionEntry[RevisionDefinition.InitiatorId].ToString()!),
			revisionEntry[RevisionDefinition.InitiatorName].AsString)
		{
			Comment = (!revisionEntry.Contains(RevisionDefinition.Comment)) ? "" : revisionEntry[RevisionDefinition.Comment].AsString
		};

		RevisionFile newRevision = new RevisionFile(origin);
		newRevision.Fields = GetRevisionFieldValues(revisionValues[Fields]);
		newRevision.Id = Int64.Parse(revisionEntry[RevisionDefinition.Revision].ToString()!);
		newRevision.ElementId = revisionEntry[RevisionDefinition.ElementId].AsString;
		newRevision.HasFile = revisionEntry[RevisionDefinition.HasFile].AsBoolean;
		newRevision.RevisionComment = (!revisionEntry.Contains(RevisionDefinition.Comment)) ? "" : revisionEntry[RevisionDefinition.Comment].AsString;
		newRevision.RevisionDateTime = DateTime.Parse(revisionEntry[RevisionDefinition.DateTime].ToString()!);
		newRevision.OperationType = (DataStoreOperationType)revisionEntry[RevisionDefinition.OperationType].AsInt32;

		return newRevision;
	}

	private IDictionary<string, RevisionFieldValue> GetRevisionFieldValues(BsonValue bsonValue)
	{
		IDictionary<string, RevisionFieldValue> fields = new Dictionary<string, RevisionFieldValue>();
		BsonDocument bsonDocument = BsonSerializer.Deserialize<BsonDocument>(bsonValue.ToJson());
		foreach (BsonElement value in bsonDocument.Elements)
		{
			RevisionFieldValue field = new RevisionFieldValue(value.Value[RevisionFieldValue.REVISION_FIELD_NAME].AsString,
															  (DataStoreFieldType)value.Value[RevisionFieldValue.REVISION_FIELD_TYPE].AsInt32,
															  (value.Value[RevisionFieldValue.REVISION_FIELD_VALUE] == BsonNull.Value)
																  ? null
																  : ParseElement(value.Value[RevisionFieldValue.REVISION_FIELD_VALUE],
																				 (DataStoreFieldType)value.Value[RevisionFieldValue.REVISION_FIELD_TYPE]
																					 .AsInt32))
			{
				Changed = value.Value[RevisionFieldValue.REVISION_FIELD_CHANGED].AsBoolean,
				Id = long.Parse(value.Value[RevisionFieldValue.REVISION_FIELD_ID].ToString()!),
				PreviousValue = (value.Value[RevisionFieldValue.REVISION_FIELD_PREVIOUS_VALUE] == BsonNull.Value)
									? null
									: ParseElement(value.Value[RevisionFieldValue.REVISION_FIELD_PREVIOUS_VALUE],
												   (DataStoreFieldType)value.Value[RevisionFieldValue.REVISION_FIELD_TYPE].AsInt32)
			};
			fields.Add(field.Name, field);
		}

		return fields;
	}

	private object? ParseElement(BsonValue value, DataStoreFieldType storageFieldType)
	{
		switch (value.BsonType)
		{
			case BsonType.Boolean:
				return value.AsBoolean;
			case BsonType.Double:
				return value.AsDouble;
			case BsonType.Int32:
				return value.AsInt32;
			case BsonType.Int64:
				return value.AsInt64;
			case BsonType.DateTime:
				return value.ToUniversalTime();
			case BsonType.Array:
				List<object?> objectList = new List<object?>();
				foreach (BsonValue bsonValue in value.AsBsonArray.Values)
				{
					objectList.Add(ParseElement(bsonValue, storageFieldType));
				}

				return objectList;
			case BsonType.String:
			default:
				if ((storageFieldType == DataStoreFieldType.DateTime
					 || storageFieldType == DataStoreFieldType.Date
					 || storageFieldType == DataStoreFieldType.Time)
					&& value.ToString() == "CurrentDateTime")
					return null;
				return value.ToString();
		}
	}

	/// <summary>
	///
	/// </summary>
	/// <param name="revisionDefinition"></param>
	/// <param name="pathId"></param>
	/// <param name="elementId"></param>
	/// <param name="newRevNumber"></param>
	/// <param name="origin"></param>
	/// <param name="hasFile"></param>
	/// <param name="operationType"></param>
	/// <returns></returns>
	private Dictionary<string, object?> GetDictionaryForRevisionData(StorageIndexDefinition revisionDefinition, long pathId,
																	 string elementId, long newRevNumber, DataStoreOperationOrigin origin,
																	 bool hasFile, DataStoreOperationType operationType)
	{
		Dictionary<string, object?> dict = new Dictionary<string, object?>();
		foreach (var fieldDefinition in revisionDefinition.Fields)
		{
			switch (fieldDefinition.Name)
			{
				case RevisionDefinition.Revision:
					dict.Add(fieldDefinition.Name, newRevNumber);
					break;
				case RevisionDefinition.ElementId:
					dict.Add(fieldDefinition.Name, elementId);
					break;
				case RevisionDefinition.DateTime:
					dict.Add(fieldDefinition.Name, DateTime.UtcNow);
					break;
				case RevisionDefinition.InitiatorId:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.InitiatorId);
					break;
				case RevisionDefinition.InitiatorName:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.InitiatorName);
					break;
				case RevisionDefinition.ActionId:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.ActionId);
					break;
				case RevisionDefinition.ActionName:
					if (origin != null && origin.ActionName != null)
						dict.Add(fieldDefinition.Name, origin.ActionName);
					break;
				case RevisionDefinition.Comment:
					if (origin != null && origin.Comment != null)
						dict.Add(fieldDefinition.Name, origin.Comment);
					break;
				case RevisionDefinition.PathId:
					dict.Add(fieldDefinition.Name, pathId);
					break;
				case RevisionDefinition.OperationType:
					dict.Add(fieldDefinition.Name, operationType);
					break;
				case RevisionDefinition.OperationOriginType:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.Type);
					break;
				case RevisionDefinition.HasFile:
					dict.Add(fieldDefinition.Name, hasFile);
					break;
				case RevisionDefinition.UserName:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.InitiatorName);
					break;
			}
		}

		return dict;
	}


	/// <summary>
	/// Transform to object with more properties
	/// Compare old and new values and mark changes to revision
	/// </summary>
	/// <param name="values"></param>
	/// <param name="oldValues"></param>
	/// <param name="dataStoreFieldDefinitions"></param>
	/// <returns></returns>
	private IDictionary<string, IDictionary<string, object?>> TransformAndCheck(IDictionary<string, object?> values,
																				IDictionary<string, object?>? oldValues,
																				IDictionary<string, StorageFieldDefinitionOrm> dataStoreFieldDefinitions)
	{
		// transform to object with more properties
		IDictionary<string, IDictionary<string, object?>> fields = new Dictionary<String, IDictionary<string, object?>>();
		foreach (var keyValuePair in values)
		{
			if (!dataStoreFieldDefinitions.TryGetValue(keyValuePair.Key, out var currentField))
				continue;

			// don't check PK for change
			if (currentField.PrimaryKey)
				continue;

			// use default value if field not exists in previous revision
			var oldValue = oldValues == null || !oldValues.ContainsKey(keyValuePair.Key)
							   ? (currentField.DefaultValue?.GetType() == typeof(JsonElement) ? null : currentField.DefaultValue)
							   : oldValues[keyValuePair.Key];

			// Compare old and new values and mark changes to revision
			bool isChangedField = IsChangedField(keyValuePair, oldValue);

			DataStoreFieldType type = currentField.Type;
			long id = currentField.Id;

			IDictionary<string, object?> newFieldValue = new Dictionary<string, object?>();
			newFieldValue.Add(RevisionFieldValue.REVISION_FIELD_NAME, keyValuePair.Key);
			newFieldValue.Add(RevisionFieldValue.REVISION_FIELD_TYPE, type);
			newFieldValue.Add(RevisionFieldValue.REVISION_FIELD_VALUE, keyValuePair.Value);
			newFieldValue.Add(RevisionFieldValue.REVISION_FIELD_ID, id);
			newFieldValue.Add(RevisionFieldValue.REVISION_FIELD_CHANGED, isChangedField);
			newFieldValue.Add(RevisionFieldValue.REVISION_FIELD_PREVIOUS_VALUE, oldValue);
			fields.Add(keyValuePair.Key, newFieldValue);
		}

		return fields;
	}

	private static bool IsChangedField(KeyValuePair<string, object?> keyValuePair, object? oldValue)
	{
		// compare null values
		if (keyValuePair.Value == null && oldValue == null)
			return false;

		if ((keyValuePair.Value == null && oldValue != null)
			|| (keyValuePair.Value != null && oldValue == null))
		{
			return true;
		}

		// compare non-arrays
		if (!keyValuePair.Value.IsArray() &&
			(!(keyValuePair.Value!.ToString()!.Equals(oldValue!.ToString()))
			 && !(JsonConvert.SerializeObject(keyValuePair.Value).Equals(JsonConvert.SerializeObject(oldValue)))))
		{
			return true;
		}

		// compare arrays
		if (keyValuePair.Value.IsArray() &&
			!(JsonConvert.SerializeObject(keyValuePair.Value).Equals(JsonConvert.SerializeObject(oldValue))))
		{
			return true;
		}

		return false;
	}

	/// <summary>
	/// transform selected revision entries to DataStoreRevisionInfo object
	/// </summary>
	/// <param name="revisionEntry"></param>
	/// <returns></returns>
	public static DataStoreRevisionInfo ToDataStoreRevisionInfo(BsonDocument revisionEntry)
	{
		DataStoreOperationType type = !revisionEntry.Contains(RevisionDefinition.OperationType)
										  ? DataStoreOperationType.Create
										  : (DataStoreOperationType)Enum.Parse(typeof(DataStoreOperationType),
																			   revisionEntry[RevisionDefinition.OperationType].ToString()!);

		DataStoreRevisionInfo info = new DataStoreRevisionInfo(
			revisionEntry[MongoId]!.ToString()!,
			DateTime.Parse(revisionEntry[RevisionDefinition.DateTime]!.ToString()!),
			revisionEntry[RevisionDefinition.UserName]!.ToString()!,
			type
		)
		{
			InitiatorId = (!revisionEntry.Contains(RevisionDefinition.InitiatorId)
							   ? 0
							   : Int64.Parse(revisionEntry[RevisionDefinition.InitiatorId].ToString()!)),
			InitiatorName = (!revisionEntry.Contains(RevisionDefinition.InitiatorName)
								 ? null
								 : revisionEntry[RevisionDefinition.InitiatorName].AsString),
			ActionName = (!revisionEntry.Contains(RevisionDefinition.ActionName)
							  ? null
							  : revisionEntry[RevisionDefinition.ActionName].AsString),
			ActionId = (!revisionEntry.Contains(RevisionDefinition.ActionId)
							? 0
							: Int64.Parse(revisionEntry[RevisionDefinition.ActionId].ToString()!)),
			Comment = (!revisionEntry.Contains(RevisionDefinition.Comment)
						   ? null
						   : revisionEntry[RevisionDefinition.Comment]!.AsString),
			HasFile = revisionEntry.Contains(RevisionDefinition.HasFile) && revisionEntry[RevisionDefinition.HasFile].AsBoolean
		};

		return info;
	}

	/// <summary>
	/// add filters to given query object
	/// </summary>
	/// <param name="builder"></param>
	/// <param name="revisionFilters"></param>
	private List<FilterDefinition<BsonDocument>> GetFilters(FilterDefinitionBuilder<BsonDocument> builder, IList<DataStoreRevisionFilter> revisionFilters)
	{
		var filterList = new List<FilterDefinition<BsonDocument>>();
		foreach (DataStoreRevisionFilter revisionFilter in revisionFilters)
		{
			string revisionPropertyName = GetRevisionPropertyName(revisionFilter.Property);
			filterList.Add(AddCompareField(builder, revisionFilter, revisionPropertyName));
		}

		return filterList;
	}

	/// <summary>
	/// translate given search property to revision field name
	/// </summary>
	/// <param name="revisionFilterProperty"></param>
	/// <returns></returns>
	private static string GetRevisionPropertyName(DataStoreRevisionProperty revisionFilterProperty)
	{
		switch (revisionFilterProperty)
		{
			case DataStoreRevisionProperty.Date:
				return RevisionDefinition.DateTime;
			case DataStoreRevisionProperty.Type:
				return RevisionDefinition.OperationType;
			case DataStoreRevisionProperty.Username:
				return RevisionDefinition.UserName;
			case DataStoreRevisionProperty.OriginType:
				return RevisionDefinition.OperationOriginType;
			case DataStoreRevisionProperty.Comment:
			default:
				return RevisionDefinition.Comment;
		}
	}

	/// <summary>
	/// add compare field for filter to given query
	/// </summary>
	/// <param name="builder"></param>
	/// <param name="revisionFilter"></param>
	/// <param name="revisionPropertyName"></param>
	private FilterDefinition<BsonDocument> AddCompareField(FilterDefinitionBuilder<BsonDocument> builder, DataStoreRevisionFilter revisionFilter,
														   string revisionPropertyName)
	{
		switch (revisionFilter.Operator)
		{
			case QueryFilterOperator.Equals:
				return builder.Eq(revisionPropertyName, revisionFilter.CompareValue);
			case QueryFilterOperator.NotEquals:
				return builder.Ne(revisionPropertyName, revisionFilter.CompareValue);
			case QueryFilterOperator.Exists:
				return builder.Exists(revisionPropertyName);
			case QueryFilterOperator.NotExists:
				return builder.Exists(revisionPropertyName, false);
			case QueryFilterOperator.In:
				return builder.In(revisionPropertyName, (IEnumerable<object>)revisionFilter.CompareValue!);
			case QueryFilterOperator.NotIn:
				return builder.Not(builder.In(revisionPropertyName, (IEnumerable<object>)revisionFilter.CompareValue!));
			case QueryFilterOperator.Like:
				string compareLike = revisionFilter.CompareValue.ToString().Replace("%", "");
				BsonRegularExpression regEx = BsonRegularExpression.Create(".*" + compareLike + ".*");
				return builder.Regex(revisionPropertyName, regEx);
			case QueryFilterOperator.NotLike:
				string compareNotLike = revisionFilter.CompareValue.ToString().Replace("%", "");
				BsonRegularExpression notRegEx = BsonRegularExpression.Create(".*" + compareNotLike + ".*");
				return builder.Not(builder.Regex(revisionPropertyName, notRegEx));
			case QueryFilterOperator.IsNull:
				return builder.Eq(revisionPropertyName, BsonNull.Value);
			case QueryFilterOperator.IsNotNull:
				return builder.Ne(revisionPropertyName, BsonNull.Value);
			case QueryFilterOperator.LessThan:
				return builder.Lt(revisionPropertyName, revisionFilter.CompareValue);
			case QueryFilterOperator.GreaterThan:
				return builder.Gt(revisionPropertyName, revisionFilter.CompareValue);
			case QueryFilterOperator.LessThanEquals:
				return builder.Lte(revisionPropertyName, revisionFilter.CompareValue);
			case QueryFilterOperator.GreaterThanEquals:
				return builder.Gte(revisionPropertyName, revisionFilter.CompareValue);
			default:
				return builder.Eq(revisionPropertyName, revisionFilter.CompareValue);
		}
	}
}