using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.FileInterface.Exception;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using FileInfo = Levelbuild.Core.FileInterface.FileInfo;

namespace Levelbuild.Domain.Storage.Helper;

/// <summary>
/// Provides helper methods for filesystem access of storage files
/// </summary>
public interface IFileStoreHelper
{
	/// <summary>
	/// encrypt and save RevisionFile
	/// </summary>
	/// <param name="revisionFile"></param>
	/// <param name="sid"></param>
	/// <param name="values"></param>
	/// <param name="tenantId"></param>
	/// <param name="encrypt"></param>
	public void SaveRevisionFile(RevisionFile revisionFile, StorageIndexDefinition sid, IDictionary<string, object?> values, string tenantId,
								 bool encrypt = false);

	/// <summary>
	/// load and decrypt RevisionFile
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="tenantId"></param>
	/// <param name="pathId"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <param name="decrypt"></param>
	/// <param name="extendedRevisionPath"></param>
	/// <returns></returns>
	public RevisionFile? LoadRevisionFile(StorageIndexDefinition storageIndexDefinition, string tenantId, long pathId, string elementId, long revisionNumber,
										  bool decrypt = false, string? extendedRevisionPath = null);

	/// <summary>
	/// returns current savePath for file and revisionFile
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fieldValues"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <param name="pathId"></param>
	/// <param name="extendedRevisionPath"></param>
	/// <returns></returns>
	public string GetCurrentSavePath(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?>? fieldValues, string elementId,
									 long? revisionNumber = null, long? pathId = null, string? extendedRevisionPath = null);

	/// <summary>
	/// Get the current save path for given elementId and revisionNumber
	/// </summary>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <returns></returns>
	public string GetContainer(string elementId, long revisionNumber);

	/// <summary>
	/// generate file id fo type meta for revision meta data file or of type data for content file
	/// </summary>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <param name="isRevisionFile"></param>
	/// <returns></returns>
	public string GetFileId(string elementId, long revisionNumber, bool isRevisionFile);

	/// <summary>
	/// saves an upload to a temporary location
	/// </summary>
	/// <param name="dataStoreFileStream"></param>
	/// <param name="fileUploadId"></param>
	/// <param name="fileSize"></param>
	/// <returns></returns>
	public string SaveTemporaryFile(DataStoreFileStream dataStoreFileStream, string fileUploadId, out long fileSize);

	/// <summary>
	/// Moves temporary file to final position
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fieldValues"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <param name="fileUploadId"></param>
	/// <param name="tenantId"></param>
	/// <param name="encrypt"></param>
	/// <returns></returns>
	public string SaveFile(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?> fieldValues, string elementId, long revisionNumber,
						   string fileUploadId, string tenantId,
						   bool encrypt = false);

	/// <summary>
	/// 
	/// </summary>
	/// <param name="fileUploadId"></param>
	/// <returns></returns>
	public FileInfo GetTempFileInfo(string fileUploadId);

	/// <summary>
	/// initialize StorageFile meta data object by given fileId
	/// </summary>
	/// <param name="fileId"></param>
	/// <returns></returns>
	public StorageFile? ParseFileId(string fileId);

	/// <summary>
	/// Load full StorageFile object from save path and set stream to read file data
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fieldValues"></param>
	/// <param name="tenantId"></param>
	/// <param name="storageFile"></param>
	/// <param name="pathId"></param>
	/// <param name="decrypt"></param>
	/// <returns></returns>
	public StorageFile LoadFile(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?>? fieldValues, string tenantId,
								StorageFile storageFile, long? pathId, bool decrypt = true);

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fieldValues"></param>
	/// <param name="tenantId"></param>
	/// <param name="storageFile"></param>
	/// <param name="pathId"></param>
	public void DeleteFile(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?>? fieldValues, string tenantId, StorageFile storageFile,
						   long pathId);

	/// <summary>
	/// Deletes physical file by FileInfo object and (if deleteEmptyDirectory = true) its containing folder, if there are no more items in it left
	/// </summary>
	/// <param name="fileInfo"></param>
	/// <param name="deleteEmptyDirectory"></param>
	/// <exception cref="FileDeleteException"></exception>
	public void DeleteFile(FileInfo fileInfo, bool deleteEmptyDirectory);

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageTempFile"></param>
	/// <returns></returns>
	public StorageFile GetTempFile(StorageTempFile storageTempFile);

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageTempFile"></param>
	/// <exception cref="FileDeleteException"></exception>
	public void DeleteTempFile(StorageTempFile storageTempFile);

	/// <summary>
	/// Give the temporary file its final name
	/// </summary>
	/// <param name="storageTempFile"></param>
	/// <returns></returns>
	public string RenameTempFile(StorageTempFile storageTempFile);
}