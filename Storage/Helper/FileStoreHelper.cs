using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.FileInterface;
using Levelbuild.Core.FileInterface.Enum;
using Levelbuild.Core.FileInterface.Exception;
using Levelbuild.Domain.FileSystemFile.Dto;
using Levelbuild.Domain.GoogleStorageFile;
using Levelbuild.Domain.S3BucketFile;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Newtonsoft.Json;
using Serilog;
using FileInfo = Levelbuild.Core.FileInterface.FileInfo;

namespace Levelbuild.Domain.Storage.Helper;

/// <inheritdoc />
public class FileStoreHelper : IFileStoreHelper
{
	private FileStore _storage;
	private FileStore _tempStorage;
	private readonly FeatureFlags _featureFlags;
	private readonly ILogger _logger;

	/// <summary>
	/// abstraction to file system 
	/// </summary>
	/// <param name="featureFlags"></param>
	/// <param name="storageContextOrm"></param>
	/// <param name="logger"></param>
	public FileStoreHelper(FeatureFlags featureFlags, StorageContextOrm storageContextOrm, ILogger logger)
	{
		_featureFlags = featureFlags;
		_logger = logger;
		var errorString = "Context with FilestoreType " + storageContextOrm.FilestoreType + " needs property {1}.";
		switch (storageContextOrm.FilestoreType)
		{
			case FileStoreType.FileSystem:
				_storage = new FileSystemStore(new FileSystemFileStoreConfig(storageContextOrm.StoragePath));
				_tempStorage = new FileSystemStore(new FileSystemFileStoreConfig(storageContextOrm.TemporaryPath));
				break;
			case FileStoreType.S3Bucket:
				_storage = new S3BucketFileStore(new S3BucketFileStoreConfig(storageContextOrm.StoragePath,
																			 storageContextOrm.FilestoreConnectionString ??
																			 throw new ContextConfigurationException(
																				 string.Format(
																					 errorString, nameof(storageContextOrm.FilestoreConnectionString))),
																			 storageContextOrm.FilestoreContainer ??
																			 throw new ContextConfigurationException(
																				 string.Format(errorString, nameof(storageContextOrm.FilestoreContainer))),
																			 storageContextOrm.S3PathStyleUrl,
																			 storageContextOrm.FilestoreUsername ??
																			 throw new ContextConfigurationException(
																				 string.Format(errorString, nameof(storageContextOrm.FilestoreUsername))),
																			 storageContextOrm.FilestorePassword ??
																			 throw new ContextConfigurationException(
																				 string.Format(errorString, nameof(storageContextOrm.FilestorePassword)))));
				_tempStorage = new S3BucketFileStore(new S3BucketFileStoreConfig(storageContextOrm.TemporaryPath, storageContextOrm.FilestoreConnectionString,
																				 storageContextOrm.FilestoreContainer, storageContextOrm.S3PathStyleUrl,
																				 storageContextOrm.FilestoreUsername, storageContextOrm.FilestorePassword));
				break;
			case FileStoreType.GoogleStorage:
				_storage = new GoogleStorageFileStore(
					new GoogleStorageFileStoreConfig(storageContextOrm.StoragePath,
													 storageContextOrm.FilestoreConnectionString ??
													 throw new ContextConfigurationException(
														 string.Format(errorString, nameof(storageContextOrm.FilestoreConnectionString))),
													 storageContextOrm.FilestoreContainer ??
													 throw new ContextConfigurationException(
														 string.Format(errorString, nameof(storageContextOrm.FilestoreContainer)))
					));
				_tempStorage = new GoogleStorageFileStore(
					new GoogleStorageFileStoreConfig(storageContextOrm.TemporaryPath, storageContextOrm.FilestoreConnectionString,
													 storageContextOrm.FilestoreContainer));
				break;
			default:
				throw new ContextConfigurationException($"FileStoreType '{storageContextOrm.FilestoreType}' not supported");
		}
	}

	/// <summary>
	/// encrypt and save RevisionFile
	/// </summary>
	/// <param name="revisionFile"></param>
	/// <param name="sid"></param>
	/// <param name="values"></param>
	/// <param name="tenantId"></param>
	/// <param name="encrypt"></param>
	/// <exception cref="FileSaveException"></exception>
	public void SaveRevisionFile(RevisionFile revisionFile, StorageIndexDefinition sid, IDictionary<string, object?> values, string tenantId,
								 bool encrypt = false)
	{
		try
		{
			string fileId = GetFileId(revisionFile.ElementId, revisionFile.Id, true);
			var revisionString = JsonConvert.SerializeObject(revisionFile);

			var saveDir = GetCurrentSavePath(sid, values, revisionFile.ElementId, revisionFile.Id);
			FileInfo revFile = _storage.GetFile(saveDir + fileId);

			if (encrypt)
			{
				byte[] key = EncryptionHelper.GetKey(tenantId, fileId, revisionFile.Id.ToString());
				if (_featureFlags.FileStreaming)
				{
					using (var outputStream = revFile.WriteFile())
					{
						MemoryStream inputStream = new MemoryStream(Encoding.UTF8.GetBytes(revisionString));
						EncryptionHelper.AesZipEncrypt(inputStream, outputStream, key);
					}
				}
				else
				{
					using (var outputStream = new MemoryStream())
					{
						using MemoryStream inputStream = new MemoryStream(Encoding.UTF8.GetBytes(revisionString));
						EncryptionHelper.AesZipEncrypt(inputStream, outputStream, key);
						revFile.WriteFile(outputStream.ToArray());
					}
				}
			}
			else
			{
				revFile.WriteText(revisionString);
			}
		}
		catch (Exception e)
		{
			throw new FileSaveException("Revision file could not be saved!", e);
		}
	}

	/// <summary>
	/// load and decrypt RevisionFile
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="tenantId"></param>
	/// <param name="pathId"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <param name="decrypt"></param>
	/// <param name="extendedRevisionPath"></param>
	/// <returns></returns>
	public RevisionFile? LoadRevisionFile(StorageIndexDefinition storageIndexDefinition, string tenantId, long pathId, string elementId, long revisionNumber,
										  bool decrypt = false, string? extendedRevisionPath = null)
	{
		RevisionFile? revisionFile = null;
		string revJson;
		string fileId = GetFileId(elementId, revisionNumber, true);
		var saveDir = GetCurrentSavePath(storageIndexDefinition, null, elementId, revisionNumber, pathId, extendedRevisionPath);
		try
		{
			FileInfo revFileInfo = _storage.GetFile(saveDir + fileId);

			if (decrypt)
			{
				byte[] key = EncryptionHelper.GetKey(tenantId, fileId, revisionNumber.ToString());
				using (var outputStream = new MemoryStream())
				{
					using MemoryStream inputStream = new MemoryStream(revFileInfo.ReadAllBytes());
					EncryptionHelper.AesZipDecrypt(inputStream, outputStream, key);
					revJson = Encoding.UTF8.GetString(outputStream.ToArray());
				}
			}
			else
			{
				revJson = Encoding.UTF8.GetString(revFileInfo.ReadAllBytes());
			}

			revisionFile = JsonConvert.DeserializeObject<RevisionFile>(revJson);
			revisionFile!.CurrentFileInfo = revFileInfo;
			return revisionFile;
		}
		catch (Exception e)
		{
			_logger.Error(e, "Problem loading revision file");
		}

		return null;
	}

	/// <summary>
	/// returns current savePath for file and revisionFile
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fieldValues"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <param name="pathId"></param>
	/// <param name="extendedRevisionPath"></param>
	/// <returns></returns>
	public string GetCurrentSavePath(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?>? fieldValues, string elementId,
									 long? revisionNumber = null, long? pathId = null, string? extendedRevisionPath = null)
	{
		StoragePath storagePath = storageIndexDefinition.CurrentStoragePath!; // current path of definition

		if (pathId != null)
			storagePath = storageIndexDefinition.StoragePathDict[(long)pathId]; // path of revision for given path id

		string currentPath = storagePath.Path + "/";

		if (currentPath.Contains("##"))
		{
			if (extendedRevisionPath != null)
				return extendedRevisionPath;
			currentPath = ReplacePlaceholders(currentPath, fieldValues);
		}

		if (revisionNumber != null)
		{
			string container = GetContainer(elementId, (long)revisionNumber);
			currentPath = currentPath + container + "/";
		}

		currentPath = currentPath + elementId + "/";

		return currentPath;
	}

	/// <summary>
	/// Finction to replace placeholders for dynamic save paths
	/// </summary>
	/// <param name="currentPath"></param>
	/// <param name="fieldValues"></param>
	/// <returns></returns>
	private string ReplacePlaceholders(string currentPath, IDictionary<string, object?>? fieldValues)
	{
		string pattern = @"##[a-zA-Z0-9_-]{1,}##";
		Match m = Regex.Match(currentPath, pattern);
		while (m.Success)
		{
			string fieldName = m.Value.Replace("##", "");
			if (fieldValues?[fieldName] != null)
			{
				string fieldValue = fieldValues[fieldName]!.ToString()!;
				fieldValue = Regex.Replace(fieldValue, @"[^a-zA-Z0-9_-]", ""); // replace all characters, not in whitelist
				currentPath = Regex.Replace(currentPath, pattern, fieldValue);
			}
			else
			{
				currentPath = Regex.Replace(currentPath, pattern, "");
			}

			m = Regex.Match(currentPath, pattern);
		}

		return currentPath;
	}

	/// <summary>
	/// Get the current save path for given elementId and revisionNumber
	/// </summary>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <returns></returns>
	public string GetContainer(string elementId, long revisionNumber)
	{
		string container = (elementId + "_" + revisionNumber);
		using (MD5 md5 = MD5.Create())
		{
			byte[] inputBytes = System.Text.Encoding.ASCII.GetBytes(container);
			byte[] hashBytes = md5.ComputeHash(inputBytes);
			container = (Convert.ToHexString(hashBytes)).Substring(0, 4);
		}

		return container;
	}

	/// <summary>
	/// generate file id fo type meta for revision meta data file or of type data for content file
	/// </summary>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <param name="isRevisionFile"></param>
	/// <returns></returns>
	public string GetFileId(string elementId, long revisionNumber, bool isRevisionFile)
	{
		return $"{elementId}_{revisionNumber}" + ((isRevisionFile) ? "_meta" : "_data");
		//return revisionNumber + ((isRevisionFile) ? "_meta" : "_data");
	}

	/// <inheritdoc />
	public string SaveTemporaryFile(DataStoreFileStream dataStoreFileStream, string fileUploadId, out long fileSize)
	{
		var hash = "";
		try
		{
			FileInfo file = _tempStorage.GetFile(fileUploadId);
			file.WriteFile(dataStoreFileStream);

			using (var md5 = MD5.Create())
				using (var stream = file.ReadFile())
				{
					hash = BitConverter.ToString(md5.ComputeHash(stream)).Replace("-", "").ToLowerInvariant();
				}

			fileSize = file.GetFileSize();
		}
		catch (Exception e)
		{
			throw new FileSaveException("Temporary file could not be saved!", e);
		}

		return hash;
	}

	/// <summary>
	/// Moves temporary file to final position
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fieldValues"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <param name="fileUploadId"></param>
	/// <param name="tenantId"></param>
	/// <param name="encrypt"></param>
	/// <returns></returns>
	/// <exception cref="FileSaveException"></exception>
	public string SaveFile(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?> fieldValues, string elementId, long revisionNumber,
						   string fileUploadId, string tenantId,
						   bool encrypt = false)
	{
		string fileId = GetFileId(elementId, revisionNumber, false);
		var saveDir = GetCurrentSavePath(storageIndexDefinition, fieldValues, elementId, revisionNumber);
		try
		{
			FileInfo fileInfo = _storage.GetFile(saveDir + fileId);
			FileInfo tempFileInfo = _tempStorage.GetFile(fileUploadId);

			if (encrypt)
			{
				byte[] key = EncryptionHelper.GetKey(tenantId, fileId, revisionNumber.ToString());
				if (_featureFlags.FileStreaming)
				{
					using (var outputStream = fileInfo.WriteFile())
						using (var inputStream = tempFileInfo.ReadFile())
						{
							EncryptionHelper.AesZipEncrypt(inputStream, outputStream, key);
						}
				}
				else
				{
					using (var outputStream = new MemoryStream())
						using (var inputStream = tempFileInfo.ReadFile())
						{
							EncryptionHelper.AesZipEncrypt(inputStream, outputStream, key);
							fileInfo.WriteFile(outputStream.ToArray());
						}
				}
			}
			else
			{
				tempFileInfo.CopyTo(fileInfo);
			}

			return fileId;
		}
		catch (Exception e)
		{
			throw new FileSaveException("File could not be saved!", e);
		}
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="fileUploadId"></param>
	/// <returns></returns>
	public FileInfo GetTempFileInfo(string fileUploadId)
	{
		FileInfo tempFileInfo = _tempStorage.GetFile(fileUploadId);
		return tempFileInfo;
	}

	/// <summary>
	/// initialize StorageFile meta data object by given fileId
	/// </summary>
	/// <param name="fileId"></param>
	/// <returns></returns>
	public StorageFile? ParseFileId(string fileId)
	{
		var fileIdParts = fileId.Split("_", StringSplitOptions.TrimEntries);

		if (fileIdParts.Length != 3)
			return null;

		string elementId = fileIdParts[0];
		string revisionNumber = fileIdParts[1];
		if (Int64.TryParse(revisionNumber, out var revNum))
			return new StorageFile(fileId, elementId, revNum);
		return null;
	}

	/// <summary>
	/// Load full StorageFile object from save path and set stream to read file data
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fieldValues"></param>
	/// <param name="tenantId"></param>
	/// <param name="storageFile"></param>
	/// <param name="pathId"></param>
	/// <param name="decrypt"></param>
	/// <returns></returns>
	public StorageFile LoadFile(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?>? fieldValues, string tenantId,
								StorageFile storageFile, long? pathId, bool decrypt = true)
	{
		var saveDir = GetCurrentSavePath(storageIndexDefinition, fieldValues, storageFile.ElementId!, storageFile.RevisionNumber, pathId);
		Stream? stream = null;
		try
		{
			FileInfo fileInfo = _storage.GetFile(saveDir + storageFile.FileId);
			stream = fileInfo.ReadFile();

			if (decrypt)
			{
				byte[] key = EncryptionHelper.GetKey(tenantId, storageFile.FileId, storageFile.RevisionNumber.ToString()!);
				Stream outputStream = EncryptionHelper.AesZipDecrypt(stream, key);
				storageFile.SetStream(outputStream);
			}
			else
			{
				storageFile.SetStream(stream);
			}
		}
		catch (Exception e)
		{
			_logger.Error(e, "File not readable: ");
			if (stream != null)
			{
				try
				{
					stream.Close();
				}
				catch (Exception)
				{
					// ignored
				}

				try
				{
					storageFile.Read().Close();
				}
				catch (Exception)
				{
					// ignored
				}
			}

			throw;
		}

		return storageFile;
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fieldValues"></param>
	/// <param name="tenantId"></param>
	/// <param name="storageFile"></param>
	/// <param name="pathId"></param>
	/// <exception cref="FileDeleteException"></exception>
	public void DeleteFile(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?>? fieldValues, string tenantId, StorageFile storageFile,
						   long pathId)
	{
		string currentSavePath = GetCurrentSavePath(storageIndexDefinition, fieldValues, storageFile.ElementId!, storageFile.RevisionNumber, pathId);
		var saveDir = GetCurrentSavePath(storageIndexDefinition, fieldValues, storageFile.ElementId!, storageFile.RevisionNumber, pathId);

		var retryCount = 5;
		for (var i = 0; i < retryCount; i++)
		{
			try
			{
				FileInfo fileInfo = _storage.GetFile(saveDir + storageFile.FileId);
				fileInfo.DeleteFile();
				break;
			}
			catch (Exception e)
			{
				if (i == (retryCount - 1))
					throw new FileDeleteException("File could not be deleted!", e);
				else
					Thread.Sleep(100);
				_logger.Debug($"DeleteFile failed, retry ({i})...");
			}
		}
	}

	/// <summary>
	/// Deletes physical file by FileInfo object and (if deleteEmptyDirectory = true) its containing folder, if there are no more items in it left
	/// </summary>
	/// <param name="fileInfo"></param>
	/// <param name="deleteEmptyDirectory"></param>
	/// <exception cref="FileDeleteException"></exception>
	public void DeleteFile(FileInfo fileInfo, bool deleteEmptyDirectory)
	{
		try
		{
			fileInfo.DeleteFile();
		}
		catch (Exception e)
		{
			throw new FileDeleteException("File could not be deleted!", e);
		}
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageTempFile"></param>
	/// <returns></returns>
	public StorageFile GetTempFile(StorageTempFile storageTempFile)
	{
		StorageFile storageFile = storageTempFile.ToStorageFile();
		FileInfo tempFileInfo = _tempStorage.GetFile(storageTempFile.FileId.ToString());
		if (!tempFileInfo.Exists())
		{
			var finalFileId = GetFileId(storageTempFile.ElementId!, (long)storageTempFile.RevisionNumber!, false);
			tempFileInfo = _tempStorage.GetFile(finalFileId);
		}

		storageFile.SetStream(tempFileInfo.ReadFile());
		storageFile.FileLength = storageTempFile.FileSize;
		return storageFile;
	}

	/// <inheritdoc />
	public string RenameTempFile(StorageTempFile storageTempFile)
	{
		FileInfo tempFileInfo = _tempStorage.GetFile(storageTempFile.FileId.ToString());
		var finalFileId = GetFileId(storageTempFile.ElementId!, (long)storageTempFile.RevisionNumber!, false);
		FileInfo newTempFileInfo = _tempStorage.GetFile(finalFileId);
		MoveWithRetry(tempFileInfo, newTempFileInfo);
		return finalFileId;
	}

	private bool MoveWithRetry(FileInfo tempFileInfo, FileInfo newTempFileInfo)
	{
		Exception? localException = null;
		int retryTimes = 5;
		for (int i = 0; i < retryTimes; i++)
		{
			try
			{
				tempFileInfo.MoveTo(newTempFileInfo);
				return true;
			}
			catch (Exception e)
			{
				localException = e;
			}
		}

		if (localException != null)
			_logger.Error(localException, "Unable to move file: ");
		return false;
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageTempFile"></param>
	/// <exception cref="FileDeleteException"></exception>
	public void DeleteTempFile(StorageTempFile storageTempFile)
	{
		try
		{
			FileInfo tempFileInfo = _tempStorage.GetFile(storageTempFile.FileId);
			if (tempFileInfo.Exists())
				tempFileInfo.DeleteFile();
		}
		catch (Exception e)
		{
			_logger.Warning(e, "Temporary file could not be deleted!");
			throw new FileDeleteException("Temporary file could not be deleted!", e);
		}
	}
}