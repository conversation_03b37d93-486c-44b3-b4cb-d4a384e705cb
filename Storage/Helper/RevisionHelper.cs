using System.Collections;
using System.Text.Json;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Domain.Storage.Db;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;
using SqlKata;
using SqlKata.Execution;

namespace Levelbuild.Domain.Storage.Helper;

public class RevisionHelper : IRevisionHelper
{
	private readonly Db.Db _db;
	private readonly StorageContextOrm _storageContext;
	private readonly IFileStoreHelper _fileStore;
	private readonly StorageConnection _storageConnection;
	private readonly ILogger _logger;

	/// <summary>
	/// Constructor to initialize RevisionHelper
	/// </summary>
	/// <param name="storageConnection"></param>
	/// <param name="db"></param>
	/// <param name="storageContext"></param>
	/// <param name="fileStoreHelper"></param>
	/// <param name="logger"></param>
	public RevisionHelper(StorageConnection storageConnection, Db.Db db,
						  StorageContextOrm storageContext, IFileStoreHelper fileStoreHelper, ILogger logger)
	{
		_storageConnection = storageConnection;
		_db = db;
		_storageContext = storageContext;
		_fileStore = fileStoreHelper;
		_logger = logger;
	}

	/// <summary>
	/// selects the meta data of last revision from database
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="limit"></param>
	/// <param name="offset"></param>
	/// <returns></returns>
	public IDictionary<string, object?>? GetRevisionDataSet(QueryFactory queryFactory, StorageIndexDefinition storageIndexDefinition, string elementId,
															int limit,
															int offset)
	{
		var resList = queryFactory.Query(storageIndexDefinition.RevisionTable)
			.Where(RevisionDefinition.ElementId, Guid.Parse(elementId))
			.OrderByDesc(new[] { RevisionDefinition.Id }).Limit(limit).Offset(offset).Get().ToList();

		IDictionary<string, object?>? resDict = ((resList.Count() > 0) ? (IDictionary<string, object?>)resList.First() : null);
		return resDict;
	}

	/// <summary>
	/// Delete all revisions under given elementId in database and physical subfolder
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	public void DeleteRevisionsForElement(StorageIndexDefinition storageIndexDefinition, string elementId)
	{
		_db.ConnectionHelper.WithQueryFactory(it =>
		{
			var query = it.Query(storageIndexDefinition.RevisionTable)
				.Where(RevisionDefinition.ElementId, Guid.Parse(elementId));
			var res = ((IList)query.Get())
				.Cast<IDictionary<string, object>>();

			foreach (var revision in res)
			{
				var revisionId = Int64.Parse(revision[RevisionDefinition.Revision]!.ToString()!);
				var pathId = Int64.Parse(revision[RevisionDefinition.PathId]!.ToString()!);
				var revisionFile = GetRevisionFileForDataSet(storageIndexDefinition, pathId, elementId, revisionId,
															 revision[RevisionDefinition.ExtendedPath]?.ToString());
				revisionFile?.CurrentFileInfo.DeleteFile();
			}
		});

		// delete all revisions for given elementId in database
		_db.ConnectionHelper.WithQueryFactory(it =>
		{
			it.Query(storageIndexDefinition.RevisionTable)
				.Where(RevisionDefinition.ElementId, Guid.Parse(elementId)).Delete();
		});
	}

	/// <summary>
	/// Delete one certain revision in database and on disk
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionId"></param>
	public void DeleteRevision(StorageIndexDefinition storageIndexDefinition, string elementId, string revisionId)
	{
		_db.ConnectionHelper.WithQueryFactory(it =>
		{
			var queryResult = it.Query(storageIndexDefinition.RevisionTable)
				.Where(RevisionDefinition.ElementId, Guid.Parse(elementId))
				.Where(RevisionDefinition.Id, Guid.Parse(revisionId)).Get();
			var revision = (IDictionary<string, object>)queryResult.First();
			var pathId = Int64.Parse(revision[RevisionDefinition.PathId!].ToString()!);
			var id = Int64.Parse(revision[RevisionDefinition.Revision!].ToString()!);

			var revisionFile = GetRevisionFileForDataSet(storageIndexDefinition, pathId, elementId, id,
														 revision[RevisionDefinition.ExtendedPath]?.ToString());

			if (revisionFile != null)
			{
				RetryFunction(() => { _fileStore.DeleteFile(revisionFile.CurrentFileInfo, true); });
			}

			_db.ConnectionHelper.WithQueryFactory(it =>
			{
				it.Query(storageIndexDefinition.RevisionTable)
					.Where(RevisionDefinition.Id, Guid.Parse(revisionId))
					.Delete();
			});
		});
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="values"></param>
	/// <param name="oldValues"></param>
	/// <param name="origin"></param>
	/// <param name="hasFile"></param>
	/// <param name="newRevNumber"></param>
	public void AddRevision(QueryFactory queryFactory, StorageIndexDefinition storageIndexDefinition, string elementId,
							IDictionary<string, object?> values, IDictionary<string, object?>? oldValues, DataStoreOperationOrigin origin,
							bool hasFile, long newRevNumber)
	{
		RemoveFavourites(values, oldValues);

		// get former revision data set
		IDictionary<string, object?>? oldRevisionDataSet;
		oldRevisionDataSet = GetRevisionDataSet(queryFactory, storageIndexDefinition, elementId, 1, 0);

		// get former revision file if exists
		RevisionFile? oldRevision;
		if (oldRevisionDataSet != null)
		{
			oldRevision =
				GetRevisionFileForDataSet(storageIndexDefinition, Int64.Parse(oldRevisionDataSet[RevisionDefinition.PathId]!.ToString()!), elementId,
										  Int64.Parse(oldRevisionDataSet[RevisionDefinition.Revision]!.ToString()!),
										  oldRevisionDataSet[RevisionDefinition.ExtendedPath]?.ToString());

			// if revision data exists and file does not -> create file synchronously
			if (oldRevision == null)
			{
				var initId = Int64.Parse(oldRevisionDataSet[RevisionDefinition.InitiatorId]!.ToString()!);
				var initName = oldRevisionDataSet[RevisionDefinition.InitiatorName]!.ToString()!;
				DataStoreOperationOrigin oldOrigin = new DataStoreOperationOrigin(
					(DataStoreOperationOriginType)oldRevisionDataSet[RevisionDefinition.OperationOriginType]!, initId, initName)
				{
					ActionId = Int64.Parse(oldRevisionDataSet[RevisionDefinition.ActionId]!.ToString()!),
					ActionName = (oldRevisionDataSet[RevisionDefinition.ActionName] != null)
									 ? oldRevisionDataSet[RevisionDefinition.ActionName]!.ToString()
									 : null
				};

				CreateOldRevisionFile(queryFactory, true, storageIndexDefinition, elementId,
									  Int64.Parse(oldRevisionDataSet[RevisionDefinition.Revision]!.ToString()!),
									  oldValues, oldOrigin, hasFile,
									  (DataStoreOperationType)oldRevisionDataSet[RevisionDefinition.OperationType]!);
			}
		}

		CreateRevision(queryFactory, storageIndexDefinition, elementId, values, origin, hasFile, DataStoreOperationType.Update, newRevNumber, oldValues);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="add"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionId"></param>
	/// <param name="oldValues"></param>
	/// <param name="origin"></param>
	/// <param name="hasFile"></param>
	/// <param name="operationType"></param>
	/// <returns></returns>
	private long CreateOldRevisionFile(QueryFactory queryFactory, bool add, StorageIndexDefinition storageIndexDefinition, string elementId, long revisionId,
									   IDictionary<string, object?> oldValues, DataStoreOperationOrigin origin, bool hasFile,
									   DataStoreOperationType operationType)
	{
		// get revision before old revision
		var formerRevisionDataSet = GetRevisionDataSet(queryFactory, storageIndexDefinition, elementId, 1, 1);
		IDictionary<string, object?>? formerValues = null;

		if (formerRevisionDataSet != null)
		{
			var formerRevision =
				GetRevisionFileForDataSet(storageIndexDefinition, Int64.Parse(formerRevisionDataSet[RevisionDefinition.PathId]!.ToString()!), elementId,
										  Int64.Parse(formerRevisionDataSet[RevisionDefinition.Revision]!.ToString()!),
										  formerRevisionDataSet[RevisionDefinition.ExtendedPath]?.ToString());
			if (formerRevision != null)
			{
				formerValues = formerRevision.Fields
					.Select(it => new KeyValuePair<string, object?>(it.Key, it.Value))
					.ToDictionary();
			}
		}

		// compare revisions values
		IDictionary<string, RevisionFieldValue> checkedFields =
			TransformAndCheck(oldValues, formerValues, storageIndexDefinition.ToDictionary());

		// and save changes to file
		_ = SaveRevisionFile(revisionId, storageIndexDefinition, elementId, oldValues,
							 Int64.Parse(oldValues[StorageSystemField.SysStoredRevision.ToString()]!.ToString()!),
							 checkedFields, origin, null, hasFile, operationType);

		return revisionId;
	}

	/// <summary>
	/// create revision entry and create revision file on storage
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="values"></param>
	/// <param name="origin"></param>
	/// <param name="hasFile"></param>
	/// <param name="operationType"></param>
	/// <param name="newRevNumber"></param>
	/// <param name="oldValues"></param>
	/// <returns></returns>
	public void CreateRevision(QueryFactory? queryFactory, StorageIndexDefinition storageIndexDefinition, string elementId,
							   IDictionary<string, object?> values, DataStoreOperationOrigin origin, bool hasFile,
							   DataStoreOperationType operationType, long newRevNumber, IDictionary<string, object?>? oldValues = null)
	{
		RemoveFavourites(values, oldValues);

		// compare revisions values
		IDictionary<string, RevisionFieldValue> checkedFields =
			TransformAndCheck(values, oldValues, storageIndexDefinition.ToDictionary());

		// create revision entry in database
		IDictionary<string, object?> dict =
			GetDictionaryForRevisionData(storageIndexDefinition.GetRevisionDefinition(false)!, storageIndexDefinition.CurrentStoragePath!.Id, elementId,
										 newRevNumber, origin, hasFile, operationType);
		dict[RevisionDefinition.ChangedFields] = checkedFields.Where(it => it.Value.Changed).Select(it => it.Value.Id).ToList();

		IDictionary<string, object?> revisionEntry;

		if (queryFactory == null)
			revisionEntry = _db.DataHelper.Insert(storageIndexDefinition.GetRevisionDefinition(false)!, dict, s => null);
		else
			revisionEntry = _db.DataHelper.Insert(queryFactory, storageIndexDefinition.GetRevisionDefinition(false)!, dict, s => null);

		string revisionElementId = revisionEntry[RevisionDefinition.Id]!.ToString()!;
		long revisionNumber = long.Parse(revisionEntry[RevisionDefinition.Revision]!.ToString()!);
		long newRevisionNumber = long.Parse(dict[RevisionDefinition.Revision]!.ToString()!);

		// Console.Out.WriteLine($"revisionElementId: {revisionElementId} - revisionNumber: {revisionNumber}");

		if (storageIndexDefinition.CurrentStoragePath!.Path.Contains("##"))
		{
			string extendedRevisionPath = _fileStore.GetCurrentSavePath(storageIndexDefinition, values, elementId, revisionNumber);
			dict = new Dictionary<string, object?>();
			dict.Add(RevisionDefinition.ExtendedPath, extendedRevisionPath);

			if (queryFactory == null)
				_db.DataHelper.Update(storageIndexDefinition.GetRevisionDefinition(false)!, revisionElementId, dict, s => null);
			else
				_db.DataHelper.Update(queryFactory, storageIndexDefinition.GetRevisionDefinition(false)!, revisionElementId, dict, s => null);
		}

		// and save changes to file
		_ = SaveRevisionFileAsync(revisionNumber, storageIndexDefinition, elementId, values, newRevisionNumber, checkedFields, origin, null, hasFile,
								  operationType);
	}

	private void RemoveFavourites(IDictionary<string, object?> values, IDictionary<string, object?>? oldValues)
	{
		// remove SysFavourites
		if (values.ContainsKey(StorageSystemField.SysFavourites.ToString()))
			values.Remove(StorageSystemField.SysFavourites.ToString());
		// remove SysFavourites
		if (oldValues != null && oldValues.ContainsKey(StorageSystemField.SysFavourites.ToString()))
			oldValues.Remove(StorageSystemField.SysFavourites.ToString());
	}

	/// <summary>
	/// Create revisions for batch insert
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementDatas"></param>
	/// <param name="origin"></param>
	/// <param name="hasFile"></param>
	/// <param name="operationType"></param>
	public void CreateMultipleRevisionsAfterInsert(QueryFactory queryFactory, StorageIndexDefinition storageIndexDefinition,
												   DataStoreResultSet<DataStoreElement> elementDatas,
												   DataStoreOperationOrigin origin, bool hasFile, DataStoreOperationType operationType)
	{
		IList<IDictionary<string, object?>> multiInserts = new List<IDictionary<string, object?>>();
		foreach (DataStoreElement dataStoreElement in elementDatas)
		{
			IDictionary<string, object?> values = dataStoreElement.Values;
			// compare revisions values
			IDictionary<string, RevisionFieldValue> checkedFields =
				TransformAndCheck(values, null, storageIndexDefinition.ToDictionary());

			IDictionary<string, object?> dict =
				GetDictionaryForRevisionData(storageIndexDefinition.GetRevisionDefinition(false)!, storageIndexDefinition.CurrentStoragePath!.Id,
											 values[StorageSystemField.Id.ToString()]!.ToString()!, 1, origin, hasFile, operationType);
			dict[RevisionDefinition.ChangedFields] = checkedFields.Where(it => it.Value.Changed).Select(it => it.Value.Id).ToList();
			multiInserts.Add(dict);
		}

		// create revision entries in database
		var revisionEntryIds = _db.DataHelper.InsertMulti(queryFactory, storageIndexDefinition.GetRevisionDefinition(false)!, multiInserts);

		IEnumerable<IDictionary<string, object>>? resDict = null;
		_db.ConnectionHelper.WithQueryFactory(it =>
		{
			var query = it.Query(storageIndexDefinition.RevisionTable).WhereIn(RevisionDefinition.Id, revisionEntryIds);
			var res = ((IList)query.Get()).Cast<IDictionary<string, object>>();
			resDict = res;
		});

		if (resDict != null)
			foreach (IDictionary<string, object> revisionEntry in resDict)
			{
				string revisionElementId = revisionEntry[RevisionDefinition.Id].ToString()!;
				long newRevisionNumber = long.Parse(revisionEntry[RevisionDefinition.Revision].ToString()!);

				DataStoreElement element = elementDatas.First(entry => entry.Id == revisionEntry[RevisionDefinition.ElementId].ToString());
				String elementId = element.Id;
				IDictionary<string, RevisionFieldValue> checkedFields = TransformAndCheck(element.Values, null, storageIndexDefinition.ToDictionary());

				if (storageIndexDefinition.CurrentStoragePath!.Path.Contains("##"))
				{
					_ = UpdateCurrentStoragePathAsync(queryFactory, storageIndexDefinition, element, revisionElementId, newRevisionNumber);
				}

				// and save changes to file
				_ = SaveRevisionFileAsync(newRevisionNumber, storageIndexDefinition, elementId, element.Values, newRevisionNumber, checkedFields, origin, null,
										  hasFile,
										  operationType);
			}
	}

	private async Task UpdateCurrentStoragePathAsync(QueryFactory queryFactory, StorageIndexDefinition storageIndexDefinition, DataStoreElement element,
													 string revisionElementId,
													 long newRevisionNumber)
	{
		await Task.Run(() =>
		{
			string extendedRevisionPath = _fileStore.GetCurrentSavePath(storageIndexDefinition, element.Values!, element.Id, newRevisionNumber);
			IDictionary<string, object?> dict = new Dictionary<string, object?>();
			dict.Add(RevisionDefinition.ExtendedPath, extendedRevisionPath);
			_db.DataHelper.Update(storageIndexDefinition.GetRevisionDefinition()!, revisionElementId, dict, s => null);
		});
	}

	/// <summary>
	/// Async save of revision file
	/// </summary>
	/// <param name="revisionId"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="values"></param>
	/// <param name="newRevId"></param>
	/// <param name="allFields"></param>
	/// <param name="origin"></param>
	/// <param name="revisionComment"></param>
	/// <param name="hasFile"></param>
	/// <param name="operationType"></param>
	public async Task SaveRevisionFileAsync(long revisionId, StorageIndexDefinition storageIndexDefinition, string elementId,
											IDictionary<string, object?> values, long newRevId,
											IDictionary<string, RevisionFieldValue> allFields,
											DataStoreOperationOrigin origin, string? revisionComment, bool hasFile, DataStoreOperationType operationType)
	{
		await SaveRevisionFile(revisionId, storageIndexDefinition, elementId, values, newRevId, allFields, origin, revisionComment, hasFile, operationType);
	}

	/// <summary>
	/// save changes to file
	/// </summary>
	/// <param name="revisionId"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="values"></param>
	/// <param name="newRevId"></param>
	/// <param name="allFields"></param>
	/// <param name="origin"></param>
	/// <param name="revisionComment"></param>
	/// <param name="hasFile"></param>
	/// <param name="operationType"></param>
	private async Task SaveRevisionFile(long revisionId, StorageIndexDefinition storageIndexDefinition, string elementId,
										IDictionary<string, object?> values, long newRevId,
										IDictionary<string, RevisionFieldValue> allFields,
										DataStoreOperationOrigin origin, string? revisionComment, bool hasFile, DataStoreOperationType operationType)
	{
		await Task.Run(() =>
		{
			RevisionFile newRevision = new RevisionFile(origin);
			newRevision.Fields = allFields;
			newRevision.Id = revisionId;
			newRevision.ElementId = elementId;
			newRevision.HasFile = hasFile;
			newRevision.RevisionComment = (revisionComment == null) ? "" : revisionComment;
			newRevision.RevisionDateTime = DateTime.UtcNow;
			newRevision.OperationType = operationType;

			try
			{
				// encrypt and save RevisionFile
				RetryFunction(() => { _fileStore.SaveRevisionFile(newRevision, storageIndexDefinition, values, _storageContext.Identifier, true); });
				_db.DataHelper.Update(storageIndexDefinition, elementId,
									  new Dictionary<string, object?>() { { StorageSystemField.SysStoredRevision.ToString(), newRevId } }, s => null);
			}
			catch (Exception e)
			{
				_logger.Error(e, "Revision could not be saved");
			}
		});
	}

	private void RetryFunction(Action action)
	{
		int tries = 0;
		bool done = false;
		while (!done)
		{
			try
			{
				action();
				done = true;
			}
			catch (Exception e)
			{
				tries++;
				if (tries == 5)
					throw new DataStoreOperationException($"Retry failed {tries} times.", e);
			}
		}
	}

	/// <summary>
	/// standard search functions for revisions with filter option
	/// </summary>
	/// <param name="dataStoreRevisionQuery"></param>
	/// <returns></returns>
	public DataStoreResultSet<DataStoreRevisionInfo> GetRevisions(DataStoreRevisionQuery dataStoreRevisionQuery)
	{
		var storageIndexDefinition = _storageConnection.WithDbContext(db =>
																		  db.StorageIndexDefinition
																			  .Include(it => it.Fields)
																			  .FirstOrDefault(it => it.Name == dataStoreRevisionQuery.DataSourceName));

		if (storageIndexDefinition == null)
			throw new DataStoreOperationException("Data source not found");

		if (!storageIndexDefinition.StoreRevisions)
			return new DataStoreResultSet<DataStoreRevisionInfo>(0);

		IEnumerable<IDictionary<string, object>> resDict = new List<IDictionary<string, object>>();
		_db.ConnectionHelper.WithQueryFactory(it =>
		{
			var query = it.Query(storageIndexDefinition.RevisionTable);
			query.Select(storageIndexDefinition.GetRevisionDefinition(false)!.Fields.Where(iter => !iter.MultiValue)
							 .Select(iter => storageIndexDefinition.RevisionTable + "." + iter.Name))
				.Where(RevisionDefinition.ElementId, Guid.Parse(dataStoreRevisionQuery.ElementId));
			AddFilters(query, dataStoreRevisionQuery.Filters, storageIndexDefinition.ToDictionary());
			AddOrderByQuery(query, dataStoreRevisionQuery.OrderBy);
			query.Limit(dataStoreRevisionQuery.Limit).Offset(dataStoreRevisionQuery.Offset);

			// if filters contain search for changed fields, check here, uncomment and move other query into else-part
			/*
			if(...)
		   query.LeftJoin(storageIndexDefinition.RevisionChangedFieldTable, storageIndexDefinition.RevisionTable + "." + RevisionDefinition.Id,
			   storageIndexDefinition.RevisionChangedFieldTable + "." + RevisionChangedFieldDefinition.RevisionId, "=")
		   .Join(StorageFieldDefinitionOrm.DefinitionName,
			   StorageFieldDefinitionOrm.DefinitionName + "." + StorageFieldDefinitionOrm.IdFieldName,
			   storageIndexDefinition.RevisionChangedFieldTable + "." + RevisionChangedFieldDefinition.FieldId, "=")
		   .Where(RevisionDefinition.ElementId, dataStoreRevisionQuery.ElementId);
		   AddFilters(query, dataStoreRevisionQuery.Filters, storageIndexDefinition.ToDictionary());
		   query.GroupBy(storageIndexDefinition.GetRevisionDefinition(false).Fields.Select(it => storageIndexDefinition.RevisionTable + "." + it.Name)
			   .ToArray());
		   AddOrderByQuery(query, dataStoreRevisionQuery.OrderBy);
		   query.Limit(dataStoreRevisionQuery.Limit).Offset(dataStoreRevisionQuery.Offset);
		   */

			var res = ((IList)query.Get())
				.Cast<IDictionary<string, object>>();
			resDict = res;
		});

		DataStoreResultSet<DataStoreRevisionInfo> set = new DataStoreResultSet<DataStoreRevisionInfo>(resDict.Count());

		foreach (var revisionEntry in resDict)
			set.Add(ToDataStoreRevisionInfo(revisionEntry!));

		return set;
	}

	/// <summary>
	/// Gets last revision id having file
	/// </summary>
	/// <param name="definition"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public long GetCurrentRevisionNumberWithFile(StorageIndexDefinition definition, string elementId)
	{
		long revisionNumber = _db.ConnectionHelper.WithQueryFactory(it =>
		{
			IList<DataStoreRevisionSort> orderByList = new List<DataStoreRevisionSort>();
			var orderBy = new DataStoreRevisionSort(DataStoreRevisionProperty.Date, DataStoreElementSortDirection.Desc);
			orderByList.Add(orderBy);

			var query = it.Query(definition.RevisionTable);
			query.Select(definition.GetRevisionDefinition(false)!.Fields.Where(iter => !iter.MultiValue)
							 .Select(iter => definition.RevisionTable + "." + iter.Name))
				.Where(RevisionDefinition.ElementId, Guid.Parse(elementId)).Where(RevisionDefinition.HasFile, true);

			AddOrderByQuery(query, orderByList);
			query.Limit(1);

			var res = ((IList)query.Get())
				.Cast<IDictionary<string, object>>().ToList();

			if (res.Count() > 0)
			{
				return (long)res.First()[RevisionDefinition.Revision];
			}

			return -1;
		});
		return revisionNumber;
	}

	/// <summary>
	/// search for revisions using names of changed fields
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="fieldName"></param>
	/// <returns></returns>
	public DataStoreResultSet<DataStoreRevisionInfo> GetRevisionsByField(StorageIndexDefinition storageIndexDefinition, string elementId,
																		 string fieldName)
	{
		IEnumerable<IDictionary<string, object?>>? resDict;

		var field = storageIndexDefinition.Fields.FirstOrDefault(it => it.Name == fieldName);
		if (field == null)
			return new DataStoreResultSet<DataStoreRevisionInfo>(0);

		FilterParser filterParser = new(_storageConnection.Db, s => null, _storageContext.Language, _storageContext.Language, true);
		DataStoreQuery filter = new(storageIndexDefinition.RevisionTable, null);
		filter.WithFilter(new QueryFilterGroup(new List<QueryFilter>()
		{
			new EqualsFilter(new QueryFilterField(RevisionDefinition.ElementId), elementId),
			new InFilter(field.Id, new QueryFilterField(RevisionDefinition.ChangedFields))
		}));
		Query query = filterParser.ParseQuery(storageIndexDefinition.GetRevisionDefinition()!, filter, out _, out _, false, true);

		resDict = _db.ConnectionHelper.WithQueryFactory(it => { return _db.DataHelper.Get(storageIndexDefinition.GetRevisionDefinition()!, query, null); });

		var revisionEntries = resDict.ToList();
		DataStoreResultSet<DataStoreRevisionInfo> set = new DataStoreResultSet<DataStoreRevisionInfo>(revisionEntries.Count());

		foreach (var revisionEntry in revisionEntries)
			set.Add(ToDataStoreRevisionInfo(revisionEntry));

		return set;
	}

	/// <summary>
	/// load a specific revision by its id
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionId"></param>
	/// <returns></returns>
	public DataStoreRevisionData? GetRevision(StorageIndexDefinition storageIndexDefinition, string elementId, string revisionId)
	{
		IDictionary<string, object?>? resDict = new Dictionary<string, object?>();
		_db.ConnectionHelper.WithQueryFactory(it =>
		{
			var resList = it.Query(storageIndexDefinition.RevisionTable)
				.Where(RevisionDefinition.ElementId, Guid.Parse(elementId))
				.Where(RevisionDefinition.Id, Guid.Parse(revisionId)).Get().ToList();

			resDict = (resList.Count() > 0) ? (IDictionary<string, object?>)resList.First() : null;
		});

		if (resDict == null)
			return null;

		var pathId = Int64.Parse(resDict[RevisionDefinition.PathId]!.ToString()!);
		var revisionNumber = Int64.Parse(resDict[RevisionDefinition.Revision]!.ToString()!);
		string? extendedPath = (resDict[RevisionDefinition.ExtendedPath] != null) ? resDict[RevisionDefinition.ExtendedPath]!.ToString() : null;
		RevisionFile revisionFile = GetRevisionFileForDataSet(storageIndexDefinition, pathId, elementId, revisionNumber,
															  extendedPath) ?? throw new FileNotFoundException();

		return revisionFile.ToDto(ToDataStoreRevisionInfo(resDict!));
	}

	/// <summary>
	/// load a specific revision by its id
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <exception cref="ElementNotFoundException">If the revision is not found</exception>
	/// <returns></returns>
	public DataStoreRevisionData GetRevision(StorageIndexDefinition storageIndexDefinition, string elementId, long revisionNumber)
	{
		IDictionary<string, object?>? resDict = null;
		_db.ConnectionHelper.WithQueryFactory(it =>
		{
			var resList = it.Query(storageIndexDefinition.RevisionTable)
				.Where(RevisionDefinition.ElementId, Guid.Parse(elementId))
				.Where(RevisionDefinition.Revision, revisionNumber).Get().ToList();

			resDict = resList.Any() ? (IDictionary<string, object?>)resList.First() : null;
		});

		if (resDict == null)
			throw new ElementNotFoundException($"Revision {revisionNumber} not found for elementId {elementId}");


		var pathId = Int64.Parse(resDict[RevisionDefinition.PathId]!.ToString()!);
		RevisionFile? revisionFile = null;
		// revision files are written asynchronous, and might still be written to disk
		for (int i = 4; i >= 0; i--)
		{
			revisionFile = GetRevisionFileForDataSet(storageIndexDefinition, pathId, elementId, revisionNumber,
													 resDict[RevisionDefinition.ExtendedPath]?.ToString());
			if (revisionFile != null)
				break;
			if (i != 0)
				Thread.Sleep(200);
		}

		return revisionFile?.ToDto(ToDataStoreRevisionInfo(resDict!)) ??
			   throw new DataStoreOperationException($"Could not load revisionfile for revision {revisionNumber} and elementId {elementId}.");
	}

	/// <summary>
	/// load the file of an older revision
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="pathId"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionId"></param>
	/// <param name="extendedRevisionPath"></param>
	/// <returns></returns>
	private RevisionFile? GetRevisionFileForDataSet(StorageIndexDefinition storageIndexDefinition, long pathId, string elementId, long revisionId,
													string? extendedRevisionPath = null)
	{
		return _fileStore.LoadRevisionFile(storageIndexDefinition, _storageContext.Identifier, pathId, elementId, revisionId, true, extendedRevisionPath);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="revisionDefinition"></param>
	/// <param name="pathId"></param>
	/// <param name="elementId"></param>
	/// <param name="newRevNumber"></param>
	/// <param name="origin"></param>
	/// <param name="hasFile"></param>
	/// <param name="operationType"></param>
	/// <returns></returns>
	private Dictionary<string, object?> GetDictionaryForRevisionData(StorageIndexDefinition revisionDefinition, long pathId,
																	 string elementId, long newRevNumber, DataStoreOperationOrigin origin,
																	 bool hasFile, DataStoreOperationType operationType)
	{
		Dictionary<string, object?> dict = new Dictionary<string, object?>();
		foreach (var fieldDefinition in revisionDefinition.Fields)
		{
			switch (fieldDefinition.Name)
			{
				case RevisionDefinition.Revision:
					dict.Add(fieldDefinition.Name, newRevNumber);
					break;
				case RevisionDefinition.ElementId:
					dict.Add(fieldDefinition.Name, elementId);
					break;
				case RevisionDefinition.DateTime:
					dict.Add(fieldDefinition.Name, DateTime.UtcNow);
					break;
				case RevisionDefinition.InitiatorId:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.InitiatorId);
					break;
				case RevisionDefinition.InitiatorName:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.InitiatorName);
					break;
				case RevisionDefinition.ActionId:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.ActionId);
					break;
				case RevisionDefinition.ActionName:
					if (origin != null && origin.ActionName != null)
						dict.Add(fieldDefinition.Name, origin.ActionName);
					break;
				case RevisionDefinition.Comment:
					if (origin != null && origin.Comment != null)
						dict.Add(fieldDefinition.Name, origin.Comment);
					break;
				case RevisionDefinition.PathId:
					dict.Add(fieldDefinition.Name, pathId);
					break;
				case RevisionDefinition.OperationType:
					dict.Add(fieldDefinition.Name, operationType);
					break;
				case RevisionDefinition.OperationOriginType:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.Type);
					break;
				case RevisionDefinition.HasFile:
					dict.Add(fieldDefinition.Name, hasFile);
					break;
				case RevisionDefinition.UserName:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.InitiatorName);
					break;
				/*
				case RevisionDefinition.Comment:
					if (origin != null)
						dict.Add(fieldDefinition.Name, origin.);
					break;
				*/
				default:
					break;
			}
		}

		return dict;
	}


	/// <summary>
	/// Transform to object with more properties
	/// Compare old and new values and mark changes to revision
	/// </summary>
	/// <param name="values"></param>
	/// <param name="oldValues"></param>
	/// <param name="dataStoreFieldDefinitions"></param>
	/// <returns></returns>
	private IDictionary<string, RevisionFieldValue> TransformAndCheck(IDictionary<string, object?> values,
																	  IDictionary<string, object?>? oldValues,
																	  IDictionary<string, StorageFieldDefinitionOrm> dataStoreFieldDefinitions)
	{
		// transform to object with more properties
		IDictionary<String, RevisionFieldValue> fields = new Dictionary<String, RevisionFieldValue>();
		foreach (var keyValuePair in values)
		{
			if (!dataStoreFieldDefinitions.TryGetValue(keyValuePair.Key, out var currentField))
				continue;

			// don't check PK for change
			if (currentField.PrimaryKey)
				continue;

			// use default value if field not exists in previous revision
			var oldValue = oldValues == null || !oldValues.ContainsKey(keyValuePair.Key)
							   ? (currentField.DefaultValue?.GetType() == typeof(JsonElement) ? null : currentField.DefaultValue)
							   : oldValues[keyValuePair.Key];

			// Compare old and new values and mark changes to revision
			bool isChangedField = IsChangedField(keyValuePair, oldValue);

			DataStoreFieldType type = currentField.Type;
			long id = currentField.Id;
			RevisionFieldValue newFieldValue = new RevisionFieldValue(keyValuePair.Key, type, keyValuePair.Value)
			{
				Id = id,
				Changed = isChangedField,
				PreviousValue = oldValue
			};

			fields.Add(keyValuePair.Key, newFieldValue);
		}

		return fields;
	}

	private static bool IsChangedField(KeyValuePair<string, object?> keyValuePair, object? oldValue)
	{
		// compare null values
		if (keyValuePair.Value == null && oldValue == null)
			return false;

		if ((keyValuePair.Value == null && oldValue != null)
			|| (keyValuePair.Value != null && oldValue == null))
		{
			return true;
		}

		// compare non-arrays
		if (!keyValuePair.Value.IsArray() &&
			(!(keyValuePair.Value!.ToString()!.Equals(oldValue!.ToString()))
			 && !(JsonConvert.SerializeObject(keyValuePair.Value).Equals(JsonConvert.SerializeObject(oldValue)))))
		{
			return true;
		}

		// compare arrays
		if (keyValuePair.Value.IsArray() &&
			!(JsonConvert.SerializeObject(keyValuePair.Value).Equals(JsonConvert.SerializeObject(oldValue))))
		{
			return true;
		}

		return false;
	}

	/// <summary>
	/// transform selected revision entries to DataStoreRevisionInfo object
	/// </summary>
	/// <param name="revisionEntry"></param>
	/// <returns></returns>
	public static DataStoreRevisionInfo ToDataStoreRevisionInfo(IDictionary<string, object?> revisionEntry)
	{
		DataStoreOperationType type = ((revisionEntry[RevisionDefinition.OperationType] == null)
										   ? DataStoreOperationType.Create
										   : (DataStoreOperationType)Enum.Parse(typeof(DataStoreOperationType),
																				revisionEntry[RevisionDefinition.OperationType]!.ToString()!));

		DataStoreRevisionInfo info = new DataStoreRevisionInfo(
			revisionEntry[RevisionDefinition.Id]!.ToString()!,
			DateTime.Parse(revisionEntry[RevisionDefinition.DateTime]!.ToString()!),
			revisionEntry[RevisionDefinition.UserName]!.ToString()!,
			type
		)
		{
			InitiatorId = ((revisionEntry[RevisionDefinition.InitiatorId] == null)
							   ? 0
							   : Int64.Parse(revisionEntry[RevisionDefinition.InitiatorId]!.ToString()!)),
			InitiatorName = ((revisionEntry[RevisionDefinition.InitiatorName] == null)
								 ? null
								 : revisionEntry[RevisionDefinition.InitiatorName]!.ToString()),
			ActionName = ((revisionEntry[RevisionDefinition.ActionName] == null)
							  ? null
							  : revisionEntry[RevisionDefinition.ActionName]!.ToString()),
			ActionId = ((revisionEntry[RevisionDefinition.ActionId] == null)
							? 0
							: Int64.Parse(revisionEntry[RevisionDefinition.ActionId]!.ToString()!)),
			Comment = ((revisionEntry[RevisionDefinition.Comment] == null)
						   ? null
						   : revisionEntry[RevisionDefinition.Comment]!.ToString()),
			HasFile = ((revisionEntry[RevisionDefinition.HasFile] == null)
						   ? false
						   : Boolean.Parse(revisionEntry[RevisionDefinition.HasFile]!.ToString()!))
		};

		return info;
	}

	/// <summary>
	/// add sorting to given query object
	/// </summary>
	/// <param name="query"></param>
	/// <param name="orderBy"></param>
	public static void AddOrderByQuery(Query query, IList<DataStoreRevisionSort> orderBy)
	{
		foreach (var dataStoreRevisionSort in orderBy)
		{
			if (dataStoreRevisionSort.SortDirection == DataStoreElementSortDirection.Asc)
			{
				query.OrderBy(new[] { GetRevisionPropertyName(dataStoreRevisionSort.Property) });
			}
			else
			{
				query.OrderByDesc(new[] { GetRevisionPropertyName(dataStoreRevisionSort.Property) });
			}
		}
	}

	/// <summary>
	/// add filters to given query object
	/// </summary>
	/// <param name="query"></param>
	/// <param name="revisionFilters"></param>
	/// <param name="fields"></param>
	public void AddFilters(Query query, IList<DataStoreRevisionFilter> revisionFilters,
						   IDictionary<string, StorageFieldDefinitionOrm> fields)
	{
		if (revisionFilters != null)
		{
			foreach (DataStoreRevisionFilter revisionFilter in revisionFilters)
			{
				string revisionPropertyName = GetRevisionPropertyName(revisionFilter.Property);
				AddCompareField(query, revisionFilter, revisionPropertyName, fields);
			}
		}
	}

	/// <summary>
	/// translate given search property to revision field name
	/// </summary>
	/// <param name="revisionFilterProperty"></param>
	/// <returns></returns>
	private static string GetRevisionPropertyName(DataStoreRevisionProperty revisionFilterProperty)
	{
		switch (revisionFilterProperty)
		{
			case DataStoreRevisionProperty.Date:
				return RevisionDefinition.DateTime;
			case DataStoreRevisionProperty.Type:
				return RevisionDefinition.OperationType;
			case DataStoreRevisionProperty.Username:
				return RevisionDefinition.UserName;
			case DataStoreRevisionProperty.OriginType:
				return RevisionDefinition.OperationOriginType;
			case DataStoreRevisionProperty.Comment:
			default:
				return RevisionDefinition.Comment;
		}
	}

	/// <summary>
	/// add compare field for filter to given query
	/// </summary>
	/// <param name="query"></param>
	/// <param name="revisionFilter"></param>
	/// <param name="revisionPropertyName"></param>
	/// <param name="fields"></param>
	private void AddCompareField(Query query, DataStoreRevisionFilter revisionFilter,
								 string revisionPropertyName, IDictionary<string, StorageFieldDefinitionOrm> fields)
	{
		var compOperation = GetCompareOperation(revisionFilter.Operator);
		if (compOperation != null)
		{
			query.Where(revisionPropertyName, compOperation,
						revisionFilter.CompareValue);
		}
		else
		{
			switch (revisionFilter.Operator)
			{
				case QueryFilterOperator.NotIn:
				case QueryFilterOperator.NotLike:
				case QueryFilterOperator.IsNotNull:
					query.Not();
					break;
			}

			IEnumerable<object>? compareTo = null;
			if (revisionFilter.Operator == QueryFilterOperator.In ||
				revisionFilter.Operator == QueryFilterOperator.NotIn)
			{
				if (revisionFilter.CompareValue is Array)
				{
					compareTo = ((revisionFilter.CompareValue as Array)!).Cast<object>();
				}
				else
				{
					compareTo = ((revisionFilter.CompareValue as IEnumerable)!).Cast<object>();
				}
			}

			switch (revisionFilter.Operator)
			{
				case QueryFilterOperator.Equals:
					query.Where(revisionPropertyName, revisionFilter.CompareValue);
					break;
				case QueryFilterOperator.NotEquals:
					query.WhereRaw(_db.DataHelper.IsNullRaw($"[{revisionPropertyName}]", "''") + " != ?",
								   revisionFilter.CompareValue);
					break;
				case QueryFilterOperator.In:
					query.WhereIn(revisionPropertyName, compareTo);
					break;
				case QueryFilterOperator.NotIn:
					query.Not(false).Where(q =>
											   q.Not().WhereIn(revisionPropertyName, compareTo)
												   .OrWhereNull(revisionPropertyName));
					break;
				case QueryFilterOperator.Like:
					query.WhereLike(revisionPropertyName, revisionFilter.CompareValue);
					break;
				case QueryFilterOperator.NotLike:
					query.WhereRaw(
						_db.DataHelper.IsNullRaw($"[{revisionPropertyName}]", "''") + " not like ?",
						revisionFilter.CompareValue);
					break;
				case QueryFilterOperator.IsNull:
				case QueryFilterOperator.IsNotNull:
					query.WhereNull(revisionPropertyName);
					break;
			}
		}
	}

	/// <summary>
	/// get operators for some special compare operation types 
	/// </summary>
	/// <param name="queryFilterOperator"></param>
	/// <returns></returns>
	private static string? GetCompareOperation(QueryFilterOperator queryFilterOperator)
	{
		return queryFilterOperator switch
		{
			QueryFilterOperator.GreaterThan => ">",
			QueryFilterOperator.LessThan => "<",
			QueryFilterOperator.GreaterThanEquals => ">=",
			QueryFilterOperator.LessThanEquals => "<=",
			_ => null
		};
	}
}