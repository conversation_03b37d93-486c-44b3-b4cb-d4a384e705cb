{"id": "levelbuild-ci", "name": "CI Environment", "values": [{"key": "testFilePdf", "type": "file", "value": "test.pdf"}, {"key": "testFilePng", "type": "file", "value": "testImage.png"}, {"key": "baseUrl", "type": "string", "value": "http://localhost:5112"}, {"key": "setupStorageConfig", "type": "string", "value": "{ \"storagePath\": \"./TestTemp\", \"MongoDbConnectionString\": \"************************************\" }"}, {"key": "setupSourceConfig", "type": "string", "value": "{ \"RevisionsEnabled\": false }"}]}