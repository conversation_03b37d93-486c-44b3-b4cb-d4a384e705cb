using System.Text.Json;
using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Page;
using Levelbuild.Entities.Features.Page.MultiData;
using Levelbuild.Entities.Features.Page.SingleData;
using Levelbuild.Entities.Interfaces;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.PublicApi.Controllers;


[ExcludeFromCodeCoverage]
public abstract class PagesControllerTest<TEntity, TDto> : ConfigControllerTest<PageEntity, PageDto>
	where TEntity : PageEntity, IAdministrationEntity<TEntity, TDto>
	where TDto : PageDto
{
	#region Test Data Properties
	
	protected override PageDto CreateDto
	{
		get
		{
			using var databaseContext = Fixture.Context;
			var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
			var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
			
			return new()
			{
				Name = "Schlumpf Page",
				DataSourceId = source.Id,
				Description = "Schlumpfbeeren ballern!",
				LastModifiedText = "-",
				Created = DateTime.Now,
				CreatedBy = UserManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions().DisplayName
			};
		}
	}
	
	#endregion

	protected PagesControllerTest(DatabaseFixture fixture) : base(fixture, initStorage: true)
	{
		ControllerInstance = new PagesController(LogManager, Fixture.ContextFactory, VersionReader);
	}
	
	#region Data Preparation
	
	protected override void PrepareQueryData()
	{
		using var databaseContext = Fixture.Context;
		var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer!.Id);
		
		QueryData.Add(new MultiDataPageDto()
		{
			Name = "MultiPage1",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test1",
			Type = PageType.MultiData,
		});
		
		QueryData.Add(new MultiDataPageDto()
		{
			Name = "MultiPage2",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test2",
			Type = PageType.MultiData,
		});
		
		QueryData.Add(new MultiDataPageDto()
		{
			Name = "MultiPage3",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test3",
			Type = PageType.MultiData,
		});
		
		QueryData.Add(new SingleDataPageDto()
		{
			Name = "SinglePage1",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test1",
			Type = PageType.SingleData
		});
		
		QueryData.Add(new SingleDataPageDto()
		{
			Name = "SinglePage2",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test2",
			Type = PageType.SingleData
		});
		
		QueryData.Add(new SingleDataPageDto()
		{
			Name = "SinglePage3",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test3",
			Type = PageType.SingleData
		});
		
		foreach (PageDto dto in QueryData)
		{
			switch(dto.Type)
			{
				case PageType.SingleData:
					databaseContext.SingleDataPages.Add(SingleDataPageEntity.FromDto((SingleDataPageDto)dto, null, databaseContext));
					break;
				
				case PageType.MultiData:
					databaseContext.MultiDataPages.Add(MultiDataPageEntity.FromDto((MultiDataPageDto)dto, null, databaseContext));
					break;
			}
		}
		
		databaseContext.SaveChanges();
	}
	
	#endregion
	
	#region Assertions
	
	protected override void AssertIsAsExpected(PageDto expected, PageDto actual)
	{
		Assert.Equal(expected.Name, actual.Name);
		Assert.False((actual.Created!.Value - expected.Created!.Value.ToUniversalTime()).TotalSeconds < -1 || actual.Created >= DateTime.Now);
		Assert.Equal(expected.CreatedBy, actual.CreatedBy);
		Assert.Equal(expected.Description, actual.Description);
		Assert.False(expected.Created >= actual.LastModified || actual.LastModified >= DateTime.Now);
		Assert.Equal(expected.LastModifiedBy, actual.LastModifiedBy);
		Assert.Equal(expected.LastModifiedText, actual.LastModifiedText);
		Assert.Equal(expected.TypeName, actual.TypeName);
		Assert.Equal(expected.Type, actual.Type);
		Assert.Equal(expected.GetType(), actual.GetType());
		Assert.Equal(expected.DataSourceId, actual.DataSourceId);
	}
	
	protected override void CheckQueryResult(IList<PageDto> queryResult)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResult.ToList().ConvertAll(config => config.Name);
		mutatedNamesList.Sort();
		var expectedList = new List<string>();
		for(var i = 0; i < count; i++)
		{
			expectedList.Add(QueryData[i].Name!);
		}
		
		Assert.Equal(queryResult.Count, count);
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			Assert.Equal(expectedList[i], mutatedNamesList[i]);
		}
	}
	
	#endregion
	
	#region Tests
	
	[Fact(DisplayName = "Get Entity by Id")]
	public async Task GetTest()
	{
		dynamic entity = PrepareSingleEntity();
		var result = await ControllerInstance!.Get(entity.Id);
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(result);
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		
		var data = (objectResult.Value as PublicApiResponse<JsonElement>)?.Data;
		Assert.NotNull(data);
		
		var json = (JsonElement)data;
		var config = json.Deserialize<TDto>(JsonOptions);
		Assert.NotNull(config);
		AssertIsAsExpected(entity.ToDto(), config);
	}
	
	[Fact(DisplayName = "List all allowed Entities")]
	public async Task ListTest()
	{
		PrepareQueryData();
		
		var result = await ControllerInstance!.List();
		var code = (result.Result as ObjectResult)?.StatusCode;
		var data = ((result.Result as ObjectResult)?.Value as PublicApiResponse<JsonElement>)?.Data;
		
		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(data);
		
		var json = (JsonElement)data;
		List<JsonElement?> itemsArray = json.EnumerateArray().ToList()
			.Select(item => item as JsonElement?)
			.ToList();
		List<PageDto> configs = new();
		foreach (var item in itemsArray)
		{
			item!.Value.TryGetProperty("type", out JsonElement value);
			var parsedValue = value.GetString();
			switch (parsedValue)
			{
				case nameof(PageType.SingleData):
					configs.Add(item.Value.Deserialize<SingleDataPageDto>(JsonOptions)!);
					break;
				case nameof(PageType.MultiData):
					configs.Add(item.Value.Deserialize<MultiDataPageDto>(JsonOptions)!);
					break;
			}
		}
		
		Assert.NotNull(configs);
		CheckQueryResult(configs);
	}
	
	#endregion
}