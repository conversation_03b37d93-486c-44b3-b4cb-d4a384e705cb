using System.Globalization;
using System.Security.Claims;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.ZitadelApiInterface;
using Levelbuild.Domain.WebAppTests.Mocks.AuthenticationService;
using Levelbuild.Domain.WebAppTests.Mocks.HttpContextAccessor;
using Levelbuild.Domain.WebAppTests.Mocks.ZitadelApi;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Localization;
using Levelbuild.Frontend.WebApp.Features.Auth.Services;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Services;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.Constants;
using Levelbuild.Frontend.WebApp.Shared.Localization;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Hosting.Internal;
using StackExchange.Redis;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.Localization;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

[ExcludeFromCodeCoverage]
public class PostgresLocalizationTests(PostgresDatabaseFixture fixture) : LocalizationTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
public class SqlServerLocalizationTests : LocalizationTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerLocalizationTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class LocalizationTests : IDisposable
{
	private readonly DatabaseFixture _fixture;

	private readonly CoreDatabaseContext _context;

	private readonly IExtendedStringLocalizerFactory _localizerFactory;
	
	private readonly IHttpContextAccessor _httpContextAccessor;
	
	private readonly MockZitadelDatabase _mockZitadelDatabase;
	
	private readonly IZitadelApiClientFactory _zitadelApiClientFactory;
	
	public LocalizationTests(DatabaseFixture fixture)
	{
		_fixture = fixture;
		
		_fixture.Services.AddSingleton<ILogManager, LogManager>();
		_fixture.Services.AddSingleton<IAuthenticationService, MockAuthenticationService>();
		_fixture.Services.AddSingleton<IHostEnvironment>(new HostingEnvironment() { EnvironmentName = Environments.Development });
		_fixture.Services.AddSingleton<IHttpContextAccessor, MockHttpContextAccessor>();
		_fixture.Services.AddSingleton<MockZitadelDatabase>();
		_fixture.Services.AddSingleton<IZitadelApiClientFactory, MockZitadelApiClientFactory>();
		
		_fixture.Services.AddSingleton(_fixture.Config);
		_fixture.Services.AddKeyedSingleton<IConnectionMultiplexer>(RedisConstants.RedisWriteConnection, ConnectionMultiplexer.Connect(_fixture.Config.GetConnectionString("Redis")!));
		_fixture.Services.AddKeyedSingleton<IConnectionMultiplexer>(RedisConstants.RedisReadConnection, ConnectionMultiplexer.Connect(_fixture.Config.GetConnectionString("Redis")!));
		_fixture.Services.AddSingleton<IRedisAccessService, RedisAccessService>();
		_fixture.Services.AddSingleton<UserImpersonationCache>();
		_fixture.Services.AddScoped<UserManager>();
		
		_fixture.Services.AddSingleton<StringLocalizerCache>();
		_fixture.Services.AddSingleton<IExtendedStringLocalizerFactory, StringLocalizerFactory>();
		_fixture.Services.AddSingleton<ITranslationService, TranslationService>();

		_fixture.Services.AddLocalization();
		_fixture.Services.ConfigureOptions<CustomRequestLocalizationOptions>();

		_fixture.BuildServices();
		_context = _fixture.Context;

		_localizerFactory = _fixture.ServiceProvider.GetService<IExtendedStringLocalizerFactory>()!;
		_httpContextAccessor = (MockHttpContextAccessor) _fixture.ServiceProvider.GetRequiredService<IHttpContextAccessor>();
		_mockZitadelDatabase = _fixture.ServiceProvider.GetRequiredService<MockZitadelDatabase>();
		_zitadelApiClientFactory = (MockZitadelApiClientFactory) _fixture.ServiceProvider.GetRequiredService<IZitadelApiClientFactory>();
	}

	[Trait("Category", "Localization Tests")]
	[Fact(DisplayName = "Translate")]
	public void CreateTest()
	{
		// create Cultures and Translations
		var de = AddCulture("de");
		var deAt = AddCulture("de-AT");
		var en = AddCulture("en");
		
		// prepare preferences
		var customer = EntityCreation.CreateCustomer(_context, _zitadelApiClientFactory);
		var user = EntityCreation.CreateUser(_context, _zitadelApiClientFactory, customer.DisplayName);
		
		_mockZitadelDatabase.CurrentUserId = user.RemoteId;
		var claims = new List<Claim>()
		{
			new Claim("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier", user.RemoteId),
			new Claim("http://schemas.microsoft.com/ws/2008/06/identity/claims/role", "admin"),
			new Claim("urn:zitadel:iam:user:resourceowner:id", user.MainCustomer!.RemoteId)
		};
		var identity = new ClaimsIdentity(claims, "MockAuthType");
		
		_httpContextAccessor.HttpContext!.User = new ClaimsPrincipal(identity);

		AddTranslation("/DataStoreConfig/list/title", "Backend Übersicht", de);
		AddTranslation("/DataStoreConfig/list/title", "Backend overview", en);

		AddTranslation("whippedCream", "Schlagsahne", de);
		AddTranslation("whippedCream", "Schlagobers", deAt);
		AddTranslation("whippedCream", "whipped cream", en);

		AddTranslation("car", "automobile", en);
		
		// save changes only once and not inside every Add-Method
		_context.SaveChanges();

		// start testing with de-DE
		Thread.CurrentThread.CurrentCulture = new CultureInfo("de-DE");

		var localizer = _localizerFactory.Create("DataStoreConfig", "list");
		Assert.Equal("Backend Übersicht", localizer["title"]);
		Assert.Equal("Schlagsahne", localizer["whippedCream"]);
		Assert.Equal("automobile", localizer["car"]);
		Assert.Equal("[unknown]", localizer["unknown"]);

		// AT should give us special AT results as well es fallback to german
		Thread.CurrentThread.CurrentCulture = new CultureInfo("de-AT");
		Assert.Equal("Backend Übersicht", localizer["title"]);
		Assert.Equal("Schlagobers", localizer["whippedCream"]);
		Assert.Equal("automobile", localizer["car"]);
		Assert.Equal("[unknown]", localizer["unknown"]);

		// do the same things again with en-US
		Thread.CurrentThread.CurrentCulture = new CultureInfo("en-US");
		Assert.Equal("Backend overview", localizer["title"]);
		Assert.Equal("whipped cream", localizer["whippedCream"]);
		Assert.Equal("automobile", localizer["car"]);
		Assert.Equal("[unknown]", localizer["unknown"]);
	}
	
	[Trait("Category", "Localization Tests")]
	[Fact(DisplayName = "Translate minding user preferences with fixed locale")]
	public void WithFixedUserPreferencesTest()
	{
		// create Cultures and Translations
		var de = AddCulture("de");
		var deAt = AddCulture("de-AT");
		var en = AddCulture("en");
		
		// prepare preferences
		var customer = EntityCreation.CreateCustomer(_context, _zitadelApiClientFactory);
		var user = EntityCreation.CreateUser(_context, _zitadelApiClientFactory, customer.DisplayName, "ExampleUserWithPreferences", true, en);
		
		_mockZitadelDatabase.CurrentUserId = user.RemoteId;
		var claims = new List<Claim>()
		{
			new Claim("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier", user.RemoteId),
			new Claim("http://schemas.microsoft.com/ws/2008/06/identity/claims/role", "admin"),
			new Claim("urn:zitadel:iam:user:resourceowner:id", user.MainCustomer!.RemoteId)
		};
		var identity = new ClaimsIdentity(claims, "MockAuthType");
		
		_httpContextAccessor.HttpContext!.User = new ClaimsPrincipal(identity);
		
		AddTranslation("/DataStoreConfig/list/title", "Backend Übersicht", de);
		AddTranslation("/DataStoreConfig/list/title", "Backend overview", en);
		
		AddTranslation("whippedCream", "Schlagsahne", de);
		AddTranslation("whippedCream", "Schlagobers", deAt);
		AddTranslation("whippedCream", "whipped cream", en);
		
		AddTranslation("car", "automobile", en);
		
		// save changes only once and not inside every Add-Method
		_context.SaveChanges();
		
		// set current culture to de-DE
		Thread.CurrentThread.CurrentCulture = new CultureInfo("de-DE");
		
		var localizer = _localizerFactory.Create("DataStoreConfig", "list");
		Assert.Equal("Backend overview", localizer["title"]);
		Assert.Equal("whipped cream", localizer["whippedCream"]);
		Assert.Equal("automobile", localizer["car"]);
		Assert.Equal("[unknown]", localizer["unknown"]);
	}

	private CultureEntity AddCulture(string name)
	{
		var culture = new CultureEntity { Name = name };
		_context.Cultures.Add(culture);

		return culture;
	}

	private void AddTranslation(string key, string value, CultureEntity culture)
	{
		_context.Translations.Add(new TranslationEntity { Key = key, Value = value, Culture = culture });
	}

	public void Dispose()
	{
		_context.Dispose();
		_fixture.CleanUp();
	}
}