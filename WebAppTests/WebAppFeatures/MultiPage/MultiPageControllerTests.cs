using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.PageView.ListView;
using Levelbuild.Frontend.WebApp.Features.MultiPage.Controllers;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SharpCompress;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.MultiPage;

[ExcludeFromCodeCoverage]
[Collection("PostgresDatabaseCollection")]
public class PostgresMultiPageControllerTests(PostgresDatabaseFixture fixture) : MultiPageControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

// [ExcludeFromCodeCoverage]
// [Collection("SqlServerDatabaseCollection")]
// public class SqlServerMultiPageControllerTests : MultiPageControllerTests, IClassFixture<SqlServerDatabaseFixture>
// {
// 	public SqlServerMultiPageControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
// 	{
// 		// nothing
// 	}
// }

public abstract class MultiPageControllerTests : IntegrationTest
{
	private readonly MultiPageController _controllerInstance;

	private readonly List<DataElementDto> _data =
	[
		new DataElementDto("1", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(2024, 4, 4) }, { "Filename", "Program.cs" }, { "InSync", true }, { "Comment", "no comment" }
		})
		{
			Favorite = true
		},
		new DataElementDto("2", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(2022, 4, 4) }, { "Filename", "Detail.cs" }, { "InSync", false }, { "Comment", "a comment" }
		}),
		new DataElementDto("3", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(2023, 8, 3) }, { "Filename", "Baldur's Gate 3.exe" }, { "InSync", true }, { "Comment", "a bad comment" }
		}),
		new DataElementDto("4", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(1990, 1, 14) }, { "Filename", "Image.png" }, { "InSync", true }, { "Comment", "a good comment" }
		}),
		new DataElementDto("5", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(2016, 12, 5) }, { "Filename", "stderr.log" }, { "InSync", false }, { "Comment", "comment a comment" }
		}),
		new DataElementDto("6", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(2025, 4, 10) }, { "Filename", "InactiveDreams.mkv" }, { "InSync", true }, { "Comment", "discard a comment" }
		})
		{
			Inactive = true
		}
	];

	private readonly List<DataElementDto> _referenceData =
	[
		new DataElementDto("1", new Dictionary<string, object?> { { "ReferenceField", "Hallo" } }),
		new DataElementDto("2", new Dictionary<string, object?> { { "ReferenceField", "Schlumpf" } })
	];

	protected MultiPageControllerTests(DatabaseFixture fixture) : base(fixture, null, true)
	{
		_controllerInstance = new MultiPageController(LogManager, UserManager, Fixture.ContextFactory, VersionReader, LocalizerFactory);
	}

	#region Tests

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items")]
	public async Task QueryTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "Filename",
					Direction = SortDirection.Asc
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;
		var mutatedNamesList = queryResult!.Rows.ToList().ConvertAll(config => config.Values["Filename"]);
		var expectedList = new List<string>
		{
			_data[2].Values["Filename"]!.ToString()!, _data[1].Values["Filename"]!.ToString()!, _data[3].Values["Filename"]!.ToString()!,
			_data[0].Values["Filename"]!.ToString()!, _data[4].Values["Filename"]!.ToString()!
		};

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);
		Assert.Equal(5, queryResult.CountTotal);
		Assert.Equal(5, queryResult.Rows.Count);

		int index;
		for (index = 0; index < mutatedNamesList.Count; index++)
		{
			Assert.Equal(expectedList[index], mutatedNamesList[index]);
		}
		
		Assert.False(queryResult.Rows[0].Inactive);
		Assert.False(queryResult.Rows[1].Inactive);
		Assert.False(queryResult.Rows[2].Inactive);
		Assert.False(queryResult.Rows[3].Inactive);
		Assert.False(queryResult.Rows[4].Inactive);
		
		Assert.False(queryResult.Rows[0].Favorite);
		Assert.False(queryResult.Rows[1].Favorite);
		Assert.False(queryResult.Rows[2].Favorite);
		Assert.True(queryResult.Rows[3].Favorite);
		Assert.False(queryResult.Rows[4].Favorite);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with limit and offset")]
	public async Task QueryLimitAndOffsetTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Limit = 2,
			Offset = 1,
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "CreateDate",
					Direction = SortDirection.Asc
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);
		Assert.Equal(5, queryResult.CountTotal);
		Assert.Equal(2, queryResult.Rows.Count);

		var expected = _data.OrderBy(entry => entry.Values["CreateDate"])
			.Select(entry => entry.Values["Filename"]!.ToString())
			.ToList();
		Assert.Equivalent(new List<string> { expected[1]!, expected[2]! },
						  queryResult.Rows.ToList().ConvertAll<string>(config => config.Values["Filename"]!.ToString()!),
						  true);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with sorting (In Sync desc, comment asc)")]
	public async Task QuerySortingTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "InSync",
					Direction = SortDirection.Desc
				},
				new()
				{
					OrderColumn = "Comment",
					Direction = SortDirection.Asc
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);
		Assert.Equal(5, queryResult.CountTotal);
		Assert.Equal(5, queryResult.Rows.Count);
		Assert.Equivalent(
			new List<string>()
			{
				_data[2].Values["Filename"]!.ToString()!, _data[3].Values["Filename"]!.ToString()!, _data[0].Values["Filename"]!.ToString()!,
				_data[1].Values["Filename"]!.ToString()!,
				_data[4].Values["Filename"]!.ToString()!
			},
			queryResult.Rows.ToList().ConvertAll<string>(config => config.Values["Filename"]!.ToString()!),
			true);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with filtering by Filename")]
	public async Task QueryFilterTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "Filename",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = "Detail.cs"
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(1, queryResult.CountTotal);
		Assert.Single(queryResult.Rows);
		Assert.Equal(_data[1].Values["Filename"], queryResult.Rows.First().Values["Filename"]);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with filtering by Inactive")]
	public async Task QueryFilterInactiveTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "",
					Operator = QueryParamFilterOperator.Inactive
				}
			},
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "Filename",
					Direction = SortDirection.Asc
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(6, queryResult.CountTotal);
		Assert.Equal(_data[5].Values["Filename"], queryResult.Rows[3].Values["Filename"]);

		Assert.False(queryResult.Rows[0].Inactive);
		Assert.False(queryResult.Rows[1].Inactive);
		Assert.False(queryResult.Rows[2].Inactive);
		Assert.True(queryResult.Rows[3].Inactive);
		Assert.False(queryResult.Rows[4].Inactive);
		Assert.False(queryResult.Rows[5].Inactive);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with filtering by Favorite")]
	public async Task QueryFilterFavoriteTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "",
					Operator = QueryParamFilterOperator.Favorite
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(1, queryResult.CountTotal);
		Assert.Single(queryResult.Rows);
		Assert.Equal(_data[0].Values["Filename"], queryResult.Rows.First().Values["Filename"]);
		Assert.True(queryResult.Rows[0].Favorite);
	}
	
	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with filtering by Favorite and allow Inactive ones")]
	public async Task QueryFilterInactiveAndFavoriteTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "",
					Operator = QueryParamFilterOperator.Favorite
				},
				new()
				{
					FilterColumn = "",
					Operator = QueryParamFilterOperator.Inactive
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(1, queryResult.CountTotal);
		Assert.Single(queryResult.Rows);
		Assert.Equal(_data[0].Values["Filename"], queryResult.Rows.First().Values["Filename"]);
		Assert.True(queryResult.Rows[0].Favorite);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with filtering by Filename twice")]
	public async Task QueryOrFilterTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "Filename",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = "Detail.cs"
				},
				new()
				{
					FilterColumn = "Filename",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = "Program.cs"
				}
			},
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "Filename",
					Direction = SortDirection.Asc
				}
			},
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(2, queryResult.CountTotal);
		Assert.Equal(2, queryResult.Rows.Count);
		Assert.Equal("Detail.cs", queryResult.Rows[0].Values["Filename"]);
		Assert.Equal("Program.cs", queryResult.Rows[1].Values["Filename"]);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with sorting, filtering and limit + offset by Filename")]
	public async Task QueryCompleteTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "InSync",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = true
				}
			},
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "Filename",
					Direction = SortDirection.Desc
				}
			},
			Limit = 2,
			Offset = 1
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(3, queryResult.CountTotal);
		Assert.Equal(2, queryResult.Rows.Count);
		Assert.Equal(_data[3].Values["Filename"], queryResult.Rows[0].Values["Filename"]);
		Assert.Equal(_data[2].Values["Filename"], queryResult.Rows[1].Values["Filename"]);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with sorting, filtering and limit + offset by Lookup and Virtual column")]
	public async Task QueryCompleteLookupTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "LookupField.Display",
					Operator = QueryParamFilterOperator.NotEquals,
					CompareValue = ""
				}
			},
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "VirtualField",
					Direction = SortDirection.Desc
				}
			},
			Limit = 2,
			Offset = 1
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(5, queryResult.CountTotal);
		Assert.Equal(2, queryResult.Rows.Count);
		Assert.Equal("Schlumpf", queryResult.Rows[0].Values["VirtualField"]);
		Assert.Equal("Hallo", queryResult.Rows[1].Values["VirtualField"]);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Group by a string field")]
	public async Task GroupByFilenameTest()
	{
		var result = (await PrepareGroupByFilterFieldValuesWithParamsAsync("Filename")).Result;

		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<FilterFieldQueryItemResultDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(3, queryResult.CountTotal);
		Assert.Equal(3, queryResult.Rows.Count);
		Assert.Equal(_data[2].Values["Filename"], queryResult.Rows[0].Value);
		Assert.Equal(_data[2].Values["Filename"], queryResult.Rows[0].Label);
		Assert.Equal(1, queryResult.Rows[0].Count);
		Assert.Equal(_data[3].Values["Filename"], queryResult.Rows[1].Value);
		Assert.Equal(_data[3].Values["Filename"], queryResult.Rows[1].Label);
		Assert.Equal(1, queryResult.Rows[1].Count);
		Assert.Equal(_data[0].Values["Filename"], queryResult.Rows[2].Value);
		Assert.Equal(_data[0].Values["Filename"], queryResult.Rows[2].Label);
		Assert.Equal(1, queryResult.Rows[2].Count);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Group by a lookup field")]
	public async Task GroupByLookupTest()
	{
		var preparationResult = await PrepareGroupByFilterFieldValuesWithParamsAsync("LookupField");
		var result = preparationResult.Result;


		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<FilterFieldQueryItemResultDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(2, queryResult.CountTotal);
		Assert.Equal(2, queryResult.Rows.Count);
		Assert.Equal(_referenceData[0].Values["ReferenceField"], queryResult.Rows[0].Value);
		Assert.Equal(_referenceData[0].Values["ReferenceField"], queryResult.Rows[0].Label);
		Assert.Equal(2, queryResult.Rows[0].Count);
		Assert.Equal(_referenceData[1].Values["ReferenceField"], queryResult.Rows[1].Value);
		Assert.Equal(_referenceData[1].Values["ReferenceField"], queryResult.Rows[1].Label);
		Assert.Equal(1, queryResult.Rows[1].Count);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query elements that contains stacking virtual field")]
	public async Task QueryStackingVirtualTest()
	{
		var result = await QueryStackingVirtualDataWithParamsAsync(new QueryParamsDto()
		{
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "Filename",
					Direction = SortDirection.Asc
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;
		var mutatedNamesList = queryResult!.Rows.ToList().ConvertAll(config => config.Values["Filename"]);
		var expectedList = new List<string>()
		{
			_data[2].Values["Filename"]!.ToString()!, _data[1].Values["Filename"]!.ToString()!, _data[3].Values["Filename"]!.ToString()!,
			_data[0].Values["Filename"]!.ToString()!,
			_data[4].Values["Filename"]!.ToString()!
		};

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);
		Assert.Equal(5, queryResult.CountTotal);
		Assert.Equal(5, queryResult.Rows.Count);

		int index;
		for (index = 0; index < mutatedNamesList.Count; index++)
		{
			Assert.Equal(expectedList[index], mutatedNamesList[index]);
		}
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Count MultiPage items")]
	public async Task CountTest()
	{
		var result = await CountDataWithParamsAsync(new QueryParamsDto());
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryCountDto>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(5, queryResult.Count);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Count MultiPage items with filtering by Filename twice")]
	public async Task CountWithFilterTest()
	{
		var result = await CountDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "Filename",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = "Detail.cs"
				},
				new()
				{
					FilterColumn = "Filename",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = "Program.cs"
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryCountDto>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(2, queryResult.Count);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Count MultiPage items with limit and offset ignored")]
	public async Task CountWithLimitAndOffsetTest()
	{
		var result = await CountDataWithParamsAsync(new QueryParamsDto()
		{
			Limit = 3,
			Offset = 2
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryCountDto>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(5, queryResult.Count);
	}

	#endregion

	#region Prepare Data

	private void CreateStorageEntries(DataSourceEntity dataSource, string referenceElementId, string anotherReferenceElementId)
	{
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var data = new List<DataStoreElementData>
		{
			new(_data[0].Values.Append(new KeyValuePair<string, object?>("LookupField", referenceElementId)).ToDictionary(), groups),
			new(_data[1].Values.Append(new KeyValuePair<string, object?>("LookupField", referenceElementId)).ToDictionary(), groups),
			new(_data[2].Values.Append(new KeyValuePair<string, object?>("LookupField", referenceElementId)).ToDictionary(), groups),
			new(_data[3].Values.Append(new KeyValuePair<string, object?>("LookupField", anotherReferenceElementId)).ToDictionary(), groups),
			new(_data[4].Values.Append(new KeyValuePair<string, object?>("LookupField", anotherReferenceElementId)).ToDictionary(), groups),
			new(_data[5].Values.Append(new KeyValuePair<string, object?>("LookupField", anotherReferenceElementId)).ToDictionary(), groups)
		};

		for (var index = 0; index < data.Count; index++)
		{
			var info = dataSource.CreateElement(data[index], origin);

			// Set element inactive
			if (_data[index].Inactive)
				dataSource.SetInactive(new Guid(info.ElementId).ToString());

			// Add element to favorites
			if (_data[index].Favorite)
				dataSource.AddFavourite(new Guid(info.ElementId).ToString());
		}
	}

	private async Task<ObjectResult> QueryDataWithParamsAsync(QueryParamsDto parameters)
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// create another data source with a field to reference
		var referenceSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
															  currentCustomer.Id, "SourceToReference");
		var referenceField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																new DataFieldDto()
																{
																	Type = DataType.String, Name = "ReferenceField", DataSourceId = referenceSource.Id,
																	Length = 100
																},
																null, databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		referenceField.Entity.CreateField();
		// add two elements
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var referenceElement = referenceSource.CreateElement(new DataStoreElementData(_referenceData[0].Values, groups), origin);
		var differentReferenceElement = referenceSource.CreateElement(new DataStoreElementData(_referenceData[1].Values, groups), origin);

		// create some records in db to query data
		// create DataSource
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id);
		var sourceId = source.Id;

		// create ListView
		var view = EntityCreation.CreateListView(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
												 currentCustomer.Id);
		var viewId = view.Id;

		// create Fields
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Date, Name = "CreateDate", DataSourceId = sourceId },
															   null, databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Filename", DataSourceId = sourceId, Length = 250 }, null,
										   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Boolean, Name = "InSync", DataSourceId = sourceId }, null,
															   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Comment", DataSourceId = sourceId, Length = 1000 }, null,
										   databaseContext));
		var lookupField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
															 new DataFieldDto()
															 {
																 Type = DataType.Guid, FieldType = DataFieldType.LookupField, Name = "LookupField",
																 DataSourceId = sourceId,
																 LookupSourceId = referenceSource.Id, LookupDisplayFieldId = referenceField.Entity.Id,
																 Length = 42
															 }, null,
															 databaseContext));
		var virtualField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
															  new DataFieldDto()
															  {
																  FieldType = DataFieldType.VirtualField, Name = "VirtualField", DataSourceId = sourceId,
																  VirtualLookupFieldId = lookupField.Entity.Id, VirtualDataFieldId = referenceField.Entity.Id
															  }, null,
															  databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var fieldList = databaseContext.DataFields.Include(field => field.LookupSource)
			.Where(field => !field.SystemField && field.DataSourceId == source.Id).ToList();
		foreach (var fieldEntity in fieldList)
		{
			if (fieldEntity.FieldType != DataFieldType.VirtualField)
				fieldEntity.CreateField();
		}

		// create Columns for ListView
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[0].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[1].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[2].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[3].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[4].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = virtualField.Entity.Id, ListViewId = viewId }));

		await databaseContext.SaveChangesAsync();

		CreateStorageEntries(source, referenceElement.ElementId, differentReferenceElement.ElementId);

		// add fields to select
		parameters.Fields = fieldList.Select(field => field.Name).ToList();

		return (_controllerInstance.Query(sourceId, parameters).Result as ObjectResult)!;
	}

	private async Task<ObjectResult> CountDataWithParamsAsync(QueryParamsDto parameters)
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// create another data source with a field to reference
		var referenceSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
															  currentCustomer.Id, "SourceToReference");
		var referenceField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																new DataFieldDto()
																{
																	Type = DataType.String, Name = "ReferenceField", DataSourceId = referenceSource.Id,
																	Length = 100
																},
																null, databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		referenceField.Entity.CreateField();
		// add two elements
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var referenceElement = referenceSource.CreateElement(new DataStoreElementData(_referenceData[0].Values, groups), origin);
		var differentReferenceElement = referenceSource.CreateElement(new DataStoreElementData(_referenceData[1].Values, groups), origin);

		// create DataSource
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id);
		var sourceId = source.Id;

		// create Fields
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Date, Name = "CreateDate", DataSourceId = sourceId },
															   null, databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Filename", DataSourceId = sourceId, Length = 250 }, null,
										   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Boolean, Name = "InSync", DataSourceId = sourceId }, null,
															   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Comment", DataSourceId = sourceId, Length = 1000 }, null,
										   databaseContext));

		var lookupField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
															 new DataFieldDto()
															 {
																 Type = DataType.Guid, FieldType = DataFieldType.LookupField, Name = "LookupField",
																 DataSourceId = sourceId,
																 LookupSourceId = referenceSource.Id, LookupDisplayFieldId = referenceField.Entity.Id,
																 Length = 42
															 }, null,
															 databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto()
										   {
											   FieldType = DataFieldType.VirtualField, Name = "VirtualField", DataSourceId = sourceId,
											   VirtualLookupFieldId = lookupField.Entity.Id, VirtualDataFieldId = referenceField.Entity.Id
										   }, null,
										   databaseContext));

		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var fieldList = databaseContext.DataFields.Include(field => field.LookupSource)
			.Where(field => !field.SystemField && field.FieldType != DataFieldType.VirtualField && field.DataSourceId == source.Id).ToList();
		foreach (var fieldEntity in fieldList)
		{
			fieldEntity.CreateField();
		}

		await databaseContext.SaveChangesAsync();

		// create some records in db to query data
		CreateStorageEntries(source, referenceElement.ElementId, differentReferenceElement.ElementId);

		return (_controllerInstance.Count(sourceId, parameters).Result as ObjectResult)!;
	}

	private async Task<ObjectResult> QueryStackingVirtualDataWithParamsAsync(QueryParamsDto parameters)
	{
		var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// create another data source with a field to reference
		var referenceSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
															  currentCustomer.Id, "SourceToReference");
		var referenceField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																new DataFieldDto()
																{
																	Type = DataType.String, Name = "ReferenceField", DataSourceId = referenceSource.Id,
																	Length = 100
																},
																null, databaseContext));
		var selfRefLookupField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																	new DataFieldDto()
																	{
																		FieldType = DataFieldType.LookupField, Name = "SelfRefLookup",
																		DataSourceId = referenceSource.Id,
																		LookupSourceId = referenceSource.Id, LookupDisplayFieldId = referenceField.Entity.Id,
																	},
																	null, databaseContext));
		var selfRefVirtualField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																	 new DataFieldDto()
																	 {
																		 FieldType = DataFieldType.VirtualField, Name = "SelfRefVirtual",
																		 DataSourceId = referenceSource.Id,
																		 VirtualLookupFieldId = selfRefLookupField.Entity.Id,
																		 VirtualDataFieldId = referenceField.Entity.Id,
																	 },
																	 null, databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		referenceField.Entity.CreateField();
		selfRefLookupField.Entity.CreateField();
		// add two elements
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var referenceElement = referenceSource.CreateElement(new DataStoreElementData(_referenceData[0].Values, groups), origin);
		var selfReferenceData = _referenceData[1].Values;
		selfReferenceData.Add("SelfRefLookup", referenceElement.ElementId);
		var differentReferenceElement = referenceSource.CreateElement(new DataStoreElementData(_referenceData[1].Values, groups), origin);

		// create some records in db to query data
		// create DataSource
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id);
		var sourceId = source.Id;

		// create ListView
		var view = EntityCreation.CreateListView(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
												 currentCustomer.Id);
		var viewId = view.Id;

		// create Fields
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Date, Name = "CreateDate", DataSourceId = sourceId },
															   null, databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Filename", DataSourceId = sourceId, Length = 250 }, null,
										   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Boolean, Name = "InSync", DataSourceId = sourceId }, null,
															   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Comment", DataSourceId = sourceId, Length = 1000 }, null,
										   databaseContext));
		var lookupField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
															 new DataFieldDto()
															 {
																 Type = DataType.Guid, FieldType = DataFieldType.LookupField, Name = "LookupField",
																 DataSourceId = sourceId,
																 LookupSourceId = referenceSource.Id, LookupDisplayFieldId = referenceField.Entity.Id,
																 Length = 42
															 }, null,
															 databaseContext));
		var stackingVirtualField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																	  new DataFieldDto()
																	  {
																		  FieldType = DataFieldType.VirtualField, Name = "VirtualField",
																		  DataSourceId = sourceId,
																		  VirtualLookupFieldId = lookupField.Entity.Id,
																		  VirtualDataFieldId = selfRefVirtualField.Entity.Id
																	  }, null,
																	  databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var fieldList = databaseContext.DataFields.Include(field => field.LookupSource)
			.Where(field => !field.SystemField && field.FieldType != DataFieldType.VirtualField && field.DataSourceId == source.Id).ToList();
		foreach (var fieldEntity in fieldList)
		{
			fieldEntity.CreateField();
		}

		// create Columns for ListView
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[0].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[1].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[2].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[3].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[4].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto()
																			 { FieldId = stackingVirtualField.Entity.Id, ListViewId = viewId }));

		await databaseContext.SaveChangesAsync();

		CreateStorageEntries(source, referenceElement.ElementId, differentReferenceElement.ElementId);

		// add fields to select
		parameters.Fields = fieldList.Select(field => field.Name).ToList();

		return (_controllerInstance.Query(sourceId, parameters).Result as ObjectResult)!;
	}

	private async Task<(ObjectResult Result, string ReferenceId, string DifferentReferenceId)> PrepareGroupByFilterFieldValuesWithParamsAsync(string fieldName)
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// create another data source with a field to reference
		var referenceSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
															  currentCustomer.Id, "SourceToReference");
		var referenceField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																new DataFieldDto()
																{
																	Type = DataType.String, Name = "ReferenceField", DataSourceId = referenceSource.Id,
																	Length = 100
																},
																null, databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		referenceField.Entity.CreateField();
		// add two elements
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var referenceElement = referenceSource.CreateElement(new DataStoreElementData(_referenceData[0].Values, groups), origin);
		var differentReferenceElement = referenceSource.CreateElement(new DataStoreElementData(_referenceData[1].Values, groups), origin);

		// create some records in db to query data
		// create DataSource
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id);
		var sourceId = source.Id;

		// create Fields
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Date, Name = "CreateDate", DataSourceId = sourceId },
															   null, databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Filename", DataSourceId = sourceId, Length = 250 }, null,
										   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Boolean, Name = "InSync", DataSourceId = sourceId }, null,
															   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Comment", DataSourceId = sourceId, Length = 1000 }, null,
										   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto()
										   {
											   Type = DataType.Guid, FieldType = DataFieldType.LookupField, Name = "LookupField",
											   DataSourceId = sourceId,
											   LookupSourceId = referenceSource.Id, LookupDisplayFieldId = referenceField.Entity.Id,
											   Length = 42
										   }, null,
										   databaseContext));

		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var fieldList = databaseContext.DataFields.Include(field => field.LookupSource)
			.Where(field => !field.SystemField && field.FieldType != DataFieldType.VirtualField && field.DataSourceId == source.Id).ToList();
		foreach (var fieldEntity in fieldList)
		{
			fieldEntity.CreateField();
		}

		await databaseContext.SaveChangesAsync();
		CreateStorageEntries(source, referenceElement.ElementId, differentReferenceElement.ElementId);

		var field = fieldList.First(field => field.Name == fieldName);
		var result = _controllerInstance.GroupFilterFieldValues(field.Id, new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "InSync",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = true
				}
			}
		}).Result as ObjectResult;

		return (result, referenceElement.ElementId, differentReferenceElement.ElementId)!;
	}

	#endregion
}