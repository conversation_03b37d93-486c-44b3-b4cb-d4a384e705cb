using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Domain.WebAppTests.Mocks.LoggerConfig;
using Levelbuild.Domain.WebAppTests.Mocks.VersionReader;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Xunit.Abstractions;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.ResponseManagement;

public class FrontendResponseTests(ITestOutputHelper output)
{
	private readonly MockController _testController = new(new MockLogManager(output), new MockVersionReader());

	#region MockClasses
	
	private class MockController(ILogManager logManager, IVersionReader versionReader) : FrontendController(
		logManager, logManager.GetLoggerForClass<MockController>(),
		versionReader)
	{
		// nothing
	}
	
	private class MockData : IResponseObject
	{
		public Guid? Id { get; set; } = Guid.NewGuid();
	}
	
	#endregion
	
	#region Tests
	
	[Trait("Category", "ControllerExtension Tests")]
	[Fact(DisplayName = "Get an empty OK response")]
	public void TestEmptyOkResponse()
	{
		var result = _testController.GetOkResponse().Result as ObjectResult;
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		
		var version = ((FrontendResponse)result.Value!).Version;
		Assert.Equal(new Version(), version);
	}
	
	[Trait("Category", "ControllerExtension Tests")]
	[Fact(DisplayName = "Get OK response with payload")]
	public void TestOkResponseWithPayload()
	{
		var result = _testController.GetOkResponse(new MockData()).Result as ObjectResult;
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		Assert.Equal(typeof(FrontendResponse<MockData>), result.Value!.GetType());
		
		var version = ((FrontendResponse)result.Value!).Version;
		Assert.True(((FrontendResponse<MockData>)result.Value).Data.Id.HasValue);
		Assert.Equal(new Version(), version);
	}
	
	[Trait("Category", "ControllerExtension Tests")]
	[Fact(DisplayName = "Get BadRequest response")]
	public void TestBadRequestResponse()
	{
		var message = "I am an error message!";
		var result = _testController.GetBadRequestResponse(message).Result as ObjectResult;
		Assert.NotNull(result);
		Assert.Equal(400, result.StatusCode);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		
		var version = ((FrontendResponse)result.Value!).Version;
		Assert.True(((FrontendResponse)result.Value).Error?.ErrorMessage == message);
		Assert.Equal(new Version(), version);
	}
	
	[Trait("Category", "ControllerExtension Tests")]
	[Fact(DisplayName = "Get NotFound response")]
	public void TestNotFoundResponse()
	{
		var message = "I am an error message!";
		var result = _testController.GetNotFoundResponse(message).Result as ObjectResult;
		Assert.NotNull(result);
		Assert.Equal(404, result.StatusCode);
		Assert.Equal(typeof(FrontendResponse), result.Value!.GetType());
		
		var version = ((FrontendResponse)result.Value!).Version;
		Assert.True(((FrontendResponse)result.Value).Error?.ErrorMessage == message);
		Assert.Equal(new Version(), version);
	}
	
	#endregion
}