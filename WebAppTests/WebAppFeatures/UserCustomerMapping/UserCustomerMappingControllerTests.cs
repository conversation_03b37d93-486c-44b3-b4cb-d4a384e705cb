using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.UserCustomerMapping;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.UserCustomerMapping;
using Levelbuild.Frontend.WebApp.Features.UserCustomerMapping.Controllers;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.UserCustomerMapping;

[ExcludeFromCodeCoverage]
public class PostgresUserCustomerMappingControllerTests(PostgresDatabaseFixture fixture)
	: UserCustomerMappingControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class UserCustomerMappingControllerTests : AdminControllerTest<UserCustomerMappingController, UserCustomerMappingEntity, UserCustomerMappingDto>
{
	#region Test Data Properties
	
	protected override UserCustomerMappingDto CreateDto
	{
		get
		{
			using var databaseContext = Fixture.Context;
			var customer = EntityCreation.CreateCustomer(databaseContext, ZitadelApiClientFactory);
			var user = EntityCreation.CreateUser(databaseContext, ZitadelApiClientFactory, customer.DisplayName);
			
			return new()
			{
				Customer = customer.ToDto(),
				CustomerId = customer.Id.ToString(),
				User = user.ToDto(),
				UserId = user.Id.ToString(),
				IsAdmin = true
			};
		}
	}
	
	protected override UserCustomerMappingDto InvalidCreateDto => new()
	{
		CustomerId	= "Invalid",
		UserId = "Invalid"
	};
	
	protected override UserCustomerMappingDto InvalidUpdateDto => new()
	{
		Id = Guid.Empty,
		CustomerId	= "Invalid",
		UserId = "Invalid"
	};
	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "customerId",
				Direction = SortDirection.Asc
			}
		}
	};
	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Limit = 2,
		Offset = 1,
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "customerId",
				Direction = SortDirection.Asc
			}
		}
	};
	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "id",
				Direction = SortDirection.Desc
			},
			new()
			{
				OrderColumn = "customerId",
				Direction = SortDirection.Asc
			}
		}
	};
	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>
		{
			new()
			{
				FilterColumn = "isAdmin",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = true
			}
		}
	};
	
	#endregion

	protected UserCustomerMappingControllerTests(DatabaseFixture fixture) : base(fixture)
	{
		var localizerFactory = Fixture.ServiceProvider.GetRequiredService<IExtendedStringLocalizerFactory>();
		
		ControllerInstance = new UserCustomerMappingController(LogManager, Fixture.ContextFactory, UserManager, localizerFactory, VersionReader, ZitadelApiClientFactory);
	}
	
	#region Data Preparation
	
	protected override UserCustomerMappingEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.UserCustomerMappings.Add(UserCustomerMappingEntity.FromDto(CreateDto));
		databaseContext.SaveChanges();
		
		return entry.Entity;
	}
	
	protected override UserCustomerMappingDto GetUpdateDto(UserCustomerMappingEntity entity)
	{
		return new UserCustomerMappingDto()
		{
			Id = entity.Id,
			User = entity.User?.ToDto(),
			UserId = entity.UserId.ToString(),
			Customer = entity.Customer?.ToDto(),
			CustomerId = entity.CustomerId.ToString(),
			IsAdmin = !entity.IsAdmin
		};
	}
	
	protected override async Task PrepareQueryDataAsync()
	{
		await using var databaseContext = Fixture.Context;
		var mockCustomer = EntityCreation.CreateCustomer(databaseContext, ZitadelApiClientFactory);
		var mockUser = EntityCreation.CreateUser(databaseContext, ZitadelApiClientFactory, mockCustomer.DisplayName);
		
		QueryData.Add(new()
		{
			User = mockUser.ToDto(),
			UserId = mockUser.Id.ToString(),
			Customer = mockCustomer.ToDto(),
			CustomerId = mockCustomer.Id.ToString(),
			CustomerName = mockCustomer.DisplayName,
			IsAdmin = true
		});
		
		mockCustomer = EntityCreation.CreateCustomer(databaseContext, ZitadelApiClientFactory, "Morty Smith Corp.");
		QueryData.Add(new()
		{
			User = mockUser.ToDto(),
			UserId = mockUser.Id.ToString(),
			Customer = mockCustomer.ToDto(),
			CustomerId = mockCustomer.Id.ToString(),
			CustomerName = mockCustomer.DisplayName,
		});
		
		mockCustomer = EntityCreation.CreateCustomer(databaseContext, ZitadelApiClientFactory, "Rick Sanchez LLC");
		QueryData.Add(new()
		{
			User = mockUser.ToDto(),
			UserId = mockUser.Id.ToString(),
			Customer = mockCustomer.ToDto(),
			CustomerId = mockCustomer.Id.ToString(),
			CustomerName = mockCustomer.DisplayName,
		});
		
		mockCustomer = EntityCreation.CreateCustomer(databaseContext, ZitadelApiClientFactory, "Summer Smith Inc.");
		QueryData.Add(new()
		{
			User = mockUser.ToDto(),
			UserId = mockUser.Id.ToString(),
			Customer = mockCustomer.ToDto(),
			CustomerId = mockCustomer.Id.ToString(),
			CustomerName = mockCustomer.DisplayName,
		});
		
		// create some records in db to query data
		foreach (var dto in QueryData)
		{
			databaseContext.UserCustomerMappings.Add(UserCustomerMappingEntity.FromDto(dto));
		}
		
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		QueryData = databaseContext.UserCustomerMappings
			.Include(mapping => mapping.User)
			.Include(mapping => mapping.Customer)
			.ToList()
			.ConvertAll(mapping => mapping.ToDto());
	}
	
	#endregion
	
	#region Assertions
	
	protected override void AssertIsAsExpected(UserCustomerMappingDto expected, UserCustomerMappingDto actual)
	{
		Assert.Equal(expected.UserId, actual.UserId);
		Assert.Equal(expected.CustomerId, actual.CustomerId);
		Assert.Equal(expected.IsAdmin, actual.IsAdmin);
	}
	
	protected override void AssertExists(UserCustomerMappingDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.UserCustomerMappings.Any(mapping => mapping.UserId == Guid.Parse(dto.UserId!) && mapping.CustomerId == Guid.Parse(dto.CustomerId!)));
	}
	
	protected override void AssertDoesNotExist(UserCustomerMappingDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.UserCustomerMappings.All(mapping => mapping.Id != dto.Id));
	}
	
	protected override bool DeleteSuccessful(UserCustomerMappingEntity entity)
	{
		AssertDoesNotExist(entity.ToDto());

		return true;
	}
	
	protected override bool CheckQueryResult(ConfigQueryResultDto<UserCustomerMappingDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var queryData = QueryData.OrderBy(mapping => mapping.CustomerId).ToList();
		
		var count = queryData.Count;
		var mutatedIdList = queryResultDto.Rows.ToList().ConvertAll(mapping => mapping.Id!);
		var expectedList = new List<Guid>();
		for(var i = offset; i < count && (expectedList.Count < limit || limit == 0); i++)
		{
			expectedList.Add((Guid)queryData[i].Id!);
		}
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (limit > 0 && queryResultDto.Rows.Count > limit)
			return false;
		
		for (var i = 0; i < mutatedIdList.Count; i++)
		{
			if (expectedList[i] != mutatedIdList[i])
				return false;
		}
		
		return true;
	}
	
	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<UserCustomerMappingDto> queryResultDto)
	{
		var queryData = QueryData
			.OrderByDescending(mapping => mapping.Id)
			.ThenBy(mapping => mapping.CustomerId)
			.ToList();
		
		var count = queryData.Count;
		var mutatedIdList = queryResultDto.Rows.ToList().ConvertAll(mapping => mapping.Id!);
		var expectedList = queryData.ConvertAll(mapping => mapping.Id!);
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;

		return !mutatedIdList.Where((t, i) => expectedList[i] != t).Any();
	}
	
	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<UserCustomerMappingDto> queryResultDto)
	{
		if (queryResultDto.CountTotal != 1)
			return false;
		if (queryResultDto.Rows.Count != 1)
			return false;
		
		using var databaseContext = Fixture.Context;
		return databaseContext.UserCustomerMappings.Find(queryResultDto.Rows.FirstOrDefault()?.Id) != null;
	}
	
	#endregion
}