using Humanizer;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.Extensions;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class StringExtensionsTests
{
	[Trait("Category", "StringExtensionTests")]
	[Theory(DisplayName = "Converting PascalCase Notation to kebab-case")]
	[InlineData("Word", "word")]
	[InlineData("TwoWords", "two-words")]
	[InlineData("DataStoreConfig", "data-store-config")]
	[InlineData("MoreThanTwoWords", "more-than-two-words")]
	[InlineData("Two_Words", "two-words")]
	[InlineData("Data_Store_Config", "data-store-config")]
	[InlineData("More_Than_Two_Words", "more-than-two-words")]
	[InlineData("More Than Two Words", "more-than-two-words")]
	public void TestPascalToKebab(string pascalNotation, string expected)
	{
		var kebabNotation = pascalNotation.Kebaberize();
		Assert.Equal(expected, kebabNotation);
	}

	[Trait("Category", "StringExtensionTests")]
	[Theory(DisplayName = "Converting kebab-case Notation to PascalCase")]
	[InlineData("word", "Word")]
	[InlineData("two-words", "TwoWords")]
	[InlineData("data-store-config", "DataStoreConfig")]
	[InlineData("more-than-two-words", "MoreThanTwoWords")]
	[InlineData("two_words", "TwoWords")]
	[InlineData("data_store_config", "DataStoreConfig")]
	[InlineData("more_than_two_words", "MoreThanTwoWords")]
	[InlineData("more than two words", "MoreThanTwoWords")]
	public void TestKebabToPascal(string kebabNotation, string expected)
	{
		var pascal = kebabNotation.Underscore().Pascalize();
		Assert.Equal(expected, pascal);
	}
}