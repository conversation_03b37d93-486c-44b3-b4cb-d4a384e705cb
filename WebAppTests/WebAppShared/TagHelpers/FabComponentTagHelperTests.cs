using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class FabComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;

	private TagHelperOutput _output;

	private readonly TagHelper _tagHelper;

	public FabComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new FabComponentTagHelper();
	}

	[Trait("Category", "FabComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToFab()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("Icon", "fa-thumb-icon"),
			TagHelperTestParameter.GetInstance("Size", FontSize.Medium, FontSize.Medium.GetFontSizeAsString())
		};

		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);

		Assert.Equal("lvl-fab", _output.TagName);
	}

	[Trait("Category", "FabComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}