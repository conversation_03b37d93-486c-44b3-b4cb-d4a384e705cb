using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class CardInfoComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;

	private TagHelperOutput _output;

	private readonly TagHelper _tagHelper;

	public CardInfoComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new CardInfoComponentTagHelper();
	}

	[Trait("Category", "CardInfoComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToCardInfo()
	{
		List<TagHelperTestParameter> arguments =
		[
			TagHelperTestParameter.GetInstance("Name", "GoodName"),
			TagHelperTestParameter.GetInstance("DecimalPlaces", 4),
			TagHelperTestParameter.GetInstance("Sign", "€"),
			TagHelperTestParameter.GetInstance("Type", InputDataType.String, InputDataType.String.GetTypeAsString()),
			TagHelperTestParameter.GetInstance("LiveEditable", true),
			TagHelperTestParameter.GetInstance("WithThousandSeparators", true),
			TagHelperTestParameter.GetInstance("RichText", true)
		];

		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);

		Assert.Equal("lvl-card-info", _output.TagName);
	}

	[Trait("Category", "CardInfoComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void WriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}