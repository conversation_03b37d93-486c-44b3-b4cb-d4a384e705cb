using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class DropdownMenuComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;

	private TagHelperOutput _output;

	private readonly TagHelper _tagHelper;

	public DropdownMenuComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new DropdownMenuComponentTagHelper();
	}

	[Trait("Category", "DropdownComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToInput()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("MinWidth", "20 px;"),
			TagHelperTestParameter.GetInstance("MaxWidth", "100 px;"),
			TagHelperTestParameter.GetInstance("Color", "#123456"),
			TagHelperTestParameter.GetInstance("BackgroundColor", "987654"),
			TagHelperTestParameter.GetInstance("BorderColor", "#147258")
		};

		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);

		Assert.Equal("lvl-menu", _output.TagName);
	}

	[Trait("Category", "InputComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}