using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class DropdownMenuItemComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;

	private TagHelperOutput _output;

	private readonly TagHelper _tagHelper;

	public DropdownMenuItemComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new DropdownMenuItemComponentTagHelper();
	}

	[Trait("Category", "DropdownComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToInput()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("Label", "Best Label"),
			TagHelperTestParameter.GetInstance("IconLeft", "hand-left"),
			TagHelperTestParameter.GetInstance("IconLeftColor", "red"),
			TagHelperTestParameter.GetInstance("IconRight", "hand-right"),
			TagHelperTestParameter.GetInstance("IconRightColor", "blue"),
			TagHelperTestParameter.GetInstance("Legend", "vor 2 min"),
			TagHelperTestParameter.GetInstance("Selected", true),
			TagHelperTestParameter.GetInstance("Checkable", true),
			TagHelperTestParameter.GetInstance("Checked", true),
			TagHelperTestParameter.GetInstance("Sortable", true),
			TagHelperTestParameter.GetInstance("PaddingLeft", "35 px;"),
			TagHelperTestParameter.GetInstance("Sorting", DataStoreElementSortDirection.Desc, DataStoreElementSortDirection.Desc.ToString().ToLower()),
		};

		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);

		Assert.Equal("lvl-menu-item", _output.TagName);
	}

	[Trait("Category", "InputComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}