using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class UrlTileComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;

	private TagHelperOutput _output;

	private readonly TagHelper _tagHelper;

	public UrlTileComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new UrlTileComponentTagHelper();
	}

	[Trait("Category", "UrlTileComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToButton()
	{
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("Url", "/Url/test"),
			TagHelperTestParameter.GetInstance("Subtitle", "Best Title"),
			TagHelperTestParameter.GetInstance("Type", TileType.Info),
			TagHelperTestParameter.GetInstance("Value", "100"),
			TagHelperTestParameter.GetInstance("ValueType", "integer"),
			TagHelperTestParameter.GetInstance("Sign", "€"),
			TagHelperTestParameter.GetInstance("DecimalPlaces", 0),
			TagHelperTestParameter.GetInstance("WithThousandSeparators", true),
			TagHelperTestParameter.GetInstance("Divider", 1),
			TagHelperTestParameter.GetInstance("IgnoreTimezone", true),
			TagHelperTestParameter.GetInstance("Icon", "fa-ban-icon"),
			TagHelperTestParameter.GetInstance("IconStyle", IconStyle.Regular),
		};

		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);

		Assert.Equal("lvl-url-tile", _output.TagName);
	}

	[Trait("Category", "UrlTileComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void WriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}