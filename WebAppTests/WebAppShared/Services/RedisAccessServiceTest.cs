using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.Services;

public class RedisAccessServiceTest
{
	private RedisAccessService _redisAccessService;
	
	public RedisAccessServiceTest()
	{
		var connectionString = new ConfigurationBuilder()
			.SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
			.AddJsonFile("testsettings.json")
			.Build()
			.GetSection("WebApp").GetSection("ConnectionStrings").GetSection("Redis").Value!;
		
		var redisMasterOptions = ConfigurationOptions.Parse(connectionString);
		var redisMaster = ConnectionMultiplexer.ConnectAsync(redisMasterOptions).Result;
		
		var redisReplicaOptions = ConfigurationOptions.Parse(connectionString);
		var redisRead = ConnectionMultiplexer.ConnectAsync(redisReplicaOptions).Result;
		
		_redisAccessService = new RedisAccessService(redisMaster, redisRead);
	}
	
	[Fact(DisplayName = "Test Redis Access")]
	public async Task TestRedisAccess()
	{
		var value = "test";
		var hashField = "testKey_" + new Guid();
		var key = "testHash";
		
		var setResult = await _redisAccessService.HashSetAsync(key, hashField, value);
		var existsResult = await _redisAccessService.HashExistsAsync(key, hashField);
		var getResult = await _redisAccessService.HashGetAsync(key, hashField);
		var deleteResult = await _redisAccessService.HashDeleteAsync(key, hashField);
		var notExistResult = await _redisAccessService.HashExistsAsync(key, hashField);
		
		Assert.True(setResult);
		Assert.True(existsResult);
		Assert.Equal(value, getResult);
		Assert.True(deleteResult);
		Assert.False(notExistResult);
	}
}