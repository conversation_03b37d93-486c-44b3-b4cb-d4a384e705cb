using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Domain.WebAppTests.WebAppFeatures;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.Thumbnail;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.Services;

[ExcludeFromCodeCoverage]
public class PostgresThumbnailHelperServiceTest(PostgresDatabaseFixture fixture) : ThumbnailHelperServiceTest(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class ThumbnailHelperServiceTest : IntegrationTest
{
	private readonly CoreDatabaseContext _thumbnailHelperServiceContext;
	
	protected ThumbnailHelperServiceTest(DatabaseFixture fixture, Action<IServiceCollection>? additionalServiceInjection = null, bool initStorage = true) : base(fixture, additionalServiceInjection, initStorage)
	{
		_thumbnailHelperServiceContext = Fixture.Context;
	}

	#region DataPreparation

	private async Task<DataSourceEntity> PrepareDataSourceAsync()
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 (await UserManager.GetCurrentCustomerAsync()).Id, syncSysFields: true);
		EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id,
									   "TestField", true, source.Name);
		return source;
	}
	
	private string AddFileToDataSource(DataSourceEntity dataSource)
	{
		var pngFile = File.OpenRead("../../../testImage.png");
		var file = new FormFile(pngFile, 0, pngFile.Length, pngFile.Name, pngFile.Name);
		
		var storeFileStream = new DataStoreFileStream(file.FileName, DateTime.Now.ToUniversalTime(), file.OpenReadStream(), file.Length);
		return dataSource.UploadFile(storeFileStream);
	}

	#endregion

	#region Tests
	
	[Fact(DisplayName = "Get ThumbnailHelperService")]
	public async Task TestGetThumbnailHelper()
	{
		var source = await PrepareDataSourceAsync();
		var fileId = AddFileToDataSource(source);
		var width = 120;
		var height = 120;
		var crop = true;
		var resultStream = await ThumbnailHelperService.GetThumbnailAsync(_thumbnailHelperServiceContext, fileId, source, width, height, crop);
		
		Assert.NotNull(resultStream);
	}
	
	[Fact(DisplayName = "Cache ThumbnailHelperService")]
	public async Task TestCacheThumbnailHelper()
	{
		var source = await PrepareDataSourceAsync();
		var fileId = AddFileToDataSource(source);
		var width = 120;
		var height = 120;
		var crop = true;
		var resultStream = await ThumbnailHelperService.GetThumbnailAsync(_thumbnailHelperServiceContext, fileId, source, width, height, crop);
		await ThumbnailHelperService.CacheThumbnailAsync(_thumbnailHelperServiceContext, resultStream!, fileId, width, height, crop);
		var resultEntity = await _thumbnailHelperServiceContext.Thumbnails.FirstOrDefaultAsync(t => t.FileId == fileId);
		
		Assert.NotNull(resultStream);
		Assert.NotNull(resultEntity);
		Assert.Equal($"{fileId}_{width}_{height}_{crop}", resultEntity.CachedFileId);
		Assert.Equal(fileId,resultEntity.FileId);
		Assert.True((DateTime.Now - resultEntity.LastTouched).TotalMinutes < 2);
		
		await resultStream.DisposeAsync();
	}
	
	[Fact(DisplayName = "Get Cached ThumbnailHelperService")]
	public async Task TestGetCachedThumbnailHelper()
	{
		var source = await PrepareDataSourceAsync();
		var fileId = AddFileToDataSource(source);
		var width = 120;
		var height = 120;
		var crop = true;
		var result = await ThumbnailHelperService.GetThumbnailAsync(_thumbnailHelperServiceContext, fileId, source, width, height, crop);
		await ThumbnailHelperService.CacheThumbnailAsync(_thumbnailHelperServiceContext, result!, fileId, width, height, crop);
		var resultEntity = await _thumbnailHelperServiceContext.Thumbnails.FirstOrDefaultAsync(t => t.FileId == fileId);
		// edit last touched to confirm no new thumbnail entity was added
		resultEntity!.LastTouched = DateTime.Now.AddHours(-1);
		await _thumbnailHelperServiceContext.SaveChangesAsync();
		
		result!.Seek(0, SeekOrigin.Begin);
		await ThumbnailHelperService.CacheThumbnailAsync(_thumbnailHelperServiceContext, result, fileId, width, height, crop);
		
		Assert.Equal(1, _thumbnailHelperServiceContext.Thumbnails.Count());
		Assert.NotNull(resultEntity);
		Assert.NotNull(result);
		Assert.Equal($"{fileId}_{width}_{height}_{crop}", resultEntity.CachedFileId);
		Assert.Equal(fileId, resultEntity.FileId);
		Assert.True((DateTime.Now - resultEntity.LastTouched).TotalMinutes > 60);
	}
	
	[Fact(DisplayName = "Delete ThumbnailHelperService")]
	public async Task TestDeleteThumbnailHelper()
	{
		var source = await PrepareDataSourceAsync();
		var fileId = AddFileToDataSource(source);
		var width = 120;
		var height = 120;
		var crop = true;
		var result = await ThumbnailHelperService.GetThumbnailAsync(_thumbnailHelperServiceContext, fileId, source, width, height, crop);
		await ThumbnailHelperService.CacheThumbnailAsync(_thumbnailHelperServiceContext, result!, fileId, width, height, crop);
		var resultEntity = await _thumbnailHelperServiceContext.Thumbnails.FirstOrDefaultAsync(t => t.FileId == fileId);
		_thumbnailHelperServiceContext.Thumbnails.Add(new CachedThumbnailEntity()
		{
			FileId = fileId,
			CachedFileId = "invalid"
		});
		await _thumbnailHelperServiceContext.SaveChangesAsync();
		
		await ThumbnailHelperService.DeleteThumbnailsAsync(_thumbnailHelperServiceContext, fileId);
		
		Assert.Equal(0, _thumbnailHelperServiceContext.Thumbnails.Count());
		Assert.Null(FileCachingService.GetFile(resultEntity!.GetDirectoryName(), fileId, resultEntity.CachedFileId));
	}
	
	[Fact(DisplayName = "Cancel Delete ThumbnailHelperService")]
	public async Task TestCancelDeleteThumbnailHelper()
	{
		var source = await PrepareDataSourceAsync();
		var fileId = AddFileToDataSource(source);
		var width = 120;
		var height = 120;
		var crop = true;
		await ThumbnailHelperService.GetThumbnailAsync(_thumbnailHelperServiceContext, fileId, source, width, height, crop);
		var cancellationTokenSource = new CancellationTokenSource();
		var cancellationToken = cancellationTokenSource.Token;
		var task = ThumbnailHelperService.DeleteThumbnailsAsync(_thumbnailHelperServiceContext, fileId, cancellationToken);
		await task;
	}
	
	#endregion
	
	public new void Dispose()
	{
		_thumbnailHelperServiceContext.Dispose();
		base.Dispose();
	}
	
}