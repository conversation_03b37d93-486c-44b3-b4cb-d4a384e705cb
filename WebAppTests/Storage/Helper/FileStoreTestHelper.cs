using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.FileInterface;
using Levelbuild.Core.FileInterface.Enum;
using Levelbuild.Core.FileInterface.Exception;
using Levelbuild.Domain.FileSystemFile.Dto;
using Levelbuild.Domain.Storage.Helper;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using FileInfo = Levelbuild.Core.FileInterface.FileInfo;

namespace Levelbuild.Domain.WebAppTests.Storage.Helper;

public class FileStoreTestHelper : IFileStoreHelper
{
	private FileStore _tempStorage;
	private int _step;

	/// <summary>
	/// abstraction to file system 
	/// </summary>
	/// <param name="temporaryPath"></param>
	/// <param name="fileStoreType"></param>
	public FileStoreTestHelper(string temporaryPath, FileStoreType fileStoreType)
	{
		switch (fileStoreType)
		{
			case FileStoreType.FileSystem:
				//FileStore storage = new FileSystemStore(new FileSystemFileStoreConfig(storagePath));
				break;
			default:
				throw new ArgumentException(fileStoreType + " is currently not supported");
		}

		switch (fileStoreType)
		{
			case FileStoreType.FileSystem:
				_tempStorage = new FileSystemStore(new FileSystemFileStoreConfig(temporaryPath));
				break;
			default:
				throw new ArgumentException(fileStoreType + " is currently not supported");
		}
	}

	public void SaveRevisionFile(RevisionFile revisionFile, StorageIndexDefinition sid, IDictionary<string, object?> values, string tenantId,
								 bool encrypt = false)
	{
		throw new NotImplementedException();
	}

	public RevisionFile LoadRevisionFile(StorageIndexDefinition storageIndexDefinition, string tenantId, long pathId, string elementId, long revisionNumber,
										 bool decrypt = false, string? extendedRevisionPath = null)
	{
		throw new NotImplementedException();
	}

	public string GetCurrentSavePath(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?>? fieldValues, string elementId,
									 long? revisionNumber = null, long? pathId = null, string? extendedRevisionPath = null)
	{
		throw new NotImplementedException();
	}

	public string GetContainer(string elementId, long revisionNumber)
	{
		throw new NotImplementedException();
	}

	public string GetFileId(string elementId, long revisionNumber, bool isRevisionFile)
	{
		return $"{elementId}_{revisionNumber}" + ((isRevisionFile) ? "_meta" : "_data");
	}

	public string SaveTemporaryFile(DataStoreFileStream dataStoreFileStream, string fileUploadId, out long fileSize)
	{
		throw new NotImplementedException();
	}

	public string SaveFile(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?> fieldValues, string elementId, long revisionNumber,
						   string? fileUploadId, string tenantId,
						   bool encrypt = false)
	{
		if (_step < 1)
		{
			_step++;
			throw new FileSaveException("Test exception for file save");
		}
		else if (_step < 2)
		{
			_step++;
			throw new DataStoreOperationException("Test exception for delete temp file entry in database");
		}

		return GetFileId(elementId, revisionNumber, false);
	}

	public FileInfo GetTempFileInfo(string fileUploadId)
	{
		FileInfo tempFileInfo = _tempStorage.GetFile(fileUploadId);
		return tempFileInfo;
	}

	public StorageFile ParseFileId(string fileId)
	{
		throw new NotImplementedException();
	}

	public StorageFile LoadFile(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?>? fieldValues, string tenantId,
								StorageFile storageFile, long? pathId, bool decrypt = true)
	{
		throw new NotImplementedException();
	}

	public void DeleteFile(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?>? fieldValues, string tenantId, StorageFile storageFile,
						   long pathId)
	{
		throw new NotImplementedException();
	}

	public void DeleteFile(FileInfo fileInfo, bool deleteEmptyDirectory)
	{
		throw new NotImplementedException();
	}

	public StorageFile GetTempFile(StorageTempFile storageTempFile)
	{
		throw new NotImplementedException();
	}

	public void DeleteTempFile(StorageTempFile storageTempFile)
	{
		if (_step < 3)
		{
			_step++;
			throw new FileDeleteException("Test exception for file save");
		}

		var finalFileId = GetFileId(storageTempFile.ElementId!, (long)storageTempFile.RevisionNumber!, false);
		FileInfo newTempFileInfo = _tempStorage.GetFile(finalFileId);
		newTempFileInfo.DeleteFile();
	}

	public string RenameTempFile(StorageTempFile storageTempFile)
	{
		FileInfo tempFileInfo = _tempStorage.GetFile(storageTempFile.FileId);
		var finalFileId = GetFileId(storageTempFile.ElementId!, (long)storageTempFile.RevisionNumber!, false);
		FileInfo newTempFileInfo = _tempStorage.GetFile(finalFileId);
		tempFileInfo.MoveTo(newTempFileInfo);
		return finalFileId;
	}
}