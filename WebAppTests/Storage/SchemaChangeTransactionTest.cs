using Levelbuild.Domain.Storage;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.WebAppTests.Storage.Setup;
using Microsoft.EntityFrameworkCore;
using Npgsql;

namespace Levelbuild.Domain.WebAppTests.Storage;


[ExcludeFromCodeCoverage]
[Collection("StoragePostgresDatabaseCollection")]
public class SchemaChangeTransactionTestPostgres : SchemaChangeTransactionTest, IClassFixture<PostgresDatabaseFixture>
{
	public SchemaChangeTransactionTestPostgres(PostgresDatabaseFixture fixture) : base(fixture)
	{
	}
}

public abstract class SchemaChangeTransactionTest
{
	private DatabaseFixture _fixture;

	public SchemaChangeTransactionTest(DatabaseFixture fixture)
	{
		_fixture = fixture;
	}


	[Fact(DisplayName = "Efcore, fluentmigrator and sqlkata in single transaction working")]
	public void TransactionTest()
	{
		var connection = (StorageConnection)_fixture.StorageInstance.GetConnection(null);
		var randomTableName = DatabaseFixture.RandomString(20);
		var randomTableName1 = DatabaseFixture.RandomString(20);

		connection.WithDbContextTransaction((db, transaction, scope) =>
		{
			connection.Migrator.Create(new StorageIndexDefinition(randomTableName, new List<StorageFieldDefinitionOrm>()), scope);
			#pragma warning disable EF1002
			db.Database.ExecuteSqlRaw($"alter table \"{randomTableName}\" add column testcol varchar(20)");
			#pragma warning restore EF1002
			db.StorageIndexDefinition.Add(new StorageIndexDefinition(randomTableName, new List<StorageFieldDefinitionOrm>())
			{
				ExternalName = "test"
			});
			db.SaveChanges();
		});
		
		Assert.Throws<DbUpdateException>(() => connection.WithDbContextTransaction((db, transaction, scope) =>
		{
			connection.Migrator.Create(new StorageIndexDefinition(randomTableName1, new List<StorageFieldDefinitionOrm>()), scope);
			db.StorageIndexDefinition.Add(new StorageIndexDefinition(randomTableName1, new List<StorageFieldDefinitionOrm>())
			{
				ExternalName = "test"
			});
			db.SaveChanges();
			// this should fail with unique constraint failure.
			db.StorageIndexDefinition.Add(new StorageIndexDefinition(randomTableName1, new List<StorageFieldDefinitionOrm>())
			{
				ExternalName = "test"
			});
			db.SaveChanges();
		}));

		var successfulTransaction = connection.WithDbContext(db =>
		{
			#pragma warning disable EF1002
			return db.Database.ExecuteSqlRaw($"update \"{randomTableName}\" set testcol = '0'") + db.StorageIndexDefinition.Count() - 1;
			#pragma warning restore EF1002
		});
		
		Assert.Equal(0, successfulTransaction);
		// table does not exist
		Assert.Throws<PostgresException>(() => connection.WithDbContext(db =>
		{
			#pragma warning disable EF1002
			return db.Database.ExecuteSqlRaw($"update \"{randomTableName1}\" set testcol = '0'");
			#pragma warning restore EF1002
		}));
		Assert.Equal(1, connection.WithDbContext(db => db.StorageIndexDefinition.Count()));
	}
}