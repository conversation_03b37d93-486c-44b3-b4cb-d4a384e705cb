From: "Tester, test" <<EMAIL>>
To: "<PERSON><PERSON>@email.com" <<PERSON>.<PERSON>erman<PERSON>@email.com>
Subject: Achtung Test
Thread-Topic: Achtung Test
Thread-Index: AQHa8waSQMDkSrFtK0GxLRL0a+RuFw==
Date: Tue, 20 Aug 2024 13:43:15 +0000
Message-ID:
	<<EMAIL>>
Content-Language: de-DE
X-MS-Has-Attach:
X-MS-Exchange-Organization-SCL: -1
X-MS-TNEF-Correlator:
X-MS-Exchange-Organization-RecordReviewCfmType: 0
msip_labels:
Content-Type: multipart/alternative;
	boundary="_000_BEZP281MB232805CE309F3414716056FBF88D2BEZP281MB2328DEUP_"
MIME-Version: 1.0

--_000_BEZP281MB232805CE309F3414716056FBF88D2BEZP281MB2328DEUP_
Content-Type: text/plain; charset="iso-8859-1"
Content-Transfer-Encoding: quoted-printable

Sehr geehrter Herr Mustermann,

dies ist ein Test. Wir bitten sie ruhig zu bleiben und zu testen.

Wenn Sie zum angegebenen Termin nicht testen k=F6nnen, testen Sie bitte.

Mit freundlichen Tests,
Test

--_000_BEZP281MB232805CE309F3414716056FBF88D2BEZP281MB2328DEUP_
Content-Type: text/html; charset="iso-8859-1"
Content-Transfer-Encoding: quoted-printable

<html>
<head>
<meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3Diso-8859-=
1">
<style type=3D"text/css" style=3D"display:none;"> P {margin-top:0;margin-bo=
ttom:0;} </style>
</head>
<body dir=3D"ltr">
<div class=3D"elementToProof" style=3D"font-family: Aptos, Aptos_EmbeddedFo=
nt, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt; c=
olor: rgb(0, 0, 0);">
Sehr geehrter Herr Mustermann,</div>
<div class=3D"elementToProof" style=3D"font-family: Aptos, Aptos_EmbeddedFo=
nt, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt; c=
olor: rgb(0, 0, 0);">
<br>
</div>
<div class=3D"elementToProof" style=3D"font-family: Aptos, Aptos_EmbeddedFo=
nt, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt; c=
olor: rgb(0, 0, 0);">
dies ist ein Test. Wir bitten sie ruhig zu bleiben und zu testen.</div>
<div class=3D"elementToProof" style=3D"font-family: Aptos, Aptos_EmbeddedFo=
nt, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt; c=
olor: rgb(0, 0, 0);">
<br>
</div>
<div class=3D"elementToProof" style=3D"font-family: Aptos, Aptos_EmbeddedFo=
nt, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt; c=
olor: rgb(0, 0, 0);">
Wenn Sie zum angegebenen Termin nicht testen k=F6nnen, testen Sie bitte.</d=
iv>
<div class=3D"elementToProof" style=3D"font-family: Aptos, Aptos_EmbeddedFo=
nt, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt; c=
olor: rgb(0, 0, 0);">
<br>
</div>
<div class=3D"elementToProof" style=3D"font-family: Aptos, Aptos_EmbeddedFo=
nt, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt; c=
olor: rgb(0, 0, 0);">
Mit freundlichen Tests,</div>
<div class=3D"elementToProof" style=3D"font-family: Aptos, Aptos_EmbeddedFo=
nt, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt; c=
olor: rgb(0, 0, 0);">
Test</div>
</body>
</html>

--_000_BEZP281MB232805CE309F3414716056FBF88D2BEZP281MB2328DEUP_--
