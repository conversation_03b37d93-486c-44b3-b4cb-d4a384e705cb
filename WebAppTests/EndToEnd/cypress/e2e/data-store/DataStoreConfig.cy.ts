describe('DataStoreConfig Configuration', () => {
	let connectionString = ''
	
	beforeEach(() => {
		cy.fixture('../../../testsettings.json').then((jsonData) => {
			connectionString = (jsonData['Storage']['ConnectionStrings']['PostgreSQL'] as string)
		})
	})
	
	it('navigates to data store entry point', () => {
		// go to DataStore via Home
		cy.visit("/");
		cy.get('body').find('.dashboard__item[href="/Admin/DataStores"]').as('navLink').should('be.visible')
		cy.get('@navLink').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores'))
		})
	})

	it('CRUDs a data store config item', () => {
		// remove example if exists
		removeDataStoreByName('Example')

		// remove renamed example if exists
		removeDataStoreByName('Renamed Example')
		
		createConfigItem()
		cy.wait(100)
		updateConfigItem()
		cy.wait(200)
		deleteConfigItem()
	})
	
	it('try to open data store config without authorization/authentication', () => {
		cy.visitWithoutUser('/Admin/DataStores')
		cy.visitWithoutAdminRights('/Admin/DataStores')
	})
	
	it('some additional navigation tests', () => {
		// open list view
		cy.visit('/Admin/DataStores')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores'))
		})
		cy.title().should('contain', 'Backends')
		
		// open create mask
		cy.get('[data-action=add]').click({ force: true })
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/Create'))
		})
		cy.title().should('contain', 'New')
		
		// close create mask via abort button
		cy.get('.side-panel > [data-action=cancel]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores'))
		})
		cy.title().should('contain', 'Backends')

		// open create mask again and reload page
		cy.get('[data-action=add]').click({ force: true })
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/Create'))
		})
		cy.title().should('contain', 'New')
		cy.reload()

		cy.get('.side-panel').shadow().find('#slider').should('have.attr', 'open')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/Create'))
		})
		
		// close create mask again
		cy.get('.side-panel > [data-action=cancel]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores'))
		})
		cy.title().should('contain', 'Backends')
		
		// create example
		createExampleViaRest()
		cy.reload()
		
		// open entry
		let storeName: string = ''
		cy.get('lvl-list').shadow().find('lvl-list-line:not([skeleton]):first-child').as('row').find('[data-name="name"]').then((cell) => {
			storeName = cell[0].innerText
			cy.get('@row').click()
			cy.url().then(url => {
				expect(url.endsWith('/Admin/DataStores/' + storeName))
			})
			cy.title().should('contain', storeName)
		})
		
		// refresh entry
		cy.reload()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/' + storeName))
			cy.title().should('contain', storeName)
		})

		// go to configuration view
		cy.get('lvl-side-nav-item[value="Configuration"]').click()
		cy.get('.form__options').should('exist')
		cy.reload()
		cy.get('.form__options').should('exist')

		// go back to basic settings
		cy.get('lvl-side-nav-item[value="BasicSettings"]').click()
		cy.get('#data-store-name').invoke('attr', 'value').should('not.be.empty')
		
		// back button should bring us back to the list view
		cy.go('back')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores'))
		})
		cy.title().should('contain', 'Backends')
		cy.get('lvl-list').should('exist')

		// refresh should keep us at the list view
		cy.reload()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores'))
		})
		cy.title().should('contain', 'Backends')
		
		// use breadcrumb navigation
		cy.visit('/Admin/DataStores/Example')
		cy.get('lvl-breadcrumb')
			.shadow()
			.find('#breadcrumbs').as('breadcrumbs')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/example'))
		})

		cy.get('@breadcrumbs').contains('Backends').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/example'))
		})
		cy.get('@breadcrumbs')
			.find('li')
			.should('have.length', 2)
		
		cy.get('@breadcrumbs')
			.find('#home').click()
		cy.title().should('contain', 'Welcome')
		cy.get('@breadcrumbs')
			.find('li')
			.should('have.length', 1)
		
		// remove example
		deleteExampleViaRest()
	})

	function createConfigItem() {
		// open list view
		cy.visit('/Admin/DataStores')

		// if example item already exists, remove it before starting the test run!
		cy.request({ url: '/Admin/DataStores/example', failOnStatusCode: false }).then((resp) => {
			if (resp.status == 200) {
				cy.visit('/Admin/DataStores/example')
				cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click({ force: true })
			}
		})

		// open create panel
		cy.get('[data-action=add]').click()
		cy.get('#data-stores-form').as('form').should('be.visible')

		// url should have changed to /Admin/DataStores/Create
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/Create'))
		})

		// input form data
		cy.get('@form')
			.find('#data-store-name').shadow()
			.find('input').as('nameInput').focus().clear()
			.type('Example')
		cy.get('@form')
			.find('#data-store-type').shadow().as('type')
			.find('input').focus().clear().type('Storage')
		cy.get('@type').find('.dropdown-content .content-container tbody tr:first-child').click()
		cy.get('@form')
			.find('#data-store-enabled')
			.click({force: true})

		cy.get('@form')
			.find('#data-store-connectionstring')
			.should('be.visible')

		// submit
		cy.get('.side-panel > [data-action=save]')
			.click()

		// warning should be visible that there are empty required fields
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

		cy.get('@form')
			.find('#data-store-connectionstring')
			.should('be.visible')
			.shadow().find('input').as('connectionInput')
			.type(connectionString)

		// submit -> CREATE
		cy.get('.side-panel > [data-action=save]')
			.click()

		// dialog should be closed
		cy.get('.side-panel').shadow().find('#slider').should('not.have.attr', 'open')

		// check reset of the form
		cy.get('[data-action=add]').click()
		cy.wait(100)
		cy.get('#data-store-connectionstring').should('not.exist')
		cy.get('#data-store-type').shadow().find('input').focus().clear().type('Storage')
		cy.get('#data-store-type').shadow().find('.dropdown-content .content-container tbody tr:first-child').click()

		cy.get('#data-store-connectionstring').shadow().find('input').should('be.empty')
		cy.get('#data-store-name').shadow().find('input').should('be.empty')
		cy.get('#data-store-enabled').shadow().find('input').should('have.not.attr', 'value', 'true')
	}

	function updateConfigItem() {
		// list view -> click on first data store
		cy.visit('/Admin/DataStores/')

		// edit view and return with back button
		cy.get('lvl-list').shadow().find('lvl-list-line:not([skeleton]):first-child').as('row').find('[data-name="name"]').then((cell) => {
			let storeName = cell[0].innerText
			cy.get('@row').click()
			cy.get('#data-store-config-form[initdone]').should('exist') // form should be visible
			cy.url().then(url => {
				expect(url.endsWith('/Admin/DataStores/' + storeName))
			})
		})
		cy.go('back')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores'))
		})

		// edit example view -> change input and save
		cy.visit('/Admin/DataStores/example')
		cy.get('#data-store-name').shadow()
			.find('input').focus().clear()
			.type('Renamed Example')
		cy.wait(500)
		cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()

		cy.get('lvl-side-nav-item[value="Configuration"]').click()
		cy.get('.form__options').get('lvl-section[heading]').should('exist')

		cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()
		cy.go('back')
		
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores'))
		})
		cy.title().should('contain', 'Backends')

		// item should be accessible
		cy.request({ url: '/Admin/DataStores/renamed-example', failOnStatusCode: false }).then((resp) => {
			expect(resp.status, 'StatusCode should be 200').to.equal(200)
		})

		// change to origin
		cy.visit('/Admin/DataStores/renamed-example')
		cy.get('#data-store-name').shadow()
			.find('input').focus().clear()
			.type('Example')
		cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()
	}

	function deleteConfigItem() {
		// edit sample data and delete item
		cy.visit('/Admin/DataStores/example')
		cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click({ force: true })
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores'))
		})

		// item should not be accessible
		cy.request({ url: '/Api/DataStores/example', failOnStatusCode: false }).then((resp) => {
			expect(resp.status, 'StatusCode should be 404').to.equal(404)
		})
	}

	function removeDataStoreByName(name: string) {
		cy.request({ url: '/Api/DataStores/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
			expect(resp.status).to.equal(200, 'Get temporary data store: Example')
			const dataStore = resp.body.data.rows.find((row: Record<string, any>) => row.name === name)
			if (dataStore != null) {
				cy.request({ url: '/Api/DataStores/' + dataStore.id, failOnStatusCode: false, method: 'DELETE' }).then((resp) => {
					expect(resp.status).to.equal(200, 'Delete temporary data store: Example')
				})
			}
		})
	}
	
	function createExampleViaRest(){
		cy.request({ url: '/Api/DataStores/', failOnStatusCode: false, method: 'POST', body: { name: 'Example', type: 'weather' } }).then((resp) => {
			expect(resp.status === 200 || resp.status === 400, 'Create temporary data store: Example').to.be.true
		})
	}
	
	function deleteExampleViaRest() {
		cy.request({ url: '/Api/DataStores/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
			expect(resp.status).to.equal(200, 'Get temporary data store: Example')
			const dataStore = resp.body.data.rows.find((row: Record<string, any>) => row.name === 'Example')
			cy.request({ url: '/Api/DataStores/' + dataStore.id, failOnStatusCode: false, method: 'DELETE' }).then((resp) => {
				expect(resp.status).to.equal(200, 'Delete temporary data store: Example')
			})
		})
	}
})

