describe('Page Configuration over DataSource', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0
	let dataSourceId = ''
	
	beforeEach(() => {
		guid = uuid()

		cy.createDataStore(guid, false).as('dataStore')
		cy.createDataSource('@dataStore', 'TestSource_' + guid).as('dataSource').then(dataSource => {
			dataSourceId = dataSource.id
		})
	})

	afterEach(() => {
		// remove dataStore (this will delete the dataSource as well)
		cy.request({ url: '/Api/DataStores/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
			expect(resp.status).to.equal(200, 'Get temporary data store: TestStore_' + guid)
			const dataStore = resp.body.data.rows.find((row: Record<string, any>) => row.name === 'TestStore_' + guid)
			cy.request({ url: '/Api/DataStores/' + dataStore.id, failOnStatusCode: false, method: 'DELETE' }).then((resp) => {
				expect(resp.status).to.equal(200, 'Delete temporary data store: TestStore_' + guid)
			})
		})
	})

	it('Create a single data page', () => {
		// open source
		cy.visit('/Admin/DataStores/teststore' + guid + '/DataSources/testsource' + guid)

		// go to pages tab
		cy.get('#data-source-menu lvl-side-nav-item[value=Pages]').click()
		cy.url().should('contain', '/Admin/DataStores/teststore' + guid + '/DataSources/testsource' + guid + '/Pages')

		cy.reload()
		cy.url().should('contain', '/Admin/DataStores/teststore' + guid + '/DataSources/testsource' + guid + '/Pages')
		cy.title().should('contain', 'TestSource_' + guid)

		// open create panel
		cy.get('[data-action=add]').click()
		cy.get('#page-form').as('form').should('be.visible')

		// url should have changed
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataSources/Pages/Create'))
		})

		// input form data
		cy.get('@form')
			.find('#page-name').shadow()
			.find('input').as('nameInput').focus().clear()
			.type('TestPage')

		cy.get('@form').find('#page-type').invoke('attr', 'value', 'SingleData')
		cy.get('@form')
			.find('#page-type').shadow()
			.find('input').as('typeInput').should('have.value', 'Single data set')
		cy.get('@form').find('[name=breadcrumbLabel]').invoke('attr', 'value', 'SingleData')

		cy.get('@form')
			.find('#page-description').shadow()
			.find('textarea').as('descriptionInput')
			.clear().type('Description for test page')

		// check that source id is set
		cy.get('@form').find('[name=dataSourceId]').should('have.attr', 'value', dataSourceId)
		
		// submit -> CREATE
		cy.get('.side-panel > [data-action=save]').click()

		// dialog should be closed
		cy.get('.side-panel').shadow().find('#slider').should('not.have.attr', 'open')

		// check reset of the form
		cy.get('[data-action=add]').click()
		cy.get('@nameInput').should('be.empty')
		cy.get('@typeInput').should('be.empty')
		cy.get('@descriptionInput').should('be.empty')
		cy.get('.side-panel > [data-action=cancel]').click()
		
		// edit view and return with back button
		cy.get('lvl-list').shadow().as('pageList')
		cy.get('@pageList').find('lvl-list-line:not([skeleton]):first-child').as('row')
			.find('[data-name=name]').then((cell) => {
			let storeName = cell[0].innerText
			cy.get('@row').click()
			cy.url().then(url => {
				expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/DataSources/testsource' + guid + '/Pages/' + storeName))
			})
		})
		cy.go('back')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/DataSources/testsource' + guid + '/Pages'))
		})
		
		// edit view create view button
		cy.visit('/Admin/DataStores/teststore' + guid + '/DataSources/testsource' + guid + '/Pages/testpage')
		cy.get('lvl-fab[data-action="add"]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/DataSources/testsource' + guid + '/Pages/testPage/BasicSettings/Create'))
		})
		
		cy.reload()

		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/DataSources/testsource' + guid + '/Pages/testPage/BasicSettings/Create'))
		})

		//add module to source
		cy.createModule('@dataStore', 'ExampleModule_' + guid)
		cy.visit('/Admin/DataStores/teststore' + guid + '/DataSources/testsource' + guid)
		cy.get('#data-source-module').shadow().find('lvl-input-button').click()
		cy.get('#data-source-module').shadow().find('lvl-popup').find('tr[tabindex="-1"]').click({force: true})
		cy.get('#data-source-module').should('have.attr', 'value')
		cy.get('lvl-button[data-action="save"]').click()
		// check for success toast
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'Successfully')

		// go to pages over module
		cy.visit('/Admin/DataStores/teststore' + guid + '/Modules/examplemodule' + guid + '/DataSources/testsource' + guid + '/Pages/testPage/BasicSettings')

		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Modules/examplemodule' + guid + '/DataSources/testsource' + guid + '/Pages/testPage/BasicSettings'))
		})
		
		// go to page view over module
		cy.visit('/Admin/DataStores/teststore' + guid + '/Modules/examplemodule' + guid + '/DataSources/testsource' + guid + '/Pages/testPage/Views/overview')

		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Modules/examplemodule' + guid + '/DataSources/testsource' + guid + '/Pages/testPage/Views/overview'))
		})
	})

	it('use filter panel and global search', () => {
		cy.createPage('@dataSource', 'Page_A', 'MultiData')
		cy.createPage('@dataSource', 'Page_B', 'MultiData')
		cy.createPage('@dataSource', 'Page_C', 'MultiData')

		cy.visit('/Admin/DataStores/teststore' + guid + '/DataSources/testsource' + guid +'/Pages')
		cy.get('lvl-filter-panel lvl-filter-panel-section[name="name"]').shadow()
			.find('lvl-input').shadow()
			.find('input').as('nameInput').should('exist')
		cy.get('lvl-list').shadow().as('list')
		cy.get('@list').find('lvl-list-line:not([skeleton])').should('have.length', 3)
		cy.get('@list').find('lvl-list-line:not([skeleton])').eq(0).find('lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_A')
		cy.get('@list').find('lvl-list-line:not([skeleton])').eq(1).find('lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_B')
		cy.get('@list').find('lvl-list-line:not([skeleton])').eq(2).find('lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_C')

		cy.get('@nameInput').focus().clear({ force: true }).type('_A{enter}', { force: true })
		cy.get('@list').find('lvl-list-line:not([skeleton])').should('have.length', 1)
		cy.get('@list').find('lvl-list-line:not([skeleton]) lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_A')

		cy.get('@nameInput').focus().clear({ force: true }).type('Page{enter}', { force: true })
		cy.get('@list').find('lvl-list-line:not([skeleton])').should('have.length', 3)

		cy.get('#global-search').shadow().find('input').as('globalSearchInput')
		cy.get('@globalSearchInput').focus().clear({ force: true }).type('Page_B{enter}', { force: true })
		cy.get('@list').find('lvl-list-line:not([skeleton])').should('have.length', 1)
		cy.get('@list').find('lvl-list-line:not([skeleton]) lvl-list-line-item[data-name="name"]').should('contain.text', 'Page_B')
	})
})