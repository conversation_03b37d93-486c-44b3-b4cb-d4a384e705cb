describe('Page Rendering ', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0

	//disable caching
	before(() => Cypress.automation(
		'remote:debugger:protocol',
		{
			command: 'Network.setCacheDisabled',
			params: { cacheDisabled: true }
		}
	))
	
	beforeEach(() => {
		guid = uuid()

		// create store
		cy.createDataStore(guid, true).as('store')

		// create data source for the single page
		cy.createDataSource('@store', 'MainSource_' + guid, { allowFile: true }).as('mainSource')

		// create a view fields 
		cy.createField('@mainSource', 'Filename', 'String', { length: 255 }).as('mainSourceField_File')
		cy.createField('@mainSource', 'Comment', 'String', { length: 255 }).as('mainSourceField_Comment')

		// create main single pages + view 
		cy.createPage('@mainSource', 'MainSinglePage', 'SingleData', {}).as('mainSinglePage')
		cy.createPageHeader('@mainSinglePage', 'header1', 'icons', 'MyHeader')
		cy.createPageView('@mainSinglePage', 'MainGridView', { type: 'Grid' }).as('mainGrViewId')

		// create main multi page + view
		cy.createPage('@mainSource', 'MainMultiPage', 'MultiData', { detailPageId: '@mainSinglePage' }).as('mainMultiPage')
		cy.createPageView('@mainMultiPage', '0_MainMultiPageView', { type: 'List', showPreview: true, display: true }).as('mainListView')
		cy.createPageView('@mainMultiPage', '1_MainMultiPageExpertView', { type: 'List', expertMode: true, display: true }).as('mainListExpertView')
		cy.updatePageView('@mainListExpertView', { type: 'List', stickyColumnCount: 2 })
		cy.createPageView('@mainMultiPage', '2_MainMultiPageGalleryView', { type: 'Gallery', display: true }).as('mainGalleryView')
		cy.updatePageView('@mainGalleryView', { type: 'Gallery', titleFieldId: '@mainSourceField_File', subtitleFieldId: '@mainSourceField_Comment' })
		
		// create an additional hidden view to check that this is not displayed
		cy.createPageView('@mainMultiPage', '3_SecondGalleryView', { type: 'Gallery', display: false })

		cy.createFieldSorting('@mainMultiPage', '@mainSourceField_File', 'desc')

		// main page: create multi page columns
		cy.forEachField('@mainSource', true, (field, index) => {
			cy.createColumn('@mainListView', field.id, index)
			cy.createColumn('@mainListExpertView', field.id, index)
		})
		
		// create workflow
		cy.createWorkflow('@mainSource', 'workflow').as('workflow')
		cy.createWorkflowNode('@workflow', 'InProgress', 1, 'arrow-progress').as('node1')
		cy.createWorkflowNode('@workflow', 'Positive', 2, 'check').as('node2')
		cy.createWorkflowNode('@workflow', 'Negative', 3, 'x').as('node3')

		// create some data for main
		cy.get<Record<string, any>>('@node1').then(node => {
			cy.createSimpleRecord('@mainSource', { Filename: 'Baldurs Gate 3', Comment: 'A good Comment', 'workflow_WorkflowStatus': node.id }).as('firstRecord')
		})
		cy.get<Record<string, any>>('@node2').then(node => {
			cy.createSimpleRecord('@mainSource', { Filename: 'DnD vs Avatar', Comment: 'Nat 20', 'workflow_WorkflowStatus': node.id }).as('secondRecord')
		})

		// upload png file
		cy.uploadFile('@mainSource', 'testFiles/testFile.png', 'testFile.png', 'image/png').as('pngFileId')
		cy.get('@pngFileId').then(fileUploadId => {
			cy.get<Record<string, any>>('@node3').then(node => {
				// create element with file attached
				cy.createRecord('@mainSource', {
					values: {
						Filename: 'Magic the Gathering',
						Comment: 'This is a comment',
						'workflow_WorkflowStatus': node.id,
					},
					fileUploadId: fileUploadId,
					groups: [ 'testgroup' ],
				}).as('thirdRecord')
			})
		})
	})

	it('open preview over page entity', () => {
		// render page
		cy.visit('/Admin/DataStores/teststore' + guid + '/Pages/mainmultipage')

		cy.window().then(win => {
			cy.stub(win, 'open').as('open')
		})

		cy.get('#content-buttons [data-action=preview]:not([hidden]):not([skeleton])').click()
		cy.get('@open').should('have.been.calledOnceWithExactly', '/Public/Pages/mainmultipage', '_blank')
	})

	it('renders a page with a list view included', () => {
		cy.visit('/Public/Pages/mainmultipage')

		cy.intercept('/Api/DataSources/**/Thumbnail*', {
			statusCode: 200,
			headers: {
				contentType: 'image/png',
			},
			fixture: 'thumbnailFile.png,null',
		}).as('thumbnailRequest')
		cy.wait('@thumbnailRequest')
		cy.wait('@thumbnailRequest')

		// get correct page view and check that the section exists with the right data attributes
		cy.get<Record<string, any>>('@mainListView').then((listView) => {
			cy.get(`.page-view[data-view-type="${listView.type}"]`)
				.should('have.attr', 'data-view-id', listView.id).as('listViewSection')
				.find('> *').should('exist')

			// get all columns for the page view and check that every column exists in the dom
			cy.request({
				url: `/Api/ListViewColumns?filters=${JSON.stringify([ { filterColumn: 'ListViewId', operator: 'Equals', compareValue: listView.id } ])}`,
				failOnStatusCode: false,
				method: 'GET',
			}).then((columnsResp) => {
				const columns = columnsResp.body.data.rows
				cy.get('@listViewSection').find('lvl-table-data-column').should('have.length', columns.length)
				columns.forEach((row: Record<string, any>) => {
					cy.get('@listViewSection').find(`[name="${row.fieldName}"]`).should('exist')
				})
				cy.get('@listViewSection').find('lvl-table').shadow().find('lvl-table-row').should('exist')
				cy.get('lvl-multi-data-view').then($element => {
					const element = $element[0] as any
					expect(element.sorting).to.have.length(1)
					expect(element.sorting[0].direction).to.equal('Desc')
					expect(element.sorting[0].orderColumn).to.equal('Filename')
				})
			})
		})
		
		//check thumbnail column
		cy.get('lvl-table').shadow().find('lvl-table-row[data-position="1"]').shadow().as('row')
		cy.get('@row').find('lvl-table-thumbnail').shadow().find('i').should('be.visible')

		cy.get<Record<string, any>>('@thirdRecord').then(record => {
			cy.get('lvl-table').shadow().find('lvl-table-row[data-position="0"]').shadow().as('row')
			cy.get('@row').find('lvl-table-thumbnail').as('thumbnail').shadow().find('.image-wrapper > img').should('be.visible')

			//refresh and check again
			cy.reload()

			cy.get('lvl-table').shadow().find('lvl-table-row[data-position="1"]').shadow().as('row')
			cy.get('@row').find('lvl-table-thumbnail').shadow().find('i').should('be.visible')

			cy.get('lvl-table').shadow().find('lvl-table-row[data-position="0"]').shadow().as('row')
			cy.get('@row').find('lvl-table-thumbnail').as('thumbnail').shadow().find('.image-wrapper > img').should('be.visible')
			
			cy.get('@thumbnail').realHover()
			cy.get('@thumbnail').shadow().find('.preview-content').should('exist')
			cy.get('@thumbnail').click()
			cy.get('@thumbnail').shadow().find('lvl-dialog > lvl-viewer').should('exist').shadow().find('.viewer-wrapper').should('not.have.property', 'webViewerLoading')
			cy.window().then(win => {
				cy.stub(win, 'open').as('open')
			})
			cy.get('@thumbnail').shadow().find('lvl-dialog').find('lvl-button[data-action="newTab"]').click({ force: true })
			cy.get('@open').should('have.been.calledOnceWithExactly', '/Public/Pages/mainsinglepage/' + record.id)

			cy.get('@thumbnail').shadow().find('lvl-dialog').should('not.exist')
			cy.get('@thumbnail').click()
			cy.get('@thumbnail').shadow().find('lvl-dialog').find('lvl-button[data-action="open"]').click({ force: true })

			cy.url().then(url => {
				expect(url.endsWith('/Public/Pages/mainsinglepage/' + record.id))
			})
		})
		
		cy.visit('/Public/Pages/mainmultipage')
		
		// check gallery view
		cy.get<Record<string, any>>('@mainGalleryView').then(galleryView => {
			// switch to gallery in the dropdown panel
			cy.get('lvl-multi-data-view').find('lvl-table').shadow().find('lvl-table-row:not([skeleton])').should('exist')
			cy.get('lvl-query-view-action-bar').shadow().find('[data-action="display-dropdown"]').as('viewDropdownButton')
			cy.get('@viewDropdownButton').click()
			cy.get('@viewDropdownButton').next().as('viewDropdown')
			cy.get('@viewDropdown').find('lvl-select-list-item').should('have.length', 3)
			cy.get('@viewDropdown').find(`lvl-select-list-item[value="${galleryView.id}"]`).click()
			
			cy.get('lvl-gallery').should('exist')
			cy.get('lvl-gallery').shadow().find('.gallery__card:not(.skeleton)').as('cards').should('have.length', 3)
		})
		
		// records should be in the right order and the first one should have an image
		cy.get('@cards').eq(0).find('.card__title').should('contain.text', 'Magic the Gathering')
		cy.get('@cards').eq(0).find('.card__subtitle').should('contain.text', 'This is a comment')
		cy.get('@cards').eq(0).find('lvl-thumbnail').shadow().find('img').should('exist')

		cy.get('@cards').eq(1).find('.card__title').should('contain.text', 'DnD vs Avatar')
		cy.get('@cards').eq(1).find('.card__subtitle').should('contain.text', 'Nat 20')

		cy.get('@cards').eq(2).find('.card__title').should('contain.text', 'Baldurs Gate 3')
		cy.get('@cards').eq(2).find('.card__subtitle').should('contain.text', 'A good Comment')
		
		// switch back to list view
		cy.get('@viewDropdownButton').click()
		cy.get('@viewDropdown').find(`lvl-select-list-item[icon="table-list"]`).eq(0).click()
	})
	
	it('check workflow states', () => {
		cy.visit('/Public/Pages/mainmultipage')
		
		cy.get('lvl-multi-data-view').find('lvl-table').shadow().find('.table__body').as('table').find('lvl-table-row:not([skeleton])').should('exist')
		
		// consider sorting filename desc
		// first row
		cy.get('@table').find('lvl-table-row').eq(2).shadow().find('lvl-workflow-state-icon').as('workflowIcon').should('exist')
		cy.get('@workflowIcon').shadow().find('.workflows').find('.workflow').should('have.length', 1)
		cy.get('@workflowIcon').shadow().find('.legend').as('legend')
		cy.get('@legend').find('.legend__item').eq(0).find('.item__name').should('contain.text', 'workflow')
		cy.get('@legend').find('.legend__item').eq(0).find('.item__state').should('contain.text', 'InProgress')
		
		// second row
		cy.get('@table').find('lvl-table-row').eq(1).shadow().find('lvl-workflow-state-icon').as('workflowIcon').should('exist')
		cy.get('@workflowIcon').shadow().find('.workflows').find('.workflow').should('have.length', 1)
		cy.get('@workflowIcon').shadow().find('.legend').as('legend')
		cy.get('@legend').find('.legend__item').eq(0).find('.item__name').should('contain.text', 'workflow')
		cy.get('@legend').find('.legend__item').eq(0).find('.item__state').should('contain.text', 'Positive')
		
		// third row
		cy.get('@table').find('lvl-table-row').eq(0).shadow().find('lvl-workflow-state-icon').as('workflowIcon').should('exist')
		cy.get('@workflowIcon').shadow().find('.workflows').find('.workflow').should('have.length', 1)
		cy.get('@workflowIcon').shadow().find('.legend').as('legend')
		cy.get('@legend').find('.legend__item').eq(0).find('.item__name').should('contain.text', 'workflow')
		cy.get('@legend').find('.legend__item').eq(0).find('.item__state').should('contain.text', 'Negative')

		// switch to gallery in the dropdown panel
		cy.get('lvl-multi-data-view').find('lvl-table').shadow().find('lvl-table-row:not([skeleton])').should('exist')
		cy.get('lvl-query-view-action-bar').shadow().find('[data-action="display-dropdown"]').as('viewDropdownButton')
		cy.get('@viewDropdownButton').click()
		cy.get('@viewDropdownButton').next().as('viewDropdown')
		cy.get('@viewDropdown').find(`lvl-select-list-item[icon="image"]`).click()
		
		// first item
		cy.get('lvl-gallery').shadow().find('.gallery__card').eq(2).as('item')
		cy.get('@item').find('lvl-workflow-item').as('workflowIcon').should('exist')
		cy.get('@workflowIcon').should('have.attr', 'label', '[InProgress]').and('have.attr', 'workflow-name', '[workflow]')
		
		// second item
		cy.get('lvl-gallery').shadow().find('.gallery__card').eq(1).as('item')
		cy.get('@item').find('lvl-workflow-item').as('workflowIcon').should('exist')
		cy.get('@workflowIcon').should('have.attr', 'label', '[Positive]').and('have.attr', 'workflow-name', '[workflow]')
		
		// third item
		cy.get('lvl-gallery').shadow().find('.gallery__card').eq(0).as('item')
		cy.get('@item').find('lvl-workflow-item').as('workflowIcon').should('exist')
		cy.get('@workflowIcon').should('have.attr', 'label', '[Negative]').and('have.attr', 'workflow-name', '[workflow]')
		
		// switch back to list view
		cy.get('@viewDropdownButton').click()
		cy.get('@viewDropdown').find(`lvl-select-list-item[icon="table-list"]`).eq(0).click()
	})

	describe('actions when in Chrome', {browser: 'chrome'}, () => {
		it('executes actions of list view items', () => {
			cy.intercept('Api/DataSources/**/Action').as('actionRequest')
			cy.visit('/Public/Pages/mainmultipage')

			cy.get('lvl-filter-panel').as('panel').should('exist')
			cy.get('@panel').shadow().find('.panel__wrapper').as('panelShadow')
			cy.get('lvl-query-view-action-bar').shadow().find('.action-bar').as('bar')
			cy.get('lvl-table').shadow().find('lvl-table-row[data-position="0"]').as('rowComponent')
			cy.get('@rowComponent').shadow().as('row')

			// set first row as favorite
			cy.get('@row').find('lvl-checkbox').realHover().click()
			cy.get('@bar').find('[data-action="favorite"]').click()
			cy.wait('@actionRequest')
			cy.get('@row').find('lvl-fav-button').should('exist').and('have.prop', 'enabled', true)
			cy.reload()

			// star should be visible after reload
			cy.get('@row').find('lvl-fav-button').should('exist').and('have.prop', 'enabled', true)

			// undo favoring
			cy.get('@row').find('lvl-checkbox').realHover().click()
			cy.get('@bar').find('[data-action="favorite"]').click()
			cy.wait('@actionRequest')
			cy.get('@bar').realHover()
			cy.get('@row').find('lvl-fav-button').should('not.exist')
			cy.reload()
			cy.get('@row').find('lvl-fav-button').should('not.exist')
			
			// set favorite = true
			cy.get('@row').find('.table__row').realHover()
			cy.get('@row').find('.row__actions').as('actions')
			cy.get('@actions').find('[data-action="favorite"]').click()
			cy.wait('@actionRequest')
			cy.reload()
			cy.get('@row').find('[data-action="favorite"]').should('have.attr', 'enabled')

			// set first row as inactive
			cy.get('@panelShadow').find('.section__system-fields [name="inactive"] [value="true"]').click()
			cy.get('@row').find('lvl-checkbox').realHover().click()
			cy.get('@bar').find('[data-action="inactive"]').click()
			cy.wait('@actionRequest')
			cy.get('@rowComponent').should('have.attr', 'inactive')
			
			cy.reload()

			// inactive should be visible after reload
			cy.get('@rowComponent').should('have.attr', 'inactive')

			// undo inactive
			cy.get('@row').find('lvl-checkbox').realHover().click()
			cy.get('@bar').find('[data-action="inactive"]').click()
			cy.wait('@actionRequest')
			cy.get('@rowComponent').should('not.have.attr', 'inactive')
			cy.reload()
			cy.get('@rowComponent').should('not.have.attr', 'inactive')
			
			// set inactive via single row
			cy.get('@row').find('.table__row').trigger('mouseenter')
			cy.get('@row').find('.row__actions').as('actions')
			cy.get('@actions').find('[data-dropdown="action-dropdown"][initdone]').as('dropdownButton')
			cy.get('@dropdownButton').click()
			cy.get('@actions').find('[data-action=inactive]').as('inactiveMenuItem')
			cy.get('@inactiveMenuItem').should('contain.text', 'Discard')
			cy.get('@rowComponent').should('not.have.attr', 'inactive')
			cy.get('@inactiveMenuItem').click()
			cy.wait('@actionRequest')
			cy.get('@rowComponent').should('have.attr', 'inactive')
			
			// after reload, it should be still inactive
			cy.reload()
			cy.get('@rowComponent').should('have.attr', 'inactive')
		})

		it('executes actions of gallery view items', () => {
			cy.intercept('Api/DataSources/**/Action').as('actionRequest')
			cy.visit('/Public/Pages/mainmultipage')

			cy.get('lvl-filter-panel').as('panel').should('exist')
			cy.get('@panel').shadow().find('.panel__wrapper').as('panelShadow')
			cy.get('lvl-query-view-action-bar').shadow().find('.action-bar').as('bar')
			cy.get('@bar').find('[data-action="display-dropdown"]').as('viewDropdownButton')
			cy.get('@viewDropdownButton').click()
			cy.get('@viewDropdownButton').next().as('viewDropdown')
			cy.get('@viewDropdown').find(`lvl-select-list-item[icon="image"]`).click()

			cy.get('lvl-gallery').shadow().find('.gallery__card').eq(0).as('item')

			// set first row as favorite
			cy.get('@item').find('lvl-fav-button').click()
			cy.wait('@actionRequest')
			cy.get('@item').find('lvl-fav-button').should('have.prop', 'enabled', true)
			cy.reload()

			// star should be visible after reload
			cy.get('@item').find('lvl-fav-button').should('exist').and('have.prop', 'enabled', true)

			// undo favoring
			cy.get('@item').find('[data-dropdown="action-dropdown"]').as('actionDropdownButton').click()
			cy.get('@item').find('[name="action-dropdown"] [data-action="favorite"]').click()
			cy.wait('@actionRequest')
			cy.get('@item').find('lvl-fav-button').should('exist').and('have.prop', 'enabled', false)
			cy.reload()
			cy.get('@item').find('lvl-fav-button').should('exist').and('have.prop', 'enabled', false)

			// set first row as inactive
			cy.get('@panelShadow').find('.section__system-fields [name="inactive"] [value="true"]').click()
			cy.get('@item').find('lvl-checkbox').realHover().click()
			cy.get('@bar').find('[data-action="inactive"]').click()
			cy.wait('@actionRequest')
			cy.get('@item').should('have.class', 'inactive')
			
			cy.reload()
			//	TODO: Remove this if filter can be restored after reload
			cy.get('@panelShadow').find('.section__system-fields [name="inactive"] [value="true"]').click()

			// inactive should be visible after reload
			cy.get('@item').should('have.class', 'inactive')
			
			// undo inactive
			cy.get('@item').find('lvl-checkbox').realHover().click()
			cy.get('@actionDropdownButton').click()
			cy.get('@item').find('[name="action-dropdown"] [data-action="inactive"]').click({ force: true })
			cy.wait('@actionRequest')
			cy.get('@item').should('not.have.class', 'inactive')
			cy.reload()
			cy.get('@item').should('not.have.class', 'inactive')
		})
		
		it('check expert configuration', () => {
			cy.visit('/Public/Pages/mainmultipage')

			cy.get('lvl-filter-panel').as('panel').should('exist')
			cy.get('@panel').shadow().find('.panel__wrapper').as('panelShadow')
			cy.get('lvl-query-view-action-bar').shadow().find('.action-bar').as('bar')
			cy.get('@bar').find('[data-action="display-dropdown"]').as('viewDropdownButton')
			cy.get('@viewDropdownButton').click()
			cy.get('@viewDropdownButton').next().as('viewDropdown')
			cy.get('@viewDropdown').find(`lvl-select-list-item[icon="table-list"]`).eq(1).click({ force: true })

			cy.get('lvl-table').shadow().find('lvl-table-row[data-position="0"]').shadow().as('row')
			cy.get('@row').find('.sticky__wrapper .data-column').should('have.length', 2)
		})
	})

	afterEach(() => {
		// remove dataStore (this will delete the dataSource as well)
		cy.removeDataStore(guid)
	})
})