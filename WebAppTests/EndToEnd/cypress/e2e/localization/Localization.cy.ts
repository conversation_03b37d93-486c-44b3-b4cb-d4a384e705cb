describe('Localization', () => {

	it('check if Autocomplete is properly localized', () => {
		// open DataStore Create view
		cy.visit('/Admin/DataStores/Create')

		// check tooltip for autocomplete button
		cy.get('#data-store-type').shadow().find('lvl-input-button').shadow().find('lvl-tooltip').should('contain.text', 'Open selection list')

		// type something, focusout and check error messages
		cy.get('#data-store-type').shadow().find('input').type('qwertz', { force: true }).blur({ force: true })
		cy.get('#data-store-type')
			.shadow()
			.find('lvl-input-icon')
			.should('have.attr', 'tooltip', 'Value has to be part of the select list!')
		cy.get('#data-store-type').shadow().find('legend').should('contain.text', 'Invalid field value')
	})

	it('CRUD Culture & Translations', () => {
		// check if test language is present? if yes -> remove it before starting our real tests
		cy.request({ url: '/Api/Cultures/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
			expect(resp.status).to.equal(200, 'Get temporary culture: XH')
			const culture = resp.body.data.rows.find((row: Record<string, any>) => row.name === 'xh')
			if (culture) {
				cy.request({ url: '/Api/Cultures/' + culture.id, failOnStatusCode: false, method: 'DELETE' }).then((resp) => {
					expect(resp.status).to.equal(200, 'Delete temporary culture: XH')
				})
			}
		})

		// open language overview
		cy.visit('/Admin/Cultures')

		// click fab button to add new language
		cy.get('lvl-fab[data-action=add]').click()
		cy.get('.side-panel lvl-form[initdone]').should('exist')
		cy.get('#culture-form #culture-name[initdone]').invoke('attr', 'value', 'xh')
		cy.get('#culture-form #culture-name[initdone]').should('have.attr', 'value', 'xh')
		cy.get('.side-panel > [data-action=save]').click()

		// overlay should show up
		cy.get('lvl-overlay[open]').should('exist')

		// check if side panel is closed and entry exists
		cy.get('.side-panel').should('not.be.visible')

		// overlay should no longer be visible
		cy.get('lvl-overlay[open]').should('not.exist')

		// open culture via datatable and check form
		cy.get('#culture-list lvl-list').shadow().find('lvl-list-line:not([skeleton])').contains('lvl-list-line-item', 'xh').click()
		cy.get('#culture-name').as('cultureName').should('have.value', 'xh')
		cy.get('@cultureName').shadow().find('input').should('contain.value', 'Xhosa')
		cy.get('@cultureName').invoke('get', 0).should('have.property', 'error', '')
		cy.get('@cultureName').invoke('get', 0).should('have.property', 'legend', '')
		cy.get('@cultureName').should('have.attr', 'readonly')

		// go back to list
		cy.go('back')

		// open same entry via absolut URL and check form
		cy.visit('/Admin/Cultures/xh')
		cy.get('#culture-name').as('cultureName').should('have.value', 'xh')
		cy.get('@cultureName').shadow().find('input').should('contain.value', 'Xhosa')
		cy.get('@cultureName').invoke('get', 0).should('have.property', 'error', '')
		cy.get('@cultureName').invoke('get', 0).should('have.property', 'legend', '')
		cy.get('@cultureName').should('have.attr', 'readonly')

		// visit translation overview and wait for list to be loaded before switching (otherwise the previous request gets aborted which leads to cypress failing)
		cy.visit('/Admin/Translations')
		waitForTranslationsLoaded()

		// switch to translations and select translation 
		cy.get('lvl-side-nav-item[label*=Xhosa]').as('sideNavItem').click()
		cy.get('@sideNavItem').should('have.attr', 'selected')

		waitForTranslationsLoaded()
		
		// add 3 translations
		cy.get('lvl-fab[data-action=add]').click()
		cy.get('lvl-input[name=key][initDone]').shadow().find('input').as('key').should('have.attr', 'required')
		cy.get('@key').focus().type('car')
		cy.get('lvl-input[name=value][initDone]').shadow().find('input').as('value').should('have.attr', 'required')
		cy.get('@value').focus().type('Auto')
		cy.get('.side-panel > [data-action=save]').click({ force: true })

		// overlay should show up
		cy.get('lvl-overlay[open]').should('exist')
		
		waitForTranslationsLoaded()

		// overlay should no longer be visible
		cy.get('lvl-overlay[open]').should('not.exist')
		
		cy.get('lvl-fab[data-action=add]').click()
		cy.get('@key').focus().type('banana')
		cy.get('@value').focus().type('Banane')
		cy.get('.side-panel > [data-action=save]').click({ force: true })

		waitForTranslationsLoaded()
		
		cy.get('lvl-fab[data-action=add]').click()
		cy.get('@key').focus().type('cherry')
		cy.get('@value').focus().type('Kirsche')
		cy.get('.side-panel > [data-action=save]').click({ force: true })

		waitForTranslationsLoaded()

		cy.get('lvl-fab[data-action=add]').click()
		cy.get('@key').focus().type('cherry')
		cy.get('@value').focus().type('Kirschn')
		cy.get('lvl-autocomplete[name=customer][initDone]').shadow().find('input').as('customer').focus().type('E2ETestCustomer').blur()

		cy.get('lvl-autocomplete[name=customer][initDone][value]').should('exist')
		cy.get('.side-panel > [data-action=save]').click({ force: true })

		waitForTranslationsLoaded()

		// remove 1 translation
		cy.get('#translation-list lvl-list').shadow().find('lvl-list-line-item').contains('banana').click()
		cy.get('lvl-input[name=key][value=banana]').should('exist')
		cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click()

		// overlay should show up
		cy.get('lvl-overlay[open]').should('exist')
		
		// goto languages
		waitForTranslationsLoaded()

		// overlay should no longer be visible
		cy.get('lvl-overlay[open]').should('not.exist')
		
		cy.visit('/Admin/Cultures')

		// check translation count
		cy.get('#culture-list lvl-list').shadow().contains('lvl-list-line-item', 'xh').as('xhRow')
			.siblings('[data-name=\'translationCount\']').should('contain.text', '3')


		// remove language (should delete remaining translations aswell)
		cy.get('@xhRow').click()
		cy.get('lvl-autocomplete[name=name]').should('exist')
		cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click()

		cy.get('#culture-list lvl-list').shadow().contains('lvl-list-line-item', 'xh').should('not.exist')
	})
})

function waitForTranslationsLoaded() {
	cy.get('#translation-list lvl-list').shadow().find('lvl-list-line[skeleton]:first-child', {timeout: 10000}).should('not.exist')
}