using Levelbuild.Core.StorageInterface.Constants;
using Levelbuild.Entities;
using Levelbuild.Entities.ContextFactories;
using Levelbuild.Frontend.WebApp.Features.DataStoreConnectionHandling.Interceptors;
using Levelbuild.Frontend.WebApp.Shared.Interceptors;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using static Levelbuild.Frontend.WebApp.Features.Database.Constants.ExtendedDatabaseProvider;

namespace Levelbuild.Domain.WebAppTests.Setup;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

[ExcludeFromCodeCoverage]
public class SqlServerDatabaseFixture : DatabaseFixture
{
	
	protected override void AddDatabaseContext()
	{
		var connectionString = Config.GetConnectionString("SqlServer")!.Replace("[GUID]", DatabaseIdentifier);
		Services.AddDbContextFactory<CoreDatabaseContext, SqlServerCoreDatabaseContextFactory>(options =>
		{
			options.UseSqlServer(connectionString, x => x.MigrationsAssembly(SqlServer.Assembly));
			
			options.ConfigureWarnings(warningsOptions => warningsOptions.Ignore(CoreEventId.ManyServiceProvidersCreatedWarning));
			
			options.AddInterceptors(DataStoreConnectionHelperInterceptor.Instance);
			options.AddInterceptors(PersistentEntityInterceptor.Instance);
		});
	}
	
	public override void InitStorageDataBase()
	{
		// Just do this once per Fixture instance
		if (!StorageConnectionString.IsNullOrEmpty())
			return;
		
		StorageConnectionString = StorageConfig.GetConnectionString("MSSQL")!.Replace("[GUID]", DatabaseIdentifier);
		
		StorageOptions = new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.DatabaseConnectionString, StorageConfig.GetConnectionString("MSSQL")!.Replace("[GUID]", DatabaseIdentifier) },
			{ StorageConfigurationConstants.DatabaseType, "mssql" }
		};
		
		var contextSection = StorageConfig.GetSection("CustomerContext");
		var storagePath = Path.Combine(contextSection.GetValue<string>("storagePath")!, "MSSQL");
		
		StorageContextConnectionString = contextSection.GetConnectionString("MSSQL")!.Replace("[GUID]", DatabaseIdentifier);
		StorageContextOptions = new Dictionary<string, object>()
		{
			{ StorageConfigurationConstants.Description, contextSection.GetValue<string>("description")! },
			{ StorageConfigurationConstants.DatabaseConnectionString, StorageContextConnectionString },
			{ StorageConfigurationConstants.StoragePath, storagePath },
			{ StorageConfigurationConstants.FilestoreContainer, contextSection.GetValue<string>("blobContainer")! },
			{ StorageConfigurationConstants.FilestoreUrl, contextSection.GetValue<string>("blobConnection")! },
			{ StorageConfigurationConstants.FilestoreType, "FileSystem" },
		};
	}
	
	public override void BuildServices()
	{
		ServiceProvider = Services.BuildServiceProvider();
		
		using var context = Context;
		context.Database.Migrate();
	}
	
	public override void CleanUp()
	{
		using var context = Context;
		context.Database.EnsureDeleted();
	}
	
	protected override void HandleDispose()
	{
		// nothing...	
	}
}