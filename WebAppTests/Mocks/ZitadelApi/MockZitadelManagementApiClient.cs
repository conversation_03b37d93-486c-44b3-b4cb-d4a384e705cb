using Grpc.Core;
using Levelbuild.Core.FrontendDtos.User;
using Levelbuild.Core.ZitadelApiInterface;
using Zitadel.User.V1;

namespace Levelbuild.Domain.WebAppTests.Mocks.ZitadelApi;

internal sealed class MockZitadelManagementApiClient : IZitadelManagementApiClient
{
	private MockZitadelDatabase _mockZitadelDatabase;
	
	public MockZitadelManagementApiClient(MockZitadelDatabase mockZitadelDatabase)
	{
		_mockZitadelDatabase = mockZitadelDatabase;
	}
	
	public User GetUser(string userId, string orgId)
	{
		if (!_mockZitadelDatabase.Users.TryGetValue(userId, out var user))
			throw new RpcException(Status.DefaultCancelled, "User not found.");

		return user;
	}
	
	public IList<User> GetActiveHumanUsers(string orgId)
	{
		return _mockZitadelDatabase.Users.Values.ToList();
	}
	
	public IList<User> GetActiveMachineUsers(string orgId)
	{
		throw new NotImplementedException();
	}
	
	public IList<User> GetInactiveUsers(string orgId)
	{
		throw new NotImplementedException();
	}
	
	public string AddHumanUser(UserDto dto, string orgId)
	{
		if (string.IsNullOrEmpty(dto.Username))
			throw new RpcException(Status.DefaultCancelled, "No username was given.");

		string guid = Guid.NewGuid().ToString();
		var user = new User()
		{
			Id = guid,
			UserName = dto.Username,
			Human = new Human()
			{
				Email = new Email()
				{
					Email_ = dto.Email,
					IsEmailVerified = true
				},
				Profile = new Profile()
				{
					DisplayName = dto.Username,
					NickName = dto.Username,
					FirstName = dto.FirstName,
					LastName = dto.LastName
				}
			}
		};
		
		_mockZitadelDatabase.Users.Add(guid, user);
		_mockZitadelDatabase.UserOrgs.Add(guid, new List<string> { orgId });
		return guid;
	}
	
	public string AddMachineUser(UserDto dto, string orgId)
	{
		if (string.IsNullOrEmpty(dto.Username))
			throw new RpcException(Status.DefaultCancelled, "No username was given.");
		
		string guid = Guid.NewGuid().ToString();
		var user = new User
		{
			Id = guid,
			UserName = dto.Username,
			Machine = new Machine()
		};
		
		_mockZitadelDatabase.Users.Add(guid, user);
		_mockZitadelDatabase.UserOrgs.Add(guid, new List<string> { orgId });
		return guid;
	}
	
	public void UpdateHumanUser(string userId, string orgId, UserDto dto)
	{
		if (string.IsNullOrEmpty(userId) || !_mockZitadelDatabase.Users.ContainsKey(userId))
			throw new RpcException(Status.DefaultCancelled, "User not found.");

		var userToUpdate = GetUser(userId, orgId);

		userToUpdate.UserName = dto.Username;
		userToUpdate.Human.Email = new Email()
		{
			Email_ = dto.Email,
			IsEmailVerified = true
		};
		userToUpdate.Human.Profile = new Profile()
		{
			DisplayName = dto.Username,
			NickName = dto.Username,
			FirstName = dto.FirstName,
			LastName = dto.LastName
		};
		
		_mockZitadelDatabase.Users[userToUpdate.Id] = userToUpdate;
	}
	
	public string CreatePersonalAccessToken(string userId, string orgId, DateTime expirationDate)
	{
		throw new NotImplementedException();
	}
	
	public void DeactivateUser(string userId, string orgId)
	{
		if (!_mockZitadelDatabase.Users.ContainsKey(userId))
			throw new RpcException(Status.DefaultCancelled, "User not found.");
	}
	
	public void ReactivateUser(string userId, string orgId)
	{
		if (!_mockZitadelDatabase.Users.ContainsKey(userId))
			throw new RpcException(Status.DefaultCancelled, "User not found.");
	}
	
	public void DeleteUser(string userId, string orgId)
	{
		if (!_mockZitadelDatabase.Users.ContainsKey(userId))
			throw new RpcException(Status.DefaultCancelled, "User not found.");
		
		_mockZitadelDatabase.Users.Remove(userId);
		_mockZitadelDatabase.UserOrgs.Remove(userId);
	}
	
	public Zitadel.Org.V1.Org GetMyOrganisation()
	{
		var orgId = _mockZitadelDatabase.UserOrgs[_mockZitadelDatabase.CurrentUserId].First();
		return new()
		{
			Id = orgId,
			Name = GetOrganisation(orgId).Name
		};
	}
	
	public Zitadel.Org.V1.Org GetOrganisation(string orgId)
	{
		if (!_mockZitadelDatabase.Orgs.TryGetValue(orgId, out var org))
			throw new RpcException(Status.DefaultCancelled, "Org not found.");

		return new()
		{
			Id = orgId,
			Name = org
		};
	}

	public string AddOrganisation(string name, IEnumerable<string> roles)
	{
		if (_mockZitadelDatabase.Orgs.ContainsValue(name))
			throw new RpcException(Status.DefaultCancelled, "Org already present.");

		string guid = Guid.NewGuid().ToString();
		
		_mockZitadelDatabase.Orgs.Add(guid, name);

		return guid;
	}

	public void UpdateOrganisation(string orgId, string name)
	{
		if (!_mockZitadelDatabase.Orgs.ContainsKey(orgId))
			throw new RpcException(Status.DefaultCancelled, "Org not found.");
		
		_mockZitadelDatabase.Orgs[orgId] = name;
	}

	public void DeactivateOrganisation(string orgId)
	{
		if (!_mockZitadelDatabase.Orgs.ContainsKey(orgId))
			throw new RpcException(Status.DefaultCancelled, "Org not found.");
	}
	
	public void ReactivateOrganisation(string orgId)
	{
		if (!_mockZitadelDatabase.Orgs.ContainsKey(orgId))
			throw new RpcException(Status.DefaultCancelled, "Org not found.");
	}
	
	public void DeleteOrganisation(string orgId)
	{
		if (!_mockZitadelDatabase.Orgs.ContainsKey(orgId))
			throw new RpcException(Status.DefaultCancelled, "Org not found.");
		
		_mockZitadelDatabase.Orgs.Remove(orgId);
	}

	public void AddUserToOrganisation(string userId, string orgId, string[]? roles = null)
	{
		if (!_mockZitadelDatabase.Users.ContainsKey(userId))
			throw new RpcException(Status.DefaultCancelled, "User not found.");

		if (!_mockZitadelDatabase.Orgs.ContainsKey(orgId))
			throw new RpcException(Status.DefaultCancelled, "Org not found.");
		
		if(_mockZitadelDatabase.UserOrgs[userId].Contains(orgId))
			throw new RpcException(Status.DefaultCancelled, "User already part of Org.");
		
		_mockZitadelDatabase.UserOrgs[userId].Add(orgId);
	}

	public void RemoveUserFromOrganisation(string userId, string orgId)
	{
		if (!_mockZitadelDatabase.Users.ContainsKey(userId))
			throw new RpcException(Status.DefaultCancelled, "User not found.");

		if (!_mockZitadelDatabase.Orgs.ContainsKey(orgId))
			throw new RpcException(Status.DefaultCancelled, "Org not found.");
		
		if(!_mockZitadelDatabase.UserOrgs[userId].Contains(orgId))
			throw new RpcException(Status.DefaultCancelled, "User not part of Org.");
		
		_mockZitadelDatabase.UserOrgs[userId].Remove(orgId);
	}

	public bool IsImpersonator(string userId, string orgId)
	{
		return true;
	}

	public void AddImpersonationRights(string userId, string orgId)
	{
		// Nothing...
	}

	public void RemoveImpersonationRights(string userId, string orgId)
	{
		// Nothing...
	}

	public IList<UserGrant> ListUserGrants(string orgId)
	{
		throw new NotImplementedException();
	}
	
	public void AddUserGrant(string userId, string orgId, IEnumerable<string> roles)
	{
		if (!_mockZitadelDatabase.Users.ContainsKey(userId))
			throw new RpcException(Status.DefaultCancelled, "User not found.");
	}

	public void UpdateUserGrant(string userId, string orgId, IEnumerable<string> roles)
	{
		if (!_mockZitadelDatabase.Users.ContainsKey(userId))
			throw new RpcException(Status.DefaultCancelled, "User not found.");
	}
	
	public void RemoveUserGrant(string userId, string orgId)
	{
		if (!_mockZitadelDatabase.Users.ContainsKey(userId))
			throw new RpcException(Status.DefaultCancelled, "User not found.");
	}
}