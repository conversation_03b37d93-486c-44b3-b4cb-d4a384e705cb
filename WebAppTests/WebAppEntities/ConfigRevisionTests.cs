using System.Security.Claims;
using Levelbuild.Core.ZitadelApiInterface;
using Levelbuild.Domain.WebAppTests.Mocks.AuthenticationService;
using Levelbuild.Domain.WebAppTests.Mocks.HttpContextAccessor;
using Levelbuild.Domain.WebAppTests.Mocks.ZitadelApi;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Customer;
using Levelbuild.Entities.Features.User;
using Levelbuild.Frontend.WebApp.Features.Auth.Services;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Services;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.Constants;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;

namespace Levelbuild.Domain.WebAppTests.WebAppEntities;

[ExcludeFromCodeCoverage]
public class PostgresConfigRevisionTests(PostgresDatabaseFixture fixture) : ConfigRevisionTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class ConfigRevisionTests : IDisposable
{
	private readonly DatabaseFixture _fixture;
	
	private readonly  IHttpContextAccessor _httpContextAccessor;
	private readonly IZitadelApiClientFactory _zitadelApiClientFactory;

	protected ConfigRevisionTests(DatabaseFixture fixture)
	{
		_fixture = fixture;
		
		_fixture.Services.AddSingleton<IAuthenticationService, MockAuthenticationService>();
		_fixture.Services.AddSingleton<IHttpContextAccessor, MockHttpContextAccessor>();
		_fixture.Services.AddSingleton<MockZitadelDatabase>();
		_fixture.Services.AddSingleton<IZitadelApiClientFactory, MockZitadelApiClientFactory>();
		_fixture.Services.AddSingleton<ILogManager, LogManager>();
		
		_fixture.Services.AddSingleton(_fixture.Config);
		_fixture.Services.AddKeyedSingleton<IConnectionMultiplexer>(RedisConstants.RedisWriteConnection, ConnectionMultiplexer.Connect(_fixture.Config.GetConnectionString("Redis")!));
		_fixture.Services.AddKeyedSingleton<IConnectionMultiplexer>(RedisConstants.RedisReadConnection, ConnectionMultiplexer.Connect(_fixture.Config.GetConnectionString("Redis")!));
		_fixture.Services.AddSingleton<IRedisAccessService, RedisAccessService>();
		_fixture.Services.AddSingleton<UserImpersonationCache>();
		_fixture.Services.AddScoped<UserManager>();
		
		_fixture.InitStorageDataBase();
		
		_fixture.BuildServices();
		
		_httpContextAccessor = (MockHttpContextAccessor) _fixture.ServiceProvider.GetRequiredService<IHttpContextAccessor>();
		_zitadelApiClientFactory = _fixture.ServiceProvider.GetRequiredService<IZitadelApiClientFactory>();
		
		// Give the UserManager something to work with.
		PrepareUsers();
		SetAdminContext();
	}

	#region Tests

	[Fact(DisplayName = "Create revised entity and check their default revision value (Should be Guid.Empty).")]
	public void DefaultRevisionTest()
	{
		var mockCustomer = EntityCreation.CreateCustomer(_fixture.Context, _zitadelApiClientFactory);
		var entity = EntityCreation.CreatePage(_fixture.Context, _fixture.StorageOptions, _fixture.StorageContextOptions, mockCustomer.Id);

		Assert.NotNull(entity);
		Assert.Equal(entity.Revision, Guid.Empty);
	}
	
	[Fact(DisplayName = "Create revised entity and simulate an update to ensure their revision is updated correctly.")]
	public void UpdateRevisionTest()
	{
		var mockCustomer = EntityCreation.CreateCustomer(_fixture.Context, _zitadelApiClientFactory);
		var entity = EntityCreation.CreatePage(_fixture.Context, _fixture.StorageOptions, _fixture.StorageContextOptions, mockCustomer.Id);
		Assert.NotNull(entity);
		
		var oldRevision = entity.Revision;
		Assert.Equal(oldRevision, Guid.Empty);
		
		entity.Touch();
		var newRevision = entity.Revision;
		Assert.NotEqual(oldRevision, newRevision);
	}
	
	[Fact(DisplayName = "Create revised entity and simulate an update to ensure their parent's revision is updated correctly.")]
	public void UpdateParentRevisionTest()
	{
		var mockCustomer = EntityCreation.CreateCustomer(_fixture.Context, _zitadelApiClientFactory);
		var parent = EntityCreation.CreateDataSource(_fixture.Context, _fixture.StorageOptions, _fixture.StorageContextOptions, mockCustomer.Id);
		Assert.NotNull(parent);
		
		var child = EntityCreation.CreateDataField(_fixture.Context, _fixture.StorageOptions, _fixture.StorageContextOptions, mockCustomer.Id, dataSourceName: parent.Name);
		Assert.NotNull(child);
		
		var oldRevision = parent.Revision;
		Assert.Equal(oldRevision, Guid.Empty);
		
		child.Touch();

		// Reload entity to get updated revision
		parent = _fixture.Context.DataSources.First();
		var newRevision = parent.Revision;
		Assert.NotEqual(oldRevision, newRevision);
	}

	#endregion
	
	private void PrepareUsers()
	{
		var customer = new CustomerEntity()
		{
			RemoteId = "1",
			DisplayName = "MockCustomer"
		};
		
		using var databaseContext = _fixture.Context;
		databaseContext.Customers.Add(customer);
		
		databaseContext.Users.Add(new UserEntity(customer)
		{
			RemoteId = "1",
			DisplayName = "MockUser",
			Email = "<EMAIL>",
			FirstName = "First",
			LastName = "Family",
		});

		databaseContext.SaveChanges();
	}
	
	// ReSharper disable once MemberCanBePrivate.Global
	public void SetAdminContext()
	{
		if (_httpContextAccessor.HttpContext == null)
			return;
		
		var claims = new List<Claim>()
		{
			new(ClaimTypes.NameIdentifier, "1"),
			new("http://schemas.microsoft.com/ws/2008/06/identity/claims/role", "admin"),
			new("urn:zitadel:iam:user:resourceowner:id", "1")
		};
		var identity = new ClaimsIdentity(claims, "MockAuthType");
		
		_httpContextAccessor.HttpContext.User = new ClaimsPrincipal(identity);
	}
	
	void IDisposable.Dispose()
	{
		_fixture.CleanUp();
	}
}