using System.Reflection;
using Levelbuild.Core.EntityInterface.Attributes;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;

namespace Levelbuild.Entities.Conventions;

/// <summary>
/// Convention to set the max length of a property of type string for our entities 
/// </summary>
public class MaxStringLengthConvention : IModelFinalizingConvention
{
	/// <inheritdoc />
	public void ProcessModelFinalizing(IConventionModelBuilder modelBuilder, IConventionContext<IConventionModelBuilder> context)
	{
		foreach (var property in modelBuilder.Metadata.GetEntityTypes()
					 .SelectMany(entityType => entityType.GetDeclaredProperties()
									 .Where(property => property.ClrType == typeof(string) &&
														property.PropertyInfo?.GetCustomAttribute<TextAttribute>() == null)))
		{
			property.Builder.HasMaxLength(property.PropertyInfo?.GetCustomAttribute<ShortStringAttribute>() != null ? 100 : 255);
		}
	}
}