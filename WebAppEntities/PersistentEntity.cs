using System.Reflection;
using System.Text.Json;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using JsonColumn = Levelbuild.Core.EntityInterface.Attributes.JsonColumn;

namespace Levelbuild.Entities;

/// <summary>
/// Base class for all WebApp EF Core entities.
/// </summary>
/// <typeparam name="TEntity">The type of the inheriting entity.</typeparam>
[PrimaryKey("Id")]
public abstract class PersistentEntity<TEntity> : IPersistentEntity where TEntity : PersistentEntity<TEntity>
{
	/// <summary>
	/// The entities id.
	/// </summary>
	public Guid Id { get; set; }

	/// <inheritdoc />
	public virtual void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		var jsonColumns = typeof(TEntity)
			.GetProperties(BindingFlags.Public | BindingFlags.Instance)
			.Where(p => p.GetCustomAttributes(typeof(JsonColumn), false).Length == 1);

		foreach (var column in jsonColumns)
		{
			if (databaseProvider == DatabaseProvider.Postgres)
			{
				modelBuilder.Entity<TEntity>()
					.Property(column.Name)
					.HasColumnType("jsonb");
			}
			else if (databaseProvider == DatabaseProvider.SqlServer)
			{
				if (column.PropertyType == typeof(Dictionary<string, object>))
				{
					modelBuilder.Entity<TEntity>()
						.Property(column.Name)
						.HasConversion(
							new ValueConverter<Dictionary<string, object>, string>(
								val => JsonSerializer.Serialize(val, (JsonSerializerOptions?) null),
								val => JsonSerializer.Deserialize<Dictionary<string, object>>(val, (JsonSerializerOptions?) null) ?? new Dictionary<string, object>()
							)
						);
				}
			}
		}
	}

	/// <summary>
	/// The string localizer factory.
	/// </summary>
	protected IExtendedStringLocalizerFactory? StringLocalizerFactory;

	/// <inheritdoc />
	public void SetStringLocalizerFactory(IExtendedStringLocalizerFactory factory)
	{
		StringLocalizerFactory = factory;
	}

	/// <summary>
	/// The database context.
	/// </summary>
	protected CoreDatabaseContext? DbContext;

	/// <inheritdoc />
	public void SetContext(DbContext context)
	{
		if (context is CoreDatabaseContext databaseContext)
			DbContext = databaseContext;
	}
}