using System.Globalization;

namespace Levelbuild.Entities.Helpers;

/// <summary>
/// static helper for everything cultures
/// </summary>
public static class CultureHelper
{
	/// <summary>
	/// static list of all cultures know to System.Globalization
	/// </summary>
	public static readonly CultureInfo[] CultureInfosAll = CultureInfo.GetCultures(CultureTypes.AllCultures);

	/// <summary>
	/// returns the full name of a culture translated to the users language
	/// </summary>
	/// <param name="cultureName">culture name abbreviation in form of languagecode2-country/ regioncode2</param>
	/// <returns></returns>
	public static string GetFullCultureName(string cultureName)
	{
		return CultureInfosAll.FirstOrDefault(c => c.Name == cultureName)?.DisplayName ?? cultureName;
	}
}