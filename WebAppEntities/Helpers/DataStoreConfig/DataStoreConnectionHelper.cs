using Levelbuild.Core.DataStoreInterface;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Domain.Storage;
using Serilog;

namespace Levelbuild.Entities.Helpers.DataStoreConfig;

/// <summary>
/// Helper class that provides methods for general DataStore actions.
/// </summary>
public class DataStoreConnectionHelper
{
	#region Attributes

	private IDataStore? _dataStoreInstance;

	/// <summary>
	/// Creates and exposes the data store instance.
	/// </summary>
	/// <exception cref="Exception"></exception>
	public IDataStore DataStoreInstance
	{
		get
		{
			if (_dataStoreInstance == null)
			{
				switch (DataStoreType)
				{
					case DataStoreType.Storage:
						_dataStoreInstance = Storage.GetInstance(Options, Logger);
						break;
					case DataStoreType.Weather:
						// TODO: Nothing yet...
						_dataStoreInstance = null!;
						break;
					default:
						throw new Exception("Unsupported DataStore type!");
				}
			}

			return _dataStoreInstance;
		}
	}

	/// <summary>
	/// The data store's type.
	/// </summary>
	protected DataStoreType DataStoreType;


	/// <summary>
	/// The data store's options.
	/// </summary>
	protected IDictionary<string, object> Options;

	/// <summary>
	/// The logger.
	/// </summary>
	protected ILogger Logger;

	/// <summary>
	/// The current user's display name.
	/// </summary>
	protected string CurrentUserName;

	/// <summary>
	/// The current user's guid.
	/// </summary>
	protected Guid CurrentUserId;

	#endregion

	#region Constructor

	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="dataStoreType">The data store type.</param>
	/// <param name="options">The data store options.</param>
	/// <param name="logger">The logger that will be used by the data store.</param>
	/// <param name="currentUserName">The current user's display name.</param>
	/// <param name="currentUserId">The current user's id.</param>
	public DataStoreConnectionHelper(DataStoreType dataStoreType, IDictionary<string, object> options, ILogger logger, string currentUserName,
									 Guid currentUserId)
	{
		DataStoreType = dataStoreType;
		Options = options;
		Logger = logger;
		CurrentUserName = currentUserName;
		CurrentUserId = currentUserId;
	}

	#endregion

	#region Methods

	/// <inheritdoc cref="IDataStoreConnection.GetDataSource"/>
	internal IDataStoreDataSource? GetDataSource(string name)
	{
		return ExecuteOperation(connection => connection.GetDataSource(name));
	}

	/// <inheritdoc cref="IDataStoreConnection.GetDataSources"/>
	internal IList<string> GetDataSources()
	{
		return ExecuteOperation(connection => connection.GetDataSources());
	}

	/// <summary>
	/// Opens a connection to the data store.
	/// </summary>
	/// <returns></returns>
	internal IDataStoreConnection OpenConnection()
	{
		var authentication = new DataStoreAuthentication(CurrentUserName, CurrentUserId, new List<string>() { "testgroup" });

		return DataStoreInstance.GetConnection(authentication);
	}

	/// <summary>
	/// Opens a connection to the given data store context.
	/// </summary>
	/// <param name="context"></param>
	/// <returns></returns>
	internal IDataStoreConnection OpenConnection(IDataStoreContext context)
	{
		var authentication = new DataStoreAuthentication(CurrentUserName, CurrentUserId, new List<string>() { "testgroup" });

		return DataStoreInstance.GetConnection(context.Identifier, authentication);
	}

	private T ExecuteOperation<T>(Func<IDataStoreConnection, T> operation)
	{
		T result;
		IDataStoreConnection? connection = null;

		try
		{
			connection = OpenConnection();

			result = operation(connection);
		}
		finally
		{
			connection?.Dispose();
		}

		return result;
	}

	#endregion
}