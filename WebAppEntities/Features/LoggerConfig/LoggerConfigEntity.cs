using System.ComponentModel.DataAnnotations;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Logging;
using Levelbuild.Entities.Attributes;
using Levelbuild.Entities.Extensions;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;
using Serilog.Events;
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Levelbuild.Entities.Features.LoggerConfig;

/// <summary>
/// Entity Class for LoggerConfiguration
/// </summary>
[Slug(nameof(LoggerSource))]
public sealed class LoggerConfigEntity : PersistentEntity<LoggerConfigEntity>, IAdministrationEntity<LoggerConfigEntity, LoggerConfigDto>
{
	[Required]
	private string _loggerSource;

	/// <summary>
	/// Used inside the URL to address the item
	/// </summary>
	public string Slug { get; private set; }

	/// <summary>
	/// Name of the configuration-source, that is currently controlling the behaviour of the logger
	/// </summary>
	[Required]
	public string LoggerSource
	{
		get => _loggerSource;
		set
		{
			_loggerSource = value;
			Slug = LoggerSource.ToSlug();
		}
	}

	/// <summary>
	/// Is the configuration-source a group?
	/// </summary>
	public bool SourceIsGroup { get; set; }

	/// <summary>
	/// Min-Level the logger is set to
	/// </summary>
	[Required]
	public LogEventLevel Level { get; set; }

	/// <summary>
	/// Should it log to a file?
	/// </summary>
	public bool LogToFile { get; set; }

	/// <summary>
	/// Path to the file it should log to
	/// </summary>
	public string? LogFilePath { get; set; }

	/// <summary>
	/// Is the logger active
	/// </summary>
	public bool IsActive { get; set; }

	/// <summary>
	/// Basic constructor
	/// </summary>
	public LoggerConfigEntity()
	{
		// nothing
	}

	private LoggerConfigEntity(LoggerConfigDto dto)
	{
		if (string.IsNullOrEmpty(dto.LoggerSource))
			throw new ArgumentException($"Property 'LoggerSource' in DataStoreConfig is not valid: {dto.LoggerSource}");
		if (dto.LogToFile == true && string.IsNullOrEmpty(dto.LogFilePath))
			throw new ArgumentException($"Property 'LogFilePath' in DataStoreConfig is not valid: {dto.LogFilePath}");

		_loggerSource = dto.LoggerSource!;
		Slug = dto.LoggerSource!.ToSlug();
		Level = dto.Level ?? LogEventLevel.Warning;
		IsActive = dto.IsActive == true;
		LogToFile = dto.LogToFile == true;
		LogFilePath = dto.LogFilePath;
		SourceIsGroup = false;
	}

	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		base.ConfigureModel(modelBuilder, databaseProvider);
		modelBuilder.Entity<LoggerConfigEntity>().HasIndex(l => l.LoggerSource).IsUnique();
	}

	/// <inheritdoc />
	public LoggerConfigDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new LoggerConfigDto(this, excludedProperties, handledObjects);
	}

	/// <inheritdoc />
	public static LoggerConfigEntity FromDto(LoggerConfigDto entityDto, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new LoggerConfigEntity(entityDto);
	}

	/// <inheritdoc />
	public void UpdatePartial(LoggerConfigDto dto, UserEntity? modifiedBy = null)
	{
		if (!string.IsNullOrEmpty(dto.LoggerSource))
			LoggerSource = dto.LoggerSource;
		if (dto.Level != null)
			Level = dto.Level.Value;
		if (dto.LogToFile != null)
			LogToFile = dto.LogToFile.Value;
		if (!string.IsNullOrEmpty(dto.LogFilePath))
			LogFilePath = dto.LogFilePath;
		if (dto.IsActive != null)
			IsActive = dto.IsActive.Value;
	}
}