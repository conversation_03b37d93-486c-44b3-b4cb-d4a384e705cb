using System.ComponentModel.DataAnnotations;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;

namespace Levelbuild.Entities.Features.Userlane
{
    /// <summary>
    /// Represents a user lane entity in the system.
    /// </summary>
    public class UserlaneResultEntity : PersistentEntity<UserlaneResultEntity>, IAdministrationEntity<UserlaneResultEntity, UserlaneResultDto>
    {
        /// <summary>
        /// Gets or sets the UserlaneId.
        /// </summary>
        [Required]
        public Guid UserlaneId { get; init; }
		
		/// <summary>
		/// Get batch ID
		/// </summary>
		[Required]
		public required Guid BatchId { get; init; }
		
		/// <summary>
		/// Gets or sets the UserlaneStepId.
		/// </summary>
		[Required]
		public Guid UserlaneStepId { get; init; }
		
		/// <summary>
		/// Gets or sets the UserlaneStepActionId.
		/// </summary>
		[Required]
		public Guid UserlaneStepActionId { get; init; }

		/// <summary>
		/// Get or sets the Title.
		/// </summary>
		[Required]
		public required string Title { get; init; }

		/// <summary>
        /// Gets or sets the StartTime.
        /// </summary>
		[Required]
        public required DateTime StartTime { get; init; }
		
		
		/// <summary>
		/// Gets or sets the EndTime.
		/// </summary>
		[Required]
		public required DateTime EndTime { get; init; }
		
		/// <summary>
		/// Gets or sets the Duration.
		/// </summary>
		[Required]
		public required int Duration { get; init; }
		
		/// <summary>
		/// Gets or sets the Complete.
		/// </summary>
		[Required]
		public required bool Complete { get; init; }
		
		/// <summary>
		/// Gets or sets the Found.
		/// </summary>
		[Required]
		public required bool Found { get; init; }
		
		/// <summary>
		/// Gets or sets the Found.
		/// </summary>
		[Required]
		public required string Result { get; init; }

		/// <inheritdoc />
		public UserlaneResultEntity()
		{
		}


		/// <summary>
		/// Converts the entity to a DTO (Data Transfer Object).
		/// </summary>
		/// <param name="excludedProperties">Properties to exclude during conversion.</param>
		/// <param name="handledObjects"></param>
		/// <returns>The corresponding DTO.</returns>
		public UserlaneResultDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
		{
			return new UserlaneResultDto(this, excludedProperties!)
			{
				UserlaneId = UserlaneId,
				UserlaneStepId = UserlaneStepId,
				UserlaneStepActionId = UserlaneStepActionId,
				Result = Result,
				Title = Title
			};
		}
		
		/// <inheritdoc />
		public static UserlaneResultEntity FromDto(UserlaneResultDto entityDto, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
		{
			return new UserlaneResultEntity
			{
				UserlaneId = entityDto.UserlaneId,
				UserlaneStepId = entityDto.UserlaneStepId,
				UserlaneStepActionId = entityDto.UserlaneStepActionId,
				Title = entityDto.Title,
				StartTime = entityDto.StartTime,
				EndTime = entityDto.EndTime,
				Duration = entityDto.Duration,
				Complete = entityDto.Complete,
				Found = entityDto.Found,
				Result = entityDto.Result,
				BatchId = entityDto.BatchId
			};
		}

		/// <inheritdoc />
		public void UpdatePartial(UserlaneResultDto dto, UserEntity? modifiedBy = null)
		{
			throw new NotImplementedException();
		}
	}
}
