using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;

namespace Levelbuild.Entities.Features.Userlane
{
    /// <summary>
    /// Represents a user lane entity in the system.
    /// </summary>
    public class UserlaneResultBatchEntity : PersistentEntity<UserlaneResultBatchEntity>, IAdministrationEntity<UserlaneResultBatchEntity, UserlaneResultBatchDto>
    {

		/// <summary>
        /// Gets or sets the UserlaneId.
        /// </summary>
        [Required]
        public Guid UserlaneId { get; set; }
		
		/// <summary>
		/// Gets or sets the UserlaneStepId.
		/// </summary>
		[Required]
		public Guid? UserId { get; set; }

		/// <summary>
		/// Gets or sets the UserName.
		/// </summary>
		public string? UserName { get; set; }
		
		/// <summary>
		/// Get or Set the Overall status of the test
		/// </summary>
		public string? Status { get; set; }
		
		/// <summary>
		/// Get or Set the Overall runtime of the batch test
		/// </summary>
		public int Runtime { get; set; }
		
		/// <summary>
		/// 
		/// </summary>
		public DateTime StartTime { get; set; }
		
		/// <summary>
		/// 
		/// </summary>
		public DateTime EndTime { get; set; }
		
		/// <summary>
		/// Gets or sets the UserlaneStepActionId.
		/// </summary>
		[Required]
		public DateTime CreatedDateTime { get; set; }

		
		/// <inheritdoc />
		public UserlaneResultBatchEntity()
		{
		}


		/// <summary>
		/// Converts the entity to a DTO (Data Transfer Object).
		/// </summary>
		/// <param name="excludedProperties">Properties to exclude during conversion.</param>
		/// <param name="handledObjects"></param>
		/// <returns>The corresponding DTO.</returns>
		public UserlaneResultBatchDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
		{
			return new UserlaneResultBatchDto(this, excludedProperties)
			{
				UserlaneId = UserlaneId
			};
		}
		/// <inheritdoc />
		public static UserlaneResultBatchEntity FromDto(UserlaneResultBatchDto entityDto, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
		{
			return new UserlaneResultBatchEntity
			{	
				//BatchNo = entityDto.BatchNo,
				UserlaneId = entityDto.UserlaneId,
				UserName = entityDto.UserName,
				UserId = createdBy?.Id,
				CreatedDateTime = entityDto.CreatedDateTime
			};
		}

		/// <inheritdoc />
		public void UpdatePartial(UserlaneResultBatchDto dto, UserEntity? modifiedBy = null)
		{
			throw new NotImplementedException();
		}
		
	}
}
