using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Entities.Features.DataField;

/// <summary>
/// configuration for a text element which is embedded into a GridViewSectionEntity
/// </summary>
public class DataFieldFilterEntity : DataSourceFilterEntity<DataFieldFilterEntity>, IAdministrationEntity<DataFieldFilterEntity, DataFieldFilterDto>
{
	/// <summary>
	/// reference to the DataFieldEntity the filter belongs to
	/// </summary>
	[Required]
	public Guid DataFieldId { get; init; }
	
	/// <summary>
	/// DataFieldEntity the filter belongs to
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(DataFieldId))]
	public DataFieldEntity? DataField { get; init; }
	
	/// <summary>
	/// constructor
	/// </summary>
	public DataFieldFilterEntity()
	{
	}
	
	private DataFieldFilterEntity(DataFieldFilterDto dto, UserEntity? createdBy)
	{
		if (dto.DataFieldId != null)
			DataFieldId = dto.DataFieldId.Value;
		if (dto.FilterFieldId != null)
			FilterFieldId = dto.FilterFieldId.Value;
		if (dto.Operator != null)
			Operator = dto.Operator.Value;
		CompareValue = dto.CompareValue;
		
		Created = DateTime.Now.ToUniversalTime();
		CreatedBy = createdBy?.DisplayName;
	}
	
	/// <inheritdoc />
	public static DataFieldFilterEntity FromDto(DataFieldFilterDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new DataFieldFilterEntity(entityInfo, createdBy);
	}
	
	/// <inheritdoc />
	public new DataFieldFilterDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new DataFieldFilterDto(this, excludedProperties, handledObjects)
		{
			FilterFieldName = FilterField?.Name,
			FilterFieldType = FilterField?.Type
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(DataFieldFilterDto dto, UserEntity? modifiedBy = null)
	{
		base.UpdatePartial(dto, modifiedBy);
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<GridViewTextEntity>().ToTable("DataFieldFilters");
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
}