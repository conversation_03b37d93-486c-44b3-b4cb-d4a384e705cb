using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Features.UserlaneStepTestCondition;
using Levelbuild.Entities.Interfaces;

namespace Levelbuild.Entities.Features.UserlaneStepTestConditionResult;

/// <summary>
/// Entity for storing test condition execution results
/// </summary>
public class UserlaneStepTestConditionResultEntity : PersistentEntity<UserlaneStepTestConditionResultEntity>, IAdministrationEntity<UserlaneStepTestConditionResultEntity, UserlaneStepTestConditionResultDto>
{
    /// <summary>
    /// The batch this test condition result belongs to
    /// </summary>
    public required Guid BatchId { get; set; }
    
    /// <summary>
    /// The test condition that was executed
    /// </summary>
    public required Guid UserlaneStepTestConditionId { get; set; }
    
    /// <summary>
    /// Type of test condition executed
    /// </summary>
    public required string ConditionType { get; set; }
    
    /// <summary>
    /// Field that was tested (if applicable)
    /// </summary>
    public string? Field { get; set; }
    
    /// <summary>
    /// Operator used in the test (if applicable)
    /// </summary>
    public string? Operator { get; set; }
    
    /// <summary>
    /// Expected value for the test
    /// </summary>
    public string? ExpectedValue { get; set; }
    
    /// <summary>
    /// Actual value found during test execution
    /// </summary>
    public string? ActualValue { get; set; }
    
    /// <summary>
    /// Whether the test condition passed
    /// </summary>
    public required bool Passed { get; set; }
    
    /// <summary>
    /// Error message if test condition failed due to an error
    /// </summary>
    public string? Error { get; set; }
    
    /// <summary>
    /// Navigation property to the test condition
    /// </summary>
    public virtual UserlaneStepTestConditionEntity? UserlaneStepTestCondition { get; init; }

    /// <inheritdoc />
    public UserlaneStepTestConditionResultEntity()
    {
    }

    /// <inheritdoc />
    public UserlaneStepTestConditionResultDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
    {
        return new UserlaneStepTestConditionResultDto(this, excludedProperties!)
        {
            BatchId = BatchId,
            UserlaneStepTestConditionId = UserlaneStepTestConditionId,
            ConditionType = ConditionType,
            Field = Field,
            Operator = Operator,
            ExpectedValue = ExpectedValue,
            ActualValue = ActualValue,
            Passed = Passed,
            Error = Error
        };
    }

    /// <inheritdoc />
    public static UserlaneStepTestConditionResultEntity FromDto(UserlaneStepTestConditionResultDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
    {
        return new UserlaneStepTestConditionResultEntity
        {
            BatchId = entityInfo.BatchId,
            UserlaneStepTestConditionId = entityInfo.UserlaneStepTestConditionId,
            ConditionType = entityInfo.ConditionType,
            Field = entityInfo.Field,
            Operator = entityInfo.Operator,
            ExpectedValue = entityInfo.ExpectedValue,
            ActualValue = entityInfo.ActualValue,
            Passed = entityInfo.Passed,
            Error = entityInfo.Error
        };
    }

    /// <inheritdoc />
    public void UpdatePartial(UserlaneStepTestConditionResultDto dto, UserEntity? modifiedBy = null)
    {
        BatchId = dto.BatchId;
        UserlaneStepTestConditionId = dto.UserlaneStepTestConditionId;
        ConditionType = dto.ConditionType;
        Field = dto.Field;
        Operator = dto.Operator;
        ExpectedValue = dto.ExpectedValue;
        ActualValue = dto.ActualValue;
        Passed = dto.Passed;
        Error = dto.Error;
    }
}