using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Features.UserlaneStep;
using Levelbuild.Entities.Interfaces;

namespace Levelbuild.Entities.Features.UserlaneStepAction;

/// <inheritdoc cref="UserlaneStepActionDto" />
public class UserlaneStepActionEntity : PersistentEntity<UserlaneStepActionEntity>, IAdministrationEntity<UserlaneStepActionEntity, UserlaneStepActionDto>
{
		
	/// <summary>
	/// 
	/// </summary>
	public required Guid UserlaneStepId { get; set; }
		
	/// <summary>
	/// 
	/// </summary>
	public int Order { get; set; }
	
	/// <summary>
	/// 
	/// </summary>
	public required UserlaneStepActionType ActionType { get; set; }
	
	/// <summary>
	/// 
	/// </summary>
	public string? Trigger { get; set; }
	
	/// <summary>
	/// 
	/// </summary>
	public required string? Target { get; set; }
	
	/// <summary>
	/// 
	/// </summary>
	public string? TargetValue { get; set; }
	
	/// <summary>
	/// 
	/// </summary>
	public int Delay { get; set; }
		
	/// <summary>
	/// 
	/// </summary>
	public virtual UserlaneStepEntity? UserlaneStep { get; init; }

	/// <inheritdoc />
	public UserlaneStepActionEntity()
	{
	}

	/// <inheritdoc />
	public UserlaneStepActionDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new UserlaneStepActionDto(this, excludedProperties!)
		{
			UserlaneStepId = UserlaneStepId,
			ActionType = ActionType,
		};
	}

	/// <inheritdoc />
	public static UserlaneStepActionEntity FromDto(UserlaneStepActionDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new UserlaneStepActionEntity
		{
			UserlaneStepId = entityInfo.UserlaneStepId,
			Order = entityInfo.Order,
			ActionType = entityInfo.ActionType,
			Trigger = entityInfo.Trigger,
			Target = entityInfo.Target,
			TargetValue = entityInfo.TargetValue,
			Delay = entityInfo.Delay,
		};
	}

	/// <inheritdoc />
	public void UpdatePartial(UserlaneStepActionDto dto, UserEntity? modifiedBy = null)
	{
		UserlaneStepId = dto.UserlaneStepId;
		Order = dto.Order;
		ActionType = dto.ActionType;
		Trigger = dto.Trigger;
		Target = dto.Target;
		TargetValue = dto.TargetValue;
		Delay = dto.Delay;
	}
}