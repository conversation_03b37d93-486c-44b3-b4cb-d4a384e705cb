namespace Levelbuild.Entities.Features.Thumbnail;

/// <summary>
/// Entity reflecting cached thumbnail files
/// </summary>
public class CachedThumbnailEntity : CachedFileEntity<CachedThumbnailEntity>
{
	/// <inheritdoc/>
	public override string GetDirectoryName()
	{
		return "Thumbnails";
	}

	/// <inheritdoc/>
	public CachedThumbnailEntity()
	{
		//empty
	}
	
	/// <inheritdoc/>
	public CachedThumbnailEntity(string cachedFileId, string fileId) : base(fileId, cachedFileId)
	{
	}
}