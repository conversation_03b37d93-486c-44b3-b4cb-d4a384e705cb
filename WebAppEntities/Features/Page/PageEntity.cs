using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Entities.Attributes;
using Levelbuild.Entities.Extensions;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.Page.MultiData;
using Levelbuild.Entities.Features.PageView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Levelbuild.Entities.Features.Page;

/// <summary>
/// base class for a page configuration 
/// </summary>
[Slug(nameof(Name))]
[PrimaryKey(nameof(Id))]
[Index(nameof(Name), IsUnique = true)]
public class PageEntity : RevisedPersistentEntity<PageEntity>, IAdministrationEntity<PageEntity, PageDto>, IPolymorphicParentEntity
{
	/// <summary>
	/// used inside the URL to address the item
	/// </summary>
	[ShortString]
	public string Slug { get; private set; }
	
	private string _name;
	
	/// <summary>
	/// name of the page
	/// </summary>
	[ShortString]
	public string Name
	{
		get => _name;
		set
		{
			_name = value;
			Slug = Name.ToSlug();
		}
	}
	
	/// <summary>
	/// type of the page (currently Create, SinglePage, MultiPage)
	/// </summary>
	public PageType Type { get; init; }
	
	/// <summary>
	/// description for the page
	/// </summary>
	[Text]
	public string? Description { get; set; }
	
	/// <summary>
	/// reference to the DataStoreEntity the DataSourceEntity is based on
	/// </summary>
	public Guid DataStoreId { get; init; }
	
	/// <summary>
	/// reference to the DataSourceEntity the page is based on
	/// </summary>
	public Guid DataSourceId { get; init; }
	
	/// <summary>
	/// DataSourceEntity the page is based on
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(DataSourceId))]
	public DataSourceEntity DataSource { get; init; }

	/// <summary>
	/// when was the page configuration created? 
	/// </summary>
	public DateTime? Created { get; init; }
	
	/// <summary>
	/// who created the page configuration?
	/// </summary>
	[ShortString]
	public string? CreatedBy { get; init; }
	
	/// <summary>
	/// when was the page configuration last modified?
	/// </summary>
	public DateTime? LastModified { get; set; }
	
	/// <summary>
	/// who modified the page configuration?
	/// </summary>
	[ShortString]
	public string? LastModifiedBy { get; set; }
	
	/// <summary>
	/// is this a page configuration targeting our mobile platform?
	/// </summary>
	public bool AppPage { get; init; }
	
	/// <summary>
	/// available views for this page
	/// </summary>
	public ICollection<PageViewEntity> Views { get; } = new List<PageViewEntity>();
	
	/// <summary>
	/// should the page always open with the most recently used view of the current user?
	/// </summary>
	public bool MemorizeDefaultView { get; set; } = true;

	/// <summary>
	/// if there are multiple Views for this page, which should be the one to use during first load of the page?
	/// </summary>
	public Guid? DefaultViewId { get; set; }
	
	/// <summary>
	/// default view entity
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(DefaultViewId))]
	public PageViewEntity? DefaultView { get; set; }
	
	/// <summary>
	/// Localizer with Prefix "Pages"
	/// </summary>
	protected IStringLocalizer? Localizer;
	
	/// <summary>
	/// Localizer with Prefix "PageType"
	/// </summary>
	protected IStringLocalizer? TypeLocalizer;
	
	/// <summary>
	/// Localizer with Prefix "List"
	/// </summary>
	protected IStringLocalizer? ListLocalizer;
	
	/// <inheritdoc />
	public PageEntity()
	{
	}
	
	/// <inheritdoc />
	protected PageEntity(PageDto dto, UserEntity? createdBy, CoreDatabaseContext? databaseContext)
	{
		// try to load dataSource
		var dataSourceInstance = databaseContext?.DataSources.Find(dto.DataSourceId);
		var defaultView = databaseContext?.PageViews.Find(dto.DefaultViewId);

		Name = dto.Name ?? "";
		Type = dto.Type;
		Description = dto.Description;
		DataSource = dataSourceInstance ?? throw new ArgumentException($"Property 'DataSourceId' in PageDto is not valid: {dto.DataSourceId}");
		DataStoreId = dataSourceInstance.DataStoreId;
		Created = DateTime.Now.ToUniversalTime();
		CreatedBy = createdBy?.DisplayName;
		MemorizeDefaultView = dto.MemorizeDefaultView ?? true;
		DefaultView = defaultView;
		AppPage = dto.AppPage == true;

		if (databaseContext != null)
		{
			SetContext(databaseContext);
			Touch();
		}
	}
	
	/// <inheritdoc />
	public static PageEntity FromDto(PageDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new PageEntity(entityInfo, createdBy, context);
	}
	
	/// <inheritdoc />
	public PageDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		excludedProperties ??= [];
		var excludedList = excludedProperties.ToList();
		if (!excludedProperties.Contains(nameof(PageViewEntity.Page)))
		{
			excludedList.Add(nameof(PageViewEntity.Page));
			excludedProperties = excludedList.ToArray();
		}
		
		Localizer ??= StringLocalizerFactory?.Create("Pages", "", true);
		TypeLocalizer ??= StringLocalizerFactory?.Create("PageType", "");
		return new PageDto(this, excludedProperties, handledObjects)
		{
			NameTranslated = Localizer != null ? Localizer[Name] : string.Empty,
			// ReSharper disable once ConditionalAccessQualifierIsNonNullableAccordingToAPIContract
			DataSourceName = DataSource?.Name,
			TypeName = TypeLocalizer != null ? TypeLocalizer[Type.ToString()] : string.Empty,
			LastModifiedText = LastModifiedText(),
			Module = DataSource?.Module?.Name
		};
	}
	
	/// <inheritdoc />
	public EntityDto ToRealDto(CoreDatabaseContext databaseContext, out Type realType, params string[] includes)
	{
		switch (Type)
		{
			case PageType.SingleData:
				var singleDataPageQuery = includes.Aggregate(
						databaseContext.SingleDataPages.AsQueryable(), 
						(current, include) => current.Include(include)
					);
				
				var singleDataPageEntity = singleDataPageQuery
					.AsSplitQuery()
					.First(entity => entity.Id == Id);
				var singleDataPageDto = singleDataPageEntity.ToDto();
				
				singleDataPageDto.Views = new List<PageViewDto>();
				foreach (var view in singleDataPageEntity.Views)
				{
					singleDataPageDto.Views.Add((PageViewDto) view.ToRealDto(databaseContext, out _));
				}
				
				realType = typeof(SingleDataPageDto);
				return singleDataPageDto;
			case PageType.MultiData:
				var multiDataPageQuery = includes.Aggregate(
						databaseContext.MultiDataPages.AsQueryable(), 
						(current, include) => current.Include(include)
					);
				
				var multiDataPageEntity = multiDataPageQuery
					.AsSplitQuery()
					.First(entity => entity.Id == Id);
				var multiDataPageDto = multiDataPageEntity.ToDto([nameof(MultiDataPageEntity.CreatePage), nameof(MultiDataPageEntity.DetailPage)]);
				
				multiDataPageDto.Views = new List<PageViewDto>();
				foreach (var view in multiDataPageEntity.Views)
				{
					multiDataPageDto.Views.Add((PageViewDto) view.ToRealDto(databaseContext, out _));
				}
				
				multiDataPageDto.DefaultSorting = new List<DataFieldSortingDto>();
				foreach (var sorting in multiDataPageEntity.DefaultSorting)
				{
					multiDataPageDto.DefaultSorting.Add(sorting.ToDto());
				}
				
				realType = typeof(MultiDataPageDto);
				return multiDataPageDto;
			case PageType.Create:
				var createPageQuery = includes.Aggregate(
					databaseContext.CreatePages.AsQueryable(),
					(current, include) => current.Include(include)
				);
				
				var createPageEntity = createPageQuery
					.AsSplitQuery()
					.First(entity => entity.Id == Id);
				var createPageDto = createPageEntity.ToDto();
				
				createPageDto.Views = new List<PageViewDto>();
				foreach (var view in createPageEntity.Views)
				{
					createPageDto.Views.Add((PageViewDto) view.ToRealDto(databaseContext, out _));
				}
				
				realType = typeof(CreatePageDto);
				return createPageDto;
		}
		
		throw new NotSupportedException($"Type \"{Type.ToString()}\" is not supported!");
	}
	
	/// <inheritdoc />
	public void UpdatePartial(PageDto dto, UserEntity? modifiedBy = null)
	{
		if (dto.Name != null)
			Name = dto.Name;
		if (dto.Description != null)
			Description = dto.Description;
		if (dto.MemorizeDefaultView != null)
			MemorizeDefaultView = dto.MemorizeDefaultView.Value;
		if (dto.DefaultViewId != null)
			DefaultViewId = dto.DefaultViewId == Guid.Empty ? null : dto.DefaultViewId;
		
		LastModified = DateTime.Now;
		LastModifiedBy = modifiedBy?.DisplayName;
		
		Touch();
	}
	
	/// <summary>
	/// changes the revision of the page
	/// </summary>
	/// <param name="touchViews">Should all views of the page be touched as well?</param>
	public void Touch(bool touchViews = false)
	{
		base.Touch();
		
		// touch all views of this page
		if (touchViews)
			DbContext?.PageViews.Where(view => view.PageId == Id).ToList().ForEach(view => view.Touch());
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<PageEntity>()
			.HasOne(page => page.DefaultView);
		
		modelBuilder.Entity<PageEntity>()
			.HasOne(page => page.DataSource);
		
		modelBuilder.Entity<PageEntity>()
			.HasMany(page => page.Views)
			.WithOne(view => view.Page);
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
	
	private string LastModifiedText()
	{
		if (LastModified == null || LastModifiedBy == null)
			return "-";

		ListLocalizer ??= StringLocalizerFactory?.Create("Page", "List");
		return ListLocalizer != null ? ListLocalizer["lastModifiedValue", LastModified.Value.ToString("g"), LastModifiedBy] : "-";
	}
}