using System.ComponentModel.DataAnnotations;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Entities.Attributes;
using Levelbuild.Entities.Extensions;
using Levelbuild.Entities.Features.Localization;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Features.UserCustomerMapping;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;

// ReSharper disable CollectionNeverUpdated.Global
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Levelbuild.Entities.Features.Customer;

/// <summary>
/// configuration for a customer/organisation
/// </summary>
[Slug(nameof(DisplayName))]
public sealed class CustomerEntity : PersistentEntity<CustomerEntity>, IAdministrationEntity<CustomerEntity, CustomerDto>
{
	/// <summary>
	/// Human readable slug.
	/// </summary>
	public string Slug { get; private set; }
	
	/// <summary>
	/// Customer Id within Zitadel.
	/// </summary>
	[Required]
	public string RemoteId { get; init; }
	
	private string _displayName;

	/// <summary>
	/// Name of the customer/organisation.
	/// </summary>
	[Required]
	public string DisplayName
	{
		get => _displayName;
		set
		{
			_displayName = value;
			Slug = _displayName.ToSlug();
		}
	}
	
	/// <summary>
	/// A disabled customer still exists but is not usable.
	/// </summary>
	public bool Enabled { get; set; } = true;

	/// <summary>
	/// Contains all user associated with this customer.
	/// </summary>
	public ICollection<UserEntity> Users { get; set; } = new List<UserEntity>();
	
	/// <summary>
	/// Contains information about the relation and rights a user has within the customer.
	/// </summary>
	public ICollection<UserCustomerMappingEntity> UserMapping { get; set; } = new List<UserCustomerMappingEntity>();
	
	/// <summary>
	/// all translations associated to this customer
	/// </summary>
	public ICollection<TranslationEntity> Translations { get; } = new List<TranslationEntity>();
	
	/// <inheritdoc />
	public CustomerEntity()
	{
		// nothing
	}
	
	/// <inheritdoc />
	public CustomerEntity(CustomerDto dto, string remoteId)
	{
		RemoteId = remoteId;
		DisplayName = dto.DisplayName;
		Slug = dto.DisplayName.ToSlug();
		Enabled = dto.Enabled ?? true;
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<CustomerEntity>()
			.HasMany(customer => customer.Users)
			.WithMany(user => user.Customers);
		
		modelBuilder.Entity<CustomerEntity>()
			.HasIndex(customer => customer.DisplayName)
			.IsUnique();
		
		modelBuilder.Entity<CustomerEntity>()
			.HasIndex(customer => customer.RemoteId)
			.IsUnique();
		
		modelBuilder.Entity<CustomerEntity>()
			.HasMany(customer => customer.UserMapping)
			.WithOne(mapping => mapping.Customer);

		base.ConfigureModel(modelBuilder, databaseProvider);
	}
	
	/// <inheritdoc />
	public static CustomerEntity FromDto(CustomerDto entityDto, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new CustomerEntity(entityDto, string.Empty);
	}
	
	/// <inheritdoc />
	public CustomerDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new CustomerDto(this, excludedProperties, handledObjects);
	}
	
	/// <inheritdoc />
	public void UpdatePartial(CustomerDto dto, UserEntity? modifiedBy = null)
	{
		if (!string.IsNullOrEmpty(dto.DisplayName))
			DisplayName = dto.DisplayName;
	}
}