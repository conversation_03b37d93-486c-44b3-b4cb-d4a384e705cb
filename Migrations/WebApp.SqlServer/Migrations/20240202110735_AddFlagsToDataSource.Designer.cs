// <auto-generated />
using System;
using Levelbuild.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Levelbuild.Migrations.SqlServer.Migrations
{
    [DbContext(typeof(CoreDatabaseContext))]
    [Migration("20240202110735_AddFlagsToDataSource")]
    partial class AddFlagsToDataSource
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("CustomerEntityUserEntity", b =>
                {
                    b.Property<Guid>("CustomersId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UsersId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("CustomersId", "UsersId");

                    b.HasIndex("UsersId");

                    b.ToTable("CustomerEntityUserEntity");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Customer.CustomerEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("DataSourceId")
                        .IsRequired()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("DecimalPlaces")
                        .HasColumnType("int");

                    b.Property<int>("Length")
                        .HasColumnType("int");

                    b.Property<bool>("Mandatory")
                        .HasColumnType("bit");

                    b.Property<bool>("Multi")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Nullable")
                        .HasColumnType("bit");

                    b.Property<bool>("Reference")
                        .HasColumnType("bit");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SystemField")
                        .HasColumnType("bit");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<bool>("Unique")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("DataSourceId");

                    b.ToTable("DataFields");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSourceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("DataStoreId")
                        .IsRequired()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Encryption")
                        .HasColumnType("bit");

                    b.Property<bool>("FulltextSearch")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Responsible")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StoragePath")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("StoreFieldContent")
                        .HasColumnType("bit");

                    b.Property<bool>("StoreFileContent")
                        .HasColumnType("bit");

                    b.Property<bool>("StoreRevision")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("DataStoreId");

                    b.ToTable("DataSources");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Enabled")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Options")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("DataStoreConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.CultureEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Cultures");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.TranslationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CultureId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Responsible")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SystemTranslation")
                        .HasColumnType("bit");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CultureId", "Key")
                        .IsUnique();

                    b.ToTable("Translations");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.LoggerConfig.LoggerConfigEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<string>("LogFilePath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("LogToFile")
                        .HasColumnType("bit");

                    b.Property<string>("LoggerSource")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SourceIsGroup")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("LoggerSource")
                        .IsUnique();

                    b.ToTable("LoggerConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CultureId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("MainCustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CultureId");

                    b.HasIndex("MainCustomerId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CustomerEntityUserEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", null)
                        .WithMany()
                        .HasForeignKey("CustomersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", null)
                        .WithMany()
                        .HasForeignKey("UsersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "DataSource")
                        .WithMany()
                        .HasForeignKey("DataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataSource");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSourceEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", "DataStore")
                        .WithMany()
                        .HasForeignKey("DataStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataStore");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.TranslationEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Localization.CultureEntity", "Culture")
                        .WithMany("Translations")
                        .HasForeignKey("CultureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Culture");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Localization.CultureEntity", "Culture")
                        .WithMany("Users")
                        .HasForeignKey("CultureId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "MainCustomer")
                        .WithMany()
                        .HasForeignKey("MainCustomerId");

                    b.Navigation("Culture");

                    b.Navigation("MainCustomer");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.CultureEntity", b =>
                {
                    b.Navigation("Translations");

                    b.Navigation("Users");
                });
#pragma warning restore 612, 618
        }
    }
}
