using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class AddStepConditionResultsDto : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserlaneFeedItems");

            migrationBuilder.CreateTable(
                name: "UserlaneStepTestConditionResults",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    BatchId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserlaneStepTestConditionId = table.Column<Guid>(type: "uuid", nullable: false),
                    ConditionType = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Field = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Operator = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ExpectedValue = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ActualValue = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Passed = table.Column<bool>(type: "boolean", nullable: false),
                    Error = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserlaneStepTestConditionResults", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserlaneStepTestConditionResults_UserlaneStepTestConditions~",
                        column: x => x.UserlaneStepTestConditionId,
                        principalTable: "UserlaneStepTestConditions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserlaneStepTestConditionResults_UserlaneStepTestConditionId",
                table: "UserlaneStepTestConditionResults",
                column: "UserlaneStepTestConditionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserlaneStepTestConditionResults");

            migrationBuilder.CreateTable(
                name: "UserlaneFeedItems",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserlaneId = table.Column<Guid>(type: "uuid", nullable: false),
                    ActivityTimestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ActivityType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Metadata = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserlaneFeedItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserlaneFeedItems_Userlanes_UserlaneId",
                        column: x => x.UserlaneId,
                        principalTable: "Userlanes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserlaneFeedItems_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserlaneFeedItems_UserId",
                table: "UserlaneFeedItems",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserlaneFeedItems_UserlaneId",
                table: "UserlaneFeedItems",
                column: "UserlaneId");
        }
    }
}
