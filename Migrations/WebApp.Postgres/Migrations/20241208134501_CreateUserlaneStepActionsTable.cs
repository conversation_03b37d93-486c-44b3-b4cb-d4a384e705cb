using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class CreateUserlaneStepActionsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
			migrationBuilder.CreateTable(
				name: "UserlaneStepActions",
				columns: table => new
				{
					Id = table.Column<Guid>(type: "uuid", nullable: false),
					UserlaneStepsId = table.Column<Guid>(type: "uuid", nullable: false),
					ActionType = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
					Trigger = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
					Target = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
					TargetValue = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
					Order = table.Column<int>(type: "integer", nullable: false),
					Delay = table.Column<int>(type: "integer", nullable: false),
				},
				constraints: table =>
				{
					table.PrimaryKey("PK_UserlaneStepActions", x => x.Id);
					table.ForeignKey("FK_UserlaneSteps", x => x.UserlaneStepsId, "UserlaneSteps", "Id");
				});

        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
			migrationBuilder.DropTable(
				name: "UserlaneStepActions");
        }
    }
}
