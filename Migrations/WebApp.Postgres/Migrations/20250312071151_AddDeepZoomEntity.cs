using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class AddDeepZoomEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "GenerateDeepZoomImage",
                table: "DataSources",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "DeepZoomImages",
                columns: table => new
                {
                    DeepZoomImageId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FileId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Overlap = table.Column<int>(type: "integer", nullable: false),
                    TileSize = table.Column<int>(type: "integer", nullable: false),
                    Width = table.Column<int>(type: "integer", nullable: false),
                    Height = table.Column<int>(type: "integer", nullable: false),
                    FolderFileCount = table.Column<int[]>(type: "integer[]", nullable: false),
                    LastTouched = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DeepZoomImages", x => x.DeepZoomImageId);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DeepZoomImages");
			
            migrationBuilder.DropColumn(
                name: "GenerateDeepZoomImage",
                table: "DataSources");
        }
    }
}
