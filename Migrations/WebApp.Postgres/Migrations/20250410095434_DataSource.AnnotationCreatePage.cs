using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class DataStoreAnnotationCreatePage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "AnnotationCreatePageId",
                table: "DataSources",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "AnnotationDetailPageId",
                table: "DataSources",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DataSources_AnnotationCreatePageId",
                table: "DataSources",
                column: "AnnotationCreatePageId");

            migrationBuilder.CreateIndex(
                name: "IX_DataSources_AnnotationDetailPageId",
                table: "DataSources",
                column: "AnnotationDetailPageId");

            migrationBuilder.AddForeignKey(
                name: "FK_DataSources_CreatePage_AnnotationCreatePageId",
                table: "DataSources",
                column: "AnnotationCreatePageId",
                principalTable: "CreatePage",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_DataSources_SingleDataPage_AnnotationDetailPageId",
                table: "DataSources",
                column: "AnnotationDetailPageId",
                principalTable: "SingleDataPage",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DataSources_CreatePage_AnnotationCreatePageId",
                table: "DataSources");

            migrationBuilder.DropForeignKey(
                name: "FK_DataSources_SingleDataPage_AnnotationDetailPageId",
                table: "DataSources");

            migrationBuilder.DropIndex(
                name: "IX_DataSources_AnnotationCreatePageId",
                table: "DataSources");

            migrationBuilder.DropIndex(
                name: "IX_DataSources_AnnotationDetailPageId",
                table: "DataSources");

            migrationBuilder.DropColumn(
                name: "AnnotationCreatePageId",
                table: "DataSources");

            migrationBuilder.DropColumn(
                name: "AnnotationDetailPageId",
                table: "DataSources");
        }
    }
}
