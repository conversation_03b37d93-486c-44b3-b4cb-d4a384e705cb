using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class DataFieldColumns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "ColumnView",
                table: "DataFields",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "DataFieldColumns",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DataFieldId = table.Column<Guid>(type: "uuid", nullable: false),
                    Position = table.Column<int>(type: "integer", nullable: false),
                    DisplayFieldId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataFieldColumns", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DataFieldColumns_DataFields_DataFieldId",
                        column: x => x.DataFieldId,
                        principalTable: "DataFields",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DataFieldColumns_DataFields_DisplayFieldId",
                        column: x => x.DisplayFieldId,
                        principalTable: "DataFields",
                        principalColumn: "Id",
						onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DataFieldColumns_DataFieldId",
                table: "DataFieldColumns",
                column: "DataFieldId");

            migrationBuilder.CreateIndex(
                name: "IX_DataFieldColumns_DisplayFieldId",
                table: "DataFieldColumns",
                column: "DisplayFieldId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DataFieldColumns");

            migrationBuilder.DropColumn(
                name: "ColumnView",
                table: "DataFields");
        }
    }
}
