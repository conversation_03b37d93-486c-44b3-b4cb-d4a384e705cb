using Newtonsoft.Json;

namespace Levelbuild.Domain.GoogleGeoData.Dto;

/// <summary>
/// dto reflecting one result of a Google geo data response
/// </summary>
public class GoogleGeoResult
{
	/// <summary>
	/// address components
	/// </summary>
	[JsonProperty("address_components")]
	public List<GoogleAddressComponent> AddressComponents = [];

	/// <summary>
	/// geometry information
	/// </summary>
	public GoogleGeometry? Geometry;
	
	/// <summary>
	/// formatted address string
	/// </summary>
	[JsonProperty("formatted_address")]
	public string FormattedAddress = "";

	/// <summary>
	/// unique place id
	/// </summary>
	[JsonProperty("place_id")]
	public string PlaceId = "";

	/// <summary>
	/// place types
	/// </summary>
	public List<string> Types = [];
}