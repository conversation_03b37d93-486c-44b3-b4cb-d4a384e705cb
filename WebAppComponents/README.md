# Readme

This is an ASP.NET dummy project, which provides all Web Components in the subfolder **ClientApp**.
The Web Components are based on **LitElement** (https://lit.dev/docs). The project has the following structure:

```ts
- `ClientApp`
	- `.storybook`    /* Root folder for storybook */
  	- `cypress`       /* Root folder for test framework: cypress */
  	- `src`
		- `components`  /* Main folder for all components */
- `assets`      /* Storage folder for static stuff, such as images, svg's, etc. */
- 'enums'		/* Useful enum types */
- 'shared'		/* utility files which can be used in all components */
		- `index.ts`    /* Entry point for the building process */
		- `index.css`   /* Main stylesheet */
```

## How to install

1. (Prerequisites) Please install the latest version of Node.js locally on your machine
2. Navigate to the root folder: `ClientApp` of WebAppComponents and open a terminal instance. (Rider: `Right Click on ClientApp -> Open In -> Terminal`)
3. Execute the following command: `npm i`, which is used to install the typescript project and all of its dependencies.

## Lit Web Components

All components are stored in the subdirectory `./src/components/*`. Please make sure that they are logically grouped by
functionality. A possible example structure would be:

```ts
- `components`
	- `collection-controls`
		- `table-control`
			- `table-control.ts` 			/* Web component */
			- `table-control.cy.ts` 		/* Test file for component */
			- `table-control.stories.ts` 	/* Story file for component */
			- `index.ts`					/* Export file for component */
	- `button`
		- `button.ts`
		- `button.cy.ts`
		- `button.stories.ts`
		- `index.ts`
	- `index.ts`							/* Export file components folder exporting the 
									button and the table-control folder */
```

**Attention:** If you write a new component then create an index.ts in your folder and export your component. Important for external usage. 

## Storybook

**Storybook** is a frontend workshop for building UI components and pages in isolation.
(https://storybook.js.org/docs/web-components/get-started/)

We use it to visualize and document our web components in all possible ways.
The Storybook folder consists of the following files and folders:

```ts
- `support` 			/* Additional files for shared functions across stories */
- `main.ts` 			/* Main configuration file */
- `preview.ts`			/* Configuration for the storybook UI - controls, layout, etc. */
- `preview-head.html`	/* Html file that serves as the basis for the rendering process. Used to integrate FontAwesome, for example. */
```

A component storybook file must be placed in the same folder as its component.

### How to use

In the component directory `src/components/example/ExampleButton.stories.ts` is an example implementation for a
component story. This should serve as a template for future stories.

The NPM scripts are included in package.json.

- `npm run storybook` starts the web interface in dev mode. Changes made to components and stories are applied
  on-the-fly.
- `npm run build-storybook` builds the storybook as a static webpage. (is rarely used)

## Cypress

With **Cypress**, you can easily create tests for your modern web applications, debug them visually,
and automatically run them in your continuous integration
builds. (https://docs.cypress.io/guides/component-testing/overview)

We use it to test our web components with the help of our storybook UI stories as configuration base.
The cypress folder consists of the following files and folders:

```ts
- `e2e`                         /* Root folder for all end-to-end tests (not used for our 'level-components' project) */
- `fixtures`                    /* Folder which contains http response mocks to test component interactions with the backend */
- `support`
	- `advanced-functions.ts`   /* Contains helper functions */
	- `commands.ts`             /* Used to extends cypress commands via: 'cy.' */
	- `component.ts`            /* Used to extends cypress commands for component tests only */
	- `component-index.html`    /* Html file that serves as the basis for the rendering process. Used to integrate FontAwesome, for example. */
	- `e2e.ts`                  /* Used to extends cypress commands for end-to-end tests only */
- `...`                         /* The rest like results, screenshots and videos are for debugging */
```

A component test file must be placed in the same folder as its component.

### How to use

In the component directory `src/components/example/ExampleButton.cy.ts` is an example implementation for a
component test file with several test cases.

The NPM scripts are included in package.json.

- `npm run test-dev` Runs the web interface in dev mode. Changes made to components and stories are applied on-the-fly.
- `npm run test:[browser]` Runs all cypress tests in the console for a specific browser.

### Limitations

Currently, there seems to be no way to test :focus based behaviour. This includes :focus based css styling as well as 
selecting the focused element inside javascript while running cypress tests. This makes testing things like key
navigation (for example focusing one row after another while pressing keydown) currently not possible. 

## Tips and Tricks

### Cypress

- Write story test: `cy.storyTest(DESCRIPTION, LEVEL_STORY_OBJECT, CALLBACK)`
- Mount story: `cy.mountStory(LEVEL_STORY_OBJECT)`
- Spy component methods: `cy.spy(ExampleButton.prototype, 'METHOD_NAME').as('spy')`
- Init log spy: `cy.spyConsole(LOG_LEVEL)`
- Get log calls: `cy.getLog(CALLBACK_WITH_CALLS_ARRAY)`
- Override setTimeout with `cy.stub` maximize test execution performance
- `cy.clear()` command could fail: use `cy.focus()`
- Use `.invoke('removeAttr',...)` for boolean attributes (set to false does not work)
- Compare component properties can be done using: `.should('have.prop', PROPERTY_NAME, COMPARE_VALUE)`
- Sometimes web components have another default `display` property in cypress
  - define it in `:host` explicitly
- Use `.invoke('get', 0)` to execute methods on HtmlElement instead of using `.then(() => {})`: 
	```ts
	// e.g. use this to check boundaries of your element
	cy.get('foo').invoke('get', 0).invoke('getBoundingClientRect').its('width').should('...')
	```
  - Why? You can work without nesting and additional `cy.wait()` calls which improves the execution performance
- Use `.invoke('get', 0).invoke('checkVisibility').should('be.true')` to check the visibility of an element instead of the default cypress should statement if your element is placed inside a dialog or popup

### Typescript

- useful type operations [Click here](https://www.typescriptlang.org/docs/handbook/utility-types.html)
- best way to create an iterable array with n entries: `Array.from({ length: LENGTH }[, (_, index) => {...}])`

### CSS

Use `src/shared/component-styles.ts` to import css into your component.

Classes:

- **clickable** - cursor-pointer on elements without **disabled**
- **disabled** - element gets an opacity and is not clickable
- **hide** - hides an element

### HTML

Use `src/shared/component-html.ts` to import html literals into your component.
This contains:

- Icon rendering


