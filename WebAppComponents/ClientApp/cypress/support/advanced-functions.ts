import { LevelStory } from '@story-home/support/commands'
import { CountResponseData, QueryResponseData, SelectParameters } from '@/shared/types.ts'
import { HttpResponse } from 'msw'
import { mockServerSuccessResponse } from '@story-home/support/mock-service.ts'
import { queryData } from '@/shared/data-operations.ts'

export const storyTest = (title: string, levelStory: LevelStory<any>, fnCallback: Mocha.Func) => {
	it(`mounts >${levelStory.storyName}< and ${title}`, fnCallback)
}

export const storyTestOnly = (title: string, levelStory: LevelStory<any>, fnCallback: Mocha.Func) => {
	it.only(`mounts >${levelStory.storyName}< and ${title}`, fnCallback)
}

export function getExpectedColor(expectedLight: string, expectedDark?: string) {
	return expectedDark && window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? expectedDark : expectedLight
}

export function getLocalTimeHR(date: Date, browserLocale: string = 'en') {
	return date.toLocaleTimeString(browserLocale, { hour: '2-digit', minute: '2-digit' })
}

export async function getSuccessListResolver(request: any, jsonData: Array<Record<string, any>>, ignoreSearchParams: boolean = false): Promise<HttpResponse> {
	const searchParameters = new URL(request.url).searchParams
	const mockResponse = await getMockedSuccessResponse(jsonData, ignoreSearchParams ? null : searchParameters)
	return HttpResponse.json(mockResponse)
}

export async function getMockedSuccessResponse(jsonData: Array<Record<string, any>>, searchParameters: URLSearchParams | null, timeout: number = 500) {
	const selectParameters: SelectParameters = searchParameters === null ? {} : {
		limit: searchParameters.has('limit') ? parseInt(searchParameters.get('limit')!) : undefined,
		offset: searchParameters.has('offset') ? parseInt(searchParameters.get('offset')!) : undefined,
		groupBy: searchParameters.has('groupBy') ? JSON.parse(searchParameters.get('groupBy')!) : undefined,
		fields: searchParameters.has('fields') ? JSON.parse(searchParameters.get('fields')!) : undefined,
		filters: searchParameters.has('filters') ? JSON.parse(searchParameters.get('filters')!) : undefined,
		sortings: searchParameters.has('sortings') ? JSON.parse(searchParameters.get('sortings')!) : undefined,
	}

	return mockServerSuccessResponse<QueryResponseData>(queryData(jsonData, selectParameters), timeout)
}

export async function getSuccessCountResolver(request: any, jsonData: Array<Record<string, any>>, ignoreSearchParams: boolean = false): Promise<HttpResponse> {
	const searchParameters = new URL(request.url).searchParams
	const mockResponse = await getMockedSuccessCountResponse(jsonData, ignoreSearchParams ? null : searchParameters)
	return HttpResponse.json(mockResponse)
}

export async function getMockedSuccessCountResponse(jsonData: Array<Record<string, any>>, searchParameters: URLSearchParams | null, timeout: number = 500) {
	const selectParameters: SelectParameters = searchParameters === null ? {} : {
		limit: searchParameters.has('limit') ? parseInt(searchParameters.get('limit')!) : undefined,
		offset: searchParameters.has('offset') ? parseInt(searchParameters.get('offset')!) : undefined,
		groupBy: searchParameters.has('groupBy') ? JSON.parse(searchParameters.get('groupBy')!) : undefined,
		fields: searchParameters.has('fields') ? JSON.parse(searchParameters.get('fields')!) : undefined,
		filters: searchParameters.has('filters') ? JSON.parse(searchParameters.get('filters')!) : undefined,
		sortings: searchParameters.has('sortings') ? JSON.parse(searchParameters.get('sortings')!) : undefined,
	}

	return mockServerSuccessResponse<CountResponseData>({ count: queryData(jsonData, selectParameters).countTotal }, timeout)
}