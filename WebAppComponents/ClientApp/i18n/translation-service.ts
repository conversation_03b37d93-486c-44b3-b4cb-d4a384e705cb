// imports
import { glob } from 'glob'
import fs from 'fs'

const GLOB_PATTERN = '**/*.i18n.json' as const

// Get files that match the glob pattern
function getFilesForGlob(sourcePath: string) {
	const normalizedSourcePath = sourcePath.endsWith('/') ? sourcePath : sourcePath + '/'
	return glob(normalizedSourcePath + GLOB_PATTERN, {
		ignore: {
			ignored: p => p.isNamed('Translations.i18n.json'),
			childrenIgnored: p => p.isNamed('bin'),
		},
	})
}

function passJsonFileToObject(filePath: string, jsonObject: Record<string, any>) {
	if (!filePath)
		return
	
	let parsedJson = JSON.parse(fs.readFileSync(filePath, 'utf8').trim())

	// get Filename from Path
	const matchedFilename = filePath.match(/([^\/\\]+)\.i18n\.json/)
	let name = matchedFilename != null && matchedFilename.length > 1 ? matchedFilename[1] : ''

	// if name ends with "Controller", strip the Controller part
	if (name.endsWith('Controller'))
		name = name.slice(0, -10)

	// get Name? object has no matches?!
	jsonObject[name] = parsedJson
}

/**
 * search for all files with the right pattern and parse their content into an json object
 * @param rootFolder Where to start with searching for translation files
 */
export async function getJsonForGlob(rootFolder: string): Promise<Record<string, any>> {
	let json = {}
	const foundTranslationFiles = await getFilesForGlob(rootFolder)
	foundTranslationFiles.sort()
	foundTranslationFiles.forEach(file => {
		passJsonFileToObject(file, json)
	})
	return json
}

export function buildComponentsFile() {
	console.log('Updating translations...')
	getJsonForGlob('src/*(components|enums|shared)/').then(translations => {
		let jsonString = JSON.stringify(translations, null, 2)
		fs.mkdirSync('./public/i18n', { recursive: true })
		fs.writeFileSync('./public/i18n/Components.i18n.json', jsonString)
	})
}