(window.webpackJsonp=window.webpackJsonp||[]).push([[29],{1477:function(t,e,n){"use strict";var o=n(0),r=n.n(o).a.createContext();e.a=r},1495:function(t,e,n){"use strict";n(23),n(8),n(24),n(344),n(436),n(438);e.a=function(t,e){var n={},o=t.getContents().ops,r=0;o.forEach((function(t){var e,o=t.attributes,i=null===(e=t.insert)||void 0===e?void 0:e.mention,a=t.insert;if(i){var l=t.insert.mention;a=l.denotationChar+l.value}var c={};(null==o?void 0:o.bold)&&(c["font-weight"]="bold"),(null==o?void 0:o.italic)&&(c["font-style"]="italic"),(null==o?void 0:o.color)&&(c.color=o.color),(null==o?void 0:o.underline)&&(c["text-decoration"]="word"),(null==o?void 0:o.strike)&&(c["text-decoration"]?c["text-decoration"]="".concat(c["text-decoration"]," line-through"):c["text-decoration"]="line-through"),(null==o?void 0:o.size)&&(c["font-size"]=o.size),(null==o?void 0:o.font)&&(c["font-family"]=o.font),n[r]=c,r+=a.length})),e.setRichTextStyle(n)}},1499:function(t,e,n){"use strict";n(18),n(107),n(153),n(88),n(19),n(12),n(13),n(8),n(14),n(10),n(9),n(11),n(16),n(15),n(20),n(26),n(27),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46);var o=n(0),r=n.n(o),i=n(4),a=n.n(i),l=n(6),c=n(123),u=n.n(c),s=n(1477),p=n(3),d=(n(36),n(56),n(64),n(65),n(66),n(67),n(37),n(39),n(40),n(63),n(1524)),f=n.n(d),h=(n(1592),n(340)),m=n(48),b=n(429),y=n(5),g=n(21);n(83),n(41);var v=function(t){if(!t)return t;var e=t.split("\n");return e.length>1?e.map((function(t){return"<p>".concat(t||"<br>","</p>")})).join(""):t};n(44),n(78),n(82);function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function x(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function q(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?x(Object(n),!0).forEach((function(e){k(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):x(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function k(t,e,n){return(e=j(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function E(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,j(o.key),o)}}function O(t,e,n){return e&&E(t.prototype,e),n&&E(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function j(t){var e=function(t,e){if("object"!==w(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==w(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===w(e)?e:String(e)}function S(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function N(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&A(t,e)}function A(t,e){return(A=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function C(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,o=L(t);if(e){var r=L(this).constructor;n=Reflect.construct(o,arguments,r)}else n=o.apply(this,arguments);return P(this,n)}}function P(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function L(t){return(L=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var I=d.Quill.import("modules/clipboard"),T=window.Core.quillShadowDOMWorkaround,_=function(t){N(n,t);var e=C(n);function n(t,o){return S(this,n),T(t),e.call(this,t,o)}return O(n)}(I),R=d.Quill.import("modules/keyboard"),M=function(t){N(n,t);var e=C(n);function n(){return S(this,n),e.apply(this,arguments)}return O(n)}(R);k(M,"DEFAULTS",q(q({},R.DEFAULTS),{},{bindings:q(q({},R.DEFAULTS.bindings),{},{"list autofill":void 0})}));var D=function(){function t(e){var n,o=this;S(this,t),k(this,"handleKeyDown",(function(t){"Escape"===t.key?o.blurQuill():"Tab"===t.key&&o.shouldSkipInput&&(t.preventDefault(),o.moveFocus(t.shiftKey))})),this.quill=e,this.noteContainer=e.root.closest(".Note"),this.shouldSkipInput=!1,null===(n=this.noteContainer)||void 0===n||n.addEventListener("keydown",this.handleKeyDown.bind(this))}return O(t,[{key:"blurQuill",value:function(){this.shouldSkipInput=!0,this.quill.blur(),this.quill.container.tabIndex=0,this.quill.container.focus()}},{key:"moveFocus",value:function(t){var e,n=Array.from(this.noteContainer.querySelectorAll(".ql-container.ql-snow, button.modular-ui")),o=n.indexOf(this.quill.container);-1!==o&&(null===(e=n[t?o-1:o+1])||void 0===e||e.focus());this.shouldSkipInput=!1,this.quill.container.tabIndex=-1}}]),t}();n(1557),n(1559);function F(t){return(F="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function z(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function H(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?z(Object(n),!0).forEach((function(e){G(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):z(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function G(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==F(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==F(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===F(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function B(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return U(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return U(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function U(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function W(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */W=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(t,n,l)}),a}function s(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var p={};function d(){}function f(){}function h(){}var m={};c(m,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(j([])));y&&y!==e&&n.call(y,i)&&(m=y);var g=h.prototype=d.prototype=Object.create(m);function v(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){var r;o(this,"_invoke",{value:function(o,i){function a(){return new e((function(r,a){!function o(r,i,a,l){var c=s(t[r],t,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==F(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){o("next",t,a,l)}),(function(t){o("throw",t,a,l)})):e.resolve(p).then((function(t){u.value=t,a(u)}),(function(t){return o("throw",t,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(t,e,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return S()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=q(a,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=s(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function q(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,q(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var r=s(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function j(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:S}}function S(){return{value:void 0,done:!0}}return f.prototype=h,o(g,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:f,configurable:!0}),f.displayName=c(h,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,c(t,l,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},v(w.prototype),c(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,o,r,i){void 0===i&&(i=Promise);var a=new w(u(e,n,o,r),i);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},v(g),c(g,l,"Generator"),c(g,i,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=j,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return a.type="throw",a.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;E(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:j(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}function V(t,e,n,o,r,i,a){try{var l=t[i](a),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(o,r)}var Y=[],K=["background","bold","color","font","code","italic","link","size","strike","script","underline","blockquote","header","indent","list","align","direction","code-block","formula","mention"];d.Quill.register("modules/keyboard",M,!0),d.Quill.register("modules/clipboard",_,!0),d.Quill.register("modules/blurInput",D);var $={mention:{allowedChars:/^[A-Za-z\sÅÄÖåäö0-9\-_]*$/,mentionDenotationChars:["@","#"],mentionContainerClass:"mention__element",mentionListClass:"mention__suggestions__list",listItemClass:"mention__suggestions__item",renderItem:function(t){var e=document.createElement("div");if(e.innerText=t.value,t.email){var n=document.createElement("p");n.innerText=t.email,n.className="email",e.appendChild(n)}return e},source:function(t,e){return(n=W().mark((function n(){var o,r;return W().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return o=h.a.getMentionLookupCallback(),n.next=3,o(Y,t);case 3:r=n.sent,e(r,t);case 5:case"end":return n.stop()}}),n)})),function(){var t=this,e=arguments;return new Promise((function(o,r){var i=n.apply(t,e);function a(t){V(i,o,r,a,l,"next",t)}function l(t){V(i,o,r,a,l,"throw",t)}a(void 0)}))})();var n}}},Q=r.a.forwardRef((function(t,e){var n=t.value,o=void 0===n?"":n,i=t.onChange,a=t.onKeyDown,c=t.onBlur,u=t.onFocus,s=t.userData,d=t.isReply,h=B(Object(b.a)(),1)[0],w=Object(l.e)((function(t){return p.a.isElementDisabled(t,y.a.NotesPanel.ADD_REPLY_ATTACHMENT_BUTTON)}));Y=s;o=v(o);var x={blurInput:{}};return r.a.createElement("div",{className:"comment-textarea",onBlur:c,onFocus:u,onClick:function(t){t.preventDefault(),t.stopPropagation()},onScroll:function(t){t.preventDefault(),t.stopPropagation()}},r.a.createElement(f.a,{className:"comment-textarea ql-container ql-editor",style:{overflowY:"visible"},ref:function(t){return t&&(t.getEditor().root.ariaLabel="".concat(h(d?"action.reply":"action.comment"))),e(t)},modules:s&&s.length>0?H(H({},x),$):x,theme:"snow",value:o,placeholder:"".concat(h(d?"action.reply":"action.comment"),"..."),onChange:i,onKeyDown:a,formats:K}),d&&!w&&r.a.createElement(m.a,{className:"add-attachment",dataElement:y.a.NotesPanel.ADD_REPLY_ATTACHMENT_BUTTON,img:"ic_fileattachment_24px",title:"".concat(h("action.add")," ").concat(h("option.type.fileattachment")),onClick:function(){var t;null===(t=Object(g.a)().querySelector("#reply-attachment-picker"))||void 0===t||t.click()}}))}));Q.displayName="CommentTextarea";var Z=Q;function J(t){return(J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function X(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function tt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?X(Object(n),!0).forEach((function(e){et(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):X(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function et(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==J(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==J(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===J(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function nt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return ot(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ot(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ot(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var rt={value:a.a.string,placeholder:a.a.string,onChange:a.a.func.isRequired,onBlur:a.a.func,onFocus:a.a.func,onSubmit:a.a.func},it=r.a.forwardRef((function(t,e){var n=nt(Object(l.e)((function(t){return[p.a.getUserData(t),p.a.isNoteSubmissionWithEnterEnabled(t),p.a.getAutoFocusNoteOnAnnotationSelection(t),p.a.getIsNoteEditing(t)]}),l.c),2),i=n[0],a=n[1],c=Object(o.useContext)(s.a).resize,d=Object(o.useRef)(),f=Object(o.useRef)();Object(o.useLayoutEffect)((function(){var t,e,n=null===(t=d.current)||void 0===t||null===(e=t.editor)||void 0===e?void 0:e.container.firstElementChild,o=(null==n?void 0:n.getBoundingClientRect())||{};f.current&&f.current!==o.height&&c(),f.current=o.height}),[t.value,c]);var m=tt(tt({},t),{},{ref:function(t){d.current=t,e(t)},onChange:u()((function(e,n,o,r){if(e=e.replace(/&nbsp;/g," "),d.current){var i="";if(r&&""===r.getText().trim()&&"<p><br></p>"===e||(i=e.target?e.target.value:e),t.onChange(i),h.a.doesDeltaContainMention(n.ops)){var a,l=h.a.getFormattedTextFromDeltas(n.ops),c=h.a.extractMentionDataFromStr(l),u=r.getText().length+c.plainTextValue.length,s=null===(a=d.current)||void 0===a?void 0:a.editor;setTimeout((function(){return null==s?void 0:s.setSelection(u,u)}),1)}}}),100),onKeyDown:function(e){if(13===e.which){var n=a,o=e.metaKey||e.ctrlKey;(n||o)&&t.onSubmit(e)}},userData:i});return r.a.createElement(r.a.Fragment,null,r.a.createElement(Z,m))}));it.displayName="NoteTextarea",it.propTypes=rt;var at=it;e.a=at},1525:function(t,e,n){"use strict";n.d(e,"a",(function(){return i}));n(36);var o=n(29),r=n(1);function i(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,i=new window.Core.Annotations.StickyAnnotation;i.InReplyTo=t.Id,i.X=t.X,i.Y=t.Y,i.PageNumber=t.PageNumber,i.Subject="Sticky Note",i.Author=r.a.getCurrentUser(),i.State=e,i.StateModel="Marked"===e||"Unmarked"===e?"Marked":"Review",i.Hidden=!0,i.enableSkipAutoLink();var a=r.a.getDisplayAuthor(i.Author,n),l=o.a.t("option.state.".concat(e.toLowerCase())),c="".concat(l," ").concat(o.a.t("option.state.setBy")," ").concat(a);return i.setContents(c),i}},1526:function(t,e,n){"use strict";n(18),n(107),n(19),n(12),n(13),n(8),n(14),n(10),n(9),n(11),n(16),n(15),n(20);var o=n(0),r=n.n(o),i=n(4),a=n.n(i),l=n(429),c=n(17),u=n.n(c);n(1532);function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return p(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return p(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function d(t){var e=t.children.replace(/\n$/,""),n=t.panelWidth,i=t.linesToBreak,a=t.renderRichText,c=t.richTextStyle,p=t.resize,d=t.style,f=t.comment,h=void 0!==f&&f,m=t.beforeContent,b=void 0===m?function(){}:m,y=s(Object(o.useState)(!1),2),g=y[0],v=y[1],w=s(Object(o.useState)(null),2),x=w[0],q=w[1],k=s(Object(o.useState)(null),2),E=k[0],O=k[1],j=s(Object(o.useState)(!1),2),S=j[0],N=j[1],A=r.a.useRef(null),C=Object(l.a)().t,P=g?e:e.substring(0,E*i),L=C(g?"action.showLess":"action.showMore"),I=u()("note-text-preview",{"preview-comment":h});return Object(o.useEffect)((function(){var t=A.current.clientWidth;q(t)}),[n]),Object(o.useLayoutEffect)((function(){var t=function(t){var e=document.createElement("canvas").getContext("2d");return e.font="13px sans-serif",e.measureText(t).width}(e),n=t/e.length,o=Math.floor(x/n);O(o),N(t/x>i)}),[e,x]),r.a.createElement("div",{className:I,ref:A,style:d,"aria-live":"polite"},b(),a&&c?a(P,c,0):P," ",S&&r.a.createElement("button",{className:"note-text-preview-prompt",onClick:function(t){t.stopPropagation(),v(!g),p&&p()}},L))}d.propTypes={panelWidth:a.a.number,linesToBreak:a.a.number,renderRichText:a.a.func,richTextStyle:a.a.any,resize:a.a.func,style:a.a.any,comment:a.a.bool,beforeContent:a.a.func},e.a=d},1528:function(t,e,n){"use strict";n(36),n(41),n(26),n(27),n(12),n(13),n(8),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46),n(14),n(10),n(9),n(11);var o=n(0),r=n(6),i=n(4),a=n.n(i),l=n(5),c=n(2),u=n(3);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?p(Object(n),!0).forEach((function(e){f(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function f(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==s(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==s(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===s(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var h=function(t,e,n){return{icon:e,label:"option.state.".concat(t.toLowerCase()),title:"option.state.".concat(t.toLowerCase()),option:t,dataElement:n}},m=[h("Accepted","icon-annotation-status-accepted","noteStateFlyoutAcceptedOption"),h("Rejected","icon-annotation-status-rejected","noteStateFlyoutRejectedOption"),h("Cancelled","icon-annotation-status-cancelled","noteStateFlyoutCancelledOption"),h("Completed","icon-annotation-status-completed","noteStateFlyoutCompletedOption"),h("None","icon-annotation-status-none","noteStateFlyoutNoneOption"),h("Marked","icon-annotation-status-marked","noteStateFlyoutMarkedOption"),h("Unmarked","icon-annotation-status-unmarked","noteStateFlyoutUnmarkedOption")],b=function(t){var e=t.noteId,n=t.handleStateChange,i=void 0===n?function(){}:n,a=t.isMultiSelectMode,s=void 0!==a&&a,p=Object(r.d)(),f=s?"":"-".concat(e),h="".concat(l.a.NOTE_STATE_FLYOUT).concat(f),b=Object(r.e)((function(t){return u.a.getFlyout(t,h)}));return Object(o.useLayoutEffect)((function(){var t={dataElement:h,className:"NoteStateFlyout",items:m.map((function(t){return d(d({},t),{},{onClick:function(){return e=t.option,void i(e);var e}})}))};p(b?c.a.updateFlyout(t.dataElement,t):c.a.addFlyout(t))}),[i]),null};b.propTypes={noteId:a.a.string,handleStateChange:a.a.func,isMultiSelectMode:a.a.bool};var y=b;e.a=y},1532:function(t,e,n){var o=n(32),r=n(1533);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1533:function(t,e,n){(t.exports=n(33)(!1)).push([t.i,".note-text-preview{font-size:13px;color:var(--gray-7);padding-right:var(--note-content-right-padding-width);-webkit-user-select:text!important;-moz-user-select:text!important;user-select:text!important;cursor:text;height:-moz-fit-content;height:fit-content;width:calc(100% - var(--note-content-right-padding-width));overflow:hidden}.note-text-preview>*{pointer-events:all}.preview-comment{color:var(--text-color)}.note-text-preview-prompt{cursor:pointer;color:var(--primary-button);text-decoration:underline;position:relative;pointer-events:auto;background:transparent;border:none;padding:0}.note-text-preview-prompt:hover{color:var(--primary-button-hover)}.trackedChangePopup .note-text-preview{max-height:400px;overflow-y:auto;width:calc(100% + var(--note-content-right-padding-width))}",""])},1557:function(t,e,n){var o=n(32),r=n(1558);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1558:function(t,e,n){(t.exports=n(33)(!1)).push([t.i,'.ql-container{box-sizing:border-box;font-family:Helvetica,Arial,sans-serif;font-size:13px;height:100%;margin:0;position:relative}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-container.ql-disabled .ql-editor ul[data-checked]>li:before{pointer-events:none}.ql-clipboard{left:-100000px;height:1px;overflow-y:hidden;position:absolute;top:50%}.ql-clipboard p{margin:0;padding:0}.ql-container .ql-editor{box-sizing:border-box;line-height:1.42;height:100%;outline:none;overflow-y:auto;padding:12px 15px;-o-tab-size:4;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text}.ql-editor>*{cursor:text}.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6,.ql-editor ol,.ql-editor p,.ql-editor pre,.ql-editor ul{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor p{margin:0;padding:0}.ql-editor ol,.ql-editor ul{padding-left:1.5em}.ql-editor ol>li,.ql-editor ul>li{list-style-type:none}.ql-editor ul>li:before{content:"\\2022"}.ql-editor ul[data-checked=false],.ql-editor ul[data-checked=true]{pointer-events:none}.ql-editor ul[data-checked=false]>li *,.ql-editor ul[data-checked=true]>li *{pointer-events:all}.ql-editor ul[data-checked=false]>li:before,.ql-editor ul[data-checked=true]>li:before{color:#777;cursor:pointer;pointer-events:all}.ql-editor ul[data-checked=true]>li:before{content:"\\2611"}.ql-editor ul[data-checked=false]>li:before{content:"\\2610"}.ql-editor li:before{display:inline-block;white-space:nowrap;width:1.2em}.ql-editor li:not(.ql-direction-rtl):before{margin-left:-1.5em;margin-right:.3em;text-align:right}.ql-editor li.ql-direction-rtl:before{margin-left:.3em;margin-right:-1.5em}.ql-editor ol li:not(.ql-direction-rtl),.ql-editor ul li:not(.ql-direction-rtl){padding-left:1.5em}.ql-editor ol li.ql-direction-rtl,.ql-editor ul li.ql-direction-rtl{padding-right:1.5em}.ql-editor ol li{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;counter-increment:list-0}.ql-editor ol li:before{content:counter(list-0,decimal) ". "}.ql-editor ol li.ql-indent-1{counter-increment:list-1}.ql-editor ol li.ql-indent-1:before{content:counter(list-1,lower-alpha) ". "}.ql-editor ol li.ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-2{counter-increment:list-2}.ql-editor ol li.ql-indent-2:before{content:counter(list-2,lower-roman) ". "}.ql-editor ol li.ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-3{counter-increment:list-3}.ql-editor ol li.ql-indent-3:before{content:counter(list-3,decimal) ". "}.ql-editor ol li.ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-4{counter-increment:list-4}.ql-editor ol li.ql-indent-4:before{content:counter(list-4,lower-alpha) ". "}.ql-editor ol li.ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-5{counter-increment:list-5}.ql-editor ol li.ql-indent-5:before{content:counter(list-5,lower-roman) ". "}.ql-editor ol li.ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-6{counter-increment:list-6}.ql-editor ol li.ql-indent-6:before{content:counter(list-6,decimal) ". "}.ql-editor ol li.ql-indent-6{counter-reset:list-7 list-8 list-9}.ql-editor ol li.ql-indent-7{counter-increment:list-7}.ql-editor ol li.ql-indent-7:before{content:counter(list-7,lower-alpha) ". "}.ql-editor ol li.ql-indent-7{counter-reset:list-8 list-9}.ql-editor ol li.ql-indent-8{counter-increment:list-8}.ql-editor ol li.ql-indent-8:before{content:counter(list-8,lower-roman) ". "}.ql-editor ol li.ql-indent-8{counter-reset:list-9}.ql-editor ol li.ql-indent-9{counter-increment:list-9}.ql-editor ol li.ql-indent-9:before{content:counter(list-9,decimal) ". "}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:3em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:4.5em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:3em}.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:4.5em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:7.5em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:7.5em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:9em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:10.5em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:9em}.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:10.5em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:13.5em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:13.5em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:15em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:16.5em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:15em}.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:16.5em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:19.5em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:19.5em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:21em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:22.5em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:21em}.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:22.5em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:24em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:25.5em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:24em}.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:25.5em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:27em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:28.5em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:27em}.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:28.5em}.ql-editor .ql-video{display:block;max-width:100%}.ql-editor .ql-video.ql-align-center{margin:0 auto}.ql-editor .ql-video.ql-align-right{margin:0 0 0 auto}.ql-editor .ql-bg-black{background-color:#000}.ql-editor .ql-bg-red{background-color:#e60000}.ql-editor .ql-bg-orange{background-color:#f90}.ql-editor .ql-bg-yellow{background-color:#ff0}.ql-editor .ql-bg-green{background-color:#008a00}.ql-editor .ql-bg-blue{background-color:#06c}.ql-editor .ql-bg-purple{background-color:#93f}.ql-editor .ql-color-white{color:#fff}.ql-editor .ql-color-red{color:#e60000}.ql-editor .ql-color-orange{color:#f90}.ql-editor .ql-color-yellow{color:#ff0}.ql-editor .ql-color-green{color:#008a00}.ql-editor .ql-color-blue{color:#06c}.ql-editor .ql-color-purple{color:#93f}.ql-editor .ql-font-serif{font-family:Georgia,Times New Roman,serif}.ql-editor .ql-font-monospace{font-family:Monaco,Courier New,monospace}.ql-editor .ql-size-small{font-size:.75em}.ql-editor .ql-size-large{font-size:1.5em}.ql-editor .ql-size-huge{font-size:2.5em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor.ql-blank:before{color:rgba(0,0,0,.6);content:attr(data-placeholder);font-style:italic;left:15px;pointer-events:none;position:absolute;right:15px}.ql-snow.ql-toolbar:after,.ql-snow .ql-toolbar:after{clear:both;content:"";display:table}.ql-snow.ql-toolbar button,.ql-snow .ql-toolbar button{background:none;border:none;cursor:pointer;display:inline-block;float:left;height:24px;padding:3px 5px;width:28px}.ql-snow.ql-toolbar button svg,.ql-snow .ql-toolbar button svg{float:left;height:100%}.ql-snow.ql-toolbar button:active:hover,.ql-snow .ql-toolbar button:active:hover{outline:none}.ql-snow.ql-toolbar input.ql-image[type=file],.ql-snow .ql-toolbar input.ql-image[type=file]{display:none}.ql-snow.ql-toolbar .ql-picker-item.ql-selected,.ql-snow .ql-toolbar .ql-picker-item.ql-selected,.ql-snow.ql-toolbar .ql-picker-item:hover,.ql-snow .ql-toolbar .ql-picker-item:hover,.ql-snow.ql-toolbar .ql-picker-label.ql-active,.ql-snow .ql-toolbar .ql-picker-label.ql-active,.ql-snow.ql-toolbar .ql-picker-label:hover,.ql-snow .ql-toolbar .ql-picker-label:hover,.ql-snow.ql-toolbar button.ql-active,.ql-snow .ql-toolbar button.ql-active,.ql-snow.ql-toolbar button:focus,.ql-snow .ql-toolbar button:focus,.ql-snow.ql-toolbar button:hover,.ql-snow .ql-toolbar button:hover{color:#06c}.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:focus .ql-fill,.ql-snow .ql-toolbar button:focus .ql-fill,.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:hover .ql-fill,.ql-snow .ql-toolbar button:hover .ql-fill,.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill{fill:#06c}.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow.ql-toolbar button.ql-active .ql-stroke,.ql-snow .ql-toolbar button.ql-active .ql-stroke,.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar button:focus .ql-stroke,.ql-snow .ql-toolbar button:focus .ql-stroke,.ql-snow.ql-toolbar button:focus .ql-stroke-miter,.ql-snow .ql-toolbar button:focus .ql-stroke-miter,.ql-snow.ql-toolbar button:hover .ql-stroke,.ql-snow .ql-toolbar button:hover .ql-stroke,.ql-snow.ql-toolbar button:hover .ql-stroke-miter,.ql-snow .ql-toolbar button:hover .ql-stroke-miter{stroke:#06c}@media(pointer:coarse){.ql-snow.ql-toolbar button:hover:not(.ql-active),.ql-snow .ql-toolbar button:hover:not(.ql-active){color:#444}.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#444}.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#444}}.ql-snow,.ql-snow *{box-sizing:border-box}.ql-snow .ql-hidden{display:none}.ql-snow .ql-out-bottom,.ql-snow .ql-out-top{visibility:hidden}.ql-snow .ql-tooltip{position:absolute;transform:translateY(10px)}.ql-snow .ql-tooltip a{cursor:pointer;text-decoration:none}.ql-snow .ql-tooltip.ql-flip{transform:translateY(-10px)}.ql-snow .ql-formats{display:inline-block;vertical-align:middle}.ql-snow .ql-formats:after{clear:both;content:"";display:table}.ql-snow .ql-stroke{fill:none;stroke:#444;stroke-linecap:round;stroke-linejoin:round;stroke-width:2}.ql-snow .ql-stroke-miter{fill:none;stroke:#444;stroke-miterlimit:10;stroke-width:2}.ql-snow .ql-fill,.ql-snow .ql-stroke.ql-fill{fill:#444}.ql-snow .ql-empty{fill:none}.ql-snow .ql-even{fill-rule:evenodd}.ql-snow .ql-stroke.ql-thin,.ql-snow .ql-thin{stroke-width:1}.ql-snow .ql-transparent{opacity:.4}.ql-snow .ql-direction svg:last-child{display:none}.ql-snow .ql-direction.ql-active svg:last-child{display:inline}.ql-snow .ql-direction.ql-active svg:first-child{display:none}.ql-snow .ql-editor h1{font-size:2em}.ql-snow .ql-editor h2{font-size:1.5em}.ql-snow .ql-editor h3{font-size:1.17em}.ql-snow .ql-editor h4{font-size:1em}.ql-snow .ql-editor h5{font-size:.83em}.ql-snow .ql-editor h6{font-size:.67em}.ql-snow .ql-editor a{text-decoration:underline}.ql-snow .ql-editor blockquote{border-left:4px solid #ccc;margin-bottom:5px;margin-top:5px;padding-left:16px}.ql-snow .ql-editor code,.ql-snow .ql-editor pre{background-color:#f0f0f0;border-radius:3px}.ql-snow .ql-editor pre{white-space:pre-wrap;margin-bottom:5px;margin-top:5px;padding:5px 10px}.ql-snow .ql-editor code{font-size:85%;padding:2px 4px}.ql-snow .ql-editor pre.ql-syntax{background-color:#23241f;color:#f8f8f2;overflow:visible}.ql-snow .ql-editor img{max-width:100%}.ql-snow .ql-picker{color:#444;display:inline-block;float:left;font-size:14px;font-weight:500;height:24px;position:relative;vertical-align:middle}.ql-snow .ql-picker-label{cursor:pointer;display:inline-block;height:100%;padding-left:8px;padding-right:2px;position:relative;width:100%}.ql-snow .ql-picker-label:before{display:inline-block;line-height:22px}.ql-snow .ql-picker-options{background-color:#fff;display:none;min-width:100%;padding:4px 8px;position:absolute;white-space:nowrap}.ql-snow .ql-picker-options .ql-picker-item{cursor:pointer;display:block;padding-bottom:5px;padding-top:5px}.ql-snow .ql-picker.ql-expanded .ql-picker-label{color:#ccc;z-index:2}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill{fill:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke{stroke:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-options{display:block;margin-top:-1px;top:100%;z-index:1}.ql-snow .ql-color-picker,.ql-snow .ql-icon-picker{width:28px}.ql-snow .ql-color-picker .ql-picker-label,.ql-snow .ql-icon-picker .ql-picker-label{padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-label svg,.ql-snow .ql-icon-picker .ql-picker-label svg{right:4px}.ql-snow .ql-icon-picker .ql-picker-options{padding:4px 0}.ql-snow .ql-icon-picker .ql-picker-item{height:24px;width:24px;padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-options{padding:3px 5px;width:152px}.ql-snow .ql-color-picker .ql-picker-item{border:1px solid transparent;float:left;height:16px;margin:2px;padding:0;width:16px}.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{position:absolute;margin-top:-9px;right:0;top:50%;width:18px}.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""]):before{content:attr(data-label)}.ql-snow .ql-picker.ql-header{width:98px}.ql-snow .ql-picker.ql-header .ql-picker-item:before,.ql-snow .ql-picker.ql-header .ql-picker-label:before{content:"Normal"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]:before{content:"Heading 1"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]:before{content:"Heading 2"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]:before{content:"Heading 3"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]:before{content:"Heading 4"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]:before{content:"Heading 5"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]:before{content:"Heading 6"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before{font-size:2em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before{font-size:1.5em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before{font-size:1.17em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before{font-size:1em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before{font-size:.83em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before{font-size:.67em}.ql-snow .ql-picker.ql-font{width:108px}.ql-snow .ql-picker.ql-font .ql-picker-item:before,.ql-snow .ql-picker.ql-font .ql-picker-label:before{content:"Sans Serif"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]:before{content:"Serif"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]:before{content:"Monospace"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{font-family:Georgia,Times New Roman,serif}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{font-family:Monaco,Courier New,monospace}.ql-snow .ql-picker.ql-size{width:98px}.ql-snow .ql-picker.ql-size .ql-picker-item:before,.ql-snow .ql-picker.ql-size .ql-picker-label:before{content:"Normal"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]:before{content:"Small"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]:before{content:"Large"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]:before{content:"Huge"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before{font-size:10px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before{font-size:18px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{font-size:32px}.ql-snow .ql-color-picker.ql-background .ql-picker-item{background-color:#fff}.ql-snow .ql-color-picker.ql-color .ql-picker-item{background-color:#000}.ql-toolbar.ql-snow{border:1px solid #ccc;box-sizing:border-box;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding:8px}.ql-toolbar.ql-snow .ql-formats{margin-right:15px}.ql-toolbar.ql-snow .ql-picker-label{border:1px solid transparent}.ql-toolbar.ql-snow .ql-picker-options{border:1px solid transparent;box-shadow:0 2px 8px rgba(0,0,0,.2)}.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label,.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options{border-color:#ccc}.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover{border-color:#000}.ql-toolbar.ql-snow+.ql-container.ql-snow{border-top:0}.ql-snow .ql-tooltip{background-color:#fff;border:1px solid #ccc;box-shadow:0 0 5px #ddd;color:#444;padding:5px 12px;white-space:nowrap}.ql-snow .ql-tooltip:before{content:"Visit URL:";line-height:26px;margin-right:8px}.ql-snow .ql-tooltip input[type=text]{display:none;border:1px solid #ccc;font-size:13px;height:26px;margin:0;padding:3px 5px;width:170px}.ql-snow .ql-tooltip a.ql-preview{display:inline-block;max-width:200px;overflow-x:hidden;text-overflow:ellipsis;vertical-align:top}.ql-snow .ql-tooltip a.ql-action:after{border-right:1px solid #ccc;content:"Edit";margin-left:16px;padding-right:8px}.ql-snow .ql-tooltip a.ql-remove:before{content:"Remove";margin-left:8px}.ql-snow .ql-tooltip a{line-height:26px}.ql-snow .ql-tooltip.ql-editing a.ql-preview,.ql-snow .ql-tooltip.ql-editing a.ql-remove{display:none}.ql-snow .ql-tooltip.ql-editing input[type=text]{display:inline-block}.ql-snow .ql-tooltip.ql-editing a.ql-action:after{border-right:0;content:"Save";padding-right:0}.ql-snow .ql-tooltip[data-mode=link]:before{content:"Enter link:"}.ql-snow .ql-tooltip[data-mode=formula]:before{content:"Enter formula:"}.ql-snow .ql-tooltip[data-mode=video]:before{content:"Enter video:"}.ql-snow a{color:#06c}.ql-container.ql-snow{border:1px solid #ccc}',""])},1559:function(t,e,n){var o=n(32),r=n(1560);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1560:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.comment-textarea{position:relative}.comment-textarea .ql-toolbar{display:none}.comment-textarea .ql-container{border:none}.comment-textarea .ql-container .ql-editor{width:100%;padding:4px 6px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);resize:none;overflow:hidden;box-sizing:border-box}.comment-textarea .ql-container .ql-editor:focus{outline:none;border:1px solid var(--focus-border)}.comment-textarea .ql-container .ql-editor.ql-blank:before{left:8px;list-style-type:none;font-style:normal;color:var(--placeholder-text)}.comment-textarea .ql-container .ql-editor p{margin:0;word-break:break-word}.comment-textarea .ql-container .ql-editor ul>li:before{content:none!important}.comment-textarea .ql-container.ql-snow{border:none}.comment-textarea .ql-container.ql-snow:focus{outline:var(--focus-visible-outline);border-radius:4px}.comment-textarea .add-attachment{position:absolute;bottom:2px;right:2px;width:24px;height:24px}.comment-textarea .add-attachment:hover{background-color:var(--blue-1)}.comment-textarea .add-attachment .Icon{padding:3px}.ql-editor ul>li:before{content:none!important}.mention__element{width:170px;z-index:9001!important;max-height:200px;overflow-y:auto;overflow-y:overlay;overflow-x:hidden;background-color:var(--component-background);border:1px solid var(--border);border-radius:4px}.mention__suggestions__list{width:100%;font-size:14px;margin-top:0;padding-left:0!important;list-style:none;word-wrap:break-word;border-radius:4px}.mention__suggestions__item{background-color:var(--component-background);white-space:nowrap;padding-left:0;text-overflow:clip;padding:7px 5px 1px!important;margin:0;width:100%;cursor:pointer}.mention__suggestions__item .email{margin-top:2px;font-size:12px;white-space:normal;color:var(--faded-text)}.mention__suggestions__item.selected{background-color:var(--view-header-button-active)!important}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1561:function(t,e,n){var o=n(32),r=n(1562);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1562:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NotePopup .options.modular-ui .option:hover{cursor:pointer;background:var(--primary-button-hover);color:var(--gray-0)}.NotePopup{flex-grow:0;display:flex;justify-content:flex-end;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:28px;height:28px;position:relative}.NotePopup .Button.overflow{width:28px;height:28px;border-radius:4px;display:flex;justify-content:center;align-items:center}.NotePopup .Button.overflow .Icon{width:24px;height:24px}.NotePopup .Button.overflow:hover{background:var(--blue-1)}.NotePopup .Button.overflow.active{background:var(--popup-button-active)}.NotePopup .options{display:flex;flex-direction:column;box-shadow:0 0 3px 0 var(--box-shadow);z-index:80;position:absolute;border-radius:4px;background:var(--component-background);top:40px;width:-moz-max-content;width:max-content}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NotePopup .options{right:0}}.NotePopup .options .note-popup-option{padding:0;border:none;background-color:transparent;align-items:flex-start}:host(:not([data-tabbing=true])) .NotePopup .options .note-popup-option,html:not([data-tabbing=true]) .NotePopup .options .note-popup-option{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NotePopup .options .note-popup-option{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NotePopup .options .note-popup-option{font-size:13px}}.NotePopup .options.options-reply{top:30px}.NotePopup .options .option{display:flex;flex-direction:column;justify-content:center;height:28px;padding-left:8px;padding-right:17px;border-radius:0}.NotePopup .options .option:hover{background-color:var(--popup-button-hover)}.NotePopup .options .option:first-child{border-top-right-radius:4px;border-top-left-radius:4px}.NotePopup .options .option:last-child{border-bottom-right-radius:4px}.NotePopup .Button{height:28px}.NotePopup .Button.active{background:var(--popup-button-active)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NotePopup .Button.note-popup-toggle-trigger{width:28px;height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NotePopup .Button.note-popup-toggle-trigger{width:28px;height:28px}}.NotePopupFlyout{min-width:unset!important;max-width:unset!important}.NotePopupFlyout .flyout-item-container{height:unset!important;margin:unset!important}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1563:function(t,e,n){var o=n(32),r=n(1564);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1564:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NoteHeader{padding-right:12px;position:relative;flex:1;color:var(--text-color);display:flex}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NoteHeader{flex:none}}.NoteHeader .type-icon{margin:2px;width:24px;height:24px}.NoteHeader .type-icon-container{padding-right:13px}.NoteHeader .type-icon-container .unread-notification{position:absolute;width:13px;height:13px;right:-2px;top:-2px;border-radius:10000px;border:2px solid var(--component-background);background:#00a5e4}.NoteHeader .author-and-date{flex:1;min-width:0;position:relative}.NoteHeader .author-and-date.isReply{padding-left:0;padding-top:0;font-size:10px}.NoteHeader .author-and-date .author-and-overflow{display:flex;justify-content:space-between}.NoteHeader .author-and-date .author-and-overflow .author-and-time{display:flex;flex-direction:column;word-break:break-word}.NoteHeader .author-and-date .author-and-overflow .author-and-time .author{font-weight:700;font-size:13px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies{display:flex}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px;color:var(--faded-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px}}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container{display:flex;flex-grow:1;padding-left:10px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-reply-icon{height:12px;width:12px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:10px;color:var(--gray-7)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:12px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:12px}}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow{display:flex;flex:1;justify-content:flex-end}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow>*{pointer-events:auto}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper .tracked-change-icon{margin:2px;width:24px;height:24px}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper:hover.accept{background-color:#d5f5ca}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper:hover.reject{background-color:#ffe8e8}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .ToggleElementButton button{width:28px;height:28px;margin:0 8px 0 13px}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .ToggleElementButton button .Icon{width:20px;height:20px}.NoteHeader .author-name{font-weight:700}.NoteHeader .note-popup-toggle-trigger{padding:0;margin-right:0!important;margin-left:0!important;min-width:28px!important}.NoteHeader .note-popup-toggle-trigger .Icon{width:24px!important;height:24px!important}.parent{padding-left:12px;padding-top:12px}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1565:function(t,e,n){var o=n(32),r=n(1566);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1566:function(t,e,n){(t.exports=n(33)(!1)).push([t.i,".reply-attachment-list{display:flex;flex-direction:column;cursor:default}.reply-attachment-list .reply-attachment{background-color:var(--gray-1);border-radius:4px;cursor:pointer;pointer-events:auto}.reply-attachment-list .reply-attachment:not(:last-child){margin-bottom:8px}.reply-attachment-list .reply-attachment .reply-attachment-preview{width:100%;max-height:80px;display:flex;justify-content:center}.reply-attachment-list .reply-attachment .reply-attachment-preview.dirty{position:relative;margin-bottom:15px}.reply-attachment-list .reply-attachment .reply-attachment-preview img{max-width:100%;max-height:100%;-o-object-fit:contain;object-fit:contain}.reply-attachment-list .reply-attachment .reply-attachment-preview .reply-attachment-preview-message{font-size:11px;color:var(--error-text-color);position:absolute;bottom:-15px;left:10px}.reply-attachment-list .reply-attachment .reply-attachment-info{display:flex;align-items:center;height:40px;padding:8px}.reply-attachment-list .reply-attachment .reply-attachment-info .reply-attachment-icon{height:24px;min-height:24px;width:24px;min-width:24px}.reply-attachment-list .reply-attachment .reply-attachment-info .reply-file-name{height:16px;width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-left:8px;margin-right:8px}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button{height:24px;min-height:24px;width:24px;min-width:24px}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button:hover{background-color:var(--blue-1)}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button .Icon{height:16px;width:16px}",""])},1567:function(t,e,n){var o=n(32),r=n(1568);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1568:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled span{color:var(--primary-button-text)}.NoteContent{--note-content-right-padding-width:12px;position:relative;display:flex;flex-direction:column;align-items:left;flex:1;color:var(--text-color);padding-bottom:12px;pointer-events:none}.NoteContent.isReply{padding-bottom:0}.NoteContent.unread.isReply{background:rgba(0,165,228,.08)}.NoteContent.unread.clicked .author-and-time span{font-weight:400}.NoteContent.unread .author-and-time span{font-weight:700}.NoteContent .container{margin-top:8px;overflow:hidden;white-space:pre-wrap;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text;cursor:text;padding-left:52px;padding-right:var(--note-content-right-padding-width)}.NoteContent .container>*{pointer-events:all}.NoteContent .container-reply{margin-top:8px;overflow:hidden;white-space:pre-wrap;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text;cursor:text}.NoteContent .edit-content{margin-top:7px;display:flex;flex-direction:column;position:relative;flex:1;padding-left:52px;padding-right:12px;padding-bottom:12px;pointer-events:auto}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NoteContent .edit-content{flex:none}}.NoteContent .edit-content textarea{width:100%;padding-left:8px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding-top:4px;padding-bottom:4px;resize:none;overflow:hidden;box-sizing:border-box}.NoteContent .edit-content textarea:focus{outline:none;border:1px solid var(--focus-border)}.NoteContent .edit-content textarea::-moz-placeholder{color:var(--placeholder-text)}.NoteContent .edit-content textarea::placeholder{color:var(--placeholder-text)}.NoteContent .edit-content .edit-buttons{display:flex;flex-direction:row;justify-content:flex-end;margin-top:8px}.NoteContent .edit-content .edit-buttons>div{margin-right:4px}.NoteContent .edit-content .edit-buttons .save-button{background-color:transparent;cursor:pointer;flex-shrink:0;background:var(--primary-button);border-radius:4px;width:-moz-fit-content;width:fit-content;border:none;height:28px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);padding:0 13px}:host(:not([data-tabbing=true])) .NoteContent .edit-content .edit-buttons .save-button,html:not([data-tabbing=true]) .NoteContent .edit-content .edit-buttons .save-button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteContent .edit-content .edit-buttons .save-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteContent .edit-content .edit-buttons .save-button{font-size:13px}}.NoteContent .edit-content .edit-buttons .save-button:hover{background:var(--primary-button-hover);color:var(--primary-button-text)}.NoteContent .edit-content .edit-buttons .save-button.disabled{background:var(--gray-6)!important;border-color:var(--gray-6)!important;cursor:not-allowed}.NoteContent .edit-content .edit-buttons .save-button.disabled span{color:var(--primary-button-text)}.NoteContent .edit-content .edit-buttons .cancel-button{border:none;background-color:transparent;color:var(--secondary-button-text);padding:0 10px;width:-moz-fit-content;width:fit-content;height:28px;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-right:2px}:host(:not([data-tabbing=true])) .NoteContent .edit-content .edit-buttons .cancel-button,html:not([data-tabbing=true]) .NoteContent .edit-content .edit-buttons .cancel-button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteContent .edit-content .edit-buttons .cancel-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteContent .edit-content .edit-buttons .cancel-button{font-size:13px}}.NoteContent .edit-content .edit-buttons .cancel-button:hover{color:var(--secondary-button-hover)}.NoteContent .reply-content{padding-left:0}.NoteContent .contents{white-space:pre-wrap;color:var(--text-color);margin-right:5px;padding:0;word-break:normal;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text}.NoteContent .contents .highlight{background:#fffc95;color:#333}.NoteContent .highlight{background:#fffc95}.NoteContent .selected-text-preview{padding-left:52px;padding-top:8px}.NoteContent .reply-attachment-list{margin-bottom:8px}.NoteContent.modular-ui .highlight{font-weight:700;color:var(--blue-5);background:none}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled{border:none}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1569:function(t,e,n){var o=n(32),r=n(1570);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1570:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.reply-area-container{border-top:1px solid var(--divider);display:flex;flex-direction:column;margin-bottom:0}.reply-area-container .reply-attachment-list{margin:12px 12px 0}.reply-area-container .reply-area-with-button{display:flex}.reply-area-container .reply-area-with-button .reply-area{position:relative;flex:1;margin:12px 17px 12px 12px;border-radius:4px;align-items:center}.reply-area-container .reply-area-with-button .reply-area.unread{background:rgba(0,165,228,.08)}.reply-area-container .reply-area-with-button .reply-area .comment-textarea .ql-container .ql-editor.ql-blank{padding:4px}.reply-area-container .reply-area-with-button .reply-area .comment-textarea .ql-container .ql-editor.ql-blank:before{left:4px}.reply-area-container .reply-area-with-button .reply-button-container{display:flex;flex-direction:column;justify-content:flex-end}.reply-area-container .reply-area-with-button .reply-button-container .reply-button{width:28px;height:28px;padding:0;border:none;background-color:transparent;right:10px;bottom:12px}:host(:not([data-tabbing=true])) .reply-area-container .reply-area-with-button .reply-button-container .reply-button,html:not([data-tabbing=true]) .reply-area-container .reply-area-with-button .reply-button-container .reply-button{outline:none}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.reply-area-container .reply-area-with-button .reply-button-container .reply-button{width:80px}}.reply-area-container .reply-area-with-button .reply-button-container .reply-button:hover{background:var(--blue-1)}.reply-area-container .reply-area-with-button .reply-button-container .reply-button.disabled{cursor:not-allowed}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1571:function(t,e,n){var o=n(32),r=n(1572);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1572:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}@media print{#line-connector-root{opacity:0}}#line-connector-root{position:relative;z-index:69}.horizontalLine{height:2px}.horizontalLine,.verticalLine{background-color:rgba(30,120,235,.5);position:fixed}.verticalLine{width:2px}.arrowHead{position:absolute;top:0;left:0;margin:auto;width:0;height:0;border-top:6px solid transparent;border-bottom:6px solid transparent;border-right:7px solid rgba(30,120,235,.5);transform:translateX(-100%) translateY(-50%) translateY(1px)}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1573:function(t,e,n){var o=n(32),r=n(1574);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1574:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Note{padding:0;border:none;background-color:transparent;display:block;text-align:left;border-radius:4px;box-shadow:0 0 3px 0 var(--note-box-shadow);margin-bottom:8px;margin-left:2px;background:var(--component-background);cursor:pointer;position:relative}:host(:not([data-tabbing=true])) .Note,html:not([data-tabbing=true]) .Note{outline:none}.Note.unread{border:1.2px solid #00a5e4}.Note.expanded{box-shadow:0 4px 16px var(--note-box-shadow-expanded),0 0 4px 0 var(--note-box-shadow)}.Note.is-multi-selected{box-shadow:0 4px 16px rgba(134,142,150,.24),0 0 4px 0 var(--note-box-shadow)}.Note.disabled{opacity:.5;pointer-events:none}.Note .note-button{position:absolute;width:100%;height:100%;top:0;left:0}.Note .mark-all-read-button{background:#00a5e4;text-align:center;color:#fff;height:16px;font-size:12px;width:100%;border-radius:0}.Note .divider{height:1px;width:100%;background:var(--divider)}.Note .reply-divider{background:var(--reply-divider);height:1px;width:100%}.Note .replies{margin-left:52px;padding-bottom:12px}.Note .reply{padding-left:12px;padding-bottom:24px;border-left:1px solid var(--reply-divider)}.Note .reply:last-of-type{padding-bottom:0}.Note .group-section{margin-left:52px;padding-bottom:12px;display:flex;flex-direction:column;grid-row-gap:4px;row-gap:4px;padding-right:12px}.Note .group-section.modular-ui .group-child:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);background:var(--faded-component-background);border-radius:4px}.Note .text-button{color:var(--secondary-button-text);display:flex;position:relative;width:auto;height:auto;flex-direction:row-reverse;justify-content:flex-end}.Note .text-button .Icon{color:var(--secondary-button-text);height:18px;width:18px}.Note .group-child{position:relative;width:auto;height:auto;display:block;text-align:left;padding-top:4px;padding-bottom:4px}.Note .group-child:hover{background:var(--view-header-button-hover)}.Note .group-child .NoteContent{padding-bottom:0}.Note:focus{outline:none}.Note.focus-visible,.Note:focus-visible{outline:var(--focus-visible-outline)}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1575:function(t,e,n){"use strict";n(15),n(8),n(56),n(22),n(12),n(13),n(14),n(10),n(9),n(11),n(64),n(65),n(66),n(67),n(37),n(39),n(23),n(24),n(40),n(63),n(16);var o=n(0),r=n.n(o),i=n(6),a=n(3);function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",u=r.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function p(t,e,n,r){var i=e&&e.prototype instanceof h?e:h,a=Object.create(i.prototype),l=new S(r||[]);return o(a,"_invoke",{value:k(t,n,l)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=p;var f={};function h(){}function m(){}function b(){}var y={};s(y,i,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(N([])));v&&v!==e&&n.call(v,i)&&(y=v);var w=b.prototype=h.prototype=Object.create(y);function x(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function q(t,e){var r;o(this,"_invoke",{value:function(o,i){function a(){return new e((function(r,a){!function o(r,i,a,c){var u=d(t[r],t,i);if("throw"!==u.type){var s=u.arg,p=s.value;return p&&"object"==l(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){o("next",t,a,c)}),(function(t){o("throw",t,a,c)})):e.resolve(p).then((function(t){s.value=t,a(s)}),(function(t){return o("throw",t,a,c)}))}c(u.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function k(t,e,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return A()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=E(a,n);if(l){if(l===f)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=d(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function E(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,E(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var r=d(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,f;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function N(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:A}}function A(){return{value:void 0,done:!0}}return m.prototype=b,o(w,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=s(b,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},x(q.prototype),s(q.prototype,a,(function(){return this})),t.AsyncIterator=q,t.async=function(e,n,o,r,i){void 0===i&&(i=Promise);var a=new q(p(e,n,o,r),i);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},x(w),s(w,u,"Generator"),s(w,i,(function(){return this})),s(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=N,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(j),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return a.type="throw",a.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),j(n),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;j(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:N(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},t}function u(t,e,n,o,r,i,a){try{var l=t[i](a),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(o,r)}e.a=function(t){var e=t.annotationId,n=t.addAttachments,o=Object(i.e)((function(t){return a.a.getReplyAttachmentHandler(t)})),l=function(){var t,r=(t=c().mark((function t(r){var i,a,l;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(i=r.target.files[0])){t.next=9;break}if(a=i,!o){t.next=8;break}return t.next=6,o(i);case 6:l=t.sent,a={url:l,name:i.name,size:i.size,type:i.type};case 8:n(e,[a]);case 9:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function a(t){u(i,o,r,a,l,"next",t)}function l(t){u(i,o,r,a,l,"throw",t)}a(void 0)}))});return function(t){return r.apply(this,arguments)}}();return r.a.createElement("input",{id:"reply-attachment-picker",type:"file",style:{display:"none"},onChange:l,onClick:function(t){t.target.value=""}})}},1585:function(t,e,n){"use strict";n(10),n(8),n(141),n(9),n(11),n(127),n(23),n(24),n(28),n(88),n(104),n(97),n(41),n(19),n(12),n(13),n(14),n(16),n(15),n(20),n(18),n(56),n(22),n(64),n(65),n(66),n(67),n(37),n(39),n(40),n(63),n(26),n(27),n(25),n(30),n(45),n(47),n(46);var o=n(0),r=n.n(o),i=n(17),a=n.n(i),l=n(4),c=n.n(l),u=n(6),s=n(429),p=n(1477),d=(n(1590),n(96),n(110),n(49),n(53),n(107),n(78),n(153),n(440),n(1598)),f=n(103),h=n.n(f),m=n(292),b=n.n(m),y=n(220),g=n.n(y),v=n(1591),w=n.n(v),x=n(1499),q=(n(36),n(60),n(44),n(1)),k=n(3),E=n(5),O=n(2),j=(n(1561),n(98));function S(t){return(S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function N(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function A(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?N(Object(n),!0).forEach((function(e){C(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):N(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function C(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==S(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==S(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===S(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function P(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return L(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return L(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var I=function(t,e,n){return{icon:e,label:"action.".concat(t.toLowerCase()),title:"action.".concat(t.toLowerCase()),option:t,dataElement:n}},T=[I("Edit","","notePopupEdit"),I("Delete","","notePopupDelete")],_={handleEdit:c.a.func,handleDelete:c.a.func,isEditable:c.a.bool,isDeletable:c.a.bool,noteId:c.a.string};function R(){}function M(t){var e=t.handleEdit,n=void 0===e?R:e,o=t.handleDelete,i=void 0===o?R:o,l=t.isEditable,c=t.isDeletable,p=t.isReply,d=t.noteId,f=Object(u.e)((function(t){var e;return null===(e=k.a.getFeatureFlags(t))||void 0===e?void 0:e.customizableUI})),h="".concat(E.a.NOTE_POPUP_FLYOUT,"-").concat(d),m=P(Object(s.a)(),1)[0];if(!l&&!c)return null;var b=a()("overflow note-popup-toggle-trigger"),y=a()("NotePopup options note-popup-options",{"options-reply":p,"modular-ui":f});return r.a.createElement("div",{className:y},r.a.createElement(j.a,{dataElement:"notePopup-".concat(d),className:b,img:"icon-tools-more",title:m("formField.formFieldPopup.options"),toggleElement:h,disabled:!1}),r.a.createElement(D,{flyoutSelector:h,handleClick:function(t){"Edit"===t?n():"Delete"===t&&i()},isEditable:l,isDeletable:c}))}var D=function(t){var e=t.flyoutSelector,n=t.handleClick,r=t.isEditable,i=t.isDeletable,a=Object(u.d)(),l=Object(u.e)((function(t){return k.a.getFlyout(t,e)})),c=P(Object(s.a)(),1)[0];return Object(o.useLayoutEffect)((function(){var t=T;r?i||(t=t.filter((function(t){return"Delete"!==t.option}))):t=t.filter((function(t){return"Edit"!==t.option}));var o={dataElement:e,className:"NotePopupFlyout",items:t.map((function(t){return A(A({},t),{},{label:c(t.label),title:c(t.title),onClick:function(){return n(t.option)}})}))};a(l?O.a.updateFlyout(o.dataElement,o):O.a.addFlyout(o))}),[r,i]),null};D.propTypes={flyoutSelector:c.a.string,handleClick:c.a.func,isEditable:c.a.bool,isDeletable:c.a.bool},M.propTypes=_;var F=M;function z(){return(z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}function H(t){return function(t){if(Array.isArray(t))return U(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||B(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||B(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(t,e){if(t){if("string"==typeof t)return U(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?U(t,e):void 0}}function U(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var W=function(t){var e=G(Object(u.e)((function(t){return[k.a.getActiveDocumentViewerKey(t)]})),1)[0],n=t.annotation,o=t.setIsEditing,i=t.noteIndex,a=G(r.a.useState(q.a.canModify(n)),2),l=a[0],c=a[1],s=G(r.a.useState(q.a.canModifyContents(n)),2),p=s[0],d=s[1];r.a.useEffect((function(){function t(){c(q.a.canModify(n,e)),d(q.a.canModifyContents(n,e))}return t(),q.a.addEventListener("updateAnnotationPermission",t,void 0,e),function(){return q.a.removeEventListener("updateAnnotationPermission",t,e)}}),[n,e]);var f={handleEdit:r.a.useCallback((function(){n instanceof window.Core.Annotations.FreeTextAnnotation&&q.a.getAnnotationManager(e).isFreeTextEditingEnabled()?q.a.getAnnotationManager(e).trigger("annotationDoubleClicked",n):o(!0,i)}),[n,o,i]),handleDelete:r.a.useCallback((function(){q.a.deleteAnnotations([n].concat(H(n.getGroupedChildren())),void 0,e)}),[n]),isEditable:p,isDeletable:l&&!(null!=n&&n.NoDelete),noteId:n?n.Id:""};return r.a.createElement(F,z({},t,f))},V=n(1528);function Y(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return K(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return K(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function K(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var $={annotation:c.a.object.isRequired,handleStateChange:c.a.func};function Q(t){var e=t.annotation,n=t.handleStateChange,o=void 0===n?function(){}:n,i=Y(Object(s.a)(),1)[0],a=e.getStatus(),l="icon-annotation-status-".concat(""===a?"none":a.toLowerCase()),c=e.Id;return r.a.createElement(r.a.Fragment,null,r.a.createElement(j.a,{dataElement:"noteState-".concat(c),title:i("option.notesOrder.status"),img:l,toggleElement:"".concat(E.a.NOTE_STATE_FLYOUT,"-").concat(c)}),r.a.createElement(V.a,{noteId:c,handleStateChange:o}))}Q.propTypes=$;var Z=Q,J=n(1525),X=n(172);function tt(){return(tt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}var et={annotation:c.a.object};function nt(t){var e=Object(u.e)((function(t){return k.a.getActiveDocumentViewerKey(t)})),n=Object(u.e)((function(t){return k.a.isElementDisabled(t,"noteState")})),i=t.annotation,a=Object(X.a)(Object(o.useCallback)((function(t){var n=Object(J.a)(i,t,e);i.addReply(n);var o=q.a.getAnnotationManager(e);o.addAnnotation(n),o.trigger("addReply",[n,i,o.getRootAnnotation(i)])}),[i,e]));return!n&&r.a.createElement("div",null,r.a.createElement(Z,tt({handleStateChange:a},t)))}nt.propTypes=et;var ot=nt,rt=n(43),it=n(122);function at(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return lt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return lt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var ct={annotationId:c.a.string,ariaLabel:c.a.string,pendingEditTextMap:c.a.object,pendingReplyMap:c.a.object,pendingAttachmentMap:c.a.object},ut=function(t){var e=t.annotationId,n=t.ariaLabel,i=t.pendingEditTextMap,a=t.pendingReplyMap,l=t.pendingAttachmentMap,c=Object(s.a)().t,u=at(Object(o.useState)(!1),2),p=u[0],d=u[1],f=at(Object(o.useState)(!1),2),h=f[0],m=f[1],b=at(Object(o.useState)(!1),2),y=b[0],g=b[1];return Object(o.useEffect)((function(){var t,n,o;d((null===(t=i[e])||void 0===t?void 0:t.length)>0),m((null===(n=a[e])||void 0===n?void 0:n.length)>0),g((null===(o=l[e])||void 0===o?void 0:o.length)>0)}),[i,a,l]),p||h||y?r.a.createElement("div",{"data-element":"unpostedCommentIndicator"},r.a.createElement(it.a,{content:c("message.unpostedComment")},r.a.createElement("div",null,r.a.createElement(rt.a,{className:"type-icon",glyph:"icon-unposted-comment",ariaLabel:n})))):null};ut.propTypes=ct;var st=ut,pt={annotationId:c.a.string,ariaLabel:c.a.string},dt=function(t){var e=t.annotationId,n=t.ariaLabel,o=Object(u.e)((function(t){return k.a.isElementDisabled(t,"unpostedCommentIndicator")})),i=r.a.useContext(p.a),a=i.pendingEditTextMap,l=i.pendingReplyMap,c=i.pendingAttachmentMap;return o?null:r.a.createElement(st,{annotationId:e,ariaLabel:n,pendingEditTextMap:a,pendingReplyMap:l,pendingAttachmentMap:c})};dt.propTypes=pt;var ft=dt,ht=n(154),mt=n(48),bt=n(218),yt=n(168),gt=n(247),vt=n(173),wt=n(217),xt=n(35),qt=n(100);n(1563);function kt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Et(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Et(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Et(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var Ot={icon:c.a.string,iconColor:c.a.string,color:c.a.string,fillColor:c.a.string,annotation:c.a.object,language:c.a.string,noteDateFormat:c.a.string,isSelected:c.a.bool,setIsEditing:c.a.func,notesShowLastUpdatedDate:c.a.bool,isUnread:c.a.bool,renderAuthorName:c.a.func,isNoteStateDisabled:c.a.bool,isEditing:c.a.bool,noteIndex:c.a.number,sortStrategy:c.a.string,activeTheme:c.a.string,isMultiSelected:c.a.bool,isMultiSelectMode:c.a.bool,handleMultiSelect:c.a.func,isGroupMember:c.a.bool,showAnnotationNumbering:c.a.bool,isTrackedChange:c.a.bool};function jt(t){var e,n,o,i=t.icon,l=t.iconColor,c=t.annotation,u=t.language,p=t.noteDateFormat,d=t.isSelected,f=t.setIsEditing,m=t.notesShowLastUpdatedDate,b=t.isReply,y=t.isUnread,g=t.renderAuthorName,v=t.isNoteStateDisabled,w=t.isEditing,x=t.noteIndex,k=t.sortStrategy,E=t.activeTheme,O=t.isMultiSelected,j=t.isMultiSelectMode,S=t.handleMultiSelect,N=t.isGroupMember,A=t.showAnnotationNumbering,C=t.timezone,P=t.isTrackedChange,L=kt(Object(s.a)(),1)[0],I=k===vt.a.MODIFIED_DATE||m&&k!==vt.a.CREATED_DATE?Object(bt.a)(c):c.DateCreated;if(C&&I){var T=I.toLocaleString("en-US",{timeZone:C});o=new Date(T)}else o=I;var _=o?h()(o).locale(u).format(p):L("option.notesPanel.noteContent.noDate"),R=c.getReplies().length,M=null===(e=c[l])||void 0===e||null===(n=e.toHexString)||void 0===n?void 0:n.call(e);E===wt.a.DARK&&M&&Object(gt.c)(M)?M=qt.b.white:E===wt.a.LIGHT&&M&&Object(gt.d)(M)&&(M=qt.b.black);var D=Object(yt.a)(c.FillColor),F=c.getAssociatedNumber(),z="#".concat(F," - "),H=a()("author-and-date",{isReply:b}),G=a()("NoteHeader",{parent:!b&&!N});return r.a.createElement("div",{className:G},!b&&r.a.createElement("div",{className:"type-icon-container"},y&&r.a.createElement("div",{className:"unread-notification"}),r.a.createElement(rt.a,{className:"type-icon",glyph:i,color:M,fillColor:D})),r.a.createElement("div",{className:H},r.a.createElement("div",{className:"author-and-overflow"},r.a.createElement("div",{className:"author-and-time"},r.a.createElement("div",{className:"author"},A&&void 0!==F&&r.a.createElement("span",{className:"annotation-number"},z),g(c)),r.a.createElement("div",{className:"date-and-num-replies"},r.a.createElement("div",{className:"date-and-time"},_,N&&" (Page ".concat(c.PageNumber,")")),R>0&&!d&&r.a.createElement("div",{className:"num-replies-container"},r.a.createElement(rt.a,{className:"num-reply-icon",glyph:"icon-chat-bubble"}),r.a.createElement("div",{className:"num-replies"},R)))),r.a.createElement("div",{className:"state-and-overflow"},j&&!N&&!b&&r.a.createElement(ht.a,{id:"note-multi-select-toggle_".concat(c.Id),"aria-label":"".concat(g(c)," ").concat(L("option.notesPanel.toggleMultiSelect")),checked:O,onClick:function(t){t.preventDefault(),t.stopPropagation(),S(!O)}}),r.a.createElement(ft,{annotationId:c.Id,ariaLabel:"Unposted Comment, ".concat(g(c),", ").concat(_)}),!v&&!b&&!j&&!N&&!P&&r.a.createElement(ot,{annotation:c,isSelected:d}),!w&&d&&!j&&!N&&!P&&r.a.createElement(W,{noteIndex:x,annotation:c,setIsEditing:f,isReply:b}),d&&P&&!j&&r.a.createElement(r.a.Fragment,null,r.a.createElement(mt.a,{title:L("officeEditor.accept"),img:"icon-menu-checkmark",className:"tracked-change-icon-wrapper accept",onClick:function(){return t=c.getCustomData(xt.o),void q.a.getOfficeEditor().acceptTrackedChange(t);var t},iconClassName:"tracked-change-icon"}),r.a.createElement(mt.a,{title:L("officeEditor.reject"),img:"icon-close",className:"tracked-change-icon-wrapper reject",onClick:function(){return t=c.getCustomData(xt.o),void q.a.getOfficeEditor().rejectTrackedChange(t);var t},iconClassName:"tracked-change-icon"}))))))}jt.propTypes=Ot;var St=jt,Nt=n(1526);function At(){return(At=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}function Ct(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Pt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Pt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var Lt=function(t){var e=Ct(Object(u.e)((function(t){return[k.a.getNotesPanelWidth(t)]}),u.c),1)[0];return r.a.createElement(Nt.a,At({},t,{panelWidth:e}))},It=(n(346),n(347),n(164));n(216);function Tt(t){return(Tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_t=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(t,n,l)}),a}function s(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var p={};function d(){}function f(){}function h(){}var m={};c(m,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(j([])));y&&y!==e&&n.call(y,i)&&(m=y);var g=h.prototype=d.prototype=Object.create(m);function v(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){var r;o(this,"_invoke",{value:function(o,i){function a(){return new e((function(r,a){!function o(r,i,a,l){var c=s(t[r],t,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==Tt(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){o("next",t,a,l)}),(function(t){o("throw",t,a,l)})):e.resolve(p).then((function(t){u.value=t,a(u)}),(function(t){return o("throw",t,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(t,e,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return S()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=q(a,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=s(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function q(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,q(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var r=s(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function j(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:S}}function S(){return{value:void 0,done:!0}}return f.prototype=h,o(g,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:f,configurable:!0}),f.displayName=c(h,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,c(t,l,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},v(w.prototype),c(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,o,r,i){void 0===i&&(i=Promise);var a=new w(u(e,n,o,r),i);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},v(g),c(g,l,"Generator"),c(g,i,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=j,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return a.type="throw",a.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;E(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:j(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}function Rt(t,e,n,o,r,i,a){try{var l=t[i](a),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(o,r)}function Mt(t){return function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function a(t){Rt(i,o,r,a,l,"next",t)}function l(t){Rt(i,o,r,a,l,"throw",t)}a(void 0)}))}}var Dt="ic-file-pdf",Ft="ic-file-img",zt="ic-file-cad",Ht="ic-file-doc",Gt="ic-file-ppt",Bt="ic-file-xls",Ut="ic-file-etc",Wt=window.Core.Annotations.FileAttachmentUtils;function Vt(t){return Yt.apply(this,arguments)}function Yt(){return(Yt=Mt(_t().mark((function t(e){return _t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",Wt.decompressWithFlateDecode(e.content,e.type));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function Kt(t){return $t.apply(this,arguments)}function $t(){return($t=Mt(_t().mark((function t(e){var n,o=arguments;return _t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:[],t.next=3,e.setAttachments(n);case 3:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function Qt(t){return!(!t.type||!t.type.startsWith("image/"))}function Zt(t){var e;if(Qt(t))return Ft;switch(null===(e=t.name)||void 0===e?void 0:e.split(".").pop().toLowerCase()){case"pdf":return Dt;case"cad":return zt;case"doc":case"docx":return Ht;case"ppt":case"pptx":return Gt;case"xls":case"xlsx":return Bt;default:return Ut}}var Jt=n(1593),Xt=n.n(Jt);function te(t){return(te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ee(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ee=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(t,n,l)}),a}function s(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var p={};function d(){}function f(){}function h(){}var m={};c(m,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(j([])));y&&y!==e&&n.call(y,i)&&(m=y);var g=h.prototype=d.prototype=Object.create(m);function v(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){var r;o(this,"_invoke",{value:function(o,i){function a(){return new e((function(r,a){!function o(r,i,a,l){var c=s(t[r],t,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==te(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){o("next",t,a,l)}),(function(t){o("throw",t,a,l)})):e.resolve(p).then((function(t){u.value=t,a(u)}),(function(t){return o("throw",t,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(t,e,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return S()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=q(a,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=s(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function q(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,q(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var r=s(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function j(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:S}}function S(){return{value:void 0,done:!0}}return f.prototype=h,o(g,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:f,configurable:!0}),f.displayName=c(h,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,c(t,l,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},v(w.prototype),c(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,o,r,i){void 0===i&&(i=Promise);var a=new w(u(e,n,o,r),i);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},v(g),c(g,l,"Generator"),c(g,i,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=j,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return a.type="throw",a.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;E(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:j(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}function ne(t,e,n,o,r,i,a){try{var l=t[i](a),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(o,r)}var oe=function(t){return new Promise((function(e){if(void 0===t.size)e(t.toString("utf-8"));else{var n=new FileReader;n.onload=function(){return e(n.result)},n.readAsText(t)}}))},re=function(t){return"image/svg+xml"===t.type},ie=function(){var t,e=(t=ee().mark((function t(e){var n,o,r,i;return ee().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,oe(e);case 2:if(n=t.sent){t.next=5;break}return t.abrupt("return",{svg:e});case 5:return o=[],Xt.a.addHook("uponSanitizeElement",(function(t,e){var n=e.tagName;e.allowedTags[n]||o.push(n)})),r=Xt.a.sanitize(n),i=new Blob([r],{type:"image/svg+xml"}),t.abrupt("return",{svg:i,isDirty:o.length>0});case 10:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function a(t){ne(i,o,r,a,l,"next",t)}function l(t){ne(i,o,r,a,l,"throw",t)}a(void 0)}))});return function(t){return e.apply(this,arguments)}}();n(1565);function ae(t){return(ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function le(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */le=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(t,n,l)}),a}function s(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var p={};function d(){}function f(){}function h(){}var m={};c(m,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(j([])));y&&y!==e&&n.call(y,i)&&(m=y);var g=h.prototype=d.prototype=Object.create(m);function v(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){var r;o(this,"_invoke",{value:function(o,i){function a(){return new e((function(r,a){!function o(r,i,a,l){var c=s(t[r],t,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==ae(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){o("next",t,a,l)}),(function(t){o("throw",t,a,l)})):e.resolve(p).then((function(t){u.value=t,a(u)}),(function(t){return o("throw",t,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(t,e,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return S()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=q(a,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=s(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function q(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,q(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var r=s(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function j(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:S}}function S(){return{value:void 0,done:!0}}return f.prototype=h,o(g,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:f,configurable:!0}),f.displayName=c(h,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,c(t,l,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},v(w.prototype),c(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,o,r,i){void 0===i&&(i=Promise);var a=new w(u(e,n,o,r),i);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},v(g),c(g,l,"Generator"),c(g,i,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=j,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return a.type="throw",a.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;E(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:j(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}function ce(t,e,n,o,r,i,a){try{var l=t[i](a),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(o,r)}function ue(t){return function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function a(t){ce(i,o,r,a,l,"next",t)}function l(t){ce(i,o,r,a,l,"throw",t)}a(void 0)}))}}function se(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return pe(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return pe(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pe(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var de=function(t){var e=t.file,n=se(Object(s.a)(),1)[0],i=se(Object(o.useState)(),2),l=i[0],c=i[1],u=se(Object(o.useState)(!1),2),p=u[0],d=u[1];return Object(o.useEffect)((function(){(function(){var t=ue(le().mark((function t(){var n,o,r,i,a;return le().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(d(!1),n=e,!(o=!(e instanceof File||e.url))){t.next=7;break}return t.next=6,Vt(e);case 6:n=t.sent;case 7:if(!(e instanceof File||o)){t.next=19;break}if(!re(e)){t.next=18;break}return t.next=11,ie(n);case 11:r=t.sent,i=r.svg,a=r.isDirty,c(URL.createObjectURL(i)),d(a),t.next=19;break;case 18:c(URL.createObjectURL(n));case 19:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}})()()}),[e]),r.a.createElement("div",{className:a()({"reply-attachment-preview":!0,dirty:p})},r.a.createElement("img",{src:l}),p&&r.a.createElement("span",{className:"reply-attachment-preview-message"},n("message.svgMalicious")))},fe=function(t){var e=t.files,n=t.isEditing,o=t.fileDeleted,i=se(Object(u.e)((function(t){return[k.a.getTabManager(t),k.a.isReplyAttachmentPreviewEnabled(t)]})),2),a=i[0],l=i[1],c=se(Object(s.a)(),1)[0],p=function(){var t=ue(le().mark((function t(e,n){var o;return le().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.preventDefault(),e.stopPropagation(),a){t.next=4;break}return t.abrupt("return",console.warn("Can't open attachment in non-multi-tab mode"));case 4:if(!(n instanceof File)){t.next=8;break}o=n,t.next=15;break;case 8:if(!n.url){t.next=12;break}o=n.url,t.next=15;break;case 12:return t.next=14,Vt(n);case 14:o=t.sent;case 15:o&&a.addTab(o,{filename:n.name,setActive:!0,saveCurrentActiveTabState:!0});case 16:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),d=function(){var t=ue(le().mark((function t(e,n){var o;return le().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.preventDefault(),e.stopPropagation(),!n.url){t.next=6;break}t.t0=n.url,t.next=9;break;case 6:return t.next=8,Vt(n);case 8:t.t0=t.sent;case 9:o=t.t0,Object(It.saveAs)(o,n.name);case 11:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}();return r.a.createElement("div",{className:"reply-attachment-list"},e.map((function(t,e){return r.a.createElement("div",{className:"reply-attachment",key:e,onClick:function(e){return p(e,t)}},l&&Qt(t)&&r.a.createElement(de,{file:t}),r.a.createElement("div",{className:"reply-attachment-info"},r.a.createElement(rt.a,{className:"reply-attachment-icon",glyph:Zt(t)}),r.a.createElement(it.a,{content:t.name},r.a.createElement("div",{className:"reply-file-name"},t.name)),n?r.a.createElement(mt.a,{className:"attachment-button",title:"".concat(c("action.delete")," ").concat(c("option.type.fileattachment")),img:"icon-close",onClick:function(e){return function(t,e){t.preventDefault(),t.stopPropagation(),o(e)}(e,t)}}):r.a.createElement(mt.a,{className:"attachment-button",title:"".concat(c("action.download")," ").concat(c("option.type.fileattachment")),img:"icon-download",onClick:function(e){return d(e,t)}})))})))},he=n(340),me=n(1495),be=function(t){var e={};if(t["font-weight"]&&"normal"!==t["font-weight"]&&(e.bold=!0),t["font-style"]&&"normal"!==t["font-style"]&&(e.italic=!0),t.color&&(e.color=t.color),t["text-decoration"]){var n=t["text-decoration"].split(" ");n.includes("line-through")&&(e.strike=!0),n.includes("word")&&(e.underline=!0)}return e},ye=function(t,e){for(var n=t.getRichTextStyle(),o=Object.keys(n),r=he.a.getFormattedTextFromDeltas(e.getContents()),i=he.a.extractMentionDataFromStr(r).plainTextValue,a=[],l=0;l<o.length;l++){var c=n[o[l]],u=be(c);if(!isNaN(o[l])){var s=isNaN(o[l+1])?i.length:o[l+1],p=i.slice(o[l],s);a.push({insert:p,attributes:u})}}e.setContents(a),e.setSelection(i.length,0)},ge=n(38),ve=n(50),we=n(215),xe=n(76);n(1567);function qe(t){return(qe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ke(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ke=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(t,n,l)}),a}function s(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var p={};function d(){}function f(){}function h(){}var m={};c(m,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(j([])));y&&y!==e&&n.call(y,i)&&(m=y);var g=h.prototype=d.prototype=Object.create(m);function v(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){var r;o(this,"_invoke",{value:function(o,i){function a(){return new e((function(r,a){!function o(r,i,a,l){var c=s(t[r],t,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==qe(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){o("next",t,a,l)}),(function(t){o("throw",t,a,l)})):e.resolve(p).then((function(t){u.value=t,a(u)}),(function(t){return o("throw",t,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(t,e,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return S()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=q(a,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=s(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function q(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,q(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var r=s(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function j(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:S}}function S(){return{value:void 0,done:!0}}return f.prototype=h,o(g,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:f,configurable:!0}),f.displayName=c(h,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,c(t,l,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},v(w.prototype),c(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,o,r,i){void 0===i&&(i=Promise);var a=new w(u(e,n,o,r),i);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},v(g),c(g,l,"Generator"),c(g,i,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=j,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return a.type="throw",a.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;E(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:j(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}function Ee(t,e,n,o,r,i,a){try{var l=t[i](a),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(o,r)}function Oe(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return je(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return je(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function je(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}h.a.extend(b.a);var Se={annotation:c.a.object.isRequired,isEditing:c.a.bool,setIsEditing:c.a.func,noteIndex:c.a.number,isUnread:c.a.bool,isNonReplyNoteRead:c.a.bool,onReplyClicked:c.a.func,isMultiSelected:c.a.bool,isMultiSelectMode:c.a.bool,handleMultiSelect:c.a.func,isGroupMember:c.a.bool,handleNoteClick:c.a.func},Ne=function(t){var e,n=t.annotation,i=t.isEditing,l=t.setIsEditing,c=t.noteIndex,f=t.isUnread,h=t.isNonReplyNoteRead,m=t.onReplyClicked,b=t.isMultiSelected,y=t.isMultiSelectMode,v=t.handleMultiSelect,x=t.isGroupMember,E=t.handleNoteClick,j=Object(u.e)((function(t){return k.a.getNoteDateFormat(t)})),S=Object(u.e)((function(t){return k.a.getIconColor(t,Object(ve.g)(n),u.c)})),N=Object(u.e)((function(t){return k.a.isElementDisabled(t,"noteStateFlyout")})),A=Object(u.e)((function(t){return k.a.getCurrentLanguage(t)})),C=Object(u.e)((function(t){return k.a.notesShowLastUpdatedDate(t)})),P=Object(u.e)((function(t){return k.a.isNotesPanelTextCollapsingEnabled(t)})),L=Object(u.e)((function(t){return k.a.isNotesPanelRepliesCollapsingEnabled(t)})),I=Object(u.e)((function(t){return k.a.getActiveTheme(t)})),T=Object(u.e)((function(t){return k.a.getTimezone(t)})),_=Object(u.e)((function(t){var e;return null===(e=k.a.getFeatureFlags(t))||void 0===e?void 0:e.customizableUI})),R=Object(o.useContext)(p.a),M=R.isSelected,D=R.searchInput,F=R.resize,z=R.pendingEditTextMap,H=R.onTopNoteContentClicked,G=R.sortStrategy,B=R.showAnnotationNumbering,U=R.setPendingEditText,W=Object(u.d)(),V=Oe(Object(s.a)(),1)[0],Y=n.isReply(),K=Object(ve.g)(n)===ve.c.TRACKED_CHANGE,$=Oe(Object(o.useState)([]),2),Q=$[0],Z=$[1];Object(o.useEffect)((function(){Z(n.getAttachments())}),[n]),Object(o.useEffect)((function(){var t=function(t,e){"modify"===e&&t.forEach((function(t){t.Id===n.Id&&Z(t.getAttachments())}))};return q.a.addEventListener("annotationChanged",t),function(){q.a.removeEventListener("annotationChanged",t)}}),[n]),Object(we.a)((function(){i||W(O.a.finishNoteEditing()),F()}),[i]);var J,X=Object(o.useCallback)((function(t){var e=q.a.getDisplayAuthor(t.Author);return e?Ie(e,D):V("option.notesPanel.noteContent.noName")}),[D]),tt=n.getSkipAutoLink&&n.getSkipAutoLink(),et=Object(o.useCallback)((function(t,e,o,i){var a=[];if(i||d.a.link(t,{stripPrefix:!1,stripTrailingSlash:!1,replaceFn:function(t){var e=t.getAnchorHref(),n=t.getAnchorText(),o=t.getOffset();switch(t.getType()){case"url":case"email":case"phone":a.push({href:e,text:n,start:o,end:o+t.getMatchedText().length})}}}),!a.length){var l=Ie(t,D,e);if(!D&&(!Y&&P||Y&&L)){return r.a.createElement(Lt,{linesToBreak:3,comment:!0,renderRichText:Le,richTextStyle:e,resize:F,style:o,beforeContent:function(){if(!K)return null;var t=1===n.TrackedChangeType?V("officeEditor.added"):V("officeEditor.deleted");return r.a.createElement("span",{style:{color:n.FillColor.toString(),fontWeight:700}},t)}},t)}return l}var c=[],u=0;return a.forEach((function(n,o){var i=n.start,a=n.end,l=n.href;u<i&&c.push(r.a.createElement("span",{key:"span_".concat(o)},Ie(t,D,e,u,i))),c.push(r.a.createElement("a",{href:l,target:"_blank",rel:"noopener noreferrer",key:"a_".concat(o)},Ie(t,D,e,i,a))),u=a})),u<t.length-1&&c.push(Ie(t,D,e,u)),c}),[D]),nt=Object(ve.e)(Object(ve.g)(n)).icon;try{J=JSON.parse(n.getCustomData("trn-mention"))}catch(t){J=n.getCustomData("trn-mention")}var ot=(null===(e=J)||void 0===e?void 0:e.contents)||n.getContents();ot=function(t){return t?w()(t):t}(ot);var rt=n.getContents(),it=n.getRichTextStyle(),at=n.TextColor;if(I===wt.a.DARK)at&&Object(gt.c)(at.toHexString())&&(at=new window.Core.Annotations.Color(255,255,255,1)),it&&Object.keys(it).forEach((function(t){it[t].color&&Object(gt.c)(it[t].color)&&(it[t].color=qt.b.white)}));else if(I===wt.a.LIGHT){if(at&&Object(gt.d)(at.toHexString())&&(at=new window.Core.Annotations.Color(0,0,0,1)),it)Object.keys(it).forEach((function(t){it[t].color&&Object(gt.d)(it[t].color)&&(it[t].color=qt.b.black)}))}var lt,ct=void 0===z[n.Id];lt=ot&&ct?ot:z[n.Id];var ut=function(t){var e;null!==(e=window.getSelection())&&void 0!==e&&e.toString()&&(null==t||t.stopPropagation()),E(t)},st=a()({NoteContent:!0,isReply:Y,unread:f,clicked:h,"modular-ui":_}),pt=Object(o.useMemo)((function(){var t={};return at&&(t.color=at.toHexString()),r.a.createElement(r.a.Fragment,null,i&&M?r.a.createElement(Ce,{annotation:n,noteIndex:c,setIsEditing:l,textAreaValue:lt,onTextAreaValueChange:U,pendingText:z[n.Id]}):rt&&r.a.createElement("div",{className:a()("container",{"reply-content":Y}),onClick:ut},Y&&Q.length>0&&r.a.createElement(fe,{files:Q,isEditing:!1}),et(rt,it,t,tt)))}),[n,M,i,l,ot,et,lt,U,Q]),dt=n.getCustomData("trn-annot-preview"),ft=Object(o.useMemo)((function(){if(""===dt)return null;var t=Ie(dt,D),e=!Y&&P;return g()(t)&&e?r.a.createElement(xe.a,{className:"selected-text-preview",dataElement:"notesSelectedTextPreview"},r.a.createElement(Lt,{linesToBreak:3},'"'.concat(t,'"'))):r.a.createElement("div",{className:"selected-text-preview",style:{paddingRight:"12px"}},t)}),[dt,D]),ht=Object(o.useMemo)((function(){return r.a.createElement(St,{icon:nt,iconColor:S,annotation:n,language:A,noteDateFormat:j,isSelected:M,setIsEditing:l,notesShowLastUpdatedDate:C,isReply:Y,isUnread:f,renderAuthorName:X,isNoteStateDisabled:N,isEditing:i,noteIndex:c,sortStrategy:G,activeTheme:I,handleMultiSelect:v,isMultiSelected:b,isMultiSelectMode:y,isGroupMember:x,showAnnotationNumbering:B,timezone:T,isTrackedChange:K})}),[nt,S,n,A,j,M,l,C,Y,f,X,q.a.getDisplayAuthor(n.Author),N,i,c,Object(bt.a)(n),G,v,b,y,x,T,K]);return r.a.createElement("div",{className:st,onClick:function(){x||(Y?m(n):i||H())}},ht,ft,pt)};Ne.propTypes=Se;var Ae=Ne,Ce=function(t){var e=t.annotation,n=t.noteIndex,i=t.setIsEditing,l=t.textAreaValue,c=t.onTextAreaValueChange,d=t.pendingText,f=Oe(Object(u.e)((function(t){return[k.a.getAutoFocusNoteOnAnnotationSelection(t),k.a.getIsMentionEnabled(t),k.a.isElementDisabled(t,E.a.INLINE_COMMENT_POPUP),k.a.isElementOpen(t,E.a.INLINE_COMMENT_POPUP),k.a.isElementOpen(t,E.a.NOTES_PANEL),k.a.getActiveDocumentViewerKey(t),k.a.isAnyCustomPanelOpen(t)]})),7),h=f[0],m=f[1],b=f[2],y=f[3],g=f[4],v=f[5],w=f[6],O=Oe(Object(s.a)(),1)[0],j=Object(o.useRef)(),S=e.isReply(),N=Object(o.useContext)(p.a),A=N.setCurAnnotId,C=N.pendingAttachmentMap,P=N.deleteAttachment,L=N.clearAttachments,I=N.addAttachments,T=!b&&y&&Object(ge.k)();Object(o.useEffect)((function(){if(w||(g||y)&&j.current){var t=j.current.getEditor();e&&e instanceof window.Core.Annotations.FreeTextAnnotation&&t.setText(""),d?Object(me.a)(t,e):t.getContents()&&setTimeout((function(){if(m){l=he.a.getFormattedTextFromDeltas(t.getContents());var n=he.a.extractMentionDataFromStr(l),o=n.plainTextValue;n.ids.length&&t.setText(o)}var r;T||h&&(null===(r=j.current)||void 0===r||r.focus(),e.getRichTextStyle()&&ye(e,t))}),100);var n=t.getLength()-1;if(T)return;setTimeout((function(){n&&t.setSelection(n,n)}),100)}}),[g,y,T]),Object(o.useEffect)((function(){if(S&&0===M.length){var t=e.getAttachments();I(e.Id,t)}}),[]);var _=function(){var t,o=(t=ke().mark((function t(o){var r,a,u,s,p;return ke().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o.preventDefault(),r=j.current.getEditor(),l=he.a.getFormattedTextFromDeltas(r.getContents()),Object(me.a)(r,e),l.length>1&&"\n"===l[l.length-1]&&(l=l.slice(0,l.length-1)),e.getSkipAutoLink&&e.getSkipAutoLink()&&e.disableSkipAutoLink(),m?(a=he.a.extractMentionDataFromStr(l),u=a.plainTextValue,s=a.ids,he.a.extractMentionDataFromAnnot(e).mentions.forEach((function(t){u.includes(t.value)&&s.push(t.id)})),e.setCustomData("trn-mention",JSON.stringify({contents:l,ids:s})),e.setContents(u)):e.setContents(l),t.next=11,Kt(e,C[e.Id]);case 11:p=e instanceof window.Core.Annotations.FreeTextAnnotation?"textChanged":"noteChanged",q.a.getAnnotationManager(v).trigger("annotationChanged",[[e],"modify",{source:p}]),e instanceof window.Core.Annotations.FreeTextAnnotation&&q.a.drawAnnotationsFromList([e]),i(!1,n),""!==l&&c(void 0,e.Id),L(e.Id);case 17:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function a(t){Ee(i,o,r,a,l,"next",t)}function l(t){Ee(i,o,r,a,l,"throw",t)}a(void 0)}))});return function(t){return o.apply(this,arguments)}}(),R=a()("edit-content",{"reply-content":S}),M=C[e.Id]||[];return r.a.createElement("div",{className:R},S&&M.length>0&&r.a.createElement(fe,{files:M,isEditing:!0,fileDeleted:function(t){return P(e.Id,t)}}),r.a.createElement(x.a,{ref:function(t){j.current=t},value:l,onChange:function(t){return c(t,e.Id)},onSubmit:_,isReply:S,onBlur:function(t){var e,n;null!==(e=t.relatedTarget)&&void 0!==e&&null!==(n=e.getAttribute("data-element"))&&void 0!==n&&n.includes("annotationCommentButton")?t.target.focus():A(void 0)},onFocus:function(){A(e.Id)}}),r.a.createElement("div",{className:"edit-buttons"},r.a.createElement(mt.a,{className:"cancel-button",label:O("action.cancel"),onClick:function(t){t.stopPropagation(),i(!1,n),c(void 0,e.Id),L(e.Id)}}),r.a.createElement(mt.a,{className:"save-button".concat(l?"":" disabled"),disabled:!l,label:O("action.save"),onClick:function(t){t.stopPropagation(),_(t)}})))};Ce.propTypes={noteIndex:c.a.number.isRequired,annotation:c.a.object.isRequired,setIsEditing:c.a.func.isRequired,textAreaValue:c.a.string,onTextAreaValueChange:c.a.func.isRequired,pendingText:c.a.string};var Pe=function(t,e,n){var o={fontWeight:e["font-weight"],fontStyle:e["font-style"],textDecoration:e["text-decoration"],color:e.color};return o.textDecoration&&(o.textDecoration=o.textDecoration.replace("word","underline")),r.a.createElement("span",{style:o,key:n},t)},Le=function(t,e,n){if(!e||!t)return t;for(var o={},r=Object.keys(e).map(Number).sort((function(t,e){return t-e})),i=0;i<r.length;i++){var a=r[i]-n;if(o[a=Math.min(Math.max(a,0),t.length)]=e[r[i]],a===t.length)break}for(var l=[],c=Object.keys(o).map(Number).sort((function(t,e){return t-e})),u=1;u<c.length;u++)l.push(Pe(t.slice(c[u-1],c[u]),o[c[u-1]],"richtext_span_".concat(u)));return l},Ie=function(t,e,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:t.length,a=t.slice(o,i),l=a.toLowerCase(),c=e.toLowerCase();n&&(n[0]=n[0]||{},n[t.length]=n[t.length]||{});var u=l.indexOf(c);if(!c.trim()||-1===u)return Le(a,n,o);var s=[],p=[u],d=c.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");if(new RegExp("(".concat(d,")"),"gi").test(l))for(;-1!==u;)-1!==(u=l.indexOf(c,u+c.length))&&p.push(u);return p.forEach((function(t,e){0===e&&0!==t&&s.push(Le(a.substring(0,t),n,o)),s.push(r.a.createElement("span",{className:"highlight",key:"highlight_span_".concat(e)},Le(a.substring(t,t+c.length),n,o+t))),t+c.length<l.length&&t+c.length!==p[e+1]&&s.push(Le(a.substring(t+c.length,p[e+1]),n,o+t+c.length))})),s};var Te=Ae;n(1569);function _e(t){return(_e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Re(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Re=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(t,n,l)}),a}function s(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var p={};function d(){}function f(){}function h(){}var m={};c(m,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(j([])));y&&y!==e&&n.call(y,i)&&(m=y);var g=h.prototype=d.prototype=Object.create(m);function v(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){var r;o(this,"_invoke",{value:function(o,i){function a(){return new e((function(r,a){!function o(r,i,a,l){var c=s(t[r],t,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==_e(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){o("next",t,a,l)}),(function(t){o("throw",t,a,l)})):e.resolve(p).then((function(t){u.value=t,a(u)}),(function(t){return o("throw",t,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(t,e,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return S()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=q(a,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=s(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function q(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,q(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var r=s(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function j(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:S}}function S(){return{value:void 0,done:!0}}return f.prototype=h,o(g,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:f,configurable:!0}),f.displayName=c(h,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,c(t,l,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},v(w.prototype),c(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,o,r,i){void 0===i&&(i=Promise);var a=new w(u(e,n,o,r),i);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},v(g),c(g,l,"Generator"),c(g,i,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=j,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return a.type="throw",a.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;E(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:j(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}function Me(t,e,n,o,r,i,a){try{var l=t[i](a),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(o,r)}function De(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Fe(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Fe(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fe(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var ze={annotation:c.a.object.isRequired},He=function(t){var e=t.annotation,n=t.isUnread,i=t.onPendingReplyChange,l=De(Object(u.e)((function(t){var n;return[k.a.getAutoFocusNoteOnAnnotationSelection(t),k.a.isDocumentReadOnly(t),k.a.isElementDisabled(t,"noteReply"),null===(n=k.a.getIsReplyDisabled(t))||void 0===n?void 0:n(e),k.a.getIsMentionEnabled(t),k.a.getIsNoteEditing(t),k.a.isElementDisabled(t,E.a.INLINE_COMMENT_POPUP),k.a.isElementOpen(t,E.a.INLINE_COMMENT_POPUP),k.a.getActiveDocumentViewerKey(t)]}),u.c),9),c=l[0],s=l[1],d=l[2],f=l[3],h=l[4],m=l[5],b=l[6],y=l[7],g=l[8],v=Object(o.useContext)(p.a),w=v.isContentEditable,j=v.isSelected,S=v.pendingReplyMap,N=v.setPendingReply,A=v.isExpandedFromSearch,C=v.scrollToSelectedAnnot,P=v.setCurAnnotId,L=v.pendingAttachmentMap,I=v.clearAttachments,T=v.deleteAttachment,_=De(Object(o.useState)(!1),2),R=_[0],M=_[1],D=Object(u.d)(),F=Object(o.useRef)(),z=!b&&y&&Object(ge.k)();Object(we.a)((function(){R||D(O.a.finishNoteEditing())}),[R]),Object(o.useEffect)((function(){z||m&&j&&!w&&c&&F&&F.current&&F.current.focus()}),[w,m,j,z]),Object(o.useEffect)((function(){if(!A&&C&&setTimeout((function(){F&&F.current&&c&&F.current.focus()}),100),F&&F.current){if(z)return;var t=F.current.getEditor().getLength()-1;setTimeout((function(){F.current&&F.current.editor.setSelection(t,t)}),100)}}),[]);var H=function(){var t,n=(t=Re().mark((function t(n){var o,r,i,a;return Re().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n.preventDefault(),n.stopPropagation(),o=F.current.getEditor(),(r=he.a.getFormattedTextFromDeltas(o.getContents())).trim()){t.next=6;break}return t.abrupt("return");case 6:if(!h){t.next=14;break}return i=he.a.createMentionReply(e,r),Object(me.a)(o,i),t.next=11,Kt(i,L[e.Id]);case 11:q.a.addAnnotations([i],g),t.next=19;break;case 14:return a=q.a.createAnnotationReply(e,r),Object(me.a)(o,a),t.next=18,Kt(a,L[e.Id]);case 18:q.a.getAnnotationManager(g).trigger("annotationChanged",[[a],"modify",{}]);case 19:N("",e.Id),I(e.Id);case 21:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function a(t){Me(i,o,r,a,l,"next",t)}function l(t){Me(i,o,r,a,l,"throw",t)}a(void 0)}))});return function(t){return n.apply(this,arguments)}}(),G=s||d||f,B=a()({"reply-area":!0,unread:n}),U=L[e.Id]||[];return G||!j?null:r.a.createElement("form",{onSubmit:H,className:"reply-area-container"},U.length>0&&r.a.createElement(fe,{files:U,isEditing:!0,fileDeleted:function(t){return T(e.Id,t)}}),r.a.createElement("div",{className:"reply-area-with-button"},r.a.createElement("div",{className:B,onMouseDown:function(t){return t.stopPropagation()}},r.a.createElement(x.a,{ref:function(t){F.current=t},value:S[e.Id],onChange:function(t){return function(t){N(t,e.Id),i&&i()}(t)},onSubmit:H,onBlur:function(){M(!1),P(void 0)},onFocus:function(){M(!0),P(e.Id)},isReply:!0})),r.a.createElement("div",{className:"reply-button-container"},r.a.createElement(mt.a,{img:"icon-post-reply",className:"reply-button",title:"action.submit",disabled:!S[e.Id],onClick:H,isSubmitType:!0}))))};He.propTypes=ze;var Ge=He;function Be(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Ue(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ue(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ue(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var We={groupAnnotations:c.a.array.isRequired,isMultiSelectMode:c.a.bool.isRequired},Ve=function(t){var e=t.groupAnnotations,n=t.isMultiSelectMode,i=Be(Object(s.a)(),1)[0],l=Object(u.d)(),c=Be(Object(o.useState)(!1),2),p=c[0],d=c[1],f=Object(u.e)((function(t){var e;return null===(e=k.a.getFeatureFlags(t))||void 0===e?void 0:e.customizableUI})),h=r.a.createElement(mt.a,{onClick:function(t){t.preventDefault(),t.stopPropagation(),d(!0)},className:"text-button",ariaLabel:i("component.noteGroupSection.open"),label:i("component.noteGroupSection.open"),img:"ic_chevron_down_black_24px"}),m=r.a.createElement(mt.a,{onClick:function(t){t.preventDefault(),t.stopPropagation(),d(!1)},className:"text-button",ariaLabel:i("component.noteGroupSection.close"),label:i("component.noteGroupSection.close"),img:"ic_chevron_up_black_24px"});return r.a.createElement("div",{className:a()({"group-section":!0,"modular-ui":f})},p?m:h,p&&e.map((function(t,e){return 0===e?null:r.a.createElement(mt.a,{key:t.Id,className:"group-child",onClick:function(e){e.preventDefault(),e.stopPropagation(),q.a.selectAnnotation(t),q.a.jumpToAnnotation(t),l(O.a.openElement("annotationPopup"))}},r.a.createElement(Te,{key:t.Id,annotation:t,isUnread:!1,isGroupMember:!0,isMultiSelectMode:n}))})))};Ve.propTypes=We;var Ye=Ve,Ke=n(116),$e=n(286),Qe=n(21);n(1571);function Ze(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Je(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Je(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Je(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var Xe=function(t){var e=t.children,n=Object(Qe.a)().querySelector("#line-connector-root"),r=document.createElement("div");return r.setAttribute("data-element",E.a.ANNOTATION_NOTE_CONNECTOR_LINE),Object(o.useEffect)((function(){return n.appendChild(r),function(){return n.removeChild(r)}}),[r,n]),Object(Ke.createPortal)(e,r)},tn=function(t){var e=t.annotation,n=t.noteContainerRef,i=t.isCustomPanelOpen,a=Ze(Object(u.e)((function(t){return[k.a.getNotesPanelWidth(t),k.a.isElementOpen(t,E.a.ANNOTATION_NOTE_CONNECTOR_LINE),k.a.isElementOpen(t,E.a.NOTES_PANEL),k.a.isElementDisabled(t,E.a.ANNOTATION_NOTE_CONNECTOR_LINE),k.a.getDocumentContainerWidth(t),k.a.getDocumentContainerHeight(t),k.a.getActiveDocumentViewerKey(t)]}),u.c),7),l=a[0],c=a[1],s=a[2],p=a[3],d=a[4],f=a[5],h=a[6],m=Object(u.d)(),b=Ze(Object(o.useState)(0),2),y=b[0],g=b[1],v=Ze(Object(o.useState)(0),2),w=v[0],x=v[1],j=Ze(Object(o.useState)(0),2),S=j[0],N=j[1],A=Ze(Object(o.useState)(0),2),C=A[0],P=A[1],L=Ze(Object(o.useState)(0),2),I=L[0],T=L[1],_=Ze(Object(o.useState)(0),2),R=_[0],M=_[1],D=Object($e.d)(e,h),F=D.bottomRight,z=D.topLeft,H=Object(o.useCallback)((function(){return"Note"===e.Subject?4:15}),[e]);if(Object(o.useEffect)((function(){var t=q.a.getScrollViewElement(h),o=t.scrollTop,r=t.scrollLeft;if(!(F&&z))return function(){m(O.a.closeElement(E.a.ANNOTATION_NOTE_CONNECTOR_LINE))};var i=F.x-z.x,a=F.y-z.y,c=window.isApryseWebViewerWebComponent?Object(Qe.a)().host.clientWidth:window.innerWidth,u=window.isApryseWebViewerWebComponent?Object(Qe.a)().host.offsetTop:0;N(l-16),x(n.current.getBoundingClientRect().top-u);var s=c-l-z.x+16+r-i;g(.75*s);var p=e.getNoZoomReferencePoint(),d=e.NoZoom&&p.x?p.x*a:0;P(s-y-H()+d),M(l-16+y);var f=e.NoZoom&&p.y?p.y*a:0;T(z.y+a/2-o-f);var b=function(){m(O.a.closeElement(E.a.ANNOTATION_NOTE_CONNECTOR_LINE))};return q.a.addEventListener("pageNumberUpdated",b,void 0,h),function(){q.a.removeEventListener("pageNumberUpdated",b,h)}}),[n,l,F,z,d,f,m,h]),c&&(s||i)&&!p){var G=Math.abs(w-I),B=w>I?I+2:w;return r.a.createElement(Xe,null,r.a.createElement("div",{className:"horizontalLine",style:{width:y,right:S,top:w}}),r.a.createElement("div",{className:"verticalLine",style:{height:G,top:B,right:S+y}}),r.a.createElement("div",{className:"horizontalLine",style:{width:C,right:R,top:I}},r.a.createElement("div",{className:"arrowHead"})))}return null};n(1573);function en(t){return(en="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function on(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?nn(Object(n),!0).forEach((function(e){rn(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nn(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function rn(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==en(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==en(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===en(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function an(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */an=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(t,n,l)}),a}function s(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var p={};function d(){}function f(){}function h(){}var m={};c(m,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(j([])));y&&y!==e&&n.call(y,i)&&(m=y);var g=h.prototype=d.prototype=Object.create(m);function v(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){var r;o(this,"_invoke",{value:function(o,i){function a(){return new e((function(r,a){!function o(r,i,a,l){var c=s(t[r],t,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==en(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){o("next",t,a,l)}),(function(t){o("throw",t,a,l)})):e.resolve(p).then((function(t){u.value=t,a(u)}),(function(t){return o("throw",t,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(t,e,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return S()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=q(a,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=s(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function q(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,q(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var r=s(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function j(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:S}}function S(){return{value:void 0,done:!0}}return f.prototype=h,o(g,"constructor",{value:h,configurable:!0}),o(h,"constructor",{value:f,configurable:!0}),f.displayName=c(h,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,c(t,l,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},v(w.prototype),c(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,o,r,i){void 0===i&&(i=Promise);var a=new w(u(e,n,o,r),i);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},v(g),c(g,l,"Generator"),c(g,i,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=j,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return a.type="throw",a.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;E(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:j(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}function ln(t,e,n,o,r,i,a){try{var l=t[i](a),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(o,r)}function cn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return un(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return un(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function un(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var sn={annotation:c.a.object.isRequired,isMultiSelected:c.a.bool,isMultiSelectMode:c.a.bool,isInNotesPanel:c.a.bool,handleMultiSelect:c.a.func},pn=0,dn=function(t){var e=t.annotation,n=t.isMultiSelected,i=t.isMultiSelectMode,l=t.isInNotesPanel,c=t.handleMultiSelect,d=t.isCustomPanelOpen,f=t.shouldHideConnectorLine,h=Object(o.useContext)(p.a),m=h.isSelected,b=h.resize,y=h.pendingEditTextMap,g=h.isContentEditable,v=h.isDocumentReadOnly,w=h.isExpandedFromSearch,x=h.setCurAnnotId,j=Object(o.useRef)(),S=Object(o.useRef)(),N=cn(Object(o.useState)({}),2),A=N[0],C=N[1],P=Object(o.useRef)([]),L=Object(u.d)(),I=cn(Object(s.a)(),1)[0],T=new Set,_=cn(Object(u.e)((function(t){return[k.a.getNoteTransformFunction(t),k.a.getCustomNoteSelectionFunction(t),k.a.getUnreadAnnotationIdSet(t),k.a.isCommentThreadExpansionEnabled(t),k.a.isRightClickAnnotationPopupEnabled(t),k.a.getActiveDocumentViewerKey(t),k.a.getIsOfficeEditorMode(t),k.a.getOfficeEditorEditMode(t)]}),u.c),8),R=_[0],M=_[1],D=_[2],F=_[3],z=_[4],H=_[5],G=_[6],B=_[7],U=e.getReplies().sort((function(t,e){return t.DateCreated-e.DateCreated}));U.filter((function(t){return D.has(t.Id)})).forEach((function(t){return T.add(t.Id)})),Object(o.useEffect)((function(){var t=function(t,e){"delete"===e&&t.forEach((function(t){D.has(t.Id)&&L(O.a.setAnnotationReadState({isRead:!0,annotationId:t.Id}))}))};return q.a.addEventListener("annotationChanged",t,void 0,H),function(){q.a.removeEventListener("annotationChanged",t,H)}}),[D]),Object(o.useEffect)((function(){var t=S.current,e=j.current.getBoundingClientRect().height;S.current=e,t&&Math.round(t)!==Math.round(e)&&b()})),Object(o.useEffect)((function(){if(R){var t=Object(Qe.a)().querySelectorAll(".NotesPanel")[0];P.current.forEach((function(e){var n=t.querySelector("[data-webviewer-custom-element=".concat(e,"]"));n&&n.parentNode.removeChild(n)})),P.current=[];var n={annotation:e,isSelected:m};R(j.current,n,(function(){var t,e=(t=document).createElement.apply(t,arguments),n="custom-element-".concat(pn);return pn++,P.current.push(n),e.setAttribute("data-webviewer-custom-element",n),e.addEventListener("mousedown",(function(t){t.stopPropagation()})),e}))}})),Object(o.useEffect)((function(){""!==y[e.Id]&&g&&!v&&J(!0,0)}),[v,g,J,e,i]),Object(we.a)((function(){!v&&g||J(!1,0)}),[v,g,J]);var W=function(){var t,o=(t=an().mark((function t(o){var r;return an().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(o&&o.stopPropagation(),!i){t.next=4;break}return c(!n),t.abrupt("return");case 4:if(D.has(e.Id)&&L(O.a.setAnnotationReadState({isRead:!0,annotationId:e.Id})),M&&M(e),m||(q.a.deselectAllAnnotations(H),setTimeout((function(){return L(O.a.openElement(E.a.ANNOTATION_NOTE_CONNECTOR_LINE))}),300)),!l||G&&B===xt.r.PREVIEW){t.next=17;break}if(q.a.selectAnnotation(e,H),x(e.Id),q.a.jumpToAnnotation(e,H),z||L(O.a.openElement(E.a.ANNOTATION_POPUP)),!G){t.next=17;break}return r=e.getCustomData(xt.o),t.next=16,q.a.getOfficeEditor().moveCursorToTrackedChange(r);case 16:q.a.getOfficeEditor().freezeMainCursor();case 17:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function a(t){ln(i,o,r,a,l,"next",t)}function l(t){ln(i,o,r,a,l,"throw",t)}a(void 0)}))});return function(t){return o.apply(this,arguments)}}(),V=T.size>0,Y=a()({Note:!0,expanded:m,"is-multi-selected":n,unread:D.has(e.Id)||V,disabled:G&&B===xt.r.PREVIEW}),K=a()({replies:!0,hidden:!m});Object(o.useEffect)((function(){i||U.forEach((function(t,e){var n=y[t.Id];""!==n&&void 0!==n&&m&&J(!0,1+e)}))}),[m,i]),Object(o.useEffect)((function(){i&&J(!1,0)}),[i]);var $=!Object.values(A).some((function(t){return t})),Q=function(t){T.has(t.Id)&&(L(O.a.setAnnotationReadState({isRead:!0,annotationId:t.Id})),q.a.getAnnotationManager(H).selectAnnotation(t))},Z=function(){if(T.size>0){var t=U.filter((function(t){return T.has(t.Id)}));q.a.getAnnotationManager(H).selectAnnotations(t),t.forEach((function(t){return L(O.a.setAnnotationReadState({isRead:!0,annotationId:t.Id}))}))}},J=Object(o.useCallback)((function(t,e){C((function(n){return on(on({},n),{},rn({},e,t))}))}),[C]),X=q.a.getGroupAnnotations(e,H),tt=X.length>1,et=Object(ve.g)(e)===ve.c.TRACKED_CHANGE,nt=U.length>0?U[U.length-1].Id:null;return r.a.createElement("div",{ref:j,className:Y,id:"note_".concat(e.Id)},r.a.createElement(mt.a,{className:"note-button",onClick:function(t){return W(t)},ariaLabelledby:"note_".concat(e.Id),ariaCurrent:m,dataElement:"expandNoteButton"}),r.a.createElement(Te,{noteIndex:0,annotation:e,setIsEditing:J,handleNoteClick:W,isEditing:A[0],isNonReplyNoteRead:!D.has(e.Id),isUnread:D.has(e.Id)||V,handleMultiSelect:function(t){x(e.Id),c(t)},isMultiSelected:n,isMultiSelectMode:i}),(m||w||F)&&!et&&r.a.createElement(r.a.Fragment,null,U.length>0&&r.a.createElement("div",{className:K},V&&r.a.createElement(mt.a,{dataElement:"markAllReadButton",className:"mark-all-read-button",label:I("action.markAllRead"),onClick:Z}),U.map((function(t,e){return r.a.createElement("div",{className:"reply",id:"note_reply_".concat(t.Id),key:"note_reply_".concat(t.Id)},r.a.createElement(Te,{noteIndex:e+1,key:t.Id,annotation:t,setIsEditing:J,isEditing:A[e+1],onReplyClicked:Q,isUnread:D.has(t.Id),handleMultiSelect:c,isMultiSelected:n,isMultiSelectMode:i,handleNoteClick:W}))}))),tt&&r.a.createElement(Ye,{groupAnnotations:X,isMultiSelectMode:i}),$&&!i&&r.a.createElement(Ge,{isUnread:nt&&D.has(nt),onPendingReplyChange:Z,annotation:e})),m&&(l||d)&&!f&&r.a.createElement(tn,{annotation:e,noteContainerRef:j,isCustomPanelOpen:d}))};dn.propTypes=sn;var fn=dn;e.a=fn},1833:function(t,e,n){var o=n(32),r=n(1834);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var i={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,i);t.exports=r.locals||{}},1834:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,".open.InlineCommentingPopup{visibility:visible}.closed.InlineCommentingPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.InlineCommentingPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.InlineCommentingPopup:empty{padding:0}.InlineCommentingPopup .buttons{display:flex}.InlineCommentingPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button{width:42px;height:42px}}.InlineCommentingPopup .Button:hover{background:var(--popup-button-hover)}.InlineCommentingPopup .Button:hover:disabled{background:none}.InlineCommentingPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button .Icon{width:24px;height:24px}}.is-vertical.InlineCommentingPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.InlineCommentingPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.InlineCommentingPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.InlineCommentingPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.InlineCommentingPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.InlineCommentingPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.InlineCommentingPopup{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);align-items:flex-start}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup{position:fixed;left:0;bottom:0;z-index:100;flex-direction:column;justify-content:flex-end;width:100%;background:var(--modal-negative-space)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup{position:fixed;left:0;bottom:0;z-index:100;flex-direction:column;justify-content:flex-end;width:100%;background:var(--modal-negative-space)}}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup{overflow:auto;max-height:calc(100% - 100px)}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup{overflow:auto;max-height:calc(100% - 100px)}}.InlineCommentingPopup .inline-comment-container{display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .inline-comment-container{flex-basis:auto;width:100%;max-height:40%;background:var(--component-background);box-shadow:0 0 3px 0 var(--document-box-shadow);border-radius:4px 4px 0 0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .inline-comment-container{flex-basis:auto;width:100%;max-height:40%;background:var(--component-background);box-shadow:0 0 3px 0 var(--document-box-shadow);border-radius:4px 4px 0 0}}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .inline-comment-container{width:260px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .inline-comment-container{width:260px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .inline-comment-container.expanded{flex-grow:1;max-height:90%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .inline-comment-container.expanded{flex-grow:1;max-height:90%}}.InlineCommentingPopup .inline-comment-container .inline-comment-header{flex-grow:0;flex-shrink:0;display:flex;flex-direction:row;align-items:center}.InlineCommentingPopup .inline-comment-container .inline-comment-header .inline-comment-header-title{flex-grow:1;font-size:16px}.InlineCommentingPopup .inline-comment-container .inline-comment-header .Button{margin:4px}.InlineCommentingPopup .Note{border-radius:0;background:none;margin:0;cursor:default}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note{flex-grow:1;display:flex;flex-direction:column;overflow:auto;box-shadow:0 0 3px 0 var(--document-box-shadow)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note{flex-grow:1;display:flex;flex-direction:column;overflow:auto;box-shadow:0 0 3px 0 var(--document-box-shadow)}}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note{box-shadow:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note{box-shadow:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>div:not(:nth-last-child(2)){flex-grow:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>div:not(:nth-last-child(2)){flex-grow:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>div:nth-last-child(2){flex-grow:1}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>div:nth-last-child(2){flex-grow:1}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>.NoteContent:only-child{flex-grow:1}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>.NoteContent:only-child{flex-grow:1}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>.NoteContent:only-child .edit-content{flex-grow:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>.NoteContent:only-child .edit-content{flex-grow:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .NoteHeader{flex-grow:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .NoteHeader{flex-grow:0}}.InlineCommentingPopup .NoteContent .edit-content{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .ql-container,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .ql-editor,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .quill{font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .ql-container,.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .ql-editor,.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .quill{font-size:16px}}.InlineCommentingPopup .Button,.InlineCommentingPopup .Button.add-attachment,.InlineCommentingPopup .Button.reply-button{margin:0}.InlineCommentingPopup .Button.add-attachment .Icon,.InlineCommentingPopup .Button.reply-button .Icon{width:22px;height:22px}.InlineCommentingPopup .Button.add-attachment{width:24px;height:24px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button.add-attachment{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button.add-attachment{width:24px;height:24px}}.InlineCommentingPopup .Button.note-popup-toggle-trigger,.InlineCommentingPopup .Button.reply-button{width:28px;height:28px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button.note-popup-toggle-trigger,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button.reply-button{width:28px;height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button.note-popup-toggle-trigger,.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button.reply-button{width:28px;height:28px}}.sb-show-main .InlineCommentingPopup .quill.comment-textarea{padding:0}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1998:function(t,e,n){"use strict";n.r(e);n(36),n(78),n(126),n(19),n(12),n(13),n(8),n(14),n(10),n(9),n(11),n(16),n(15),n(20),n(18),n(26),n(27),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46);var o=n(0),r=n.n(o),i=n(6),a=n(176),l=n.n(a),c=n(17),u=n.n(c),s=n(4),p=n.n(s),d=n(429),f=n(1477),h=n(1585),m=n(1575),b=n(48),y=n(5),g=n(50);n(1833);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function w(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function x(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?w(Object(n),!0).forEach((function(e){q(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function q(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==v(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==v(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===v(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function k(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return E(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return E(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var O={isMobile:p.a.bool,isUndraggable:p.a.bool,isNotesPanelClosed:p.a.bool,popupRef:p.a.any,position:p.a.object,closeAndReset:p.a.func,commentingAnnotation:p.a.object,contextValue:p.a.object,annotationForAttachment:p.a.string,addAttachments:p.a.func},j=function(t){var e=t.isMobile,n=t.isUndraggable,i=t.isNotesPanelClosed,a=t.popupRef,c=t.position,s=t.closeAndReset,p=t.commentingAnnotation,v=t.contextValue,w=t.annotationForAttachment,q=t.addAttachments,E=k(Object(d.a)(),1)[0],O=k(Object(o.useState)(!1),2),j=O[0],S=O[1],N=Object(g.g)(p)===g.c.TRACKED_CHANGE,A=r.a.createElement("div",{className:u()({Popup:!0,InlineCommentingPopup:!0,open:i,trackedChangePopup:N}),ref:a,"data-element":y.a.INLINE_COMMENT_POPUP,style:x({},c),onMouseMove:function(t){t.stopPropagation()},onMouseDown:function(t){e&&(t.stopPropagation(),s())},onKeyDown:function(t){"Escape"===t.key&&s()}},r.a.createElement("div",{className:u()({"inline-comment-container":!0,expanded:j}),onMouseDown:function(t){e&&t.stopPropagation()}},e&&r.a.createElement("div",{className:"inline-comment-header"},r.a.createElement(b.a,{img:j?"icon-chevron-down":"icon-chevron-up",className:"expand-arrow",dataElement:y.a.INLINE_COMMENT_POPUP_EXPAND_BUTTON,onClick:function(){return S(!j)}}),r.a.createElement("span",{className:"inline-comment-header-title"},E("action.comment")),r.a.createElement(b.a,{img:"icon-close",dataElement:y.a.INLINE_COMMENT_POPUP_CLOSE_BUTTON,onClick:s})),r.a.createElement(f.a.Provider,{value:v},r.a.createElement(h.a,{annotation:p,isMultiSelected:!1,isMultiSelectMode:!1,handleMultiSelect:function(){}}),r.a.createElement(m.a,{annotationId:w,addAttachments:q}))));return n||N?A:r.a.createElement(l.a,{cancel:".Button, .cell, svg, select, button, input, .quill, .note-text-preview"},A)};j.propTypes=O;var S=j,N=n(1),A=n(286),C=n(204),P=n(135),L=n(2),I=n(3),T=n(38),_=n(21),R=n(109),M=n.n(R),D=n(57);function F(t){return(F="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function z(t){return function(t){if(Array.isArray(t))return V(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||W(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function H(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function G(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?H(Object(n),!0).forEach((function(e){B(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):H(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function B(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==F(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==F(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===F(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function U(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){u=!0,r=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw r}}return l}}(t,e)||W(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(t,e){if(t){if("string"==typeof t)return V(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?V(t,e):void 0}}function V(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var Y={annotation:p.a.object,closeAndReset:p.a.func},K=function(t){var e=t.annotation,n=t.closeAndReset,a=U(Object(i.e)((function(t){return[I.a.isElementOpen(t,y.a.NOTES_PANEL),I.a.getNotesInLeftPanel(t),I.a.isElementOpen(t,y.a.LEFT_PANEL),I.a.getActiveLeftPanel(t),I.a.isAnnotationNumberingEnabled(t),I.a.getSortStrategy(t),I.a.isDocumentReadOnly(t),I.a.getActiveDocumentViewerKey(t)]}),i.c),8),l=a[0],c=a[1],u=a[2],s=a[3],p=a[4],d=a[5],f=a[6],h=a[7],m=Object(i.d)(),b=U(Object(o.useState)({left:0,top:0}),2),g=b[0],v=b[1],w=Object(o.useRef)(),x=Object(T.k)(),q=x||!!T.l||T.e,k=l||c&&u&&"notesPanel"===s;Object(P.a)(w,(function(t){var n=Object(_.a)().querySelector('[data-element="notesPanel"]'),o=null==n?void 0:n.contains(t.target),r=Object(_.a)().querySelector('[data-element="noteStateFlyout-'.concat(e.Id,'"]')),i=null==r?void 0:r.contains(t.target),a=Object(C.b)(),l=Object(C.d)(),c=Object(C.c)();o||i||l||c||a||m(L.a.closeElement(y.a.INLINE_COMMENT_POPUP))}));var E=!k,O=function(){E&&w.current&&!x&&v(Object(A.c)(e,w,h))};Object(o.useLayoutEffect)((function(){O()}),[h,e]);var j=M()((function(){O()}),16,{trailing:!0,leading:!1});Object(o.useLayoutEffect)((function(){return window.addEventListener("resize",j),function(){window.removeEventListener("resize",j)}}),[]);var R=U(Object(o.useState)({}),2),F=R[0],H=R[1],W=function(t,e){H((function(n){return G(G({},n),{},B({},t,[].concat(z(n[t]||[]),z(e))))}))},V=U(Object(o.useState)(void 0),2),Y=V[0],K=V[1],$=U(Object(o.useState)({}),2),Q=$[0],Z=$[1],J=Object(o.useCallback)((function(t,e){Z((function(n){return G(G({},n),{},B({},e,t))}))}),[Z]),X=U(Object(o.useState)({}),2),tt=X[0],et=X[1],nt=Object(o.useCallback)((function(t,e){et((function(n){return G(G({},n),{},B({},e,t))}))}),[et]),ot={searchInput:"",resize:function(){var t;(null===(t=N.a.getDocument())||void 0===t?void 0:t.getType())===D.a.OFFICE_EDITOR&&v(Object(A.c)(e,w,h))},isSelected:!0,isContentEditable:N.a.canModifyContents(e)&&!e.getContents(),pendingEditTextMap:Q,setPendingEditText:J,pendingReplyMap:tt,setPendingReply:nt,isDocumentReadOnly:f,onTopNoteContentClicked:function(){},isExpandedFromSearch:!1,scrollToSelectedAnnot:!1,sortStrategy:d,showAnnotationNumbering:p,setCurAnnotId:K,pendingAttachmentMap:F,addAttachments:W,clearAttachments:function(t){H((function(e){return G(G({},e),{},B({},t,[]))}))},deleteAttachment:function(t,e){var n=F[t];if((null==n?void 0:n.length)>0){var o=n.indexOf(e);o>-1&&(n.splice(o,1),H((function(e){return G(G({},e),{},B({},t,z(n)))})))}}};return r.a.createElement(S,{isMobile:x,isUndraggable:q,isNotesPanelClosed:E,popupRef:w,position:g,closeAndReset:n,commentingAnnotation:e,contextValue:ot,annotationForAttachment:Y,addAttachments:W})};K.propTypes=Y;var $=K;e.default=$}}]);
//# sourceMappingURL=chunk.29.js.map