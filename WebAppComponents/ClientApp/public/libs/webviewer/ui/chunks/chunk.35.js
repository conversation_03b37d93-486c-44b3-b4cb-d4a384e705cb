(window.webpackJsonp=window.webpackJsonp||[]).push([[35],{1520:function(e,t,n){"use strict";var r=n(0),o=n.n(r),a=(n(1536),function(e){var t=e.height,n=void 0===t?"50px":t,r=e.width,a={height:n,width:void 0===r?"54px":r};return o.a.createElement("div",{className:"spinner",style:a})});t.a=a},1527:function(e,t,n){"use strict";var r=n(0),o=n.n(r),a=n(4),i=n.n(a),c=(n(1538),{renderContent:i.a.func,children:i.a.node}),l=function(e){var t=e.renderContent?e.renderContent():e.children;return o.a.createElement("h4",{className:"ListSeparator"},t)};l.propTypes=c;var s=o.a.memo(l);t.a=s},1536:function(e,t,n){var r=n(32),o=n(1537);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,a);e.exports=o.locals||{}},1537:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.spinner{border-top:5px solid var(--border);border:5px solid var(--border);border-top-color:var(--focus-border);border-radius:50%;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1538:function(e,t,n){var r=n(32),o=n(1539);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,a);e.exports=o.locals||{}},1539:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ListSeparator{margin-top:16px;margin-bottom:8px;font-family:Lato;font-weight:500;color:var(--list-separator-color);-webkit-user-select:none;-moz-user-select:none;user-select:none}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1692:function(e,t,n){var r=n(32),o=n(1693);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,a);e.exports=o.locals||{}},1693:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SearchResult{background-color:transparent;border:1px solid transparent;display:block;width:calc(100% - 4px);text-align:left;cursor:pointer;border-radius:4px;box-shadow:0 0 3px 0 var(--box-shadow);padding:10px 12px;margin-left:2px;margin-bottom:8px;background:var(--component-background);word-break:break-all}:host(:not([data-tabbing=true])) .SearchResult,html:not([data-tabbing=true]) .SearchResult{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchResult{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchResult{font-size:13px}}.SearchResult .search-value{background:var(--yellow-1);word-break:break-all}.SearchResult.modular-ui.selected,.SearchResult.modular-ui:hover{border:1px solid var(--focus-border)}.SearchResult.modular-ui.selected{background-color:var(--faded-component-background);box-shadow:none}.SearchResult.modular-ui.focus-visible,.SearchResult.modular-ui:focus-visible{outline:var(--focus-visible-outline)}.SearchResult.modular-ui .search-value{font-weight:700;color:var(--blue-5);background:none}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1710:function(e,t,n){var r=n(32),o=n(1711);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,a);e.exports=o.locals||{}},1711:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SearchOverlay.modular-ui .replace-buttons .btn-replace:hover:not(:disabled){background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.SearchOverlay.modular-ui .extra-options .Button:hover,.SearchOverlay.modular-ui .replace-buttons .btn-replace-all:hover:not(:disabled){border:none;background:none;color:var(--blue-6)}.SearchOverlay.modular-ui .extra-options .Button:hover .Icon,.SearchOverlay.modular-ui .replace-buttons .btn-replace-all:hover:not(:disabled) .Icon{color:var(--blue-6)}.SearchOverlay.modular-ui .footer .buttons .button:hover,.SearchOverlay.modular-ui .input-container .clearSearch-button:hover{cursor:pointer;border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.SearchOverlay{position:static;display:flex;flex-direction:column;padding-right:8px;flex-wrap:nowrap;border:0;border-radius:0;background:transparent;visibility:visible!important;flex-grow:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay .search-and-replace-title{margin:8px 0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay .search-and-replace-title{margin:8px 0}}.SearchOverlay .input-container{position:relative;box-sizing:border-box;border:1px solid var(--border);border-radius:4px;height:28px;display:flex;align-items:center;justify-content:flex-end;color:var(--text-color);padding:6px 2px 6px 6px;background:var(--component-background)}.SearchOverlay .input-container input{width:100%;padding-right:26px;height:20px;border:none;background:transparent}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay .input-container input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay .input-container input{font-size:13px}}.SearchOverlay .input-container input::-moz-placeholder{color:var(--placeholder-text)}.SearchOverlay .input-container input::placeholder{color:var(--placeholder-text)}.SearchOverlay .input-container .Icon{width:16px;height:16px}.SearchOverlay .input-container .clearSearch-button{padding:0;border:none;background-color:transparent;cursor:pointer;border-radius:4px;display:flex;align-items:center;justify-content:center;position:absolute;width:24px;height:24px}:host(:not([data-tabbing=true])) .SearchOverlay .input-container .clearSearch-button,html:not([data-tabbing=true]) .SearchOverlay .input-container .clearSearch-button{outline:none}.SearchOverlay .input-container .clearSearch-button svg{color:var(--gray-7)}.SearchOverlay .input-container .clearSearch-button:hover{background:var(--blue-1)}.SearchOverlay .divider{height:1px;width:100%;background:var(--divider);margin:16px 0}.SearchOverlay .options{display:flex;flex-wrap:wrap;margin-top:8px}.SearchOverlay .options>span{margin-right:16px}.SearchOverlay .options>span>label{white-space:nowrap}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay .options>span>label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay .options>span>label{font-size:13px}}.SearchOverlay .replace-options .replace-buttons{display:flex;flex-direction:row;justify-content:flex-end;padding-top:10px}.SearchOverlay .replace-options .replace-buttons .spinner{margin:0;position:absolute;left:30px}.SearchOverlay .replace-options .replace-buttons .btn-replace{display:flex;justify-content:center;align-items:center;background-color:var(--blue-5);border:1px solid var(--blue-5);color:var(--gray-0);padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:32px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay .replace-options .replace-buttons .btn-replace{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay .replace-options .replace-buttons .btn-replace{font-size:13px}}.SearchOverlay .replace-options .replace-buttons .btn-replace-all{display:flex;justify-content:center;align-items:center;color:var(--blue-5);padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;height:32px;cursor:pointer;margin-right:5px}.SearchOverlay .replace-options .replace-buttons .btn-replace-all:hover:not(:disabled){color:var(--blue-6)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay .replace-options .replace-buttons .btn-replace-all{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay .replace-options .replace-buttons .btn-replace-all{font-size:13px}}.SearchOverlay .replace-options .replace-buttons button:disabled{opacity:.5;cursor:default}.SearchOverlay .extra-options{margin-top:8px}.SearchOverlay .extra-options button.Button{display:flex;justify-content:center;align-items:center;color:var(--blue-5);width:auto;cursor:pointer;margin-right:5px;font-weight:500;height:25px}.SearchOverlay .extra-options button.Button .Icon{color:var(--blue-5);width:14px;margin-top:10px;margin-left:6px}.SearchOverlay .footer{display:flex;align-items:center;justify-content:space-between;color:var(--faded-text);margin-bottom:16px}.SearchOverlay .footer .spinner{height:25px!important;width:25px!important;margin:0}.SearchOverlay .footer .buttons{display:flex;flex-direction:row;align-self:flex-end;justify-content:space-evenly;margin-left:auto;width:64px;height:28px}.SearchOverlay .footer .buttons .button{padding:0;border:none;background-color:transparent;width:28px;height:28px;border-radius:4px;display:flex;align-items:center;justify-content:center;cursor:pointer}:host(:not([data-tabbing=true])) .SearchOverlay .footer .buttons .button,html:not([data-tabbing=true]) .SearchOverlay .footer .buttons .button{outline:none}.SearchOverlay .footer .buttons .button svg{color:var(--gray-6)}.SearchOverlay .footer .buttons .button:hover{background-color:var(--blue-1)}.SearchOverlay .footer .buttons .arrow{width:18px;height:18px}.SearchOverlay.modular-ui .input-container[focus-within]{outline:none;border:1px solid var(--focus-border)}.SearchOverlay.modular-ui .input-container:focus-within{outline:none;border:1px solid var(--focus-border)}.SearchOverlay.modular-ui .input-container .search-panel-input{padding-left:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay.modular-ui .input-container .search-panel-input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay.modular-ui .input-container .search-panel-input{font-size:13px}}.SearchOverlay.modular-ui .extra-options .Button:hover .Icon{color:var(--blue-6)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay.modular-ui .extra-options .Button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay.modular-ui .extra-options .Button{font-size:13px}}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1712:function(e,t,n){var r=n(32),o=n(1713);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,a);e.exports=o.locals||{}},1713:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.search-panel-container{z-index:65;flex-direction:column;background-color:var(--panel-background);display:flex;flex-direction:row;position:relative;overflow:hidden}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .search-panel-container{z-index:95}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .search-panel-container{z-index:95}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .search-panel-container{border-left:1px solid var(--side-panel-border)}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .search-panel-container{border-left:1px solid var(--side-panel-border)}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .search-panel-container{position:fixed;top:0;right:0;height:100%;width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .search-panel-container{position:fixed;top:0;right:0;height:100%;width:100%}}.SearchPanel{padding:16px 8px 0 16px;display:flex;flex-direction:column;height:100%}.SearchPanel .ListSeparator:first-child{margin-top:0}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.SearchPanel .ReactVirtualized__Grid__innerScrollContainer{max-width:1000px!important}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel{width:100%;min-width:100%;padding-top:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:28px;margin-bottom:8px;width:100%;padding-right:12px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel .close-container .close-icon-container{outline:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel{width:100%;min-width:100%;padding-top:0}.App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:28px;margin-bottom:8px;width:100%;padding-right:12px}.App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel .close-container .close-icon-container{outline:none}.App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}.SearchPanel .results{overflow-y:auto;overflow-y:overlay;flex:1}.SearchPanel .results.wild-card-visible{margin-top:110px!important}.SearchPanel .results .ReactVirtualized__List{overflow:overlay!important;padding-right:8px;width:auto!important}.SearchPanel .results .ReactVirtualized__List:focus{outline:none}.SearchPanel .results .ReactVirtualized__List .ReactVirtualized__Grid__innerScrollContainer{max-width:unset!important}.SearchPanel .loader-wrapper{display:flex;padding:10px;justify-content:center}.SearchPanel .info{padding:15px 0}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1766:function(e,t,n){"use strict";n.r(t);n(19),n(12),n(13),n(8),n(14),n(10),n(9),n(11),n(16),n(15),n(20),n(18),n(26),n(27),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46);var r=n(0),o=n.n(r),a=n(6),i=n(1),c=n(2),l=n(3),s=n(61),u=n(4),p=n.n(u),d=n(429),h=(n(78),n(60),n(44),n(36),n(192)),m=(n(1692),n(1694)),f=n.n(m),b=n(1706),y=n.n(b),v=n(1527),g=n(17),w=n.n(g),x=["measureRef","contentRect"];function S(){return(S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function O(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function E(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var A={currentResultIndex:p.a.number.isRequired,searchResults:p.a.arrayOf(p.a.object).isRequired,t:p.a.func.isRequired,pageLabels:p.a.arrayOf(p.a.any).isRequired,isProcessingSearchResults:p.a.bool};function k(e){var t=e.currentResultIndex,n=e.searchResults,r=e.t,a=e.pageLabels,i=0===t?t:t-1,c=n[t],l=n[i],s=l===c,u=l.pageNum!==c.pageNum;if(s||u){var p="".concat(r("option.shared.page")," ").concat(a[c.pageNum-1]);return o.a.createElement("div",{role:"cell"},o.a.createElement(v.a,null,p))}return null}k.propTypes=A;var j={result:p.a.object.isRequired,currentResultIndex:p.a.number.isRequired,activeResultIndex:p.a.number.isRequired,onSearchResultClick:p.a.func,activeDocumentViewerKey:p.a.number};function P(e){var t=E(Object(a.e)((function(e){return[e.featureFlags.customizableUI]})),1)[0],n=e.result,r=e.currentResultIndex,i=e.activeResultIndex,c=e.onSearchResultClick,l=e.activeDocumentViewerKey,s=n.ambientStr,u=n.resultStrStart,p=n.resultStrEnd,d=n.resultStr,h=s.slice(0,u),m=""===s?d:s.slice(u,p),f=s.slice(p);return o.a.createElement("button",{role:"cell",className:w()({SearchResult:!0,selected:r===i,"modular-ui":t}),onClick:function(){c&&c(r,n,l)},"aria-current":r===i},h,o.a.createElement("span",{className:"search-value"},m),f)}P.propTypes=j;var C={width:p.a.number,height:p.a.number,activeResultIndex:p.a.number,searchStatus:p.a.oneOf(["SEARCH_NOT_INITIATED","SEARCH_IN_PROGRESS","SEARCH_DONE"]),searchResults:p.a.arrayOf(p.a.object),t:p.a.func.isRequired,onClickResult:p.a.func,pageLabels:p.a.arrayOf(p.a.any),activeDocumentViewerKey:p.a.number};function I(e){var t=e.height,n=e.searchStatus,r=e.searchResults,a=e.activeResultIndex,i=e.t,c=e.onClickResult,l=e.pageLabels,s=e.isProcessingSearchResults,u=e.isSearchInProgress,p=e.activeDocumentViewerKey,d=o.a.useMemo((function(){return new b.CellMeasurerCache({defaultHeight:50,fixedWidth:!0})}),[]),h=o.a.useRef(null),m=E(o.a.useState(0),2),v=m[0],g=m[1];0===r.length&&d.clearAll(),r.length&&r.length!==v&&(g(r.length),d.clearAll());var w=o.a.useCallback((function(e){var t=e.index,n=e.key,s=e.parent,u=e.style,h=r[t];return o.a.createElement(y.a,{cache:d,columnIndex:0,key:n,parent:s,rowIndex:t},(function(e){var n=e.registerChild;return o.a.createElement("div",{role:"row",ref:n,style:u},o.a.createElement(k,{currentResultIndex:t,searchResults:r,pageLabels:l,t:i}),o.a.createElement(P,{result:h,currentResultIndex:t,activeResultIndex:a,onSearchResultClick:c,activeDocumentViewerKey:p}))}))}),[d,r,a,i,l]);return o.a.useEffect((function(){var e;h&&(null===(e=h.current)||void 0===e||e.scrollToRow(a))}),[a]),null==t?null:"SEARCH_DONE"!==n||0!==r.length||s?o.a.createElement(f.a,{width:200,height:t,tabIndex:-1,overscanRowCount:10,rowCount:r.length,deferredMeasurementCache:d,rowHeight:d.rowHeight,rowRenderer:w,ref:h,scrollToIndex:a-1}):u?null:o.a.createElement("div",{className:"info"},o.a.createElement("p",{className:"no-margin"},i("message.noResults")))}function _(e){var t=e.measureRef,n=e.contentRect,r=O(e,x),a=n.bounds.height;return o.a.createElement("div",{className:"results",ref:t},o.a.createElement(I,S({height:a},r)))}I.propTypes=C,_.propTypes={contentRect:p.a.object,measureRef:p.a.oneOfType([p.a.func,p.a.shape({current:p.a.any})])};var N=Object(h.b)("bounds")(_),T=function(e){return o.a.createElement(N,e)},D=(n(440),n(110),n(88),n(56),n(64),n(65),n(66),n(67),n(37),n(39),n(40),n(63),n(1709)),L=n.n(D),W=n(123),H=n.n(W),V=n(43),z=n(288),M=n(1520),q=n(21),G=n(73);n(1710),n(293);function B(e){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function F(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */F=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function s(e,t,n,o){var a=t&&t.prototype instanceof d?t:d,i=Object.create(a.prototype),c=new R(o||[]);return r(i,"_invoke",{value:x(e,n,c)}),i}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var p={};function d(){}function h(){}function m(){}var f={};l(f,a,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(A([])));y&&y!==t&&n.call(y,a)&&(f=y);var v=m.prototype=d.prototype=Object.create(f);function g(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){var o;r(this,"_invoke",{value:function(r,a){function i(){return new t((function(o,i){!function r(o,a,i,c){var l=u(e[o],e,a);if("throw"!==l.type){var s=l.arg,p=s.value;return p&&"object"==B(p)&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(p).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,c)}))}c(l.arg)}(r,a,o,i)}))}return o=o?o.then(i,i):i()}})}function x(e,t,n){var r="suspendedStart";return function(o,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw a;return k()}for(n.method=o,n.arg=a;;){var i=n.delegate;if(i){var c=S(i,n);if(c){if(c===p)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var l=u(e,t,n);if("normal"===l.type){if(r=n.done?"completed":"suspendedYield",l.arg===p)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r="completed",n.method="throw",n.arg=l.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var o=u(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,p;var a=o.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function R(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function A(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:k}}function k(){return{value:void 0,done:!0}}return h.prototype=m,r(v,"constructor",{value:m,configurable:!0}),r(m,"constructor",{value:h,configurable:!0}),h.displayName=l(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,l(e,c,"GeneratorFunction")),e.prototype=Object.create(v),e},e.awrap=function(e){return{__await:e}},g(w.prototype),l(w.prototype,i,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,r,o,a){void 0===a&&(a=Promise);var i=new w(s(t,n,r,o),a);return e.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},g(v),l(v,c,"Generator"),l(v,a,(function(){return this})),l(v,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=A,R.prototype={constructor:R,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return i.type="throw",i.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,p):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:A(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},e}function K(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(r,o)}function U(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){K(a,r,o,i,c,"next",e)}function c(e){K(a,r,o,i,c,"throw",e)}i(void 0)}))}}function $(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Y(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var J={isPanelOpen:p.a.bool,isSearchOverlayDisabled:p.a.bool,searchValue:p.a.string,searchStatus:p.a.oneOf(["SEARCH_NOT_INITIATED","SEARCH_IN_PROGRESS","SEARCH_DONE"]),isCaseSensitive:p.a.bool,isWholeWord:p.a.bool,isWildcard:p.a.bool,searchResults:p.a.arrayOf(p.a.object),activeResultIndex:p.a.number,setSearchValue:p.a.func.isRequired,setCaseSensitive:p.a.func.isRequired,setSearchStatus:p.a.func.isRequired,setWholeWord:p.a.func.isRequired,setWildcard:p.a.func.isRequired,executeSearch:p.a.func.isRequired,selectNextResult:p.a.func,selectPreviousResult:p.a.func,isProcessingSearchResults:p.a.bool,activeDocumentViewerKey:p.a.number};function Q(e){var t=Object(d.a)().t,n=e.isSearchOverlayDisabled,s=e.searchResults,u=e.activeResultIndex,p=e.selectNextResult,h=e.selectPreviousResult,m=e.isProcessingSearchResults,f=e.activeDocumentViewerKey,b=e.searchValue,y=e.setSearchValue,v=e.executeSearch,g=e.replaceValue,x=e.nextResultValue,S=e.setReplaceValue,O=e.isCaseSensitive,E=e.setCaseSensitive,R=e.isWholeWord,A=e.setWholeWord,k=e.isWildcard,j=e.setWildcard,P=e.setSearchStatus,C=e.isSearchInProgress,I=e.setIsSearchInProgress,_=e.searchStatus,N=e.isPanelOpen,T=$(Object(r.useState)(!0),2),D=T[0],W=T[1],B=$(Object(r.useState)(!0),2),K=B[0],Y=B[1],J=$(Object(r.useState)(!0),2),Q=J[0],X=J[1],Z=$(Object(r.useState)(!1),2),ee=Z[0],te=Z[1],ne=$(Object(r.useState)(!0),2),re=ne[0],oe=ne[1],ae=$(Object(r.useState)(!1),2),ie=ae[0],ce=ae[1],le=Object(a.e)((function(e){return l.a.isElementDisabled(e,"searchAndReplace")})),se=Object(a.e)((function(e){var t;return null===(t=l.a.getFeatureFlags(e))||void 0===t?void 0:t.customizableUI})),ue=Object(r.useRef)();Object(r.useEffect)((function(){try{new RegExp("(?<!</?[^>]*|&[^;]*)")}catch(e){oe(!1)}}),[]),Object(r.useEffect)((function(){Re>0&&P("SEARCH_DONE")}),[s]),Object(r.useEffect)((function(){ue.current&&N&&setTimeout((function(){ue.current.focus(),ce(!0)}),300),le||re||!N||console.warn("Search and Replace is not supported in this browser")}),[N,O]),Object(r.useEffect)((function(){b&&b.length>0?ie&&v(b,{caseSensitive:O,wholeWord:R,wildcard:k}):fe()}),[O,R,k,f]),Object(r.useEffect)((function(){return i.a.addEventListener("pagesUpdated",pe),function(){i.a.removeEventListener("pagesUpdated",pe)}}));var pe=function(){de(b)},de=function(){var e=U(F().mark((function e(t){return F().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t&&t.length>0)){e.next=9;break}if(I(!0),P("SEARCH_IN_PROGRESS"),!Object(G.g)()){e.next=6;break}return e.next=6,i.a.getDocument().getOfficeEditor().updateSearchData();case 6:v(t,{caseSensitive:O,wholeWord:R,wildcard:k}),e.next=10;break;case 9:fe();case 10:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),he=Object(r.useCallback)(L()(de,300),[O,R,k]),me=Object(r.useCallback)(H()(de,300),[O,R,k]);Object(r.useEffect)((function(){var e,t=function(){b&&b.length>0&&me(b)};return null===(e=i.a.getDocument())||void 0===e||e.addEventListener("officeDocumentEdited",t),function(){var e;null===(e=i.a.getDocument())||void 0===e||e.removeEventListener("officeDocumentEdited",t)}}),[b]);function fe(){i.a.clearSearchResults(),y(""),P("SEARCH_NOT_INITIATED"),S(""),W(!0),Y(!0)}var be=Object(r.useCallback)((function(e){var t=e.target.checked;E(t)}),[]),ye=Object(r.useCallback)((function(e){var t=e.target.checked;A(t)}),[]),ve=Object(r.useCallback)((function(e){var t=e.target.checked;j(t)}),[]),ge=Object(r.useCallback)((function(){p&&p(s,u)}),[p,s,u]),we=Object(r.useCallback)((function(){h&&h(s,u)}),[h,s,u]),xe=Object(r.useCallback)(function(){var e=U(F().mark((function e(){return F().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!K||!x){e.next=2;break}return e.abrupt("return");case 2:return te(!0),e.next=5,Object(q.c)().instance.Core.ContentEdit.searchAndReplaceText({documentViewer:Object(q.c)().instance.Core.documentViewer,searchResults:i.a.getPageSearchResults(),replaceWith:g});case 5:te(!1);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),[g]),Se=function(){window.localStorage.setItem("searchMoreOption",!Q),X(!Q)},Oe=Object(r.useCallback)(function(){var e=U(F().mark((function e(){return F().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!D||!x){e.next=2;break}return e.abrupt("return");case 2:return te(!0),e.next=5,Object(q.c)().instance.Core.ContentEdit.searchAndReplaceText({documentViewer:Object(q.c)().instance.Core.documentViewer,replaceWith:g,searchResults:[i.a.getActiveSearchResult()]});case 5:te(!1);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),[g,x,D]),Ee=Object(a.d)();if(n)return null;var Re=s?s.length:0,Ae="SEARCH_DONE"===_&&!m,ke=!Ae||C?o.a.createElement(M.a,null):null,je=o.a.createElement("div",{className:"options"},o.a.createElement(z.a,{dataElement:"caseSensitiveSearchOption",id:"case-sensitive-option",checked:O,onChange:be,label:t("option.searchPanel.caseSensitive"),tabIndex:N?0:-1}),o.a.createElement(z.a,{dataElement:"wholeWordSearchOption",id:"whole-word-option",checked:R,onChange:ye,label:t("option.searchPanel.wholeWordOnly"),tabIndex:N?0:-1}),o.a.createElement(z.a,{dataElement:"wildCardSearchOption",id:"wild-card-option",checked:k,onChange:ve,label:t("option.searchPanel.wildcard"),tabIndex:N?0:-1}));return o.a.createElement("div",{className:w()({SearchOverlay:!0,"modular-ui":se})},o.a.createElement("div",{className:"input-container"},se&&o.a.createElement(V.a,{glyph:"icon-header-search"}),o.a.createElement("input",{className:"search-panel-input",ref:ue,type:"text",autoComplete:"off",onChange:function(e){y(e.target.value),he(e.target.value),e.target.value&&g&&(W(!1),Y(!1))},value:b,placeholder:se?"":t("message.searchDocumentPlaceholder"),"aria-label":t("message.searchDocumentPlaceholder"),id:"SearchPanel__input",tabIndex:N?0:-1}),void 0!==b&&b.length>0&&o.a.createElement("button",{className:"clearSearch-button",onClick:fe,"aria-label":t("message.searchDocumentPlaceholder")},o.a.createElement(V.a,{glyph:"icon-close"}))),le||!re?null:Q?o.a.createElement("div",{className:"extra-options"},o.a.createElement("button",{className:"Button",onClick:Se},t("option.searchPanel.lessOptions")," ",o.a.createElement(V.a,{glyph:"icon-chevron-up"}))):o.a.createElement("div",{className:"extra-options"},o.a.createElement("button",{className:"Button",onClick:Se},t("option.searchPanel.moreOptions")," ",o.a.createElement(V.a,{glyph:"icon-chevron-down"}))),Q?o.a.createElement("div",null,je,le||!re?null:o.a.createElement("div",{"data-element":"searchAndReplace",className:"replace-options"},o.a.createElement("p",{className:"search-and-replace-title"},t("option.searchPanel.replace")),o.a.createElement("div",{className:"input-container"},o.a.createElement("input",{type:"text","aria-label":t("option.searchPanel.replace"),onChange:function(e){S(e.target.value),e.target.value&&b&&(W(!1),Y(!1))},value:g})),o.a.createElement("div",{className:"replace-buttons"},ee?o.a.createElement(M.a,{width:25,height:25}):null,o.a.createElement("button",{className:"Button btn-replace-all",disabled:K,onClick:function(){var e=t("option.searchPanel.replaceText"),n={message:t("option.searchPanel.confirmMessageReplaceAll"),title:e,confirmBtnText:t("option.searchPanel.confirm"),onConfirm:function(){xe()}};Ee(c.a.showWarningMessage(n))}},t("option.searchPanel.replaceAll")),o.a.createElement("button",{className:"Button btn-replace",disabled:D||!x||!i.a.getActiveSearchResult(),onClick:function(){var e=t("option.searchPanel.replaceText"),n={message:t("option.searchPanel.confirmMessageReplaceOne"),title:e,confirmBtnText:t("option.searchPanel.confirm"),onConfirm:function(){Oe()}};Ee(c.a.showWarningMessage(n))}},t("option.searchPanel.replace"))))):je,o.a.createElement("div",{className:"divider"}),o.a.createElement("div",{className:"footer"},"SEARCH_NOT_INITIATED"===_?null:ke,o.a.createElement("p",{className:"no-margin","aria-live":"assertive"},Ae&&!C?"".concat(Re," ").concat(t("message.numResultsFound")):void 0),Re>0&&o.a.createElement("div",{className:"buttons"},o.a.createElement("button",{className:"button",onClick:we,"aria-label":t("action.prevResult")},o.a.createElement(V.a,{className:"arrow",glyph:"icon-chevron-left"})),o.a.createElement("button",{className:"button",onClick:ge,"aria-label":t("action.nextResult")},o.a.createElement(V.a,{className:"arrow",glyph:"icon-chevron-right"})))))}Q.propTypes=J;var X=Q,Z=n(159),ee=n(444);function te(e){return(te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ne(){return(ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function re(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function oe(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==te(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==te(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===te(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ae(e,t,n){var r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?re(Object(n),!0).forEach((function(t){oe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):re(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({regex:!1},t);if(null!=e){var o=Object(Z.b)();if(o)o(e,r);else Object(ee.a)(n)(e,r,!1)}}function ie(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;if(e.length>0){var r=t===e.length-1?0:t+1;if(i.a.setActiveSearchResult(e[r]),n){var o=r>0?r-1:0;n(c.a.setNextResultValue(e[r],o))}}}function ce(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;if(e.length>0){var r=t<=0?e.length-1:t-1;i.a.setActiveSearchResult(e[r]),n&&n(c.a.setNextResultValue(e[r]))}}var le=function(e){var t=Object(a.d)(),n=Object(a.f)();return o.a.createElement(X,ne({executeSearch:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ae(e,t,n)},selectNextResult:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0;ie(e,n,t)},selectPreviousResult:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0;ce(e,n,t)}},e))},se=n(5),ue={closeElements:c.a.closeElements,setSearchValue:c.a.setSearchValue,setReplaceValue:c.a.setReplaceValue,setCaseSensitive:c.a.setCaseSensitive,setWholeWord:c.a.setWholeWord,setWildcard:c.a.setWildcard};var pe=Object(a.b)((function(e){return{isSearchOverlayDisabled:l.a.isElementDisabled(e,se.a.SEARCH_OVERLAY),searchValue:l.a.getSearchValue(e),replaceValue:l.a.getReplaceValue(e),nextResultValue:l.a.getNextResultValue(e),isCaseSensitive:l.a.isCaseSensitive(e),isWholeWord:l.a.isWholeWord(e),isWildcard:l.a.isWildcard(e),isProcessingSearchResults:l.a.isProcessingSearchResults(e)}}),ue)((function(e){return o.a.createElement(le,e)})),de=function(e){return o.a.createElement(pe,e)},he=n(1506),me=n(76);n(1712),n(121);function fe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return be(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return be(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function be(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var ye=function(e){var t=fe(o.a.useState([]),2),n=t[0],r=t[1],l=fe(o.a.useState(),2),s=l[0],u=l[1],p=fe(o.a.useState(0),2),d=p[0],h=p[1],m=fe(o.a.useState("SEARCH_NOT_INITIATED"),2),f=m[0],b=m[1],y=Object(a.d)(),v=i.a.getDocumentViewers().length;return o.a.useEffect((function(){var t=i.a.getDocumentViewer(e).getPageSearchResults()||[];if(t.length>0){var n=i.a.getActiveSearchResult();if(n){var o=t.findIndex((function(e){return i.a.isSearchResultEqual(e,n)}));r(t),o>=0&&(u(t[o]),h(o))}else r(t),u(t[0]),h(0)}}),[]),o.a.useEffect((function(){var t=i.a.getDocumentViewer(e);function n(e){var n=(t.getPageSearchResults()||[]).findIndex((function(t){return i.a.isSearchResultEqual(t,e)}));n>=0&&(u(e),h(n))}function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];r(e),e&&0===e.length&&(u(void 0),h(-1))}function a(e){if(null==e)b("SEARCH_NOT_INITIATED");else if(e)b("SEARCH_IN_PROGRESS");else{var n=t.getActiveSearchResult();if(n){u(n);var r=(t.getPageSearchResults()||[]).findIndex((function(e){return i.a.isSearchResultEqual(e,n)}));h(r),y(c.a.setNextResultValue(n))}b("SEARCH_DONE")}}var l=i.a.getDocumentViewers();return l.forEach((function(e){e.addEventListener("activeSearchResultChanged",n),e.addEventListener("searchResultsChanged",o),e.addEventListener("searchInProgress",a)})),function(){l.forEach((function(e){e.removeEventListener("activeSearchResultChanged",n),e.removeEventListener("searchResultsChanged",o),e.removeEventListener("searchInProgress",a)}))}}),[u,h,b,y,v,e]),{searchStatus:f,searchResults:n,activeSearchResult:s,activeSearchResultIndex:d,setSearchStatus:b}};function ve(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ge(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ge(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ge(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var we={isOpen:p.a.bool,isMobile:p.a.bool,pageLabels:p.a.array,currentWidth:p.a.number,closeSearchPanel:p.a.func,setActiveResult:p.a.func,isInDesktopOnlyMode:p.a.bool,isProcessingSearchResults:p.a.bool,activeDocumentViewerKey:p.a.number,isCustomPanel:p.a.bool};function xe(){}function Se(e){var t=e.isOpen,n=e.currentWidth,r=e.pageLabels,a=e.closeSearchPanel,i=void 0===a?xe:a,c=e.setActiveResult,l=void 0===c?xe:c,s=e.setNextResultValue,u=void 0===s?xe:s,p=e.isMobile,h=void 0!==p&&p,m=e.isInDesktopOnlyMode,f=e.isProcessingSearchResults,b=e.activeDocumentViewerKey,y=e.dataElement,v=void 0===y?"searchPanel":y,g=e.isCustomPanel,w=void 0!==g&&g,x=Object(d.a)().t,S=ye(b),O=S.searchStatus,E=S.searchResults,R=S.activeSearchResultIndex,A=S.setSearchStatus,k=o.a.useCallback((function(){i&&i()}),[i]),j=o.a.useCallback((function(e,t,n){l(t,n),!m&&h&&i(),u(t)}),[i,h]),P=ve(o.a.useState(!1),2),C=P[0],I=P[1],_=function(){I(!1)};o.a.useEffect((function(){Object(Z.a)(_)}),[]),o.a.useEffect((function(){return function(){Object(Z.e)(_)}}),[]);var N=Object(he.a)("Panel SearchPanel",{isOpen:t}),D={};return w||!m&&h||(D={width:"".concat(n,"px"),minWidth:"".concat(n,"px")}),o.a.createElement(me.a,{className:N,dataElement:v,style:D},!m&&h&&o.a.createElement("div",{className:"close-container"},o.a.createElement("button",{className:"close-icon-container",onClick:k},o.a.createElement(V.a,{glyph:"ic_close_black_24px",className:"close-icon"}))),o.a.createElement(de,{searchStatus:O,setSearchStatus:A,searchResults:E,activeResultIndex:R,isPanelOpen:t,isSearchInProgress:C,setIsSearchInProgress:I,activeDocumentViewerKey:b}),o.a.createElement(T,{t:x,searchStatus:O,searchResults:E,activeResultIndex:R,onClickResult:j,pageLabels:r,isProcessingSearchResults:f,isSearchInProgress:C,activeDocumentViewerKey:b}))}Se.propTypes=we;var Oe=Se,Ee=n(31);function Re(e){return(Re="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ke(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ae(Object(n),!0).forEach((function(t){je(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ae(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function je(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Re(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Re(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Re(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Pe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ce(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ce(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ce(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ie=function(e){var t=e.dataElement,n=void 0===t?se.a.SEARCH_PANEL:t,r=e.parentDataElement,u=void 0===r?void 0:r,p=Object(s.b)(),d=Pe(Object(a.e)((function(e){return[l.a.isElementOpen(e,n),l.a.getPageLabels(e),l.a.shouldClearSearchPanelOnClose(e),l.a.isInDesktopOnlyMode(e),l.a.isProcessingSearchResults(e),l.a.getActiveDocumentViewerKey(e)]}),a.c),6),h=d[0],m=d[1],f=d[2],b=d[3],y=d[4],v=d[5],g=Object(a.e)((function(e){return u||n!==se.a.SEARCH_PANEL?l.a.getPanelWidth(e,u||n):l.a.getSearchPanelWidth(e)})),w=Object(a.d)(),x=o.a.useCallback((function(){w(c.a.closeElements([n]))}),[w]),S=o.a.useCallback((function(){w(c.a.setSearchValue(""))}),[w]),O=o.a.useCallback((function(e){w(c.a.setNextResultValue(e))}),[w]),E=o.a.useCallback((function(e,t){if(t)return i.a.getDocumentViewer(t).setActiveSearchResult(e)}),[]);o.a.useEffect((function(){!b&&p||!h&&f&&(i.a.clearSearchResults(),S())}),[p,h,f,b]),o.a.useEffect((function(){return function(){!b&&p||f&&(i.a.clearSearchResults(),S())}}),[p,f,b]),n!==se.a.SEARCH_PANEL&&(g-=16,g-=Ee.b);var R=ke(ke({},e),{},{isOpen:h,currentWidth:g,pageLabels:m,closeSearchPanel:x,setActiveResult:E,setNextResultValue:O,isMobile:p,isInDesktopOnlyMode:b,isProcessingSearchResults:y,activeDocumentViewerKey:v});return o.a.createElement(Oe,R)};t.default=Ie}}]);
//# sourceMappingURL=chunk.35.js.map