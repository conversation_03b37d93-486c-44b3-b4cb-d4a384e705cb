{"version": 3, "sources": ["webpack:///./src/ui/src/components/LinkModal/LinkModal.js", "webpack:///./src/ui/src/components/LinkModal/index.js"], "names": ["i", "LinkModal", "rightClickedAnnotation", "setRightClickedAnnotation", "useSelector", "state", "selectors", "isElementDisabled", "DataElements", "LINK_MODAL", "isElementOpen", "getTotalPages", "getCurrentPage", "getSelectedTab", "getPageLabels", "isRightClickAnnotationPopupEnabled", "getActiveDocumentViewerKey", "isDisabled", "isOpen", "totalPages", "currentPage", "tabSelected", "pageLabe<PERSON>", "activeDocumentViewerKey", "selectedTab", "t", "useTranslation", "dispatch", "useDispatch", "urlInput", "React", "createRef", "pageLabelInput", "useState", "url", "setURL", "pageLabel", "setPageLabel", "isRightClickedAnnotationSelected", "core", "isAnnotationSelected", "selectedAnnotations", "getSelectedAnnotations", "annotManager", "getAnnotationManager", "closeModal", "actions", "closeElement", "setToolMode", "defaultTool", "newLink", "x", "y", "width", "height", "linkPageNumber", "link", "window", "Core", "Annotations", "Link", "PageNumber", "StrokeColor", "Color", "StrokeStyle", "StrokeThickness", "Author", "getCurrentUser", "Subject", "X", "Y", "<PERSON><PERSON><PERSON>", "Height", "createLink", "action", "linksResults", "quads", "getSelectedTextQuads", "selectedText", "getSelectedText", "currPageLinks", "currPageNumber", "for<PERSON>ach", "quad", "push", "Math", "min", "x1", "x3", "y1", "y3", "abs", "parseInt", "createHighlightAnnot", "annot", "groupedAnnots", "getGroupAnnotations", "length", "linksToDelete", "filter", "deleteAnnotations", "addAction", "addAnnotations", "groupAnnotations", "linkAnnotArray", "text", "linkAnnot", "highlight", "TextHighlightAnnotation", "Opacity", "Quads", "setContents", "index", "addURLLink", "e", "preventDefault", "urlWithProtocol", "isValidURI", "Actions", "URI", "uri", "pageNumbersToDraw", "map", "Set", "pageNumberToDraw", "drawAnnotations", "undefined", "addPageLink", "options", "dest", "Dest", "GoTo", "page", "indexOf", "useEffect", "urls", "match", "current", "focus", "addEventListener", "removeEventListener", "modalClass", "classNames", "Modal", "open", "closed", "DataElementWrapper", "dataElement", "className", "ModalWrapper", "title", "<PERSON><PERSON><PERSON><PERSON>", "onCloseClick", "swipeToClose", "onMouseDown", "stopPropagation", "Tabs", "id", "onSubmit", "htmlFor", "ref", "value", "onChange", "target", "<PERSON><PERSON>", "label", "onClick", "disabled", "includes"], "mappings": "wpCACA,8lGAAAA,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,IAAAA,IAAA,ygBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAeA,IAsTeC,EAtTG,SAAH,GAA8D,IAAxDC,EAAsB,EAAtBA,uBAAwBC,EAAyB,EAAzBA,0BAqBzC,IAVEC,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUC,kBAAkBF,EAAOG,IAAaC,YAChDH,IAAUI,cAAcL,EAAOG,IAAaC,YAC5CH,IAAUK,cAAcN,GACxBC,IAAUM,eAAeP,GACzBC,IAAUO,eAAeR,EAAOG,IAAaC,YAC7CH,IAAUQ,cAAcT,GACxBC,IAAUS,mCAAmCV,GAC7CC,IAAUU,2BAA2BX,GACrCC,IAAUO,eAAeR,EAAO,iBAChC,GAnBAY,EAAU,KACVC,EAAM,KACNC,EAAU,KACVC,EAAW,KACXC,EAAW,KACXC,EAAU,KACVP,EAAkC,KAClCQ,EAAuB,KACvBC,EAAW,KAYNC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAEXC,EAAWC,IAAMC,YACjBC,EAAiBF,IAAMC,YAEK,IAAZE,mBAAS,IAAG,GAA3BC,EAAG,KAAEC,EAAM,KAC4B,IAAZF,mBAAS,IAAG,GAAvCG,EAAS,KAAEC,EAAY,KACxBC,EAAmCC,IAAKC,qBAAqBtC,EAAwBqB,GACrFkB,EAAsBF,IAAKG,uBAAuBnB,GAClDoB,EAAeJ,IAAKK,qBAAqBrB,GAEzCsB,EAAa,WACjBlB,EAASmB,IAAQC,aAAavC,IAAaC,aAC3C0B,EAAO,IACPI,IAAKS,YAAYC,KACjB9C,EAA0B,OAGtB+C,EAAU,SAACC,EAAGC,EAAGC,EAAOC,GAAyC,IAAjCC,EAAiB,UAAH,6CAAGnC,EAC/CoC,EAAO,IAAIC,OAAOC,KAAKC,YAAYC,KAWzC,OAVAJ,EAAKK,WAAaN,EAClBC,EAAKM,YAAc,IAAIL,OAAOC,KAAKC,YAAYI,MAAM,EAAG,IAAK,KAC7DP,EAAKQ,YAAc,YACnBR,EAAKS,gBAAkB,EACvBT,EAAKU,OAAS3B,IAAK4B,iBACnBX,EAAKY,QAAU,OACfZ,EAAKa,EAAIlB,EACTK,EAAKc,EAAIlB,EACTI,EAAKe,MAAQlB,EACbG,EAAKgB,OAASlB,EACPE,GAGHiB,EAAa,SAACC,GAClB,IAAMC,EAAe,GAEfC,EAAQrC,IAAKsC,qBAAqBtD,GAGxC,GAAIqD,EAAO,CACT,IAAME,EAAevC,IAAKwC,gBAAgBxD,GAAyB,cAEjE,IAAMyD,EAAgB,GACtBJ,EAAMK,GAAgBC,SAAQ,SAACC,GAC7BH,EAAcI,KACZlC,EACEmC,KAAKC,IAAIH,EAAKI,GAAIJ,EAAKK,IACvBH,KAAKC,IAAIH,EAAKM,GAAIN,EAAKO,IACvBL,KAAKM,IAAIR,EAAKI,GAAKJ,EAAKK,IACxBH,KAAKM,IAAIR,EAAKM,GAAKN,EAAKO,IACxBE,SAASX,QAIfY,EACEb,EACAJ,EAAMK,GACNH,EACAJ,GAEFC,EAAaS,KAAI,MAAjBT,EAAqBK,IAnBvB,IAAK,IAAMC,KAAkBL,EAAK,KA6CpC,QAtB+B7D,GAAsCuB,EAAoCG,EAAsB,CAACvC,IAE3GgF,SAAQ,SAACY,GAC5B,GAAKA,EAAL,CAGA,IAAMC,EAAgBpD,EAAaqD,oBAAoBF,GACvD,GAAIC,EAAcE,OAAS,EAAG,CAC5B,IAAMC,EAAgBH,EAAcI,QAAO,SAACL,GAAK,OAAKA,aAAiBrC,OAAOC,KAAKC,YAAYC,QAC3FsC,EAAcD,OAAS,GACzB1D,IAAK6D,kBAAkBF,EAAe3E,GAK1C,IAAMiC,EAAON,EAAQ4C,EAAMzB,EAAGyB,EAAMxB,EAAGwB,EAAMvB,MAAOuB,EAAMtB,QAC1DhB,EAAK6C,UAAU,IAAK3B,GACpBnC,IAAK+D,eAAe,CAAC9C,GAAOjC,GAC5BoD,EAAaS,KAAK5B,GAClBb,EAAa4D,iBAAiBT,EAAO,CAACtC,QAGjCmB,GAGHkB,EAAoB,eA/H5B,EA+H4B,GA/H5B,EA+H4B,UAAG,WAAOW,EAAgB5B,EAAO6B,EAAM/B,GAAM,yEAC/DgC,EAAYF,EAAe,IAC3BG,EAAY,IAAIlD,OAAOC,KAAKC,YAAYiD,yBACpC/C,WAAa6C,EAAU7C,WACjC8C,EAAUtC,EAAIqC,EAAUrC,EACxBsC,EAAUrC,EAAIoC,EAAUpC,EACxBqC,EAAUpC,MAAQmC,EAAUnC,MAC5BoC,EAAUnC,OAASkC,EAAUlC,OAC7BmC,EAAU7C,YAAc,IAAIL,OAAOC,KAAKC,YAAYI,MAAM,EAAG,EAAG,EAAG,GACnE4C,EAAUE,QAAU,EACpBF,EAAUG,MAAQlC,EAClB+B,EAAUzC,OAAS3B,IAAK4B,eAAe5C,GACvCoF,EAAUI,YAAYN,GAEtBD,EAAetB,SAAQ,SAAC1B,EAAMwD,GAC5BxD,EAAK6C,UAAU,IAAK3B,GACV,IAAVsC,EAAczE,IAAK+D,eAAe,CAAC9C,EAAMmD,GAAYpF,GAA2BgB,IAAK+D,eAAe,CAAC9C,GAAOjC,MAE9GoB,EAAa4D,iBAAiBI,EAAWH,EAAgBjF,GAAyB,2CAjJtF,+KAkJG,gBAnByB,4CAqBpB0F,EAAa,SAACC,GAGlB,GAFAA,EAAEC,iBAEGjF,EAAI+D,OAAT,CAIA,IAAImB,EAIFA,EAHG7E,IAAK8E,WAAWnF,GAGDA,EAFA,WAAH,OAAcA,GAK/B,IAAMwC,EAAS,IAAIjB,OAAOC,KAAK4D,QAAQC,IAAI,CAAEC,IAAKJ,IAG9CK,EAFUhD,EAAWC,GAEKgD,KAAI,SAAClE,GAAI,OAAKA,EAAKK,eACjD4D,EAAoB,EAAI,IAAIE,IAAIF,KACdvC,SAAQ,SAAC0C,GACzBrF,IAAKsF,gBAAgBD,EAAkB,MAAM,OAAME,EAAWvG,MAGhEsB,MAOIkF,EAAc,SAACb,GACnBA,EAAEC,iBAEF,IAEMa,EAAU,CAAEC,KAAM,IAAIC,EAFfzE,OAAOC,KAAK4D,QAAQa,KAAKD,MAEL,CAAEE,KAAM9G,EAAW+G,QAAQjG,GAAa,KACnEsC,EAAS,IAAIjB,OAAOC,KAAK4D,QAAQa,KAAKH,GAIxCP,EAFUhD,EAAWC,GAEKgD,KAAI,SAAClE,GAAI,OAAKA,EAAKK,eACjD4D,EAAoB,EAAI,IAAIE,IAAIF,KACdvC,SAAQ,SAAC0C,GACzBrF,IAAKsF,gBAAgBD,EAAkB,MAAM,OAAME,EAAWvG,MAGhEsB,KAGFyF,qBAAU,WACR,GAAIpH,EAAQ,CAEV,IAAM4D,EAAevC,IAAKwC,gBAAgBxD,GAC1C,GAAIuD,EAAc,CAChB,IACMyD,EAAOzD,EAAa0D,MADT,sGAEbD,GAAQA,EAAKtC,OAAS,GACxB9D,EAAOoG,EAAK,IAIhBlG,EAAaf,EAAW2E,OAAS,EAAI3E,EAAW,GAAK,QAEtD,CAACH,EAAYD,IAEhBoH,qBAAU,WACY,0BAAhBjH,GAA2CH,EAC7Cc,EAAeyG,QAAQC,QACE,mBAAhBrH,GAAoCH,GAC7CW,EAAS4G,QAAQC,UAElB,CAACrH,EAAaH,EAAQc,EAAgBH,IAEzCyG,qBAAU,WAER,OADA/F,IAAKoG,iBAAiB,mBAAoB9F,GACnC,WACLN,IAAKqG,oBAAoB,mBAAoB/F,MAE9C,IAEH,IAAMgG,EAAaC,IAAW,CAC5BC,OAAO,EACP9I,WAAW,EACX+I,KAAM9H,EACN+H,QAAS/H,IAGX,OAAOD,EAAa,KAClB,kBAACiI,EAAA,EAAkB,CAACC,YAAa3I,IAAaC,WAAY2I,UAAWP,GACnE,kBAACQ,EAAA,EAAY,CACXC,MAAO7H,EAAE,yBACTP,OAAQA,EACRqI,aAAc1G,EACd2G,aAAc3G,EACd4G,cAAY,GACZ,yBAAKL,UAAU,YAAYM,YAAa,SAACxC,GAAC,OAAKA,EAAEyC,oBAC/C,kBAACC,EAAA,EAAI,CAACC,GAAG,aACP,yBAAKT,UAAU,yBACb,yBAAKA,UAAU,YACb,kBAAC,IAAG,CAACD,YAAY,kBACf,4BAAQC,UAAU,sBAAsB3H,EAAE,cAE5C,yBAAK2H,UAAU,wBACf,kBAAC,IAAG,CAACD,YAAY,yBACf,4BAAQC,UAAU,sBAAsB3H,EAAE,iBAIhD,kBAAC,IAAQ,CAAC0H,YAAY,YACpB,yBAAKC,UAAU,cACb,yBAAKA,UAAU,gBACb,0BAAMU,SAAU7C,GACd,2BAAO8C,QAAQ,WAAWX,UAAU,cAAc3H,EAAE,qBACpD,yBAAK2H,UAAU,aACb,2BACES,GAAG,WACHT,UAAU,WACVY,IAAKnI,EACLoI,MAAO/H,EACPgI,SAAU,SAAChD,GAAC,OAAK/E,EAAO+E,EAAEiD,OAAOF,eAO7C,kBAAC,IAAQ,CAACd,YAAY,mBACpB,yBAAKC,UAAU,cACb,yBAAKA,UAAU,gBACb,0BAAMU,SAAU/B,GACd,2BAAOgC,QAAQ,YAAYX,UAAU,cAAc3H,EAAE,mBACrD,yBAAK2H,UAAU,aACb,2BACES,GAAG,YACHT,UAAU,YACVY,IAAKhI,EACLiI,MAAO7H,EACP8H,SAAU,SAAChD,GAAC,OAAK7E,EAAa6E,EAAEiD,OAAOF,gBAQrD,yBAAKb,UAAU,YACf,yBAAKA,UAAU,UAEM,mBAAhB5H,EAGG,kBAAC4I,EAAA,EAAM,CACLhB,UAAU,YACVD,YAAY,mBACZkB,MAAO5I,EAAE,eACT6I,QAASrD,EACTsD,UAAWrI,EAAI+D,SAKjB,kBAACmE,EAAA,EAAM,CACLhB,UAAU,YACVD,YAAY,mBACZkB,MAAO5I,EAAE,eACT6I,QAASvC,EACTwC,WA3ITjJ,aAAU,EAAVA,EAAYkJ,SAASpI,WC9KjBnC", "file": "chunks/chunk.87.js", "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport defaultTool from 'constants/defaultTool';\nimport core from 'core';\nimport { Tabs, Tab, TabPanel } from 'components/Tabs';\nimport Button from 'components/Button';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport ModalWrapper from 'components/ModalWrapper';\nimport DataElementWrapper from 'components/DataElementWrapper';\n\nimport './LinkModal.scss';\n\nconst LinkModal = ({ rightClickedAnnotation, setRightClickedAnnotation }) => {\n  const [\n    isDisabled,\n    isOpen,\n    totalPages,\n    currentPage,\n    tabSelected,\n    pageLabels,\n    isRightClickAnnotationPopupEnabled,\n    activeDocumentViewerKey,\n    selectedTab,\n  ] = useSelector((state) => [\n    selectors.isElementDisabled(state, DataElements.LINK_MODAL),\n    selectors.isElementOpen(state, DataElements.LINK_MODAL),\n    selectors.getTotalPages(state),\n    selectors.getCurrentPage(state),\n    selectors.getSelectedTab(state, DataElements.LINK_MODAL),\n    selectors.getPageLabels(state),\n    selectors.isRightClickAnnotationPopupEnabled(state),\n    selectors.getActiveDocumentViewerKey(state),\n    selectors.getSelectedTab(state, 'linkModal'),\n  ]);\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const urlInput = React.createRef();\n  const pageLabelInput = React.createRef();\n\n  const [url, setURL] = useState('');\n  const [pageLabel, setPageLabel] = useState('');\n  const isRightClickedAnnotationSelected = core.isAnnotationSelected(rightClickedAnnotation, activeDocumentViewerKey);\n  const selectedAnnotations = core.getSelectedAnnotations(activeDocumentViewerKey);\n  const annotManager = core.getAnnotationManager(activeDocumentViewerKey);\n\n  const closeModal = () => {\n    dispatch(actions.closeElement(DataElements.LINK_MODAL));\n    setURL('');\n    core.setToolMode(defaultTool);\n    setRightClickedAnnotation(null);\n  };\n\n  const newLink = (x, y, width, height, linkPageNumber = currentPage) => {\n    const link = new window.Core.Annotations.Link();\n    link.PageNumber = linkPageNumber;\n    link.StrokeColor = new window.Core.Annotations.Color(0, 165, 228);\n    link.StrokeStyle = 'underline';\n    link.StrokeThickness = 2;\n    link.Author = core.getCurrentUser();\n    link.Subject = 'Link';\n    link.X = x;\n    link.Y = y;\n    link.Width = width;\n    link.Height = height;\n    return link;\n  };\n\n  const createLink = (action) => {\n    const linksResults = [];\n\n    const quads = core.getSelectedTextQuads(activeDocumentViewerKey);\n\n    // If annotation popup is on right click, this won't clear selected text if there's any, adding links will add links for both right-clicked annotation and selected text\n    if (quads) {\n      const selectedText = core.getSelectedText(activeDocumentViewerKey);\n      for (const currPageNumber in quads) {\n        const currPageLinks = [];\n        quads[currPageNumber].forEach((quad) => {\n          currPageLinks.push(\n            newLink(\n              Math.min(quad.x1, quad.x3),\n              Math.min(quad.y1, quad.y3),\n              Math.abs(quad.x1 - quad.x3),\n              Math.abs(quad.y1 - quad.y3),\n              parseInt(currPageNumber)\n            )\n          );\n        });\n        createHighlightAnnot(\n          currPageLinks,\n          quads[currPageNumber],\n          selectedText,\n          action\n        );\n        linksResults.push(...currPageLinks);\n      }\n    }\n\n    const annotationsToAddLink = (!isRightClickAnnotationPopupEnabled || isRightClickedAnnotationSelected) ? selectedAnnotations : [rightClickedAnnotation];\n\n    annotationsToAddLink.forEach((annot) => {\n      if (!annot) {\n        return;\n      }\n      const groupedAnnots = annotManager.getGroupAnnotations(annot);\n      if (groupedAnnots.length > 1) {\n        const linksToDelete = groupedAnnots.filter((annot) => annot instanceof window.Core.Annotations.Link);\n        if (linksToDelete.length > 0) {\n          core.deleteAnnotations(linksToDelete, activeDocumentViewerKey);\n        }\n      }\n\n      // if multi-select an annotation with no link option with an annotation with link option and right click on the latter, link button will show up and will add links for all annotations\n      const link = newLink(annot.X, annot.Y, annot.Width, annot.Height);\n      link.addAction('U', action);\n      core.addAnnotations([link], activeDocumentViewerKey);\n      linksResults.push(link);\n      annotManager.groupAnnotations(annot, [link]);\n    });\n\n    return linksResults;\n  };\n\n  const createHighlightAnnot = async (linkAnnotArray, quads, text, action) => {\n    const linkAnnot = linkAnnotArray[0];\n    const highlight = new window.Core.Annotations.TextHighlightAnnotation();\n    highlight.PageNumber = linkAnnot.PageNumber;\n    highlight.X = linkAnnot.X;\n    highlight.Y = linkAnnot.Y;\n    highlight.Width = linkAnnot.Width;\n    highlight.Height = linkAnnot.Height;\n    highlight.StrokeColor = new window.Core.Annotations.Color(0, 0, 0, 0);\n    highlight.Opacity = 0;\n    highlight.Quads = quads;\n    highlight.Author = core.getCurrentUser(activeDocumentViewerKey);\n    highlight.setContents(text);\n\n    linkAnnotArray.forEach((link, index) => {\n      link.addAction('U', action);\n      index === 0 ? core.addAnnotations([link, highlight], activeDocumentViewerKey) : core.addAnnotations([link], activeDocumentViewerKey);\n    });\n    annotManager.groupAnnotations(highlight, linkAnnotArray, activeDocumentViewerKey);\n  };\n\n  const addURLLink = (e) => {\n    e.preventDefault();\n\n    if (!url.length) {\n      return;\n    }\n\n    let urlWithProtocol;\n    if (!core.isValidURI(url)) {\n      urlWithProtocol = `https://${url}`;\n    } else {\n      urlWithProtocol = url;\n    }\n\n    const action = new window.Core.Actions.URI({ uri: urlWithProtocol });\n    const links = createLink(action);\n\n    let pageNumbersToDraw = links.map((link) => link.PageNumber);\n    pageNumbersToDraw = [...new Set(pageNumbersToDraw)];\n    pageNumbersToDraw.forEach((pageNumberToDraw) => {\n      core.drawAnnotations(pageNumberToDraw, null, true, undefined, activeDocumentViewerKey);\n    });\n\n    closeModal();\n  };\n\n  const isValidPageLabel = () => {\n    return pageLabels?.includes(pageLabel);\n  };\n\n  const addPageLink = (e) => {\n    e.preventDefault();\n\n    const Dest = window.Core.Actions.GoTo.Dest;\n\n    const options = { dest: new Dest({ page: pageLabels.indexOf(pageLabel) + 1 }) };\n    const action = new window.Core.Actions.GoTo(options);\n\n    const links = createLink(action);\n\n    let pageNumbersToDraw = links.map((link) => link.PageNumber);\n    pageNumbersToDraw = [...new Set(pageNumbersToDraw)];\n    pageNumbersToDraw.forEach((pageNumberToDraw) => {\n      core.drawAnnotations(pageNumberToDraw, null, true, undefined, activeDocumentViewerKey);\n    });\n\n    closeModal();\n  };\n\n  useEffect(() => {\n    if (isOpen) {\n      //  prepopulate URL if URL is selected\n      const selectedText = core.getSelectedText(activeDocumentViewerKey);\n      if (selectedText) {\n        const urlRegex = /(http(s)?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)/g;\n        const urls = selectedText.match(urlRegex);\n        if (urls && urls.length > 0) {\n          setURL(urls[0]);\n        }\n      }\n\n      setPageLabel(pageLabels.length > 0 ? pageLabels[0] : '1');\n    }\n  }, [totalPages, isOpen]);\n\n  useEffect(() => {\n    if (tabSelected === 'PageNumberPanelButton' && isOpen) {\n      pageLabelInput.current.focus();\n    } else if (tabSelected === 'URLPanelButton' && isOpen) {\n      urlInput.current.focus();\n    }\n  }, [tabSelected, isOpen, pageLabelInput, urlInput]);\n\n  useEffect(() => {\n    core.addEventListener('documentUnloaded', closeModal);\n    return () => {\n      core.removeEventListener('documentUnloaded', closeModal);\n    };\n  }, []);\n\n  const modalClass = classNames({\n    Modal: true,\n    LinkModal: true,\n    open: isOpen,\n    closed: !isOpen,\n  });\n\n  return isDisabled ? null : (\n    <DataElementWrapper dataElement={DataElements.LINK_MODAL} className={modalClass}>\n      <ModalWrapper\n        title={t('link.insertLinkOrPage')}\n        isOpen={isOpen}\n        closeHandler={closeModal}\n        onCloseClick={closeModal}\n        swipeToClose>\n        <div className=\"container\" onMouseDown={(e) => e.stopPropagation()}>\n          <Tabs id=\"linkModal\">\n            <div className=\"tabs-header-container\">\n              <div className=\"tab-list\">\n                <Tab dataElement=\"URLPanelButton\" >\n                  <button className=\"tab-options-button\">{t('link.url')}</button>\n                </Tab>\n                <div className=\"tab-options-divider\" />\n                <Tab dataElement=\"PageNumberPanelButton\">\n                  <button className=\"tab-options-button\">{t('link.page')}</button>\n                </Tab>\n              </div>\n            </div>\n            <TabPanel dataElement=\"URLPanel\">\n              <div className=\"panel-body\">\n                <div className=\"add-url-link\">\n                  <form onSubmit={addURLLink}>\n                    <label htmlFor=\"urlInput\" className=\"inputLabel\">{t('link.enterUrlAlt')}</label>\n                    <div className=\"linkInput\">\n                      <input\n                        id=\"urlInput\"\n                        className=\"urlInput\"\n                        ref={urlInput}\n                        value={url}\n                        onChange={(e) => setURL(e.target.value)}\n                      />\n                    </div>\n                  </form>\n                </div>\n              </div>\n            </TabPanel>\n            <TabPanel dataElement=\"PageNumberPanel\">\n              <div className=\"panel-body\">\n                <div className=\"add-url-link\">\n                  <form onSubmit={addPageLink}>\n                    <label htmlFor=\"pageInput\" className=\"inputLabel\">{t('link.enterpage')}</label>\n                    <div className=\"linkInput\">\n                      <input\n                        id=\"pageInput\"\n                        className=\"pageInput\"\n                        ref={pageLabelInput}\n                        value={pageLabel}\n                        onChange={(e) => setPageLabel(e.target.value)}\n                      />\n                    </div>\n                  </form>\n                </div>\n              </div>\n            </TabPanel>\n          </Tabs>\n          <div className=\"divider\"></div>\n          <div className=\"footer\">\n            {\n              (selectedTab === 'URLPanelButton')\n                ?\n                (\n                  <Button\n                    className=\"ok-button\"\n                    dataElement=\"linkSubmitButton\"\n                    label={t('action.link')}\n                    onClick={addURLLink}\n                    disabled={!url.length}\n                  />\n                )\n                :\n                (\n                  <Button\n                    className=\"ok-button\"\n                    dataElement=\"linkSubmitButton\"\n                    label={t('action.link')}\n                    onClick={addPageLink}\n                    disabled={!isValidPageLabel()}\n                  />\n                )\n            }\n          </div>\n        </div>\n      </ModalWrapper>\n    </DataElementWrapper>\n  );\n};\n\nexport default LinkModal;\n", "import LinkModal from './LinkModal';\n\nexport default LinkModal;"], "sourceRoot": ""}