(window.webpackJsonp=window.webpackJsonp||[]).push([[66],{1936:function(t,e,o){var r=o(32),n=o(1937);"string"==typeof(n=n.__esModule?n.default:n)&&(n=[[t.i,n,""]]);var a={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,o=document){const r=[];return o.querySelectorAll(e).forEach(t=>r.push(t)),o.querySelectorAll("*").forEach(o=>{o.shadowRoot&&r.push(...t(e,o.shadowRoot))}),r}("apryse-webviewer"));const o=[];for(let r=0;r<e.length;r++){const n=e[r];if(0===r)n.shadowRoot.appendChild(t),t.onload=function(){o.length>0&&o.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);n.shadowRoot.appendChild(e),o.push(e)}}},singleton:!1};r(n,a);t.exports=n.locals||{}},1937:function(t,e,o){(e=t.exports=o(33)(!1)).push([t.i,'.open.HeaderFooterOptionsModal{visibility:visible}.closed.HeaderFooterOptionsModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.HeaderFooterOptionsModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.HeaderFooterOptionsModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.cancel:hover,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.HeaderFooterOptionsModal .footer .modal-button.cancel,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.HeaderFooterOptionsModal .footer .modal-button.cancel.disabled,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.HeaderFooterOptionsModal .footer .modal-button.cancel.disabled span,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.HeaderFooterOptionsModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.HeaderFooterOptionsModal .modal-container .wrapper .modal-content{padding:10px}.HeaderFooterOptionsModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.HeaderFooterOptionsModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.HeaderFooterOptionsModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.HeaderFooterOptionsModal .footer .modal-button.confirm{margin-left:4px}.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .footer .modal-button{padding:23px 8px}}.HeaderFooterOptionsModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .swipe-indicator{width:32px}}.HeaderFooterOptionsModal{flex-direction:column}.HeaderFooterOptionsModal .modal-container{display:flex;flex-direction:column;height:auto;width:480px}.HeaderFooterOptionsModal .modal-container .modal-body{padding:16px;display:flex;flex-direction:column;font-size:var(--font-size-default);font-family:var(--font-family);grid-gap:16px;gap:16px}.HeaderFooterOptionsModal .modal-container .modal-body .title{line-height:16px;font-weight:var(--font-weight-bold)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container{display:flex;flex-direction:column;grid-gap:8px;gap:8px}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .label{color:var(--gray-12);display:block;text-align:left;font-weight:var(--font-weight-normal)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input{border-color:var(--gray-5);position:relative}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input:after{content:"cm";font-size:13px;color:var(--gray-8);position:absolute;right:16px;pointer-events:none}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input.ui__input--focused{box-shadow:none;border-color:var(--focus-border)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input{padding:8px 40px 8px 8px;height:32px;font-size:var(--font-size-default)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input[type=number]{-moz-appearance:textfield}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input::-webkit-inner-spin-button,.HeaderFooterOptionsModal .modal-container .modal-body .input-container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container{display:flex;flex-direction:column;grid-gap:16px;gap:16px;margin:0}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container .ui__choice--checked .ui__choice__input__check{border-color:var(--blue-5)}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container .ui__choice__input__check{border-color:var(--gray-7)}.HeaderFooterOptionsModal .modal-container .footer{padding:16px;display:flex;justify-content:flex-end;border-top:1px solid var(--gray-5)}.HeaderFooterOptionsModal .modal-container .footer button{border:none;border-radius:4px;background:var(--primary-button);min-width:59px;width:auto;padding:8px 16px;height:32px;color:var(--primary-button-text)}.HeaderFooterOptionsModal .modal-container .footer button:hover{background:var(--primary-button-hover)}',""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},2020:function(t,e,o){"use strict";o.r(e);o(18),o(107),o(143),o(343),o(10),o(8),o(56),o(9),o(11),o(88),o(19),o(12),o(13),o(14),o(16),o(15),o(20),o(22),o(64),o(65),o(66),o(67),o(37),o(39),o(23),o(24),o(40),o(63);var r=o(0),n=o.n(r),a=o(6),i=o(429),d=o(3),l=o(2),c=o(17),s=o.n(c),p=o(5),u=o(48),f=o(341),h=o(1966),m=o(1313),y=o(1),b=o(35),v=o(1754);o(1936);function g(t){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function x(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */x=function(){return t};var t={},e=Object.prototype,o=e.hasOwnProperty,r=Object.defineProperty||function(t,e,o){t[e]=o.value},n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",d=n.toStringTag||"@@toStringTag";function l(t,e,o){return Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,o){return t[e]=o}}function c(t,e,o,n){var a=e&&e.prototype instanceof u?e:u,i=Object.create(a.prototype),d=new _(n||[]);return r(i,"_invoke",{value:E(t,o,d)}),i}function s(t,e,o){try{return{type:"normal",arg:t.call(e,o)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var p={};function u(){}function f(){}function h(){}var m={};l(m,a,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==e&&o.call(b,a)&&(m=b);var v=h.prototype=u.prototype=Object.create(m);function w(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){var n;r(this,"_invoke",{value:function(r,a){function i(){return new e((function(n,i){!function r(n,a,i,d){var l=s(t[n],t,a);if("throw"!==l.type){var c=l.arg,p=c.value;return p&&"object"==g(p)&&o.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,i,d)}),(function(t){r("throw",t,i,d)})):e.resolve(p).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,d)}))}d(l.arg)}(r,a,n,i)}))}return n=n?n.then(i,i):i()}})}function E(t,e,o){var r="suspendedStart";return function(n,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===n)throw a;return T()}for(o.method=n,o.arg=a;;){var i=o.delegate;if(i){var d=F(i,o);if(d){if(d===p)continue;return d}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if("suspendedStart"===r)throw r="completed",o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r="executing";var l=s(t,e,o);if("normal"===l.type){if(r=o.done?"completed":"suspendedYield",l.arg===p)continue;return{value:l.arg,done:o.done}}"throw"===l.type&&(r="completed",o.method="throw",o.arg=l.arg)}}}function F(t,e){var o=e.method,r=t.iterator[o];if(void 0===r)return e.delegate=null,"throw"===o&&t.iterator.return&&(e.method="return",e.arg=void 0,F(t,e),"throw"===e.method)||"return"!==o&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+o+"' method")),p;var n=s(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,p;var a=n.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function M(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function H(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function _(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(M,this),this.reset(!0)}function k(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function e(){for(;++r<t.length;)if(o.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:T}}function T(){return{value:void 0,done:!0}}return f.prototype=h,r(v,"constructor",{value:h,configurable:!0}),r(h,"constructor",{value:f,configurable:!0}),f.displayName=l(h,d,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,l(t,d,"GeneratorFunction")),t.prototype=Object.create(v),t},t.awrap=function(t){return{__await:t}},w(O.prototype),l(O.prototype,i,(function(){return this})),t.AsyncIterator=O,t.async=function(e,o,r,n,a){void 0===a&&(a=Promise);var i=new O(c(e,o,r,n),a);return t.isGeneratorFunction(o)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},w(v),l(v,d,"Generator"),l(v,a,(function(){return this})),l(v,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),o=[];for(var r in e)o.push(r);return o.reverse(),function t(){for(;o.length;){var r=o.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=k,_.prototype={constructor:_,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(H),!t)for(var e in this)"t"===e.charAt(0)&&o.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(o,r){return i.type="throw",i.arg=t,e.next=o,r&&(e.method="next",e.arg=void 0),!!r}for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n],i=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var d=o.call(a,"catchLoc"),l=o.call(a,"finallyLoc");if(d&&l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(d){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,p):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.finallyLoc===t)return this.complete(o.completion,o.afterLoc),H(o),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc===t){var r=o.completion;if("throw"===r.type){var n=r.arg;H(o)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,o){return this.delegate={iterator:k(t),resultName:e,nextLoc:o},"next"===this.method&&(this.arg=void 0),p}},t}function w(t,e,o,r,n,a,i){try{var d=t[a](i),l=d.value}catch(t){return void o(t)}d.done?e(l):Promise.resolve(l).then(r,n)}function O(t){return function(){var e=this,o=arguments;return new Promise((function(r,n){var a=t.apply(e,o);function i(t){w(a,r,n,i,d,"next",t)}function d(t){w(a,r,n,i,d,"throw",t)}i(void 0)}))}}function E(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var r,n,a,i,d=[],l=!0,c=!1;try{if(a=(o=o.call(t)).next,0===e){if(Object(o)!==o)return;l=!1}else for(;!(l=(r=a.call(o)).done)&&(d.push(r.value),d.length!==e);l=!0);}catch(t){c=!0,n=t}finally{try{if(!l&&null!=o.return&&(i=o.return(),Object(i)!==i))return}finally{if(c)throw n}}return d}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return F(t,e);var o=Object.prototype.toString.call(t).slice(8,-1);"Object"===o&&t.constructor&&(o=t.constructor.name);if("Map"===o||"Set"===o)return Array.from(t);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return F(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(t,e){(null==e||e>t.length)&&(e=t.length);for(var o=0,r=new Array(e);o<e;o++)r[o]=t[o];return r}var M=function(){var t=E(Object(i.a)(),1)[0],e=Object(a.d)(),o=Object(a.e)((function(t){return d.a.isElementOpen(t,p.a.HEADER_FOOTER_OPTIONS_MODAL)})),c=E(Object(r.useState)(""),2),g=c[0],w=c[1],F=E(Object(r.useState)(""),2),M=F[0],H=F[1],_=E(Object(r.useState)(""),2),k=_[0],T=_[1],L=E(Object(r.useState)(""),2),j=L[0],A=L[1],S=E(Object(r.useState)(!1),2),N=S[0],I=S[1],P=E(Object(r.useState)(!1),2),D=P[0],R=P[1],C=E(Object(r.useState)(0),2),z=C[0],G=C[1],B=E(Object(r.useState)("cm"),2),W=B[0],q=B[1],J=function(t){if(t&&t<=0)return"0";var e=t.replace(/^0+(?!\.)/,""),o=z;return"cm"===W&&(o=z*b.c),parseFloat(e)>o?o.toFixed(2):e},Y=function(t){var e=t;return"cm"===W&&(e=(parseFloat(t)*b.c).toFixed(2)),e},U=function(t){var e=parseFloat(t);return"cm"===W&&(e/=b.c),e},V=function(){var t=O(x().mark((function t(){var o,r,n,a;return x().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e(l.a.closeElement(p.a.HEADER_FOOTER_OPTIONS_MODAL)),o=U(k),r=U(j),n=v.a.getPageNumber(),a=g!==k||M!==j,t.abrupt("return",Promise.all([y.a.getOfficeEditor().setDifferentFirstPage(n,N),y.a.getOfficeEditor().setOddEven(D),a&&y.a.getOfficeEditor().setHeaderFooterMarginsInInches(n,o,r)]));case 6:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();Object(r.useEffect)(O(x().mark((function t(){var e,r,n,a,i,d,l;return x().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!o){t.next=28;break}return q("cm"),e=v.a.getPageNumber(),t.next=5,y.a.getOfficeEditor().getHeaderFooterMarginsInInches(e);case 5:return r=t.sent,n=r.headerDistanceToTop,a=r.footerDistanceToBottom,i=Y(n),d=Y(a),T(i),A(d),w(i),H(d),t.t0=I,t.next=17,y.a.getOfficeEditor().getDifferentFirstPage(e);case 17:return t.t1=t.sent,(0,t.t0)(t.t1),t.t2=R,t.next=22,y.a.getOfficeEditor().getOddEven();case 22:return t.t3=t.sent,(0,t.t2)(t.t3),t.next=26,y.a.getOfficeEditor().getMaxHeaderFooterDistance(e);case 26:l=t.sent,G(l);case 28:case"end":return t.stop()}}),t)}))),[o]);var $=function(){e(l.a.closeElement(p.a.HEADER_FOOTER_OPTIONS_MODAL)),setTimeout((function(){y.a.getOfficeEditor().focusContent()}),0)},K=s()({HeaderFooterOptionsModal:!0});return o&&n.a.createElement("div",{className:K,"data-element":p.a.HEADER_FOOTER_OPTIONS_MODAL},n.a.createElement(f.a,{isOpen:o,title:t("officeEditor.headerFooterOptionsModal.title"),closehandler:$,onCloseClick:$,swipeToClose:!0},n.a.createElement("div",{className:"modal-body"},n.a.createElement("div",{className:"title"},t("officeEditor.headerFooterOptionsModal.margins")),n.a.createElement("div",{className:"input-container"},n.a.createElement("label",{htmlFor:"headerToTopInput",className:"label"},t("officeEditor.headerFooterOptionsModal.headerFromTop")),n.a.createElement(h.a,{type:"number",id:"headerToTopInput","data-testid":"headerToTopInput",onChange:function(t){var e=J(t.target.value);T(e)},onBlur:function(t){""===t.target.value&&T("0")},value:k,min:"0",step:"any"})),n.a.createElement("div",{className:"input-container"},n.a.createElement("label",{htmlFor:"footerToBottomInput",className:"label"},t("officeEditor.headerFooterOptionsModal.footerFromBottom")),n.a.createElement(h.a,{type:"number",id:"footerToBottomInput","data-testid":"footerToBottomInput",onChange:function(t){var e=J(t.target.value);A(e)},onBlur:function(t){""===t.target.value&&A("0")},value:j,min:"0",step:"any"})),n.a.createElement("div",{className:"title"},t("officeEditor.headerFooterOptionsModal.layouts.layout")),n.a.createElement(m.a,{id:"different-first-page",label:t("officeEditor.headerFooterOptionsModal.layouts.differentFirstPage"),"aria-label":t("officeEditor.headerFooterOptionsModal.layouts.differentFirstPage"),checked:N,"aria-checked":N,onChange:function(t){return I(t.target.checked)}}),n.a.createElement(m.a,{id:"different-odd-even",label:t("officeEditor.headerFooterOptionsModal.layouts.differentEvenOddPages"),"aria-label":t("officeEditor.headerFooterOptionsModal.layouts.differentEvenOddPages"),checked:D,"aria-checked":D,onChange:function(t){return R(t.target.checked)}})),n.a.createElement("div",{className:"footer"},n.a.createElement(u.a,{onClick:V,label:t("action.save")}))))};e.default=M}}]);
//# sourceMappingURL=chunk.66.js.map