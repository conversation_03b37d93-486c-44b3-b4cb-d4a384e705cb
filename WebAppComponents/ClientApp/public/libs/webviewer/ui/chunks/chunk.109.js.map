{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/ar-sa.js"], "names": ["module", "exports", "_", "t", "default", "e", "d", "name", "weekdays", "split", "months", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiem", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,sDAAsDC,MAAM,KAAKC,OAAO,6EAA6ED,MAAM,KAAKE,cAAc,wCAAwCF,MAAM,KAAKG,YAAY,6EAA6EH,MAAM,KAAKI,YAAY,gBAAgBJ,MAAM,KAAKK,QAAQ,SAASZ,GAAG,OAAOA,GAAGa,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,0BAA0BC,SAAS,SAASpB,GAAG,OAAOA,EAAE,GAAG,IAAI,KAAKqB,aAAa,CAACC,OAAO,QAAQC,KAAK,SAASC,EAAE,OAAOC,EAAE,QAAQC,GAAG,WAAWC,EAAE,OAAOC,GAAG,WAAWxB,EAAE,MAAMyB,GAAG,UAAUC,EAAE,MAAMC,GAAG,UAAUC,EAAE,MAAMC,GAAG,aAAa,OAAOhC,EAAEC,QAAQgC,OAAO9B,EAAE,MAAK,GAAIA,EAApiCD,CAAE,EAAQ", "file": "chunks/chunk.109.js", "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_ar_sa=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"ar-sa\",weekdays:\"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت\".split(\"_\"),months:\"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر\".split(\"_\"),weekdaysShort:\"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت\".split(\"_\"),monthsShort:\"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر\".split(\"_\"),weekdaysMin:\"ح_ن_ث_ر_خ_ج_س\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},meridiem:function(_){return _>12?\"ص\":\"م\"},relativeTime:{future:\"في %s\",past:\"منذ %s\",s:\"ثوان\",m:\"دقيقة\",mm:\"%d دقائق\",h:\"ساعة\",hh:\"%d ساعات\",d:\"يوم\",dd:\"%d أيام\",M:\"شهر\",MM:\"%d أشهر\",y:\"سنة\",yy:\"%d سنوات\"}};return t.default.locale(d,null,!0),d}));"], "sourceRoot": ""}