/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[5],{621:function(ya,ua,n){n.r(ua);var na=n(0);ya=n(53);var ma=n(237),oa=n(537),ka=n(298),ia=window;n=function(){function fa(x,y){this.fba=function(r){r=r.split(".");return r[r.length-1].match(/(jpg|jpeg|png|gif)$/i)};y=y||{};this.url=x;this.filename=y.filename||x;this.Tf=y.customHeaders;this.iSa=!!y.useDownloader;this.withCredentials=!!y.withCredentials}fa.prototype.VO=function(x){this.Tf=x};fa.prototype.getCustomHeaders=function(){return this.Tf};
fa.prototype.getFileData=function(x){var y=this,r=this,e=new XMLHttpRequest,a=0===this.url.indexOf("blob:")?"blob":"arraybuffer";e.open("GET",this.url,!0);e.withCredentials=this.withCredentials;e.responseType=a;this.Tf&&Object.keys(this.Tf).forEach(function(h){e.setRequestHeader(h,y.Tf[h])});var f=/^https?:/i.test(this.url);e.addEventListener("load",function(h){return Object(na.b)(this,void 0,void 0,function(){var b,w,z,aa,ea,ba;return Object(na.d)(this,function(ca){switch(ca.label){case 0:if(200!==
this.status&&(f||0!==this.status))return[3,10];r.trigger(fa.Events.DOCUMENT_LOADING_PROGRESS,[h.loaded,h.loaded]);if("blob"!==this.responseType)return[3,4];b=this.response;return r.fba(r.filename)?[4,Object(ka.b)(b)]:[3,2];case 1:return w=ca.aa(),r.fileSize=w.byteLength,x(new Uint8Array(w)),[3,3];case 2:z=new FileReader,z.onload=function(ha){ha=new Uint8Array(ha.target.result);r.fileSize=ha.length;x(ha)},z.readAsArrayBuffer(b),ca.label=3;case 3:return[3,9];case 4:ca.pd.push([4,8,,9]);aa=new Uint8Array(this.response);
if(!r.fba(r.filename))return[3,6];b=new Blob([aa.buffer]);return[4,Object(ka.b)(b)];case 5:return w=ca.aa(),r.fileSize=w.byteLength,x(new Uint8Array(w)),[3,7];case 6:r.fileSize=aa.length,x(aa),ca.label=7;case 7:return[3,9];case 8:return ca.aa(),r.trigger(fa.Events.ERROR,["pdfLoad","Out of memory"]),[3,9];case 9:return[3,11];case 10:ea=h.currentTarget,ba=Object(ma.b)(ea),r.trigger(fa.Events.ERROR,["pdfLoad","".concat(this.status," ").concat(ea.statusText),ba]),ca.label=11;case 11:return r.LH=null,
[2]}})})},!1);e.onprogress=function(h){r.trigger(fa.Events.DOCUMENT_LOADING_PROGRESS,[h.loaded,0<h.total?h.total:0])};e.addEventListener("error",function(){r.trigger(fa.Events.ERROR,["pdfLoad","Network failure"]);r.LH=null},!1);e.send();this.LH=e};fa.prototype.getFile=function(){var x=this;return new Promise(function(y){ia.da.isJSWorker&&y(x.url);if(x.iSa){var r=Object(na.a)({url:x.url},x.Tf?{customHeaders:x.Tf}:{});y(r)}y(null)})};fa.prototype.abort=function(){this.LH&&(this.LH.abort(),this.LH=null)};
fa.Events={DOCUMENT_LOADING_PROGRESS:"documentLoadingProgress",ERROR:"error"};return fa}();Object(ya.a)(n);Object(oa.a)(n);Object(oa.b)(n);ua["default"]=n}}]);}).call(this || window)
