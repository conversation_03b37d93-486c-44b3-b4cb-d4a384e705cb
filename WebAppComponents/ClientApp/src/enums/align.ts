export const enum Align {
	Left = 'left',
	Center = 'center',
	Right = 'right',
	Justify = 'justify'
}

// Convert string to enum
export function toAlignType(value: string | null | undefined): Align | null {
	if (!value)
		return null

	switch (value.toLowerCase()) {
		case 'left':
			return Align.Left
		case 'center':
			return Align.Center
		case 'right':
			return Align.Right
		case 'justify':
			return Align.Justify


		// something unknow -> warning
		default:
			console.warn(`unable to parse: ${value}`)
			return null
	}
}

export function getTextAlignClass(textAlign: Align | undefined | null): string {
	if (textAlign == null)
		return ''

	switch (textAlign.toString()) {
		case Align.Left:
			return ''
		case Align.Center:
			return 'align-center'
		case Align.Right:
			return 'align-right'
		case Align.Justify:
			return 'align-justify'
		default:
			return ''
	}
}