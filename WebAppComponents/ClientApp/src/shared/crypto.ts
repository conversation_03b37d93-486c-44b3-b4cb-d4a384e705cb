export class CipherTool {

	public static async encryptData(plaintext: string, password: string) {
		if (!plaintext || !password) {
			console.warn('Encryption failed because text or password was empty')
			return plaintext
		}
		
		const salt = window.crypto.getRandomValues(new Uint8Array(16))
		const counter = generateCounter()
		const key = await deriveKey(password, salt)

		const encrypted = await crypto.subtle.encrypt(
			{ name: 'AES-CTR', counter, length: 64 }, // Länge: Bit-Anzahl für Counter (z. B. 64)
			key,
			encode(plaintext),
		)

		const fullData = new Uint8Array([ ...salt, ...counter, ...new Uint8Array(encrypted) ])
		return btoa(String.fromCharCode(...fullData))
	}

	public static async decryptData(ciphertextBase64: string, password: string) {
		if (!ciphertextBase64 || !password) {
			console.warn('Decryption failed because ciphertext or password was empty')
			return ciphertextBase64
		}

		try {
			const fullData = Uint8Array.from(atob(ciphertextBase64), c => c.charCodeAt(0))

			const salt = fullData.slice(0, 16)
			const counter = fullData.slice(16, 32)
			const data = fullData.slice(32)
			const key = await deriveKey(password, salt)

			const decrypted = await crypto.subtle.decrypt(
				{ name: 'AES-CTR', counter, length: 64 },
				key,
				data,
			)
			
			return decode(decrypted)
		} catch (e) {
			console.error('Decryption failed', e)
			return ''
		}
	}

	public static async storeMessageWithEncryption(key: string, message: string, password: string) {
		const encrypted = await this.encryptData(message, password)
		localStorage.setItem(key, encrypted)
	}
	
	public static async loadAndDecryptMessage(key: string, password: string) {
		const encrypted = localStorage.getItem(key)
		if (!encrypted)
			return ''

		return await this.decryptData(encrypted, password)
	}
}

function encode(text: string) {
	return new TextEncoder().encode(text)
}

function decode(bufferSource: BufferSource) {
	return new TextDecoder().decode(bufferSource)
}

function generateCounter() {
	return window.crypto.getRandomValues(new Uint8Array(16)) // 128-bit Counter
}

async function deriveKey(password: string, salt: BufferSource) {
	const baseKey = await crypto.subtle.importKey(
		'raw',
		encode(password),
		{ name: 'PBKDF2' },
		false,
		[ 'deriveKey' ],
	)

	return crypto.subtle.deriveKey(
		{
			name: 'PBKDF2',
			salt,
			iterations: 100_000,
			hash: 'SHA-256',
		},
		baseKey,
		{ name: 'AES-CTR', length: 256 },
		false,
		[ 'encrypt', 'decrypt' ],
	)
}