import { stories } from './CheckboxGroup.stories'
import { storyTest } from '@test-home/support/advanced-functions'
import { CheckboxGroup } from '@/components/checkbox-group/CheckboxGroup.ts'

import('./CheckboxGroup')

describe('<lvl-checkbox-group />', () => {

	storyTest('checks the default checkbox group story', stories.default, () => {
		cy.mountStory(stories.default)
		cy.get('lvl-checkbox-group').shadow().find('.checkbox-group').should('exist')
		cy.get('lvl-checkbox[initDone]').should('have.length', 9)
		
		// 4 Values are selected by default
		cy.get('lvl-checkbox[checked]').should('have.length', 4)
		
		// check values
		cy.get('lvl-checkbox-group').then((group) => {
			let checkboxGroup = (group[0] as CheckboxGroup)

			let value = checkboxGroup.value
			expect(value).to.have.length(4)
			expect(value).to.contain('Apple')
			expect(value).to.contain('Peach')
			expect(value).to.contain('Orange')
			expect(value).to.contain('<PERSON><PERSON><PERSON>')
			expect(value).to.not.contain('Banana')

			// set different value and check again
			checkboxGroup.value = ["Cherry", "Banana"]

			value = checkboxGroup.value
			expect(value).to.have.length(2)
			expect(value).to.contain('Cherry')
			expect(value).to.contain('Banana')
			expect(value).to.not.contain('Orange')

			cy.get('lvl-checkbox[checked]').should('have.length', 2)
		});

		// check key navigation
		cy.get('.checkbox-group').focus()
		
		// Key down should focus first checkbox
		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowDown',force:true})
		cy.get('lvl-checkbox[value=Apple]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowDown',force:true})
		cy.get('lvl-checkbox[value=Strawberry]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowRight',force:true})
		cy.get('lvl-checkbox[value=Peach]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowRight',force:true})
		cy.get('lvl-checkbox[value=Orange]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowRight',force:true})
		cy.get('lvl-checkbox[value=Blueberry]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowRight',force:true})
		cy.get('lvl-checkbox[value=Raspberry]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowRight',force:true})
		cy.get('lvl-checkbox[value=Pineapple]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowLeft',force:true})
		cy.get('lvl-checkbox[value=Raspberry]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowLeft',force:true})
		cy.get('lvl-checkbox[value=Blueberry]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowLeft',force:true})
		cy.get('lvl-checkbox[value=Orange]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowLeft',force:true})
		cy.get('lvl-checkbox[value=Peach]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowRight',force:true})
		cy.get('lvl-checkbox[value=Orange]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowUp',force:true})
		cy.get('lvl-checkbox[value=Banana]').should('have.focus')

		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowUp',force:true})
		cy.get('lvl-checkbox[value=Banana]').should('have.focus')

		cy.get('lvl-checkbox[value=Banana]').should('have.attr', 'checked')
		cy.get('lvl-checkbox-group').then((group) => {
			let checkboxGroup = (group[0] as CheckboxGroup)
			expect(checkboxGroup.value).to.contain( 'Banana')
		})
		
		cy.get('.checkbox-group').trigger('keydown', { key: 'Enter', force: true })
		cy.get('lvl-checkbox[value=Banana]').should('not.have.attr', 'checked')
		cy.get('lvl-checkbox-group').then((group) => {
			let checkboxGroup = (group[0] as CheckboxGroup)
			expect(checkboxGroup.value).to.not.contain('Banana')
		})

		cy.get('.checkbox-group').trigger('keydown', { key: 'Enter', force: true })
		cy.get('lvl-checkbox[value=Banana]').should('have.attr', 'checked')
		cy.get('lvl-checkbox-group').then((group) => {
			let checkboxGroup = (group[0] as CheckboxGroup)
			expect(checkboxGroup.value).to.contain('Banana')
		})
		
		// set readonly
		cy.get('lvl-checkbox-group').then((checkboxGroup) => {
			checkboxGroup[0].setAttribute("readonly", '')
			cy.get('input[type=checkbox][readonly]').should('have.length', '9')
		});

		// readonly groups should ignore key navigation and stay focused
		cy.get('.checkbox-group').focus()
		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowRight',force:true})
		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowLeft',force:true})
		cy.get('.checkbox-group').should('have.focus')
	})

	storyTest('checks the column behaviour', stories.singleColumn, () => {
		cy.mountStory(stories.singleColumn)
		
		// element should have full width of the group
		cy.get('lvl-checkbox[value=Apple]').invoke('width').should('be.gte', 450)

		// element should have half the width of the group
		cy.get('lvl-checkbox-group').invoke('attr', 'max-columns', 2)
		cy.get('lvl-checkbox[value=Apple]').invoke('width').should('be.lt', 350)

		cy.get('lvl-checkbox-group').invoke('removeAttr', 'max-columns')
		cy.get('lvl-checkbox[value=Apple]').invoke('width').should('be.gte', 150).should('be.lt', 200)
	})
	
	storyTest('checks the readonly checkbox group story', stories.readonly, () => {
		cy.mountStory(stories.readonly)

		// readonly groups should ignore key navigation and stay focused
		cy.get('.checkbox-group').focus()
		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowRight',force:true})
		cy.get('.checkbox-group').trigger('keydown', {key:'ArrowLeft',force:true})
		cy.get('.checkbox-group').should('have.focus')
	})
})