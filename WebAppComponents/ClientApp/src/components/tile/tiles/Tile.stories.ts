import type { <PERSON><PERSON>, <PERSON><PERSON>bj as Story } from "@storybook/web-components";
import { html } from "lit";
import { ifDefined } from "lit/directives/if-defined.js";
import { LevelStory } from "@story-home/support/commands";
import { TileAttributes, TileType } from "./Tile";
import { IconStyle } from "@/enums/icon-style";
import { DataType } from "@/enums";

import("./Tile");

/*
 * More on how to set up stories at: https://storybook.js.org/docs/web-components/writing-stories/introduction
 */

type TileProperties = Partial<TileAttributes & { clickable?: boolean }>;
type TileStory = Story<TileProperties>;

// Main object that contains the basic configuration for all stories to the web component.
const meta: Meta = {
	component: "lvl-tile",
	tags: ["autodocs"],
	render: (_args: TileProperties) => html`
		<lvl-tile
			subtitle="${ifDefined(_args.subtitle)}"
			type="${ifDefined(_args.type)}"
			value="${ifDefined(_args.value)}"
			value-type="${ifDefined(_args.valueType)}"
			sign="${ifDefined(_args.sign)}"
			decimal-places="${ifDefined(_args.decimalPlaces)}"
			divider="${ifDefined(_args.divider)}"
			?with-thousand-separators=${_args.withThousandSeparators}
			?ignore-timezone=${_args.ignoreTimezone}
			icon="${ifDefined(_args.icon)}"
			icon-style="${ifDefined(_args.iconStyle)}"
			.onTileClick=${_args.clickable ? () => console.log("Tile clicked") : undefined}
			skeleton="${ifDefined(_args.skeleton)}"
		>
		</lvl-tile>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		subtitle: {
			control: "text",
			table: {
				type: { summary: "text" },
			},
			description: "",
		},
		type: {
			control: "select",
			options: ["default", TileType.Error, TileType.Info, TileType.Success, TileType.Warn, TileType.Inactive],
			table: {
				type: { summary: "select" },
				defaultValue: { summary: "default" },
			},
			description: "",
		},
		value: {
			control: "text",
			table: {
				type: { summary: "text" },
			},
			description: "",
		},
		valueType: {
			control: "select",
			options: [DataType.Boolean, DataType.Date, DataType.DateTime, DataType.Double, DataType.Enum, DataType.String, DataType.Time],
			table: {
				type: { summary: "select" },
				defaultValue: { summary: DataType.String },
			},
			description: "",
		},
		sign: {
			control: "text",
			table: {
				type: { summary: "text" },
			},
			description: "",
		},
		decimalPlaces: {
			control: "number",
			table: {
				type: { summary: "number" },
			},
			description: "",
		},
		divider: {
			control: "number",
			table: {
				type: { summary: "number" },
			},
			description: "",
		},
		withThousandSeparators: {
			control: "boolean",
			table: {
				type: { summary: "boolean" },
			},
			description: "",
		},
		ignoreTimezone: {
			control: "boolean",
			table: {
				type: { summary: "boolean" },
			},
			description: "",
		},
		icon: {
			control: "select",
			options: ["", "dog", "user", "brands/discord", "bomb"],
			table: {
				type: { summary: "select" },
			},
			description: "Icon which is located to the left of the button",
		},
		iconStyle: {
			control: { type: "select" },
			options: [IconStyle.Solid, IconStyle.Regular, IconStyle.Light, IconStyle.DuoTone, IconStyle.Thin],
			table: {
				type: { summary: "text" },
				defaultValue: { summary: IconStyle.Light },
			},
			description: "",
		},
		clickable: {
			control: "boolean",
			table: {
				type: { summary: "boolean" },
			},
			description: "",
		},
		skeleton: {
			control: "boolean",
			table: {
				type: { summary: "boolean" },
			},
			description: "",
		},
	},
	includeStories: /^[A-Z]/,
};

export default meta;

//#region Stories

/**
 * Appearance of a default Tile without attributes
 */
export const Default: TileStory = {};

export const Skeleton: TileStory = {
	args: {
		skeleton: true,
	},
};

/**
 * Appearance of a Tile with Text and Subtitle
 */
export const Subtitle: TileStory = {
	args: {
		subtitle: "Subtitle",
	},
};

export const StringTile: TileStory = {
	args: {
		value: "Hello World",
		valueType: DataType.String,
	},
};

export const DateTile: TileStory = {
	args: {
		value: "2022-12-31",
		valueType: DataType.Date,
	},
};

export const Value: TileStory = {
	args: {
		value: "1234",
		valueType: DataType.Integer,
		sign: "€",
		decimalPlaces: 0,
		withThousandSeparators: false,
	},
};

export const Icon: TileStory = {
	args: {
		icon: "dog",
		subtitle: "Icon",
	},
};

export const IconValue: TileStory = {
	args: {
		value: "1234",
		valueType: DataType.Integer,
		sign: "€",
		decimalPlaces: 0,
		withThousandSeparators: false,
		icon: "dog",
		subtitle: "Icon",
	},
};

export const ErrorTile: TileStory = {
	args: {
		value: "-3000",
		valueType: DataType.Integer,
		sign: "€",
		withThousandSeparators: true,
		type: TileType.Error,
	},
};

export const ClickableTile: TileStory = {
	args: {
		value: "1234000",
		divider: 1000,
		valueType: DataType.Integer,
		sign: "T€",
		decimalPlaces: 0,
		withThousandSeparators: true,
		clickable: true,
	},
};

//#endregion

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default),
	skeleton: new LevelStory(meta, Skeleton, "Skeleton"),
	subtitle: new LevelStory(meta, Subtitle, "Subtitle"),
	string: new LevelStory(meta, StringTile, "String"),
	date: new LevelStory(meta, DateTile, "Date"),
	value: new LevelStory(meta, Value, "Value"),
	icon: new LevelStory(meta, Icon, "Icon"),
	iconValue: new LevelStory(meta, IconValue, "Icon Value"),
	error: new LevelStory(meta, ErrorTile, "Error"),
	clickable: new LevelStory(meta, ClickableTile, "Clickable"),
} as const;
