import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, queryAll, state } from 'lit/decorators.js'
import { pageDesignerStyles } from './pageDesignerStyles'
import * as styles from '@/shared/component-styles'
import CommunicationServiceProvider, { CommunicationResponse, CommunicationResponseType } from '@/shared/communication-service'
import { Grid, GridSnapTargetType, SnapTargetType } from '@/components/individual/grid/Grid'
import { GridElement, GridElementConfig } from '@/components/individual/grid-element/GridElement'
import { DataType, toDataType } from '@/enums/data-type'
import interact from 'interactjs'
import { StringLocalizer } from '@/shared/string-localizer'
import { Form } from '@/components/form/Form'
import { LevelResponse, PageNavigatorType } from '@/shared/types'
import { ButtonGroup } from '@/components/button-group/ButtonGroup'
import { query } from 'lit/decorators/query.js'
import { InputDataType, InputElementType } from '@/components/inputs/InputElement'
import { fontAwesome } from '@/shared/font-awesome.ts'
import { DialogPreset } from '@/enums/dialog-preset.ts'
import { Button } from '@/components/atomics/button/Button.ts'
import { InputFieldType } from '@/components/inputs/InputElement.ts'

export type PageDesignerType = {
	skeleton?: boolean
}

export type ColumnOptions = {
	columnMinWidths: Record<number, number>
	columnRatios: Record<number, number>
}

type GridViewFieldType = {
	id: string
	rowStart: number
	rowEnd: number
	colStart: number
	colEnd: number
	flag: string
	labelTranslated: string
	dataType: InputDataType
	dataTypeTranslated: string
	dataFieldId?: string
	required: boolean
	readonly: boolean
	placeholderTranslated: string
	helpTextTranslated: string
	dataField?: DataFieldType
}

export type DataFieldType = {
	id: string
	name: string
}

export enum DataSourceType {
	Default = 'default',
	Blueprint = 'blueprint',
	Annotation = 'annotation',
	ElementType = 'elementType'
}

type GridViewTextType = {
	id: string
	rowStart: number
	rowEnd: number
	colStart: number
	colEnd: number
	flag: string
	label?: string
	text: string
	textTranslated: string
	color?: string
	textAlign?: string
	textType: string
	textTypeTranslated?: string
}

type GridViewPageType = GridViewSectionType & {
	id: string
	gridViewPageType: string
	rowStart: number
	rowEnd: number
	colStart: number
	colEnd: number
}

//duplicate
type GridViewTileType = GridViewSectionType & {
	id: string
	rowStart: number
	rowEnd: number
	colStart: number
	colEnd: number
}

type GridViewSectionType = {
	titleTranslated: string
	showTitle: boolean
	flag: string
	title: string
}

type ColumnSnapTargetType = SnapTargetType & {
	column?: HTMLElement,
	nextSection?: HTMLElement
}

type DragPositionType = {
	x: number,
	y: number,
	scrollTop: number
	scrollLeft: number
}

type DraggableElement = HTMLElement & {
	startRect?: DOMRect
	sourceElement?: HTMLElement,
	entityName?: string,
	config?: GridElementConfig,
	additionalData?: object
}

const enum DraggableActionType {
	Create = 'create',
	Update = "update"
}

type ElementCreateCallback = (event: MouseEvent, sourceElement: HTMLElement) => any;

type DraggableOptions = {
	container?: string | HTMLElement
	restriction?: string
}

/**
 * Web component as button with icon and label
 */
@customElement('lvl-page-designer')
export class PageDesigner extends LitElement implements PageDesignerType {

	// enable animations for vanishing scrollbar
	static {
		const documentStyles = css`
			@property --scrollbar-color {
				syntax: "<color>";
				inherits: true;
				initial-value: transparent;
			}
		`;
		document.adoptedStyleSheets.push(documentStyles.styleSheet!);
	}

	static styles = [
		styles.base,
		styles.color,
		styles.animation,
		styles.skeleton,
		styles.vanishingScrollbar,
		pageDesignerStyles,
		fontAwesome,
		css`
			:host {
				overflow: hidden;
				display: flex;
				width: 100%;
				height: 100%;
			}
		`,
	]

	//#region attributes

	@property({ type: Number, reflect: true })
	public columns: number = 1

	@property({ type: Object, attribute: 'column-options' })
	public columnOptions: ColumnOptions = { columnMinWidths: {}, columnRatios: {}}

	@state()
	public activeColumn?: number

	@property({ attribute: 'page-view-id', reflect: true })
	public pageViewId: string = ""

	@property({ type: Boolean, reflect: true })
	public skeleton: boolean = false

	@property({ type: Boolean, reflect: true })
	public initDone: boolean = false

	@property({ type: Boolean, attribute: 'allow-embedded-pages' })
	public allowEmbeddedPages: boolean = false
	
	@property({ attribute: 'data-source-type' })
	public dataSourceType: DataSourceType = DataSourceType.Default

	//#endregion

	//#private properties

	@query('#page-designer-content')
	private pageDesignerContent!: HTMLElement

	@query('#page-column-wrapper')
	private pageColumnWrapper!: HTMLElement

	@query('#columnSwitcher')
	private columnSwitcher!: HTMLElement

	@queryAll('#page-column-wrapper .page-column')
	private pageColumns!: HTMLElement[]

	private columnSnapTargets: ColumnSnapTargetType[] = []

	private gridSnapTargets: SnapTargetType[] = []

	private dragPosition: DragPositionType = { x: 0, y: 0, scrollTop: 0, scrollLeft: 0 }

	private hasScrolled: boolean = false

	private lastMoveEvent: number = 0

	// @ts-ignore
	private pendingAction?: Function

	private GRID_COLUMNS = 24;
	private GRID_ROW_HEIGHT = 64;
	private GRID_ROW_GAP = 8;
	private GRID_COL_GAP = 4;
	private GRID_ELEMENT_GAP = 18; // 2x8px grid padding + 2x1px draggable element border
	private DESIGNER_COLUMN_MIN_WIDTH = 300;

	private static readonly _localizer: StringLocalizer = new StringLocalizer('PageDesigner')

	//#endregion

	//#region Lit functions

	// first time we can access the rendered elements and attach events to them
	protected async firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)

		// make existing sections selectable
		for (let section of this.querySelectorAll(':scope > section')) {
			section.addEventListener('click', () => {
				this.editColumnElement(section as HTMLElement)
			})
		}

		// catch click on grid elements and trigger edit
		this.addEventListener('gridElementClick', (event) => {
			this.editGridElement(event.target as GridElement)
			this.appendDragListener(event.target as GridElement)
			this.appendResizeListener(event.target as GridElement)
		})

		// close edit slideout if clicked outside of page element 
		this.pageDesignerContent.addEventListener('click', (event) => {
			if (!window.Page.editSlideOut?.open)
				return

			if ((event.target as HTMLElement).closest('.page-designer__section') == null)
				window.Page.editSlideOut.open = false
		})

		// base configuration for interact
		interact.dynamicDrop(true) // needed for dropzones to work properly in combination with autoscroll

		// detect scroll while dragging/resizing
		interact(this.pageDesignerContent).on('scroll', () => this.hasScrolled = true)

		// column dragging
		this.initColumnDraggable('.columnElement', DraggableActionType.Create)
		this.initColumnDraggable('section > .section-flag', DraggableActionType.Update)
		//this.initColumnDropzone('#page-column-wrapper .page-column')
		for (const column of this.pageColumns) {
			this.initColumnDropzone(column)
		}

		// grid element dragging
		this.initGridDraggable('.layoutElement', this.createTextDraggable)
		this.initGridDraggable('.gridElement', this.createFieldDraggable)
		this.initGridDraggable('.tileElement', this.createTileDraggable)

		// init dropzones on every section grid
		for (let section of this.querySelectorAll('.page-designer__section[data-type=Section]')) {
			this.initGridDropzone(section as HTMLElement)
		}

		// make column switcher work
		this.columnSwitcher.addEventListener('change', (event) => {
			const selectedColumn = (event.target as ButtonGroup).value
			if (selectedColumn == 'ALL')
				this.activeColumn = undefined
			else
				this.activeColumn = parseInt(selectedColumn)
		})

		this.initDone = true
	}

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		// prepare column switcher and columns
		const columnButtons = [];
		const columns = [];
		for (let i = 1; i <= this.columns; i++) {
			columnButtons.push(html`<lvl-button label="${PageDesigner._localizer.localize("column")} ${i}" value="${i}"></lvl-button>`);
			columns.push(html`<div class="page-column"><slot name="${i-1}"></slot></div>`)
		}
		if (this.columns > 1)
			columnButtons.push(html`<lvl-button label="${PageDesigner._localizer.localize("columnsAll")}" value="ALL"></lvl-button>`);

		// calculate styling
		const templateColumns = []
		let totalRatio = 0
		for (let i = 1; i <= this.columns; i++) {
			templateColumns.push(this.columnOptions.columnMinWidths[i] ? `minmax(${this.columnOptions.columnMinWidths[i]}px, ${this.columnOptions.columnRatios[i]}fr)` : `${this.columnOptions.columnRatios[i]}fr`)
			totalRatio+= this.columnOptions.columnRatios[i]
		}

		const columnMaxWidth = totalRatio > 3 ? 400 : totalRatio > 2 ? 500 : 800;
		const wrapperMinWidth = this.activeColumn ? this.columnOptions.columnMinWidths[this.activeColumn] ?? this.DESIGNER_COLUMN_MIN_WIDTH : totalRatio * this.DESIGNER_COLUMN_MIN_WIDTH;
		const wrapperMaxWidth = this.activeColumn ?(this.columnOptions.columnRatios[this.activeColumn] || 1) * columnMaxWidth : totalRatio * columnMaxWidth;

		return html`
			<style>
				:host {
					--wrapper-template-columns: ${this.activeColumn ? 'minmax(0, 1fr)' : templateColumns.join(' ')};
					--wrapper-min-width: ${wrapperMinWidth}px;
					--wrapper-max-width: ${wrapperMaxWidth}px;
				}
			</style>
			<div id="page-designer-content" class="vanishing-scrollbar static-scrollbar">
				<lvl-button-group id="columnSwitcher" value="${this.activeColumn ?? "ALL"}" style="${this.columns > 1 ? '' : 'display:none;'}">
					${columnButtons}
				</lvl-button-group>
				<div id="page-column-wrapper" data-columns="${this.columns > 1 ? this.activeColumn ?? "ALL" : 1}">
					${columns}
				</div>
			</div>
			<aside id="page-designer-options">
				<lvl-tab-bar>
					<lvl-tab label="Seiten-Elemente">
						<section>
							<label>${PageDesigner._localizer.localize("structure")}</label>
							<div class="option-grid">
								<div class="pageElement columnElement" data-type="Section">
									<i class="fal fa-window-maximize icon"></i>${PageDesigner._localizer.localize("elementType/section")}
								</div>
								<div class="pageElement columnElement ${this.allowEmbeddedPages ? "" : "hidden"}" data-type="EmbeddedPage">
									<i class="fal fa-puzzle-piece icon"></i>${PageDesigner._localizer.localize("elementType/embeddedPage")}
								</div>
								<div class="pageElement tileElement ${this.allowEmbeddedPages ? "" : "hidden"}" data-type="EmbeddedTile">
									<i class="fal fa-square-up-right icon"></i>${PageDesigner._localizer.localize("elementType/embeddedTile")}
								</div>
							</div>
						</section>
						<section>
							<label>${PageDesigner._localizer.localize("layout")}</label>
							<div class="option-grid">
								<div class="pageElement layoutElement" data-type="Headline1">
									<i class="fal fa-header icon"></i>${PageDesigner._localizer.localize("elementType/headline")} 1
								</div>
								<div class="pageElement layoutElement" data-type="Headline2">
									<i class="fal fa-header icon" style="font-size:20px;"></i>${PageDesigner._localizer.localize("elementType/headline")} 2
								</div>
								<div class="pageElement layoutElement" data-type="Headline3">
									<i class="fal fa-header icon" style="font-size:16px;"></i>${PageDesigner._localizer.localize("elementType/headline")} 3
								</div>
								<div class="pageElement layoutElement" data-type="Plain">
									<i class="fal fa-text icon"></i>${PageDesigner._localizer.localize("elementType/text")}
								</div>
								<div class="pageElement layoutElement" data-type="Separator">
									<i class="fal fa-horizontal-rule icon"></i>${PageDesigner._localizer.localize("elementType/separator")}
								</div>
								<div class="pageElement layoutElement disabled">
									<i class="fal fa-rectangle-code icon"></i>${PageDesigner._localizer.localize("elementType/button")}
								</div>
							</div>
						</section>
						<section>
							<label>${PageDesigner._localizer.localize("formFields")}</label>
							<div class="option-grid">
								<div class="pageElement gridElement" data-type="String">
									<i class="fal fa-input-text icon"></i>${PageDesigner._localizer.localize("elementType/string")}
								</div>
								<div class="pageElement gridElement" data-type="Double">
									<i class="fal fa-input-numeric icon"></i>${PageDesigner._localizer.localize("elementType/number")}
								</div>
								<div class="pageElement gridElement" data-type="Text">
									<i class="fal fa-subtitles icon"></i>${PageDesigner._localizer.localize("elementType/text")}
								</div>
								<div class="pageElement gridElement" data-type="Date">
									<i class="fal fa-calendar icon"></i>${PageDesigner._localizer.localize("elementType/date")}
								</div>
								<div class="pageElement gridElement" data-type="Time">
									<i class="fal fa-clock icon"></i>${PageDesigner._localizer.localize("elementType/time")}
								</div>
								<div class="pageElement gridElement" data-type="DateTime">
									<i class="fal fa-calendar-clock icon"></i>${PageDesigner._localizer.localize("elementType/dateTime")}
								</div>
								<div class="pageElement gridElement" data-type="Boolean">
									<i class="fal fa-toggle-on icon"></i>${PageDesigner._localizer.localize("elementType/toggle")}
								</div>
								<div class="pageElement gridElement" data-type="Guid">
									<i class="fal fa-list-dropdown icon"></i>${PageDesigner._localizer.localize("elementType/dropdown")}
								</div>
								<div class="pageElement gridElement disabled">
									<i class="fal fa-list icon"></i>${PageDesigner._localizer.localize("elementType/checkboxGroup")}
								</div>
								<div class="pageElement gridElement disabled">
									<i class="fal fa-rectangle-wide icon"></i>${PageDesigner._localizer.localize("elementType/buttonGroup")}
								</div>
							</div>
						</section>
						${this.dataSourceType == DataSourceType.Annotation ? html`
							<section>
								<label>${PageDesigner._localizer.localize("miscElements")}</label>
								<div class="option-grid">
									<div class="pageElement gridElement" data-type="Annotation">
										<i class="fal fa-map-location-dot icon"></i>${PageDesigner._localizer.localize("elementType/annotationPreview")}
									</div>
								</div>
							</section>
						` : null}
					</lvl-tab>
					<lvl-tab label="${PageDesigner._localizer.localize("dataFields")}" under-construction>
						<slot name="field-list"></slot>
					</lvl-tab>
				</lvl-tab-bar>
			</aside>`
	}

	//#endregion

	//#region lifecycle callbacks

	//#endregion

	//#region public methods

	public async refreshSectionElements(section: HTMLElement) {
		const sectionId = section.id
		const sectionGrid = section.querySelector("lvl-grid")
		if (!sectionGrid)
			return

		const response = await CommunicationServiceProvider.get(`/Api/GridViewSections/${sectionId}/Elements`)
		if(response.state != CommunicationResponseType.Ok)
			return

		const elements = response.data
		elements.fields.forEach((elementInfo: GridViewFieldType) => this.renderFieldEntity(sectionGrid, elementInfo))
		elements.texts.forEach((elementInfo: GridViewTextType) => this.renderTextEntity(sectionGrid, elementInfo))
		elements.pages.forEach((elementInfo: GridViewPageType) => {
			if(elementInfo.gridViewPageType == "Tile")
				this.renderTileEntity(sectionGrid, elementInfo)
			else
				this.renderPageEntity(sectionGrid, elementInfo)
		})
	}

	private createTextDraggable(event: MouseEvent, sourceElement: HTMLElement) {

		const textType = sourceElement.dataset.type!
		const draggable = this.createGridDraggable(event, sourceElement, "GridViewText", {TextType: textType})

		let contentElement = document.createElement('span')
		contentElement.classList.add('page-designer__text', 'empty', textType)
		switch (textType) {
			case 'Plain':
				contentElement.innerHTML = sourceElement.innerText
				draggable.config = GridElementConfig.Text
				break
			case 'Separator':
				contentElement = document.createElement('hr')
				contentElement.classList.add('page-designer__separator')
				draggable.config = GridElementConfig.Separator
				break
			default:
				contentElement.innerHTML = '<label>' + sourceElement.innerText + '</label>'
				draggable.config = GridElementConfig.Headline
				break
		}
		draggable.append(contentElement)

		return draggable
	}

	private createTileDraggable(event: MouseEvent, sourceElement: HTMLElement) {
		const dataType = sourceElement.dataset.type!
		const draggable = this.createGridDraggable(event, sourceElement, "GridViewPage", {DataType: dataType}) as DraggableElement
		draggable.config = GridElementConfig.Tile
		draggable.classList.add('tilePreview', 'pagePreview')

		const label = PageDesigner._localizer.localize("newEmbeddedTile")
		draggable.innerHTML = `<label>${label}</label>`

		return draggable
	}

	private createFieldDraggable(event: MouseEvent, sourceElement: HTMLElement) {

		const dataType = sourceElement.dataset.type!
		const draggable = this.createGridDraggable(event, sourceElement, "GridViewField", {DataType: dataType})
		draggable.config = GridElementConfig.Input

		const inputPreview = document.createElement('div')
		inputPreview.classList.add('inputPreview')

		let input
		switch (dataType) {
			case 'Text':
				draggable.classList.add('doubleHeight')
				input = document.createElement('lvl-textarea')
				draggable.config = GridElementConfig.TextInput
				break
			case 'Boolean':
				input = document.createElement('lvl-toggle')
				break
			case 'Guid':
				input = document.createElement('lvl-autocomplete')
				break
			case 'Annotation':
				draggable.config = GridElementConfig.Annotation
				inputPreview.innerHTML = '<div class="annotationPreview"><i class="fal fa-map-location-dot icon"></i><i class="disconnected" data-tooltip="disconnectedTooltip"></i><lvl-tooltip name="disconnectedTooltip" orbit placement="right-start">Kein Plan verknüpft</lvl-tooltip></div>'
				break;
			default:
				input = document.createElement('lvl-input')
				input.setAttribute('type', dataType.toLowerCase())
				break
		}

		if (input) {
			input.label = sourceElement.innerText
			input.preview = true
			input.disconnected = true
			inputPreview.append(input)
		}
		draggable.append(inputPreview)

		return draggable
	}

	private createGridDraggable(event: MouseEvent, sourceElement: HTMLElement, entityName: string, additionalData: Record<string, any>) {

		// create drag element aka element preview
		const draggable = document.createElement('div') as DraggableElement
		draggable.style.position = 'absolute' // important to prevent pipeline flakies
		draggable.classList.add('dragElement', 'gridDragElement', 'dragging')

		// remember our input values for later
		draggable.sourceElement = sourceElement
		draggable.entityName = entityName
		draggable.additionalData = additionalData

		// set width according to current column widths (which depend on screen/window size as well as column count) 
		let maxWidth = undefined
		for (const column of this.pageColumnWrapper.querySelectorAll('.page-column')) {
			const columnMaxWidth = column.getBoundingClientRect().width - this.GRID_ELEMENT_GAP
			if (maxWidth === undefined || columnMaxWidth < maxWidth)
				maxWidth = columnMaxWidth / 2
		}
		draggable.style.width = maxWidth+'px'
		draggable.style.setProperty('--top', (event.clientY - (this.GRID_ROW_HEIGHT / 2)).toString())
		draggable.style.setProperty('--left', (event.clientX - (maxWidth || 0) + (this.GRID_ROW_HEIGHT / 2)).toString())

		this.parentElement!.appendChild(draggable)
		return draggable
	}

	public initGridDraggable(elementSelector: string | HTMLElement, createCallback: ElementCreateCallback, options?: DraggableOptions) {
		// remember "this" to use it inside the events
		const pageDesigner = this

		interact(elementSelector).unset()
		interact(elementSelector).draggable({
			manualStart: true,
			autoScroll: {
				container: options?.container || pageDesigner.pageDesignerContent,
			},
			modifiers: [
				interact.modifiers.restrictRect({
					restriction: '#content',
				}),
				interact.modifiers.snap({
					targets: pageDesigner.gridSnapTargets,
					relativePoints: [
						{ x: 1, y: 0 },
					],
					offset: 'self',
				})
			],
			listeners: {
				start(event) {
					// remember start positions to recalculate snap Targets later
					const startRect = event.target.getBoundingClientRect()
					event.target.startRect = startRect

					// calculate allowed snap targets
					pageDesigner.calculateGridSnapTargets(startRect, -1, Math.round(startRect.height / pageDesigner.GRID_ROW_HEIGHT), event.target.config)

					const startX = parseInt(event.target.style.getPropertyValue('--dragX') || '0')
					const startY = parseInt(event.target.style.getPropertyValue('--dragY') || '0')
					pageDesigner.dragPosition = { x: startX, y: startY, scrollTop: pageDesigner.pageDesignerContent.scrollTop, scrollLeft: pageDesigner.pageDesignerContent.scrollLeft }
				},
				move(event) {
					const draggable = event.currentTarget

					// recalculate snap targets after autoscroll
					if (pageDesigner.hasScrolled) {
						const now = window.performance.now()
						pageDesigner.lastMoveEvent = now
						setTimeout(() => {
							if (pageDesigner.lastMoveEvent !== now)
								return
							pageDesigner.calculateGridSnapTargets(event.target.startRect, -1, Math.round(event.target.startRect.height / pageDesigner.GRID_ROW_HEIGHT), event.target.config)
							pageDesigner.hasScrolled = false
						}, 10)
					}

					// move element to target Position
					pageDesigner.dragPosition.x += event.dx
					pageDesigner.dragPosition.y += event.dy
					draggable.style.setProperty('--dragX', pageDesigner.dragPosition.x)
					draggable.style.setProperty('--dragY', pageDesigner.dragPosition.y)

					// revert previous sibling positions
					pageDesigner.querySelectorAll('lvl-grid-element[col-start-preview]').forEach((element) => (element as GridElement).revertPreview())
					draggable.classList.remove('snapped', 'snappedToGrid')
					draggable.style.removeProperty('--widthPreview')

					// if snapped, apply new previews
					if (event.modifiers[1].inRange) {
						const snapTarget = event.modifiers[1].target.source
						pageDesigner.handleSnapTarget(draggable, snapTarget, event.target.startRect)
					}
				},
				end(event) {
					const draggable = event.currentTarget
					if (!event.modifiers[1].inRange) {
						draggable.classList.add('resetPosition')
						draggable.style.setProperty('--dragX', 0)
						draggable.style.setProperty('--dragY', 0)
						setTimeout(() => {
							draggable.remove()
						}, 100)
					}
				},
			},
		}).on('down', (event) => {
			event.interaction.allowStart = true
		}).on('up', (event) => {
			event.interaction.allowStart = false
		}).on('move', function (event) {
			const interaction = event.interaction

			// if the pointer was moved while being held down
			// and an interaction hasn't started yet
			if (interaction.pointerIsDown && !interaction.interacting() && interaction.allowStart) {
				const draggable = createCallback.call(pageDesigner, event, event.currentTarget)

				// start a drag interaction targeting the clone
				interaction.start({ name: 'drag' }, event.interactable, draggable)
				interaction.allowStart = false
			}
		})
	}

	private handleSnapTarget(draggable: DraggableElement, snapTarget: SnapTargetType, startRect: DOMRect) {
		draggable.classList.add('snapped')
		if ((snapTarget as GridSnapTargetType).grid)
			draggable.classList.add('snappedToGrid')

		switch (snapTarget.type) {
			case 'Drop':
				if (snapTarget.squeezeWidth) {
					draggable.style.setProperty('--widthPreview', snapTarget.squeezeWidth + 'px')
					draggable.style.setProperty('--dragX', (this.dragPosition.x + (startRect.width - snapTarget.squeezeWidth)).toString())
				}
				break
			case 'Squeeze':
				const gridSnapTarget = snapTarget as GridSnapTargetType
				const siblings = gridSnapTarget.siblings
				const gridRect = gridSnapTarget.grid.getBoundingClientRect()
				const colWidth = gridRect.width / this.GRID_COLUMNS
				const colSpread = this.GRID_COLUMNS / ((siblings?.length ?? 0) + 1)

				// update draggable width
				draggable.style.setProperty('--widthPreview', (colWidth * colSpread) + 'px')
				draggable.style.setProperty('--dragX', (this.dragPosition.x + (startRect.width - colWidth * colSpread)).toString())

				if (!siblings?.length || gridSnapTarget.position == undefined)
					return

				// update sibling widths for preview purposes
				for (let position = 0; position < siblings.length; position++) {
					const sibling = siblings[position]
					if (position < gridSnapTarget.position) {
						sibling.colStartPreview = (colSpread * position) + 1
						sibling.colEndPreview = (colSpread * (position + 1)) + 1
					} else {
						sibling.colStartPreview = (colSpread * (position + 1)) + 1
						sibling.colEndPreview = (colSpread * (position + 2)) + 1
					}
				}
				break
		}
	}

	public initColumnDraggable(elementSelector: string | HTMLElement, actionType: DraggableActionType) {
		// remember "this" to use it inside the events
		const pageDesigner = this

		interact(elementSelector).unset()
		interact(elementSelector).draggable({
			manualStart: true,
			autoScroll: {
				container: this.pageDesignerContent,
			},
			modifiers: [
				interact.modifiers.restrictRect({
					restriction: 'lvl-page-designer',
				}),
				interact.modifiers.snap({
					targets: this.columnSnapTargets,
					relativePoints: [
						{ x: 1, y: 0 },
					],
					offset: 'self',
				})
			],
			listeners: {
				start(event) {
					pageDesigner.classList.add('dragActive')

					// remember start positions to recalculate snap Targets later
					event.target.startRect = event.target.getBoundingClientRect()

					// calculate allowed snap targets after animation is done
					setTimeout(() => {
						pageDesigner.calculateColumnSnapTargets(event.target.startRect, event.target.dataset.type == "EmbeddedPage")
					}, 100)


					const startX = parseInt(event.target.style.getPropertyValue('--dragX') || '0')
					const startY = parseInt(event.target.style.getPropertyValue('--dragY') || '0')
					pageDesigner.dragPosition = { x: startX, y: startY, scrollTop: pageDesigner.pageDesignerContent.scrollTop, scrollLeft: pageDesigner.pageDesignerContent.scrollLeft }

					// if hiding the parent element caused the scrollbar to disappear or the scrollTop to change, recalculate the snap targets!
					const scrollBarVisible = pageDesigner.pageDesignerContent.scrollHeight > pageDesigner.pageDesignerContent.clientHeight
					setTimeout(() => {
						const stillVisible = pageDesigner.pageDesignerContent.scrollHeight > pageDesigner.pageDesignerContent.clientHeight
						if (scrollBarVisible !== stillVisible)
							pageDesigner.calculateColumnSnapTargets(event.interaction.element.startRect, event.interaction.element.dataset.type == "EmbeddedPage")
					}, 10)
				},
				move(event) {
					const draggable = event.currentTarget

					// recalculate snap targets after autoscroll
					if (pageDesigner.hasScrolled) {
						const now = window.performance.now()
						pageDesigner.lastMoveEvent = now

						// debounced to prevent too many recalculations
						setTimeout(() => {
							if (pageDesigner.lastMoveEvent !== now)
								return
							pageDesigner.calculateColumnSnapTargets(event.target.startRect, event.target.dataset.type == "EmbeddedPage")
							pageDesigner.hasScrolled = false
						}, 10)
					}

					// move element to target Position
					pageDesigner.dragPosition.x += event.dx
					pageDesigner.dragPosition.y += event.dy
					draggable.style.setProperty('--dragX', pageDesigner.dragPosition.x)
					draggable.style.setProperty('--dragY', pageDesigner.dragPosition.y)

					draggable.classList.remove('snapped', 'snappedToGrid')
					draggable.style.removeProperty('--widthPreview')

					// reset move down and possibly also squeezed grid elements if our column element is an embedded page
					pageDesigner.resetMoveDown()
					if (draggable.dataset.type == "EmbeddedPage")
						pageDesigner.querySelectorAll('lvl-grid-element[col-start-preview]').forEach((element) => (element as GridElement).revertPreview())

					// check if element is snapped
					if (event.modifiers[1].inRange) {
						const snapTarget = event.modifiers[1].target.source
						pageDesigner.handleSnapTarget(draggable, snapTarget, event.target.startRect)
						pageDesigner.moveDown(draggable, snapTarget)
					}
				},
				end(event) {
					pageDesigner.classList.remove('dragActive')

					const draggable = event.currentTarget
					if (!event.modifiers[1].inRange) {
						if (event.target.sourceElement) {
							event.target.sourceElement.classList.remove('dragActive')
							event.target.sourceElement.style.maxHeight = null
						}
						draggable.classList.add('resetPosition')
						draggable.style.setProperty('--dragX', 0)
						draggable.style.setProperty('--dragY', 0)
						setTimeout(() => {
							draggable.remove()
						}, 100)
						pageDesigner.resetMoveDown()
					} else {
						draggable.remove()
						pageDesigner.resetMoveDown(true)
					}
				}
			},
		}).on('down', (event) => {
			const interaction = event.interaction
			interaction.allowStart = true
		}).on('up', (event) => {
			const interaction = event.interaction
			interaction.allowStart = false
		}).on('move', function (event) {
			const interaction = event.interaction

			// if the pointer was moved while being held down
			// and an interaction hasn't started yet
			if (interaction.pointerIsDown && !interaction.interacting() && interaction.allowStart) {
				const section = actionType == DraggableActionType.Update ? event.currentTarget.closest('section') : null
				let label
				let flag
				if (actionType == DraggableActionType.Update) {
					label = section.querySelector(':scope > label')?.innerText ?? ''
					flag = section.querySelector('.section-flag')
				} else {
					switch (event.target.dataset.type) {
						case "Section":
							label = PageDesigner._localizer.localize("newSection")
							break;
						case "EmbeddedPage":
							label = PageDesigner._localizer.localize("newEmbeddedPage")
							break;
					}
				}

				// create drag element aka element preview
				const draggable = document.createElement('div') as DraggableElement
				draggable.style.position = 'absolute' // important to prevent pipeline flakies
				draggable.sourceElement = event.target
				draggable.entityName = 'GridViewPage'
				draggable.classList.add('columnDragElement', 'dragging')
				if (actionType == DraggableActionType.Update) {
					draggable.setAttribute('id', section.getAttribute('id'))
					draggable.classList.add('existingElement')
				}
				draggable.innerHTML = `${flag?.outerHTML ?? ""}<label>${label}</label>`
				if (event.target.dataset.type == "EmbeddedPage" || section?.dataset.type == "EmbeddedPage")
					draggable.innerHTML = '<span class="pagePreview">'+draggable.innerHTML+'</span>'
				draggable.dataset.type = actionType == DraggableActionType.Update ? section.dataset.type : event.target.dataset.type
				if (draggable.dataset.type == 'EmbeddedPage')
					draggable.classList.add('gridDragElement')
				let maxWidth: number = 0
				for (const column of pageDesigner.pageColumnWrapper.querySelectorAll('.page-column')) {
					const columnMaxWidth = column.getBoundingClientRect().width - pageDesigner.GRID_ELEMENT_GAP
					if (maxWidth == 0 || columnMaxWidth < maxWidth)
						maxWidth = columnMaxWidth
				}

				if (actionType == DraggableActionType.Update) {
					const sectionRect = section.getBoundingClientRect()
					draggable.style.width = '400px'
					draggable.style.setProperty('--top', sectionRect.y)
					draggable.style.setProperty('--left', (sectionRect.x + sectionRect.width - 400).toString())

					// hide real element and remember reference
					section.style.maxHeight = sectionRect.height + 'px'
					section.getBoundingClientRect() // force redraw before adding class (otherwise animation of height is not working)
					section.classList.add('dragActive');
					(draggable as any as DraggableElement).sourceElement = section
				} else {
					draggable.style.width = maxWidth+"px"
					draggable.style.setProperty('--top', (event.clientY - (pageDesigner.GRID_ROW_HEIGHT / 2)).toString())
					draggable.style.setProperty('--left', (event.clientX - maxWidth + (pageDesigner.GRID_ROW_HEIGHT / 2)).toString())
				}

				// append draggable to DOM
				pageDesigner.parentElement!.appendChild(draggable)

				// start a drag interaction targeting the clone
				interaction.start({ name: 'drag' }, event.interactable, draggable)
				interaction.allowStart = false

				if (actionType == DraggableActionType.Update) {
					// recalculate snap targets after hiding the dragged element is complete
					setTimeout(() => {
						pageDesigner.calculateColumnSnapTargets(draggable.startRect, draggable.dataset.type == "EmbeddedPage")
					}, 100)
				}
			}
		})
	}

	private initColumnDropzone(elementSelector: HTMLElement) {
		// remember "this" to use it inside the events
		const pageDesigner = this

		interact(elementSelector).unset()
		interact(elementSelector).dropzone({
			accept: '.columnDragElement',
			overlap: 'center',
			ondrop: async function (event) {
				const elementId = event.relatedTarget.getAttribute('id')
				let success
				if (elementId != null)
					success = await pageDesigner.updateColumnElement(event.currentTarget, event.relatedTarget)
				else
					success = await pageDesigner.createColumnElement(event.currentTarget, event.relatedTarget)
				if (!success)
					return

				// remove draggable and reset temporary manipulated section
				event.relatedTarget.remove()
				pageDesigner.resetMoveDown(true)
			}
		})
		elementSelector.dataset.cy = 'dropzone'
	}

	//#endregion

	//#region private methods

	private calculateColumnSnapTargets(startRect?: DOMRect, includeGridTargets: boolean = false) {
		let snapTargets: ColumnSnapTargetType[] = []
		if (!startRect) {
			console.warn('calculateColumnSnapTargets called without startRect')
			return
		}

		// reset array (overwriting it with [] destroys the reference)
		this.columnSnapTargets.length = 0

		this.pageColumnWrapper.querySelectorAll('.page-column').forEach((column, i) => {
			if (i < this.columns) {
				const columnRect = column.getBoundingClientRect()
				const sections = this.querySelectorAll(`:scope > section[slot='${i}']:not(.dragActive)`)

				if (sections.length > 0) {
					sections.forEach((section, k) => {
						const sectionRect = section.getBoundingClientRect()
						snapTargets.push({
							type: 'Drop',
							x: sectionRect.x - startRect.x + sectionRect.width,
							y: sectionRect.y - startRect.y,
							range: 25,
							squeezeWidth: columnRect.width - this.GRID_ELEMENT_GAP,
							column: column as HTMLElement,
							nextSection: section as HTMLElement
						})
						if (k + 1 === sections.length)
							snapTargets.push({
								type: 'Drop',
								x: sectionRect.x - startRect.x + sectionRect.width,
								y: sectionRect.y + sectionRect.height - startRect.y + this.GRID_ROW_GAP,
								range: 25,
								squeezeWidth: columnRect.width - this.GRID_ELEMENT_GAP,
								column: column as HTMLElement
							})
					})
				} else
					snapTargets.push({
						type: 'Drop',
						x: columnRect.x - startRect.x + +columnRect.width - 8,
						y: columnRect.y - startRect.y + this.GRID_ROW_GAP,
						range: 25,
						squeezeWidth: columnRect.width - this.GRID_ELEMENT_GAP,
						column: column as HTMLElement
					})
			}
		})

		this.columnSnapTargets.push(...snapTargets)

		// embedded Pages may be added inside the grid as well
		if (includeGridTargets) {
			this.calculateGridSnapTargets(startRect, -1, 1, GridElementConfig.Page)
			this.columnSnapTargets.push(...this.gridSnapTargets)
		}
	}

	private moveDown(draggable: DraggableElement, snapTarget: ColumnSnapTargetType) {
		// abort if there is no target column
		let targetColumn = snapTarget.column
		if (targetColumn === undefined)
			return

		// remember "this" to use it inside the events
		const pageDesigner = this

		// remember scrollTop of pageContainer and prevent any changes
		const scrollTop = this.pageDesignerContent.scrollTop

		const preventScroll = function () {
			pageDesigner.pageDesignerContent.scrollTop = scrollTop
		}
		this.pageDesignerContent.addEventListener('scroll', preventScroll)

		// mark next sibling as "moved down"
		if (snapTarget.nextSection !== undefined)
			snapTarget.nextSection.classList.add('moveDown')

		// recalculate snap targets
		this.calculateColumnSnapTargets(draggable.startRect, draggable.dataset.type == "EmbeddedPage")

		// remove our fake event and recalculate the snap targets
		setTimeout(() => {
			pageDesigner.pageDesignerContent.removeEventListener('scroll', preventScroll)
		}, 100)
	}

	private resetMoveDown(skipTransition = false) {
		const section = this.querySelector(':scope > section.moveDown')
		if (section === null)
			return

		if (skipTransition) {
			section.classList.add('resetMoveDown')
			setTimeout(() => {
				section.classList.remove('resetMoveDown')
			})
		}
		section.classList.remove('moveDown')
	}

	public async refreshGridEntity(gridElement: GridElement, elementInfo: GridViewTextType | GridViewFieldType | GridViewPageType | GridViewTileType) {
		// get config from lvl-grid-element
		const config = gridElement.config

		// switch over the element type and make changes accordingly
		switch (config.name) {
			case "Headline":
				elementInfo = elementInfo as GridViewTextType
				const headlineElement = gridElement.querySelector(`.page-designer__text`)
				if (!headlineElement)
					return

				// update flag
				gridElement.flag = elementInfo.textTypeTranslated

				// update text
				const sectionTitle = headlineElement.querySelector('label')
				if (!sectionTitle)
					return
				sectionTitle.innerText = elementInfo.textTranslated

				// update textType
				if (!headlineElement.classList.contains(elementInfo.textType)) {
					headlineElement.classList.remove('Headline1', 'Headline2', 'Headline3')
					headlineElement.classList.add(elementInfo.textType)
				}

				// update color
				sectionTitle.style.color = elementInfo.color ?? ''

				// update empty flag
				if (elementInfo.text === '')
					headlineElement.classList.add('empty')
				else
					headlineElement.classList.remove('empty')
				break;
			case "Text":
				elementInfo = elementInfo as GridViewTextType
				const textElement = gridElement.querySelector(`.page-designer__text`) as HTMLElement | null
				if (!textElement)
					return

				// update flag
				gridElement.flag = elementInfo.label || elementInfo.textTypeTranslated

				// update text
				textElement.innerText = elementInfo.text //TODO: make translatable

				// update color
				textElement.style.color = elementInfo.color ?? ''

				// update text align
				textElement.style.textAlign = elementInfo.textAlign?.toLowerCase() ?? 'left'

				// update empty flag
				if (elementInfo.text === '')
					textElement.classList.add('empty')
				else
					textElement.classList.remove('empty')
				break;
			case "Separator":
				elementInfo = elementInfo as GridViewTextType

				// update flag
				gridElement.flag = elementInfo.label || elementInfo.textTypeTranslated
				break;
			case "Annotation":
				elementInfo = elementInfo as GridViewFieldType
				if (elementInfo.dataFieldId)
					gridElement.querySelector('i.disconnected')?.classList.add('hidden')
				else
					gridElement.querySelector('i.disconnected')?.classList.remove('hidden')
				break;
			case "Input":
			case "TextInput":
				elementInfo = elementInfo as GridViewFieldType
				const inputElement = gridElement.querySelector(`lvl-input, lvl-toggle, lvl-textarea, lvl-autocomplete`) as InputElementType | null
				if (!inputElement)
					return

				// update visible config values
				gridElement.flag = elementInfo.dataField?.name || elementInfo.dataTypeTranslated
				inputElement.label = elementInfo.labelTranslated || elementInfo.dataField?.name || elementInfo.dataTypeTranslated
				inputElement.disconnected = elementInfo.dataFieldId == null || elementInfo.dataFieldId === ''
				inputElement.required = elementInfo.required
				inputElement.readonly = elementInfo.readonly
				inputElement.placeholder = elementInfo.placeholderTranslated
				inputElement.tooltip = elementInfo.helpTextTranslated
				inputElement.textAlign = elementInfo.textAlign
				break;
			case "Page":
				elementInfo = elementInfo as GridViewPageType

				// update flag
				gridElement.flag = elementInfo.flag

				// update text
				const pageTitle = gridElement.querySelector('label')!
				pageTitle.innerText = elementInfo.titleTranslated
				break;
			case "Tile":
				elementInfo = elementInfo as GridViewTileType

				// update flag
				gridElement.flag = elementInfo.flag

				// update text
				const tileTitle = gridElement.querySelector('label')!
				tileTitle.innerText = elementInfo.titleTranslated
				break;
		}
	}

	public async refreshColumnEntity(section: HTMLElement, elementInfo: GridViewSectionType) {
		const sectionTitle = section.querySelector('label')!
		sectionTitle.innerText = elementInfo.titleTranslated
		if (elementInfo.showTitle)
			sectionTitle.style.display = 'block'
		else
			sectionTitle.style.display = 'none'

		const sectionFlag = section.querySelector(".section-flag > span") as HTMLElement;
		sectionFlag.innerText = elementInfo.flag ?? elementInfo.title;
	}

	private async deleteColumnElement(element: HTMLElement) {
		let entityName
		switch (element.dataset.type) {
			case "Section":
				entityName = 'GridViewSections'
				break
			case "EmbeddedPage":
				entityName = 'GridViewPages'
				break
		}
		window.Page.deleteEntry(`/Api/${entityName}/${element.getAttribute('id')}`).then(function () {
			element.remove()
		})
	}

	private async renderFieldEntity(grid: Grid, elementInfo: GridViewFieldType) {
		let gridElement = this.getGridElement(grid, elementInfo.id)
		gridElement.rowStart = elementInfo.rowStart
		gridElement.rowEnd = elementInfo.rowEnd
		gridElement.colStart = elementInfo.colStart
		gridElement.colEnd = elementInfo.colEnd
		gridElement.flag = elementInfo.flag
		gridElement.config = toDataType(elementInfo.dataType) == DataType.Text ? GridElementConfig.TextInput : GridElementConfig.Input;

		gridElement.innerHTML = ''
		const inputWrapper = document.createElement('span')
		inputWrapper.classList.add('inputPreview')

		switch (elementInfo.dataType.toLowerCase()) {
			case DataType.Boolean:
				let toggle = document.createElement('lvl-toggle')
				toggle.preview = true
				toggle.label = elementInfo.labelTranslated
				toggle.disconnected = !elementInfo.dataFieldId
				inputWrapper.appendChild(toggle)
				break
			case DataType.Text:
				let textarea = document.createElement('lvl-textarea')
				textarea.preview = true
				textarea.label = elementInfo.labelTranslated
				textarea.disconnected = !elementInfo.dataFieldId
				inputWrapper.appendChild(textarea)
				break;
			case DataType.Guid:
				let autocomplete = document.createElement('lvl-autocomplete')
				autocomplete.preview = true
				autocomplete.label = elementInfo.labelTranslated
				autocomplete.disconnected = !elementInfo.dataFieldId
				autocomplete.required = elementInfo.required
				autocomplete.readonly = elementInfo.readonly
				autocomplete.placeholder = elementInfo.placeholderTranslated
				autocomplete.tooltip = elementInfo.helpTextTranslated
				inputWrapper.appendChild(autocomplete)
				break;
			case InputFieldType.Annotation:
				gridElement.config = GridElementConfig.Annotation
				inputWrapper.innerHTML = `<div class="annotationPreview"><i class="fal fa-map-location-dot icon"></i><i class="disconnected ${elementInfo.dataFieldId ? 'hidden' : ''}" data-tooltip="disconnectedTooltip"></i><lvl-tooltip name="disconnectedTooltip" orbit placement="right-start">Kein Plan verknüpft</lvl-tooltip></div>`
				break;
			default:
				let input = document.createElement('lvl-input')
				input.preview = true
				input.label = elementInfo.labelTranslated
				input.disconnected = !elementInfo.dataFieldId
				input.type = toDataType(elementInfo.dataType) || DataType.String
				input.required = elementInfo.required
				input.readonly = elementInfo.readonly
				input.placeholder = elementInfo.placeholderTranslated
				input.tooltip = elementInfo.helpTextTranslated
				input.textAlign = elementInfo.textAlign
				inputWrapper.appendChild(input)
				break;
		}

		gridElement.appendChild(inputWrapper)
	}

	private async renderPageEntity(grid: Grid, elementInfo: GridViewPageType) {
		let gridElement = this.getGridElement(grid, elementInfo.id)
		gridElement.rowStart = elementInfo.rowStart
		gridElement.rowEnd = elementInfo.rowEnd
		gridElement.colStart = elementInfo.colStart
		gridElement.colEnd = elementInfo.colEnd
		gridElement.flag = elementInfo.flag
		gridElement.config = GridElementConfig.Page

		gridElement.innerHTML = ''
		const textWrapper = document.createElement('span')
		textWrapper.classList.add('pagePreview')
		const label = document.createElement('label')
		label.textContent = elementInfo.titleTranslated
		textWrapper.appendChild(label)
		gridElement.appendChild(textWrapper)
	}

	//duplicate
	private async renderTileEntity(grid: Grid, elementInfo: GridViewTileType) {
		let gridElement = this.getGridElement(grid, elementInfo.id)
		gridElement.rowStart = elementInfo.rowStart
		gridElement.rowEnd = elementInfo.rowEnd
		gridElement.colStart = elementInfo.colStart
		gridElement.colEnd = elementInfo.colEnd
		gridElement.flag = elementInfo.flag
		gridElement.config = GridElementConfig.Tile

		gridElement.innerHTML = ''
		const textWrapper = document.createElement('span')
		textWrapper.classList.add('tilePreview', 'pagePreview')
		const label = document.createElement('label')
		label.textContent = elementInfo.titleTranslated
		textWrapper.appendChild(label)
		gridElement.appendChild(textWrapper)
	}

	private async renderTextEntity(grid: Grid, elementInfo: GridViewTextType) {
		let gridElement = this.getGridElement(grid, elementInfo.id)
		gridElement.rowStart = elementInfo.rowStart
		gridElement.rowEnd = elementInfo.rowEnd
		gridElement.colStart = elementInfo.colStart
		gridElement.colEnd = elementInfo.colEnd
		gridElement.flag = elementInfo.flag
		gridElement.config = elementInfo.textType == 'Separator' ? GridElementConfig.Separator : elementInfo.textType == 'Plain' ? GridElementConfig.Text : GridElementConfig.Headline;

		if (elementInfo.textType === "Separator") {
			gridElement.innerHTML = `<hr class="page-designer__separator"/>`
			return
		}

		gridElement.innerHTML = ''
		const textWrapper = document.createElement('span')
		textWrapper.classList.add('page-designer__text', elementInfo.textType)
		if (!elementInfo.text)
			textWrapper.classList.add('empty')
		const label = document.createElement('label')
		if (elementInfo.color)
			label.style.color = elementInfo.color
		if (elementInfo.textAlign)
			label.style.textAlign = elementInfo.textAlign
		label.textContent = elementInfo.textTranslated
		textWrapper.appendChild(label)
		gridElement.appendChild(textWrapper)
	}

	private getGridElement(grid: Grid, elementId: string) {
		let gridElement: GridElement | null = this.querySelector(`lvl-grid-element[id='${elementId}']`)
		if (!gridElement) {
			gridElement = document.createElement('lvl-grid-element')
			gridElement.id = elementId
			grid.appendChild(gridElement)
		} else if (gridElement.parentElement && gridElement.parentElement !== grid) {
			gridElement.parentElement.removeChild(gridElement)
			grid.appendChild(gridElement)
		}

		return gridElement
	}

	private async createColumnElement(targetColumn: HTMLElement, draggable: DraggableElement) {
		// abort if there is no target column
		if (targetColumn === undefined)
			return

		// get index of column
		const columnIndex = Array.from(targetColumn.parentNode?.children ?? []).indexOf(targetColumn)

		// remember "this" to use it inside the events
		const pageDesigner = this

		// did the element snap? (otherwise we only allow dropping at the end of the column)
		const snapped = draggable.classList.contains("snapped");

		// create dummy to insert
		const dummySection = document.createElement('section')
		dummySection.classList.add('page-designer__section', 'skeleton__block', 'embedded')
		dummySection.innerHTML = draggable.querySelector('label')?.outerHTML ?? draggable.innerHTML
		dummySection.dataset.type = draggable.dataset.type ?? "EmbeddedPage"
		dummySection.setAttribute("slot", columnIndex.toString())

		// find position relative to other sections
		const draggableRect = draggable.getBoundingClientRect()
		const sections = this.querySelectorAll(`:scope > section[slot='${columnIndex}']`)
		let i = 0
		for (const section of sections) {
			const sectionRect = section.getBoundingClientRect()
			if (sectionRect.y >= draggableRect.y) {
				// don't allow unsnapped dropping over used space
				if (!snapped)
					return

				this.insertBefore(dummySection, section)
				break
			}
			i++

			// don't allow drop over last section
			if (i === sections.length && !snapped && sectionRect.y + sectionRect.height >= draggableRect.y)
				return
		}

		// append to bottom if there is no section we need to insert it before
		if (i === sections.length)
			this.append(dummySection)

		let entityName = ""
		switch (draggable.dataset.type) {
			case "Section":
				entityName = "GridViewSection";
				break;
			case "EmbeddedPage":
				entityName = "GridViewPage";
				break;
		}

		const handleResponse = (response: CommunicationResponse<Record<string, any>>) => {
			if (response.state != CommunicationResponseType.Ok) {
				console.error("received empty response from server for create request")
				return
			}

			dummySection.setAttribute('id', response.data?.id)
			dummySection.addEventListener('click', () => {
				pageDesigner.editColumnElement(dummySection)
			})

			// TODO: improve section flag rendering (remove redundant code)
			dummySection.innerHTML += `<div class="section-flag"><span>${response.data?.flag ?? response.data?.title}</span><i class="fal fa-arrows-up-down-left-right"></i></div>`
			if (entityName === 'GridViewSection') {
				dummySection.innerHTML += '<lvl-grid rows="1"></lvl-grid>'
				pageDesigner.initGridDropzone(dummySection)
			}
			dummySection.classList.remove('embedded', 'skeleton__block')
		}

		// if the draggable represents an existing grid element, we need to update instead of create
		if (draggable.classList.contains('existingElement')) {
			// remove from grid
			const sourceElement = draggable.sourceElement as GridElement
			const grid = sourceElement?.closest('lvl-grid')
			grid?.removeElement(sourceElement)

			// close edit slideout (because button events are targeted to grid element instead of column element
			if(window.Page.editSlideOut)
				window.Page.editSlideOut.open = false

			// patch entry
			CommunicationServiceProvider.patch(`/Api/GridViewPages/${draggable.sourceElement?.id}`, null,{
				body: { GridViewId: this.pageViewId, GridViewColumn: columnIndex, Position: i }
			}).then((response) => {
				handleResponse(response)
				dummySection.dispatchEvent(new Event('click'))

				const grid = draggable.sourceElement?.closest('lvl-grid')
				if (grid) {
					const oldSiblings = grid.getRowElements((draggable.sourceElement as GridElement).rowStart)
					for (const sibling of oldSiblings) {
						if (!sibling.hasChanged())
							continue
						sibling.skeleton = true
						window.Page.updateEntry(`/Api/${sibling.config.entityName}s/${sibling.id}`, {
							ColStart: sibling.colStartPreview || sibling.colStartLocked,
							ColEnd: sibling.colEndPreview || sibling.colEndLocked,
						}).then(() => {
							sibling.applyPreview()
							sibling.skeleton = false
						})
					}
				}

				draggable.sourceElement?.remove()
			}).catch(() => {
				dummySection.remove()
			})
			return true
		}

		// wait for server request to return and remove skeleton
		CommunicationServiceProvider.post(`/Api/${entityName}s`, null, {
			body: { GridViewId: this.pageViewId, GridViewColumn: columnIndex, Position: i }
		}).then((response) => {
			handleResponse(response)
		}).catch((e) => {
			console.log('Failed to create new section', e)
			dummySection.remove()
		})

		return true
	}

	async updateColumnElement(targetColumn: HTMLElement, draggable: DraggableElement) {
		const targetElement = draggable.sourceElement

		// get index of column
		const columnIndex = Array.from(targetColumn.parentNode?.children ?? []).indexOf(targetColumn)

		// abort if there is no target column or no target element
		if (!targetColumn || !targetElement)
			return

		// did the element snap? (otherwise we only allow dropping at the end of the column)
		const snapped = draggable.classList.contains("snapped");

		// find position relative to other sections
		const draggableRect = draggable.getBoundingClientRect()
		const sections = this.querySelectorAll(`:scope > section[slot='${columnIndex}']:not(.dragActive)`)
		let i = 0
		for (const section of sections) {
			const sectionRect = section.getBoundingClientRect()
			if (sectionRect.y >= draggableRect.y) {
				// don't allow unsnapped dropping over used space and prevent unnecessary updates
				if (!snapped || section.id === draggable.id)
					return

				// detach element from old position and append to new one
				this.removeChild(targetElement)
				this.insertBefore(targetElement, section)

				// update slot
				targetElement.slot = columnIndex.toString()
				break
			}
			i++

			// don't allow drop over last section
			if (i === sections.length && !snapped && sectionRect.y + sectionRect.height >= draggableRect.y)
				return
		}

		// append to bottom if there is no section we need to insert it before
		if (i === sections.length) {
			this.removeChild(targetElement)
			this.append(targetElement)

			// update slot
			targetElement.slot = columnIndex.toString()
		}

		// show at new position
		targetElement.classList.remove('dragActive')

		// wait for server request to return and remove skeleton
		const elementId = targetElement.getAttribute('id')

		let entityName
		switch (targetElement.dataset.type) {
			case "Section":
				entityName = 'GridViewSections'
				break
			case "EmbeddedPage":
				entityName = 'GridViewPages'
				break
		}

		await CommunicationServiceProvider.patch(`/Api/${entityName}/${elementId}`, null, { body: { gridViewColumn: columnIndex, position: i }})
		return true
	}

	private yesNoDialog(callback: Function) {
		const form = window.Page.editSlideOut?.querySelector('lvl-form')
		if (!form)
			return

		// show yesno dialog and ask the user what to do
		const yesNoDialog = document.createElement('lvl-dialog')
		yesNoDialog.preset = DialogPreset.UnsavedChanges
		yesNoDialog.showOnce() // showOnce = dialog gets appended automatically to the DOM and removes itself after any of the preset buttons was clicked

		yesNoDialog.addEventListener('dialog-discard', () => {
			form.updateResetMarkers()
			callback()
		})
		yesNoDialog.addEventListener('dialog-save', () => {
			window.Page.editSlideOut?.querySelector<Button>('lvl-button[data-action=save]')?.click()
			this.pendingAction = () => {
				this.pendingAction = undefined
				callback()
			}
		})
	}

	async editColumnElement(element: HTMLElement) {
		// abort if already in edit mode for this element
		if (element.classList.contains('selected') || !window.Page.editSlideOut)
			return

		// check if there is an unchanged element pending
		if (window.Page.editSlideOut.open) {
			const form = window.Page.editSlideOut.querySelector('lvl-form')
			if (!form)
				return

			if (form.hasChanges()) {
				this.yesNoDialog(() => {
					this.editColumnElement(element)
				})
				return
			}
		}

		// remember "this" to use it inside the events
		const pageDesigner = this

		// deselect currently selected element
		this.querySelectorAll('section.selected').forEach((section) => {
			section.classList.remove('selected')
		})
		this.querySelectorAll('lvl-grid-element[selected]').forEach((gridElement) => {
			gridElement.removeAttribute('selected')
		})

		// enable skeleton loading animation
		let headerLabel
		let entityName: string
		let formName
		switch (element.dataset.type) {
			case "Section":
				headerLabel = PageDesigner._localizer.localize('editSection')
				entityName = 'GridViewSections'
				formName = 'grid-view-section-form'
				break;
			case "EmbeddedPage":
				headerLabel = PageDesigner._localizer.localize('editEmbeddedPage')
				entityName = 'GridViewPages'
				formName = 'grid-view-page-form'
				break;
			default:
				console.warn("unknown entity type for element", element)
				return
		}
		window.Page.editSlideOut.skeleton = true
		window.Page.editSlideOut.setAttribute('heading', headerLabel || "")
		element.classList.add('selected')

		// start to load data
		const loadData = window.Page.getJSON<Record<string, any>>(`/Api/${entityName}/${element.getAttribute('id')}`)

		// load page (if needed) and update page info
		let editForm = window.Page.editSlideOut.querySelector('lvl-form')
		if (editForm?.getAttribute('id') !== formName || editForm?.getAttribute('element-type') != 'columnElement') {
			await window.Page.showEditPanel(`/Admin/${entityName}/Edit`, {}, headerLabel)
			editForm = window.Page.editSlideOut.querySelector('lvl-form')
			editForm?.setAttribute('element-type', 'columnElement')
			window.Page.setPanelInfo(window.Page.getMainPageUrl() + '/Designer', {}, headerLabel)
			window.Page.editSlideOut.querySelector('[data-action=delete]')?.addEventListener('click', () => {
				const form =  window.Page.editSlideOut?.querySelector('lvl-form')
				form?.updateResetMarkers()
				this.deleteColumnElement(document.querySelector('.selected')!)
				window.Page.editSlideOut!.open = false
			})

			// add change event to translation fields (update element representation inside the designer)
			window.Page.editSlideOut.querySelectorAll('lvl-input[type=translation]').forEach((field) => {
				field.addEventListener('translations:changed', async () => {
					let elementId = window.Page.editSlideOut!.querySelector('lvl-form')?.querySelector('[name=id]')?.getAttribute('value')
					let currentElement = this.querySelector(`section[id='${elementId}']`) // we cant just use "element" here because the events stay the same if we edit multiple sections one after another using the same form)
					let json = await window.Page.getJSON<GridViewSectionType>(`/Api/${entityName}/${elementId}`)
					if (json.data)
						pageDesigner.refreshColumnEntity(currentElement as HTMLElement, json.data)
				})
			})
		} else {
			(window.Page.editSlideOut.querySelector('lvl-form') as Form)?.clear(true)
			window.Page.editSlideOut.open = true
		}

		// load user info
		let json = await loadData

		// update Autocomplete URL BEFORE inserting data (otherwise url change triggers reset() and removes the newly set value)
		const form = window.Page.editSlideOut.querySelector('lvl-form')
		if (!form)
			return

		// update Autocomplete URLs
		if (element.dataset.type === "EmbeddedPage")
			this.prepareGridViewPageURLs(form, json.data || {})

		await window.Page.setFormData(form, json.data || {})

		// disable skeleton
		window.Page.editSlideOut.skeleton = false
	}

	async editGridElement(element: GridElement) {
		// abort if already in edit mode
		if (element.selected)
			return

		// return if no edit slide out is there
		if (!window.Page.editSlideOut)
			return

		// check if there is an unchanged element pending
		if (window.Page.editSlideOut.open) {
			const form = window.Page.editSlideOut.querySelector('lvl-form')
			if (!form)
				return

			if (form.hasChanges()) {
				this.yesNoDialog(() => {
					this.editGridElement(element)
				})
				return
			}
		}

		// deselect currently selected element
		document.querySelectorAll('section.selected').forEach((section) => {
			section.classList.remove('selected')
		})
		document.querySelectorAll('lvl-grid-element[selected]').forEach((gridElement) => {
			(gridElement as any as GridElement).selected = false
		})

		// enable skeleton loading animation
		window.Page.editSlideOut.skeleton = true
		element.selected = true

		// start to load data
		const loadData = window.Page.getJSON<Record<string, any>>(`/Api/${element.config.entityName}s/${element.id}`)

		// set slideout heading
		const title = element.config.getEditLabel()
		window.Page.editSlideOut.setAttribute('heading', title)

		// load page (if needed) and update page info
		let form = window.Page.editSlideOut.querySelector('lvl-form')
		if (form?.id !== element.config.formName || form.getAttribute('element-type') != 'gridElement') {
			await window.Page.showEditPanel(`/Admin/${element.config.entityName}s/Edit`, element.config.entityParams, title)
			form = window.Page.editSlideOut.querySelector('lvl-form') as Form
			form.setAttribute('element-type', 'gridElement')

			window.Page.setPanelInfo(window.Page.getMainPageUrl() + '/Designer', {}, title)
			window.Page.editSlideOut.heading = title
			window.Page.editSlideOut.querySelector('[data-action=delete]')?.addEventListener('click', () => {
				let elementId = (form!.querySelector('[name=id]') as HTMLInputElement).value
				let currentElement = this.querySelector(`lvl-grid-element[id='${elementId}']`) as GridElement
				form?.updateResetMarkers()
				this.deleteGridElement(currentElement)
				window.Page.editSlideOut!.open = false
			})

			// add change event to translation fields (update grid element representation inside the designer)
			window.Page.editSlideOut.querySelectorAll('lvl-input[type=translation]').forEach((field) => {
				field.addEventListener('translations:changed', async () => {
					let elementId = (form!.querySelector('[name=id]') as HTMLInputElement).value
					let currentElement = this.querySelector(`lvl-grid-element[id='${elementId}']`) as GridElement
					let json = await window.Page.getJSON(`/Api/${element.config.entityName}s/${elementId}`)
					this.refreshGridEntity(currentElement, json.data as any)
				})
			})
		} else {
			form.clear(true)
			window.Page.editSlideOut.open = true
		}

		let json = await loadData
		if(!json.data)
			return

		if (element.config == GridElementConfig.Page || element.config == GridElementConfig.Tile) {
			// update Autocomplete URLs BEFORE inserting data (otherwise url change triggers reset() and removes the newly set value)
			this.prepareGridViewPageURLs(form, json.data)
		} else {
			const dataFieldAutocomplete = form.querySelector('#data-field')
			if (dataFieldAutocomplete)
				dataFieldAutocomplete.setAttribute('url', `/Api/GridViews/${this.pageViewId}/Fields/${element.id}/DataFields`)
		}

		// insert data
		await window.Page.setFormData(form, json.data)

		// disable skeleton
		window.Page.editSlideOut.skeleton = false
	}

	private prepareGridViewPageURLs(form: Form, data: Record<string, any>) {
		const referenceFieldAutocomplete = form.querySelector('#reference-field')
		if (referenceFieldAutocomplete)
			referenceFieldAutocomplete.setAttribute('url', `/Api/DataSources/${window.Page.getFormData().page.dataSourceId}/LookupFields`)

		const pageAutocomplete = form.querySelector('#embedded-page')
		if (pageAutocomplete) {
			let embeddedPageUrl = `/Api/DataSources/${window.Page.getFormData().page.dataSourceId}/EmbeddedPages?type=MultiData`
			if (data.referenceType == "ForeignElement")
				embeddedPageUrl += `&referenceField=${data.referenceFieldId}`
			pageAutocomplete.setAttribute('url', embeddedPageUrl)
		}

		const keyFieldAutocomplete = form.querySelector('#key-field')
		if (keyFieldAutocomplete) {
			let keyFieldUrl = `/Api/DataSources/${window.Page.getFormData().page.dataSourceId}/EmbeddedPages/${data.embeddedPageId}/KeyFields`
			if (data.referenceType == "ForeignElement")
				keyFieldUrl += `?referenceField=${data.referenceFieldId}`
			keyFieldAutocomplete.setAttribute('url', keyFieldUrl)
		}

		const viewAutocomplete = form.querySelector('#embedded-view')
		if (viewAutocomplete)
			viewAutocomplete.setAttribute('url', `/Api/EmbeddedPages/${data.embeddedPageId}/Views`)
	}

	// append drag listener to active grid element
	private appendDragListener(gridElement: GridElement) {
		const pageDesigner = this

		interact(gridElement).draggable({
			allowFrom: '#gridElementFlag',
			manualStart: true,
			autoScroll: {
				container: this.pageDesignerContent,
			},
			modifiers: [
				interact.modifiers.restrictRect({
					restriction: '#content',
				}),
				interact.modifiers.snap({
					targets: this.gridSnapTargets,
					relativePoints: [
						{ x: 1, y: 0 },
					],
					offset: 'self',
				}),
			],
			listeners: {
				start(event) {
					pageDesigner.classList.add('dragActive')
					const sourceElement = event.currentTarget.sourceElement
					const grid = sourceElement.parentElement

					// check if items filled the row evenly
					const allowSqueezeBack = grid.allowSqueezeBack(sourceElement)

					// temporarily hide sourceElement from grid
					grid.hideElement(sourceElement)

					// reposition remaining siblings
					if (allowSqueezeBack) {
						sourceElement.squeezeBack = true
						const siblings = grid.getRowElements(sourceElement.rowStart)
						const elementCount = siblings.length
						const colSpread = pageDesigner.GRID_COLUMNS / elementCount
						for (let i = 0; i < elementCount; i++) {
							const sibling = siblings[i]
							sibling.colStartLocked = (colSpread * i) + 1
							sibling.colEndLocked = (colSpread * (i + 1)) + 1
						}
					}

					// remember start positions to recalculate snap Targets later
					event.target.startRect = event.target.getBoundingClientRect()
					pageDesigner.dragPosition = { x: 0, y: 0, scrollTop: pageDesigner.pageDesignerContent.scrollTop, scrollLeft: pageDesigner.pageDesignerContent.scrollLeft }

					// calculate allowed snap targets
					pageDesigner.calculateGridSnapTargets(event.target.startRect, sourceElement.colEnd - sourceElement.colStart, sourceElement.rowEnd - sourceElement.rowStart, sourceElement.config)
				},
				move(event) {
					const draggable = event.currentTarget

					// recalculate snap targets after autoscroll
					if (pageDesigner.hasScrolled) {
						const now = window.performance.now()
						pageDesigner.lastMoveEvent = now
						setTimeout(() => {
							if (pageDesigner.lastMoveEvent !== now)
								return
							const sourceElement = draggable.sourceElement
							pageDesigner.calculateGridSnapTargets(event.target.startRect, sourceElement.colEnd - sourceElement.colStart, sourceElement.rowEnd - sourceElement.rowStart, sourceElement.config)
							pageDesigner.hasScrolled = false
						}, 10)
					}

					// move element to target Position
					pageDesigner.dragPosition.x += event.dx
					pageDesigner.dragPosition.y += event.dy
					draggable.style.setProperty('--dragX', pageDesigner.dragPosition.x)
					draggable.style.setProperty('--dragY', pageDesigner.dragPosition.y)

					// revert previous sibling positions
					if (draggable.sourceElement.config == GridElementConfig.Page)
						pageDesigner.resetMoveDown()
					pageDesigner.resetMoveDown()
					pageDesigner.querySelectorAll('lvl-grid-element[col-start-preview]').forEach((element) => (element as GridElement).revertPreview())
					draggable.classList.remove('snapped', 'snappedToGrid')
					draggable.style.removeProperty('--widthPreview')

					// if snapped, apply new previews
					if (event.modifiers[1].inRange) {
						const snapTarget = event.modifiers[1].target.source
						draggable.classList.add('snapped')
						if ((snapTarget as GridSnapTargetType).grid)
							draggable.classList.add('snappedToGrid')

						switch (snapTarget.type) {
							case 'Drop':
								if (snapTarget.squeezeWidth) {
									draggable.style.setProperty('--widthPreview', snapTarget.squeezeWidth + 'px')
									draggable.style.setProperty('--dragX', pageDesigner.dragPosition.x + (event.target.startRect.width - snapTarget.squeezeWidth))
								}
								break
							case 'Squeeze':
								const siblings = snapTarget.siblings
								const gridRect = snapTarget.grid.getBoundingClientRect()
								const colWidth = gridRect.width / pageDesigner.GRID_COLUMNS
								const colSpread = pageDesigner.GRID_COLUMNS / (siblings.length + 1)

								// update draggable width
								draggable.style.setProperty('--widthPreview', (colWidth * colSpread) + 'px')
								draggable.style.setProperty('--dragX', pageDesigner.dragPosition.x + (event.target.startRect.width - colWidth * colSpread))

								// update sibling widths for preview purposes
								for (let position = 0; position < siblings.length; position++) {
									const sibling = siblings[position]
									if (position < snapTarget.position) {
										sibling.colStartPreview = (colSpread * position) + 1
										sibling.colEndPreview = (colSpread * (position + 1)) + 1
									} else {
										sibling.colStartPreview = (colSpread * (position + 1)) + 1
										sibling.colEndPreview = (colSpread * (position + 2)) + 1
									}
								}
								break
						}

						// pages may interact with column elements
						if (draggable.sourceElement.config == GridElementConfig.Page)
							pageDesigner.moveDown(draggable, snapTarget)
					}
				},
				end(event) {
					pageDesigner.classList.remove('dragActive')
					const draggable = event.currentTarget

					// show source element again if move was not successfull				
					if (!event.modifiers[1].inRange) {
						const grid = draggable.sourceElement.parentElement
						const sourceElement = draggable.sourceElement

						// animate draggable back to start position
						draggable.classList.add('resetPosition')
						const scrollTop = pageDesigner.dragPosition.scrollTop - pageDesigner.pageDesignerContent.scrollTop
						const scrollLeft = pageDesigner.dragPosition.scrollLeft - pageDesigner.pageDesignerContent.scrollLeft
						draggable.style.setProperty('--dragX', scrollLeft)
						draggable.style.setProperty('--dragY', scrollTop)

						// reposition siblings (if they were squeezed on dragstart)
						if (sourceElement.squeezeBack)
							grid.getRowElements(sourceElement.rowStart).forEach((element: GridElement) => element.revertPreview(true))

						setTimeout(() => {
							// remove draggable
							draggable.remove()

							// show real element again
							grid.showElement(sourceElement)

							// scroll back to start if needed
							pageDesigner.pageDesignerContent.classList.add('resetScroll')
							setTimeout(() => {
								sourceElement.scrollIntoViewIfNeeded()
								pageDesigner.pageDesignerContent.classList.remove('resetScroll')
							})
						}, 100)

						window.addEventListener('click', (event) => {
							event.stopImmediatePropagation()
						}, { capture: true, once: true })
					}
				},
			},
		}).on('down', (event) => {
			const interaction = event.interaction
			if (event.target.matches('#gridElementFlag') || event.target.closest('#gridElementFlag') != null)
				interaction.allowStart = true
		}).on('up', (event) => {
			const interaction = event.interaction
			interaction.allowStart = false
		}).on('move', function (event) {
			const interaction = event.interaction

			// if the pointer was moved while being held down
			// and an interaction hasn't started yet
			if (interaction.pointerIsDown && !interaction.interacting() && interaction.allowStart) {

				// create draggable element
				const draggable = pageDesigner.createGridDragElement(gridElement)

				// start a drag interaction targeting the clone
				interaction.start({ name: 'drag' }, event.interactable, draggable)
				interaction.allowStart = false
			}
		})
	}

	// append resize listener to active grid element
	private appendResizeListener(gridElement: GridElement) {
		let colWidth = 0
		let rowHeight = 72
		const pageDesigner = this

		interact(gridElement).resizable({
			//allowFrom: '.dragHandle',
			edges: {
				left: gridElement.config.resizeHorizontal,
				right: gridElement.config.resizeHorizontal,
				top: gridElement.config.resizeVertical,
				bottom: gridElement.config.resizeVertical,
			},
			margin: 4,
			autoScroll: { container: pageDesigner.pageDesignerContent },
			invert: 'negate', // needed in order to get correct numbers for scroll calculations
			listeners: {
				start: function (event) {
					// calculate current grid dimensions
					const parentRect = gridElement.parentElement!.getBoundingClientRect()
					colWidth = ((parentRect.width - ((pageDesigner.GRID_COLUMNS-1) * 4)) / pageDesigner.GRID_COLUMNS) + 4

					// init delta
					event.target.deltaLeft = 0
					event.target.deltaTop = 0

					// remember scroll position of autoscroll container
					event.target.dataset.scrollTop = pageDesigner.pageDesignerContent.scrollTop
					event.target.dataset.scrollLeft = pageDesigner.pageDesignerContent.scrollLeft
				},
				move: function (event) {
					const scrollTop = pageDesigner.pageDesignerContent.scrollTop - parseFloat(event.target.dataset.scrollTop)
					const scrollLeft = pageDesigner.pageDesignerContent.scrollLeft - parseFloat(event.target.dataset.scrollLeft)

					const x = event.target.deltaLeft + event.deltaRect.left
					const y = event.target.deltaTop + event.deltaRect.top

					const gridRowStart = event.target.rowStart
					const gridRowEnd = event.target.rowEnd
					const gridColumnStart = event.target.colStart
					const gridColumnEnd = event.target.colEnd

					const rStart = y !== 0 ? gridRowStart + Math.round((y + scrollTop) / rowHeight) : gridRowStart
					const rEnd = y === 0 ? gridRowStart + Math.round((event.rect.height + scrollTop + pageDesigner.GRID_ROW_GAP) / rowHeight) : gridRowEnd
					const cStart = x !== 0 ? gridColumnStart + Math.round((x + scrollLeft) / colWidth) : gridColumnStart
					const cEnd = x === 0 ? gridColumnStart + Math.round((event.rect.width + scrollLeft + pageDesigner.GRID_COL_GAP) / colWidth) : gridColumnEnd

					// remember current total delta
					event.target.deltaLeft = x
					event.target.deltaTop = y

					// apply calculated dimensions
					if (event.target.parentElement.allowResize(event.target, cStart, cEnd, rStart, rEnd)) {
						event.target.colStartPreview = cStart
						event.target.colEndPreview = cEnd
						event.target.rowStartPreview = rStart
						event.target.rowEndPreview = rEnd
					}
				},
				end(event) {
					// store changes if any
					if (event.target.hasChanged()) {
						event.target.skeleton = true
						window.Page.updateEntry(`/Api/${event.target.config.entityName}s/${event.target.id}`, {
							ColStart: event.target.colStartPreview,
							ColEnd: event.target.colEndPreview,
							RowStart: event.target.rowStartPreview,
							RowEnd: event.target.rowEndPreview,
						}).then(() => {
							event.target.applyPreview()
							event.target.skeleton = false
						})
					}

					// prevent end of drag to be detected as click
					window.addEventListener('click', (event) => {
						event.stopImmediatePropagation()
					}, { capture: true, once: true })
				},
			},
		}).styleCursor(false)
	}

	// snap targets for elements inside section grids
	private calculateGridSnapTargets(startRect: DOMRect, elementWidth: number, elementHeight: number, elementConfig: GridElementConfig) {
		if (startRect === undefined) {
			console.warn('calculateGridSnapTargets called without startRect', startRect)
			return
		}

		// reset array (overwriting it with [] destroys the reference)
		this.gridSnapTargets.length = 0

		this.querySelectorAll('section.page-designer__section > lvl-grid[initDone]:not([skeleton])').forEach((grid) => {
			this.gridSnapTargets.push(...(grid as Grid).getSnapTargets(startRect, elementWidth, elementHeight, elementConfig))
		})

		if (elementConfig == GridElementConfig.Page) {
			this.calculateColumnSnapTargets(startRect)
			this.gridSnapTargets.push(...this.columnSnapTargets)
		}
	}

	private createGridDragElement(dragSource: GridElement) {
		// create drag element aka element preview
		const draggable = document.createElement('div') as DraggableElement
		draggable.style.position = 'absolute' // important to prevent pipeline flakies
		draggable.sourceElement = dragSource
		draggable.classList.add('dragElement', 'gridDragElement', 'dragging')
		if (dragSource.config == GridElementConfig.Page)
			draggable.classList.add('columnDragElement')
		draggable.innerHTML = dragSource.innerHTML

		const dragSourceRect = dragSource.getBoundingClientRect()
		draggable.classList.add('existingElement')

		// calculate maximum width of the drag element
		let maxWidth = undefined
		for (const column of this.pageColumnWrapper.querySelectorAll('.page-column')) {
			const columnMaxWidth = column.getBoundingClientRect().width - this.GRID_ELEMENT_GAP
			if (maxWidth === undefined || columnMaxWidth < maxWidth)
				maxWidth = columnMaxWidth / 2
		}

		let draggableWidth = !maxWidth || dragSourceRect.width < maxWidth ? dragSourceRect.width : maxWidth
		draggable.style.width = draggableWidth + 'px'
		if (dragSource.config !== GridElementConfig.Page)
			draggable.style.height = (dragSource.config.minHeight * this.GRID_ROW_HEIGHT + ((dragSource.config.minHeight - 1) * this.GRID_ROW_GAP)) + 'px'
		if (dragSource.flag !== undefined) {
			const flag = document.createElement('div')
			flag.classList.add('element-flag')
			flag.innerText = dragSource.flag
			const moveIcon = document.createElement('i')
			moveIcon.classList.add('fal', 'fa-arrows-up-down-left-right')
			flag.append(moveIcon)
			draggable.append(flag)
		}

		// position draggable to match drag button position
		draggable.style.setProperty('--top', dragSourceRect.y.toString())
		draggable.style.setProperty('--left', (dragSourceRect.x + dragSourceRect.width - draggableWidth).toString())
		this.parentElement!.appendChild(draggable)

		return draggable
	}

	private initGridDropzone(section: HTMLElement) {
		const sectionId = section.id
		const lvlGrid = section.querySelector('lvl-grid')
		if (!lvlGrid)
			return

		// remember "this" to use it inside the events
		const pageDesigner = this

		interact(lvlGrid).dropzone({
			accept: '.gridDragElement, lvl-grid-element',
			overlap: 0.1,
			ondrop: function (event) {
				if (!event.dragEvent.modifiers[1].inRange)
					return

				const snapTarget = event.dragEvent.modifiers[1].target.source
				const sourceElement = event.relatedTarget.sourceElement

				if (sourceElement.tagName == 'LVL-GRID-ELEMENT')
					pageDesigner.updateGridElement(sectionId, snapTarget, sourceElement)
				else
					pageDesigner.createGridElement(sectionId, snapTarget, event.relatedTarget)

				// remove draggable
				event.relatedTarget.remove()
			},
		})

		lvlGrid.dataset.cy = 'dropzone'
	}

	private async createGridElement(sectionId: string, snapTarget: GridSnapTargetType, draggable: DraggableElement) {
		// abort if there is no snapTarget
		if (snapTarget === undefined)
			return

		// add content to grid and wait for server request to finish
		const gridElement = snapTarget.grid.addElement(draggable.children, snapTarget.rowStart, snapTarget.rowEnd, snapTarget.colStart, snapTarget.colEnd, true)
		if (!gridElement)
			return

		const handleResponse = (response: LevelResponse<Record<string, any>>): GridElement => {
			gridElement.id = response.data.id
			switch (draggable.entityName) {
				case "GridViewField":
					gridElement.setAttribute('config', response.data.dataType == 'Text' ? 'TextInput' : response.data.dataType == 'Annotation' ? 'Annotation' : 'Input')
					gridElement.flag = response.data.dataTypeTranslated
					break
				case "GridViewText":
					gridElement.setAttribute('config', response.data.textType == 'Plain' ? 'Text' : response.data.textType == 'Separator' ? 'Separator' : 'Headline')
					gridElement.flag = response.data.textTypeTranslated
					break
				case "GridViewPage":
					if(draggable.config?.name == "Tile"){
						gridElement.setAttribute('config', 'Tile')
						gridElement.flag = response.data.flag
						gridElement.innerHTML = '<span class="tilePreview pagePreview"><label>'+response.data.titleTranslated+'</label></span>'
						break
					}
					gridElement.setAttribute('config', 'Page')
					gridElement.flag = response.data.flag
					gridElement.innerHTML = '<span class="pagePreview"><label>'+response.data.titleTranslated+'</label></span>'
					break
			}
			gridElement.skeleton = false

			// update squeezed elements if necessary
			if (snapTarget.type === 'Squeeze') {
				for (const sibling of snapTarget.siblings || []) {
					sibling.skeleton = true
					const path = `/Api/${sibling.config.entityName}s/${sibling.id}`
					window.Page.updateEntry(path, { ColStart: sibling.colStartPreview, ColEnd: sibling.colEndPreview }).then(() => {
						sibling.applyPreview()
						sibling.skeleton = false
					})
				}
			}

			return gridElement
		}

		// if the draggable represents an existing column element, we need to update instead of create
		if (draggable.classList.contains('existingElement')) {
			// close edit slideout (because button events are targeted to column element instead of grid element
			if(window.Page.editSlideOut)
				window.Page.editSlideOut.open = false

			window.Page.updateEntry(`/Api/${draggable.entityName}s/${draggable.id}`, {
				SectionId: sectionId,
				RowStart: snapTarget.rowStart,
				RowEnd: snapTarget.rowEnd,
				ColStart: snapTarget.colStart,
				ColEnd: snapTarget.colEnd,
				...draggable.additionalData ?? {}
			}).then((response: LevelResponse<Record<string, any>>) => {
				const gridElement = handleResponse(response)
				gridElement?.dispatchEvent(new Event('click'))
				draggable.sourceElement?.remove()
			}).catch(() => {
				gridElement.remove()
			})
			return
		}
		
		if(draggable.config?.name == "Tile")
			draggable.additionalData = {GridViewPageType: "Tile"}
		
		// create new element
		window.Page.createEntry(`/Api/${draggable.entityName}s`, {
			GridViewId: this.pageViewId,
			SectionId: sectionId,
			RowStart: snapTarget.rowStart,
			RowEnd: snapTarget.rowEnd,
			ColStart: snapTarget.colStart,
			ColEnd: snapTarget.colEnd,
			...draggable.additionalData ?? {}
		}).then((response: LevelResponse<Record<string, any>>) => handleResponse(response)).catch(() => {
			gridElement.remove()
		})
	}

	private async updateGridElement(sectionId: String, snapTarget: GridSnapTargetType, element: GridElement) {
		// abort if there is no snapTarget
		if (snapTarget === undefined)
			return

		// remember old siblings and remove element from old position
		const grid = element.closest('lvl-grid')
		if (!grid)
			return

		const oldSiblings = grid.getRowElements(element.rowStart)
		grid.removeElement(element)

		// add at new position
		const gridElement = snapTarget.grid.addElement(element.children, snapTarget.rowStart, snapTarget.rowEnd, snapTarget.colStart, snapTarget.colEnd, true)
		if (!gridElement)
			return

		gridElement.id = element.id
		gridElement.flag = element.flag
		gridElement.config = element.config

		// select and make resizeable 
		gridElement.selected = true
		this.appendDragListener(gridElement)
		this.appendResizeListener(gridElement)

		// update server entry
		window.Page.updateEntry(`/Api/${element.config.entityName}s/${element.id}`, {
			SectionId: sectionId,
			RowStart: snapTarget.rowStart,
			RowEnd: snapTarget.rowEnd,
			ColStart: snapTarget.colStart,
			ColEnd: snapTarget.colEnd,
		}).then(function () {
			gridElement.skeleton = false

			// collect unique affected siblings
			const siblings = Array.from(new Set([ ...oldSiblings || [], ...snapTarget.siblings || [] ]))

			// update affected siblings
			for (const sibling of siblings) {
				if (!sibling.hasChanged())
					continue

				sibling.skeleton = true
				window.Page.updateEntry(`/Api/${sibling.config.entityName}s/${sibling.id}`, {
					ColStart: sibling.colStartPreview || sibling.colStartLocked,
					ColEnd: sibling.colEndPreview || sibling.colEndLocked,
				}).then(() => {
					sibling.applyPreview()
					sibling.skeleton = false
				})
			}
		}).catch(() => {
			gridElement.remove()
		})
	}

	private async deleteGridElement(element: GridElement) {
		const grid = element.closest('lvl-grid')
		if (!grid)
			return

		// remember "this" to use it inside the events
		const pageDesigner = this

		// check if we can "squeeze back"
		window.Page.deleteEntry(`/Api/${element.config.entityName}s/${element.id}`).then(function () {

			// check if items filled the row evenly
			const allowSqueezeBack = grid.allowSqueezeBack(element)

			// remove current item
			grid.removeElement(element)

			// reposition remaining siblings
			if (allowSqueezeBack) {
				const siblings = grid.getRowElements(element.rowStart)
				const elementCount = siblings.length
				const colSpread = pageDesigner.GRID_COLUMNS / elementCount
				for (let i = 0; i < elementCount; i++) {
					const sibling = siblings[i]
					sibling.colStartPreview = (colSpread * i) + 1
					sibling.colEndPreview = (colSpread * (i + 1)) + 1
					sibling.skeleton = true
					window.Page.updateEntry(`/Api/${sibling.config.entityName}s/${sibling.id}`, { ColStart: sibling.colStartPreview, ColEnd: sibling.colEndPreview })
								.then(() => {
									sibling.applyPreview()
									sibling.skeleton = false
								})
				}
			}
		})
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-page-designer': PageDesigner
	}

	interface Window {
		Page: PageNavigatorType
	}
}