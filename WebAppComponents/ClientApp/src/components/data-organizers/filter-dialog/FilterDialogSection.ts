import { css, html, LitElement } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { DataType } from '@/enums'
import { formatData, isText } from '@/enums/data-type.ts'
import { ifDefined } from 'lit/directives/if-defined.js'
import { DataFilter } from '@/shared/types.ts'
import { Size } from '@/enums/size.ts'
import { Operator } from '@/enums/operator.ts'
import { getAllDateSpans } from '@/enums/date-span.ts'
import { FilterSectionMixin, FilterSectionMixinType, PreviewValueType } from '@/components/data-organizers/filter-mixins/FilterSectionMixin.ts'
import { query } from 'lit/decorators/query.js'
import { capitalize } from '@/shared/data-operations.ts'

export type FilterDialogSectionType = FilterSectionMixinType & {
	focused: boolean
}

const FilterSectionWrapper = FilterSectionMixin(LitElement)

/**
 * PanelSection web component as part of FilterPanel using LIT (https://lit.dev)
 */
@customElement('lvl-filter-dialog-section')
export class FilterDialogSection extends FilterSectionWrapper implements FilterDialogSectionType {

	static styles = [
		styles.base,
		styles.color,
		styles.skeleton,
		css`
			:host {
				--clr-skeleton-block: var(--cp-clr-background-lvl-2);
				--clr-label: var(--cp-clr-text-primary-positiv); 
			}
			
			.dialog__filter-section {
				display: grid;
				row-gap: var(--size-spacing-s);
				margin-bottom: var(--size-spacing-m);
				padding-left: var(--size-spacing-xs);
			}

			.filter-section__header {
				position: sticky;
				top: 0;
				z-index: 1;

				display: flex;
				align-items: center;
				height: 2.4rem;
				gap: var(--size-spacing-s);
				background-color: var(--cp-clr-background-lvl-0);

				& span {
					font-weight: bold;
				}
			}

			.section__empty {
				display: block;
				text-align: center;
				color: var(--cp-clr-text-tertiary);
			}
			
			.skeleton__block {
				height: 4.8rem
			}
		`,
	]

	//#region attributes
	
	@property({ type: Boolean, reflect: true })
	focused: boolean = false
	
	//#endregion

	//#region states
	//#endregion states

	//#region private properties
	
	@query('.filter-section__body')
	private _htmlBody!: HTMLElement
	
	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		return html`
			<div class="dialog__filter-section" @focusout="${this.handleFocusout}">
				<div class="filter-section__header">
					<span class="text--secondary">${this.label || this.name}</span>
				</div>
				<div class="filter-section__body ${this.loading ? 'skeleton__block' : ''}">
					${this.valuePreview ? this.renderSelectList() : this.renderInput()}
				</div>
			</div>
		`
	}

	renderInput() {
		if (this.type === DataType.Boolean) {
			const labelTrue = capitalize(formatData(true, DataType.Boolean, this.getTypeOptions())) || this.localize('filterBooleanYes')
			const labelFalse = capitalize(formatData(false, DataType.Boolean, this.getTypeOptions())) || this.localize('filterBooleanNo')
			return html`
				<lvl-button-group value="${this.value}" @change="${this.handleInputChange}">
					<lvl-button label="${this.localize('filterBooleanAll')}" value=""></lvl-button>
					<lvl-button label="${labelTrue}" value="true"></lvl-button>
					<lvl-button label="${labelFalse}" value="false"></lvl-button>
				</lvl-button-group>
			`
		}

		if (this.type === DataType.Date || this.type === DataType.DateTime)
			return this.renderSelectList(getAllDateSpans().map(span => ({ label: this.localizeDateSpan(span), value: span })))

		return html`
			<lvl-input type="${this.type}" .value="${this.value}" ?multi-value="${this.multiValue}" nullable
								 placeholder="${ifDefined(this.placeholder)}" @change="${this.handleInputChange}"></lvl-input>`
	}

	renderSelectList(listItems: PreviewValueType[] = this.possibleValues ?? []) {
		if (!listItems || listItems.length == 0) {
			return html`<span class="section__empty">${this.localize('noEntries')}</span>`
		}
		const renderTooManyItemsMessage = () => html`<span class="section__hint">${this.localize('tooManyItemsHint')}</span>`
		return html`
			<lvl-select-list .value="${this.value}" ?multi-value="${this.multiValue}" nullable float
											 size="${Size.Small}" @change="${this.handleSelectChange}" @focusin="${this.handleFocusin}">
				${listItems?.slice(0, 50)?.map(record => html`
					<lvl-select-list-item label="${record.label || '-'}" .value="${record.value}"
																count="${record.count}"
																?selected="${this.multiValue ? this.value?.includes(record.value) : record.value === this.value}"></lvl-select-list-item>`)}
			</lvl-select-list>
			${listItems?.length > 50 ? renderTooManyItemsMessage() : ''}
		`
	}

	//#region lifecycle callbacks
	//#endregion

	//#region public methods

	/**
	 * override the default focus method of HTMLElement
	 * @param options
	 */
	focus(options?: FocusOptions) {
		const firstItemElement = this._htmlBody.querySelector('lvl-input, lvl-select-list-item, lvl-button-group > lvl-button') as HTMLElement
		firstItemElement?.focus(options)
	}

	/*
	 * Build the filter for this section
	 */
	public getFilters(): DataFilter[] | null {
		if (this.multiValue)
			return this.getMultiFilter()

		const filter = this.value != null ? this.getSingleFilter(this.value, this.displayValue.toString()) : null
		return filter ? [ filter ] : null
	}

	//#endregion

	//#region private methods

	private getSingleFilter(compareValue?: any | null, compareLabel?: string): DataFilter | null {
		if (compareValue == null || compareValue === '')
			return { filterColumn: this.name, operator: Operator.IsNull, compareLabel: compareLabel, columnLabel: this.label }

		const useLike = isText(this.type as DataType) && !this.valuePreview
		return {
			filterColumn: this.name, operator: this.isMultiField ? Operator.Contains : (useLike ? Operator.Like : Operator.Equals),
			compareValue: !this.isMultiField && useLike ? `%${compareValue}%` : compareValue,
			compareLabel: compareLabel,
			columnLabel: this.label,
		}
	}

	private getMultiFilter(): DataFilter[] | null {
		const displayValueList = this.displayValue
		return this.value?.map((compareValue: any, index: number) => this.getSingleFilter(compareValue, displayValueList[index]))
	}

	private handleFocusin(event: FocusEvent) {
		event.preventDefault()
		event.stopPropagation()
		this.dispatchEvent(new FocusEvent('focusin', { bubbles: true }))
	}
	
	private handleFocusout(event: MouseEvent) {
		event.preventDefault()
		event.stopPropagation()
		if (!this._htmlBody.querySelector(':focus-within')) {
			this.dispatchEvent(new FocusEvent('focusout', { bubbles: true }))
		}
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-filter-dialog-section': FilterDialogSection
	}
}