import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import { ButtonType } from '@/components/atomics/button/Button.ts'
import { ColorState } from '@/enums/color-state.ts'
import { FilterMixin, FilterMixinType } from '@/components/data-organizers/filter-mixins/FilterMixin.ts'
import { renderIcon } from '@/shared/component-html.ts'
import { NotImplementedException } from '@/shared/exception.ts'
import { Dialog, FilterDialogSection, Searchbar, SelectList } from '@/components'
import { DataType, formatData } from '@/enums/data-type.ts'
import { SelectListItemType } from '@/components/list-views/select-list/SelectListItem.ts'
import { Size } from '@/enums/size.ts'
import { query } from 'lit/decorators/query.js'
import { CountResponseData, DataFilter } from '@/shared/types.ts'
import CommunicationServiceProvider, { CommunicationResponseType } from '@/shared/communication-service.ts'
import { Operator } from '@/enums/operator.ts'

export type FilterDialogType = FilterMixinType & {
	open: boolean
	more: boolean
	loading: boolean
}

type SearchSectionType = Pick<SelectListItemType, 'label' | 'value'> & {
	hidden: boolean
}

const FilterWrapper = FilterMixin(LitElement)

/**
 * FilterDialog web component using LIT (https://lit.dev)
 */
@customElement('lvl-filter-dialog')
export class FilterDialog extends FilterWrapper implements FilterDialogType {

	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		FilterWrapper.styles || [],
		css`
			:host {

			}

			.dialog {
				display: grid;
				grid-template-columns: 19.2rem 1fr;
				column-gap: var(--size-spacing-l);
				padding: var(--size-spacing-l);
				padding-right: 0;
				overflow: hidden;
			}

			.dialog__main, .dialog__search {
				overflow: hidden;
				display: flex;
				flex-direction: column;
				row-gap: var(--size-spacing-l);
			}
			
			.dialog__main > :not(.vanishing-scrollbar) {
				padding-right: var(--size-spacing-l);
			}
			
			.search__option-wrapper {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-weight: bold;
			}

			.search__section-wrapper {
				display: flex;
				flex-direction: column;
				overflow-y: auto;
				row-gap: var(--size-spacing-l);

				& .search__section > span {
					display: block;
					height: 2.4rem;
					line-height: 2.4rem;
					padding: 0 var(--size-spacing-m);
					color: var(--cp-clr-text-secondary);
					border-bottom: 1px solid var(--cp-clr-border-medium);
					margin-bottom: var(--size-spacing-s);
				}
			}

			.search-text .search-text__value {
				flex-grow: 0;
			}
			
			.dialog__active-filter {
				display: flex;
				flex-direction: column;
				row-gap: var(--size-spacing-m);
				padding: 0 var(--size-spacing-xs); /* Workaround to prevent button outline overflow */
			}
			
			.filter-actions {
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				& .filter-actions__left {
					display: flex;
					column-gap: var(--size-spacing-m); 
				}
			}

			.filter__sections-wrapper {
				display: flex;
				flex-direction: column;
				overflow-y: auto;
				gap: var(--size-spacing-l);
				padding: 0 var(--size-spacing-xs); /* Workaround to prevent button outline overflow */
				scroll-behavior: smooth;

				.filter__toggles {
					display: flex;
					column-gap: 4rem;
					flex-wrap: wrap;
					
					& .toggle-field {
						display: grid;
						gap: var(--size-spacing-m);
						
						& label {
							font-weight: 700;
						}
						
						& lvl-button-group {
							width: 29rem
						}
					}
				}

				.filter__sections {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					column-gap: var(--size-spacing-l);
					
					& ::slotted(lvl-filter-dialog-section:is([value-preview],[type*="date"])) {
						grid-column: span 2;
					}
				}
				
				& ::slotted(lvl-filter-panel-section) {
					--background-color: var(--cp-clr-background-lvl-0);
				}
			}

			.separator {
				border: 1px solid var(--cp-clr-border-medium);

				&.separator--with-space {
					margin: var(--size-spacing-m) 0;
				}
			}

			.filter-list {
				position: relative;
				display: flex;
				flex-wrap: wrap;
				gap: var(--size-spacing-s);
				align-content: start;
				height: 7.1rem;
				overflow-y: auto;
			}

			.filter-list__empty {
				position: absolute;
				inset: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				gap: var(--size-spacing-m);
				color: var(--cp-clr-text-tertiary);

				& .icon {
					font-size: var(--size-text-l);
				}
			}
		`,
	]

	//#region attributes

	@property({ type: Boolean, reflect: true })
	open: boolean = false

	@property({ type: Boolean, reflect: true })
	more: boolean = false

	@property({ type: Boolean, reflect: true })
	loading: boolean = false

	//#endregion

	//#region states

	@state()
	private searchSections: SearchSectionType[] = []

	//#endregion states

	//#region private properties	
	
	@query('.dialog__wrapper')
	private _htmlDialogWrapper!: Dialog

	@query('#section__searchbar')
	private _htmlSearchbar!: Searchbar
	
	@query('#search__item-list')
	private _htmlSearchItemList!: SelectList

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		const countButtonLabel = () => {
			const countFormatted = formatData(this.count ?? 0, DataType.Integer, { withThousandSeparators: true })
			return `${countFormatted} ${this.localize(this.count === 1 ? 'resultCountSingle' : 'resultCount')}`
		}

		return html`
			<lvl-dialog class="dialog__wrapper" heading="${this.localize('additionalFilters')}" icon="filters" ?open="${this.open}" includes-section modal width="960">
				<div class="dialog">
					<aside class="dialog__search">
						<div class="search__option-wrapper">
							<label class="text--secondary">${this.localize('moreOptions')}</label>
							<lvl-toggle value="${this.more}" readonly tooltip="${this.localize('mvpTooltip')}"></lvl-toggle>
						</div>
						<lvl-search id="section__searchbar" @change="${this.handleSectionSearchChange}"></lvl-search>
						<div class="search__section-wrapper vanishing-scrollbar">
								<!-- TODO: Uncomment when special filters will be included
							<div class="search__section">
								<span>${this.localize('quickLink')}</span>
								<lvl-select-list>
									<lvl-select-list-item label="${this.localize('quickFilters')}" value="quickFilters"></lvl-select-list-item>
									<lvl-select-list-item label="${this.localize('myFilters')}" value="myFilters"></lvl-select-list-item>
								</lvl-select-list>
							</div>
							-->
							<div class="search__section">
								<span>${this.localize('properties')}</span>
								<lvl-select-list id="search__item-list" size="${Size.Small}" @change="${this.handleSectionQuickLinkClick}">
									${this.searchSections.map(section => html`
										<lvl-select-list-item class="${section.hidden ? 'hidden' : ''}" label="${section.label}" value="${section.value}"></lvl-select-list-item>
									`)}
								</lvl-select-list>
							</div>
						</div>
					</aside>
					<div class="dialog__main">
						${this.searchText ? html`
							<div class="search-text">
								${renderIcon('search', { additionalClasses: [ 'search-text__icon' ], inline: true })}
								<span class="search-text__label">${this.localize('searchText')}:</span>
								<div class="search-text__value">“<span>${this.searchText}</span>”</div>
								${renderIcon('close', { data: { action: 'clear-search' }, onClick: () => this.searchText = '' })}
							</div>
						` : ''}
						<div class="dialog__active-filter">
							<div class="filter-list vanishing-scrollbar">
								${this.userFilters.map(filter => this.renderFilterFries(filter))}
								${this.userFilters.some(filter => !filter.hidden) ? '' : html`
									<div class="filter-list__empty">
										${renderIcon('filter-slash', { inline: true })}
										<span>${this.localize('noFilter')}</span>
									</div>`}
							</div>
							<div class="filter-actions">
								<div class="filter-actions__left">
									<lvl-button data-action="clear" type="${ButtonType.Primary}"
															label="${this.localize('clearAll')}" @click="${this.clear}"
															?disabled="${this.userFilters.length === 0}"></lvl-button>
									<lvl-button data-action="reset" type="${ButtonType.Secondary}" color="${ColorState.Active}"
															label="${this.localize('resetFilters')}" @click="${this.reset}"
															?disabled="${this.predefinedFilters.toString() == this.userFilters.toString()}"></lvl-button>
								</div>
								<lvl-button data-action="saveFilters" type="${ButtonType.Tertiary}" color="${ColorState.Active}" tooltip="${this.localize('mvpTooltip')}"
														label="${this.localize('saveFilters')}" trailing-icon="save" @click="${this.handleSaveFilterButtonClick}" disabled></lvl-button>
							</div>
						</div>
						<div class="filter__sections-wrapper vanishing-scrollbar static-scrollbar">
							<div class="filter__toggles">
								<div class="toggle-field">
									<label class="text--secondary">${this.localize('favorite')}</label>
									<lvl-toggle name="favorite" value="${this.favorite}" data-tooltip="favoriteTooltip" @change="${this.handleFavoriteFilterChange}"></lvl-toggle>
									<lvl-tooltip name="favoriteTooltip" delayed orbit>${this.localize('favoriteTooltip')}</lvl-tooltip>
								</div>
								<div class="toggle-field">
									<label class="text--secondary">${this.localize('inactive')}</label>
									<lvl-button-group name="inactive" value="${this.inactive}" @change="${this.handleInactiveFilterChange}">
										<lvl-button label="${this.localize('hide')}" value="false"></lvl-button>
										<lvl-button label="${this.localize('show')}" value="true"></lvl-button>
									</lvl-button-group>
								</div>
							</div>
							<div class="separator"></div>
							<div class="filter__sections">
								<slot @slotchange="${this.handleSlotchange}" @change="${this.handleSectionChange}" @focusin="${this.handleItemFocusin}" @focusout="${this.handleItemFocusout}"></slot>
							</div>
						</div>
					</div>
				</div>
				<lvl-button slot="button-left" data-action="cancel" label="${this.localize('abortButton')}" color="${ColorState.Info}"></lvl-button>
				<lvl-button slot="button-right" data-action="apply" type="${ButtonType.Primary}" ?soft-loading="${this.loading}" label="${countButtonLabel()}" trailing-icon="arrow-right" @click="${this.handleSaveButtonClick}"></lvl-button>
			</lvl-dialog>
		`
	}

	//#region lifecycle callbacks

	protected firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)

		this._htmlDialogWrapper.addEventListener('dialog-close', () => {
			this.dispatchEvent(new CustomEvent('filter-dialog:close', { bubbles: true }))
		})
	}

	protected willUpdate(_changedProperties: PropertyValues) {
		super.willUpdate(_changedProperties)
		if (_changedProperties.get('open') != null) {
			if (this.open) {
				const fulltextFilters = this.searchText ? [ { filterColumn: '', operator: Operator.FulltextSearch, compareValue: this.searchText } ] : []
				const filterList = [ ...this.userFilters, ...this.forcedFilters, ...fulltextFilters ]
				this.sections?.forEach(section => {
					if (section.valuePreview && section.possibleValues == null)
						section.loadPossibleValues(filterList)
				})
			} else {
				this.sections?.filter(section => section.valuePreview)
						?.forEach(section => section.possibleValues = undefined)
			}
		}
		
		// load count if the filter or search text was changed or the count was not passed while opening the dialog
		const filterChanged = _changedProperties.get('userFilters') != null || _changedProperties.get('searchText') != null
		const noCountAfterOpening = _changedProperties.has('open') && this.count == null
		if ((filterChanged || noCountAfterOpening) && this.open) {
			const fulltextFilters = this.searchText ? [ { filterColumn: '', operator: Operator.FulltextSearch, compareValue: this.searchText } ] : []
			this.loadCount([ ...this.userFilters, ...fulltextFilters])
		}
	}

	//#endregion

	//#region public methods
	//#endregion

	//#region private methods

	/**
	 * fetches the filter count
	 * @param filters current applied filter
	 */
	private async loadCount(filters: DataFilter[]) {
		if (!this.countUrl)
			return
		
		this.loading = true
		const options = filters?.length > 0 ? { searchParams: { filters: filters } } : undefined
		const fetchData = await CommunicationServiceProvider.get<CountResponseData>(this.countUrl, options)
		if (fetchData.state == CommunicationResponseType.Aborted)
			return
		
		if (fetchData.state == CommunicationResponseType.Error) {
			this.count = 0
			this.loading = false
			return
		}
		
		this.count = fetchData.data?.count ?? 0
		this.loading = false
	}

	private handleSaveFilterButtonClick(_: MouseEvent) {
		throw new NotImplementedException()
	}

	private handleSectionSearchChange() {
		const searchValue = this._htmlSearchbar.value
	
		this.searchSections.forEach(section => {
			section.hidden = searchValue && !section.label.toLowerCase().includes(searchValue.toLowerCase())
		})

		this.requestUpdate()
	}

	// scrolls to filter section if list item is clicked  
	private handleSectionQuickLinkClick(event: MouseEvent) {
		const selectList = event.target as SelectList
		if (!selectList.value)
			return
		
		// remove focus of all
		this.sections?.forEach((section ) => {
			const dialogSection = section as FilterDialogSection
			if (section.name !== selectList.value) {
				return
			}

			if(this._htmlSectionWrapper?.parentElement)
				this._htmlSectionWrapper.parentElement.scrollTo({ top: dialogSection.offsetTop - this._htmlSectionWrapper.parentElement.offsetTop })
			
			dialogSection.focus()
		})
	}
	
	private handleSaveButtonClick() {
		this.dispatchEvent(new CustomEvent('filter-dialog:save', { bubbles: true }))
	}

	protected handleSlotchange() {
		this.searchSections = Object.values(this.querySelectorAll<FilterDialogSection>('lvl-filter-dialog-section'))
															?.map(section => this.toSearchSection(section))
															?.sort((a, b) => a.label < b.label ? -1 : (a.label === b.label ? 0 : 1)) ?? []
	}
	
	private handleItemFocusin(event: FocusEvent) {
		const currentSection = event.target as FilterDialogSection
		if (this._htmlSearchItemList)
			this._htmlSearchItemList.value = currentSection.name
	}
	
	private handleItemFocusout(_: FocusEvent) {
		if (this._htmlSearchItemList)
			this._htmlSearchItemList.value = null
	}

	private toSearchSection(section: FilterDialogSection): SearchSectionType {
		return { 
			label: section.getAttribute('label') || '', 
			value: section.getAttribute('name') || '', 
			hidden: false 
		}
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-filter-dialog': FilterDialog
	}
}