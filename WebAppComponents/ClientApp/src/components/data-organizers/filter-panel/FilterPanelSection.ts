import { css, html, LitElement } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { DataType } from '@/enums'
import { formatData, isText } from '@/enums/data-type.ts'
import { ifDefined } from 'lit/directives/if-defined.js'
import { DataFilter } from '@/shared/types.ts'
import { Size } from '@/enums/size.ts'
import { Operator } from '@/enums/operator.ts'
import { getAllDateSpans } from '@/enums/date-span.ts'
import { FilterSectionMixin, FilterSectionMixinType, PreviewValueType } from '@/components/data-organizers/filter-mixins/FilterSectionMixin.ts'
import { capitalize } from '@/shared/data-operations.ts'

export type FilterPanelSectionType = FilterSectionMixinType & {
	collapsed: boolean
	hidden: boolean
}

const FilterSectionWrapper = FilterSectionMixin(LitElement)

/**
 * PanelSection web component as part of FilterPanel using LIT (https://lit.dev)
 */
@customElement('lvl-filter-panel-section')
export class FilterPanelSection extends FilterSectionWrapper implements FilterPanelSectionType {

	//TODO: Add animations for minimize and collapse
	static styles = [
		styles.base,
		styles.color,
		styles.skeleton,
		css`
			:host {
				--clr-skeleton-block: var(--cp-clr-background-lvl-2);
			}

			:host([collapsed]) .filter-section__body, :host([hidden]) {
				display: none;
			}
			
			.panel__filter-section {
				margin-bottom: var(--size-spacing-m);
			}

			.filter-section__header {
				position: sticky;
				top: 0;
				z-index: 1;

				display: flex;
				align-items: center;
				height: 2.4rem;
				gap: var(--size-spacing-s);
				background-color: var(--panel-background, var(--cp-clr-background-lvl-1));

				& span {
					font-weight: bold;
				}
			}
			
			.filter-section__body {
				padding-top: var(--size-spacing-xs);
			}

			:host(:not([collapsed])) .skeleton__block {
				height: 17.2rem;
			}

			.section__actions {
				flex-grow: 1;

				display: flex;
				align-items: center;
				justify-content: right;
			}

			.section__hint {
				display: block;
				padding: var(--size-spacing-m);
				border: 1px solid var(--cp-clr-border-medium);
				border-left: 0;
				border-right: 0;
				font-size: var(--size-text-s);
				line-height: var(--size-text-l);
			}
			
			.section__hint, .section__minimize {
				margin-top: var(--size-spacing-m)
			}

			.section__empty {
				display: flex;
				justify-content: center;
				align-items: center;
				line-height: 2.4rem;
				color: var(--cp-clr-text-tertiary);
			}

			lvl-button-group {
				--background: var(--panel-background-secondary, var(--cp-clr-background-lvl-0));
			}
		`,
	]

	//#region attributes

	@property({ type: Boolean, reflect: true })
	collapsed: boolean = false

	@property({ type: Boolean, reflect: true })
	hidden: boolean = false

	//#endregion

	//#region states

	@state()
	minimized: boolean = true

	//#endregion states

	//#region private properties
	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		return html`
			<div class="panel__filter-section ${this.loading ? 'skeleton__block' : (this.valuePreview && !this.possibleValues?.length ? 'hidden' : '') }">
				<div class="filter-section__header">
					<span class="text--secondary">${this.label || this.name}</span>
					<lvl-button data-action="toggle" class="section__expand" size="${Size.Small}"
											icon="${this.collapsed ? 'chevron-down' : 'chevron-up'}"
											tooltip="${this.localize(this.collapsed ? 'expand' : 'collapse')}"
											@click="${this.handleToggleButtonClick}"></lvl-button>
					<div class="section__actions">
						${this.valuePreview && !this.collapsed && this.possibleValues && this.possibleValues.length > 5 ?
							html`
								<lvl-button class="section__search" size="${Size.Small}" icon="search" disabled data-tooltip="searchTooltip" style="cursor:not-allowed;">
								</lvl-button>
								<lvl-tooltip name="searchTooltip" orbit>${this.localize('mvpTooltip')}</lvl-tooltip>` :
							''}
					</div>
				</div>
				<div class="filter-section__body">
					${this.valuePreview ? this.renderSelectList() : this.renderInput()}
				</div>
			</div>
		`
	}

	renderInput() {
		if (this.type === DataType.Boolean) {
			const labelTrue = capitalize(formatData(true, DataType.Boolean, this.getTypeOptions())) || this.localize('filterBooleanYes')
			const labelFalse = capitalize(formatData(false, DataType.Boolean, this.getTypeOptions())) || this.localize('filterBooleanNo')
			return html`
				<lvl-button-group value="${this.value}" @change="${this.handleInputChange}">
					<lvl-button label="${this.localize('filterBooleanAll')}" value=""></lvl-button>
					<lvl-button label="${labelTrue}" value="true"></lvl-button>
					<lvl-button label="${labelFalse}" value="false"></lvl-button>
				</lvl-button-group>
			`
		}

		if (this.type === DataType.Date || this.type === DataType.DateTime)
			return this.renderSelectList(getAllDateSpans().map(span => ({ label: this.localizeDateSpan(span), value: span })))

		return html`
			<lvl-input type="${this.type}" .value="${this.value}" ?multi-value="${this.multiValue}" nullable
								 placeholder="${ifDefined(this.placeholder)}" @change="${this.handleInputChange}"></lvl-input>`
	}

	renderSelectList(listItems: PreviewValueType[] = this.possibleValues ?? []) {
		const renderTooManyItemsMessage = () => html`<span class="section__hint">${this.localize('tooManyItemsHint')}</span>`
		const renderExpandButton = () => html`
			<lvl-button class="section__minimize text--secondary" size="${Size.Small}"
									label="${this.minimized ? this.localize('displayMore') : this.localize('displayLess')}"
									@click="${() => this.minimized = !this.minimized}"></lvl-button>`
		return html`
			<lvl-select-list .value="${this.value}" ?multi-value="${this.multiValue}" ?minimized="${this.minimized}" nullable
											 size="${Size.Small}" @change="${this.handleSelectChange}">
				${listItems?.slice(0, 50)?.map(record => html`
					<lvl-select-list-item label="${record.label || '-'}" .value="${record.value}"
																count="${record.count}"
																?selected="${this.multiValue ? this.value?.includes(record.value) : record.value === this.value}"></lvl-select-list-item>`)}
			</lvl-select-list>
			${listItems?.length > 50 && !this.minimized ? renderTooManyItemsMessage() : ''}
			${listItems?.length > 5 ? renderExpandButton() : ''}
		`
	}

	//#region lifecycle callbacks
	//#endregion

	//#region public methods

	/*
	 * Build the filter for this section
	 */
	public getFilters(): DataFilter[] | null {
		if (this.multiValue)
			return this.getMultiFilter()

		const filter = this.value != null ? this.getSingleFilter(this.value, this.displayValue.toString()) : null
		return filter ? [ filter ] : null
	}
	
	//#endregion

	//#region private methods

	private getSingleFilter(compareValue?: any | null, compareLabel?: string): DataFilter | null {
		if (compareValue == null || compareValue === '')
			return { filterColumn: this.name, operator: Operator.IsNull, compareLabel: compareLabel, columnLabel: this.label }

		const useLike = isText(this.type as DataType) && !this.valuePreview
		return {
			filterColumn: this.name, operator: this.isMultiField ? Operator.Contains : (useLike ? Operator.Like : Operator.Equals),
			compareValue: !this.isMultiField && useLike ? `%${compareValue}%` : compareValue,
			compareLabel: compareLabel,
			columnLabel: this.label,
		}
	}

	private getMultiFilter(): DataFilter[] | null {
		const displayValueList = this.displayValue
		return this.value?.map((compareValue: any, index: number) => this.getSingleFilter(compareValue, displayValueList[index]))
	}

	private handleToggleButtonClick(event: MouseEvent) {
		event.stopPropagation()
		this.collapsed = !this.collapsed
		this.dispatchEvent(new CustomEvent('toggle-section:click', { bubbles: true }))
	}
	
	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-filter-panel-section': FilterPanelSection
	}
}