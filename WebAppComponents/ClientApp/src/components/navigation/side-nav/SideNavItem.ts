import { css, html, LitElement } from 'lit'
import { customElement, property, queryAssignedElements } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { renderIcon } from '@/shared/component-html.ts'
import { ColorState } from '@/enums/color-state.ts'
import { classMap } from 'lit/directives/class-map.js'
import { fontAwesome } from '@/shared/font-awesome.ts'

export type SideNavItemType = {
	label: string
	value?: string
	icon?: string
	state?: ColorState
	selected?: boolean,
	disabled?: boolean,
	skeleton?: boolean,
	open: boolean,
	slot?: string,
	urlParams?: Record<string, any>
	minimized: boolean
}

export type SideNavItemDetail = Pick<SideNavItemType, 'value' | 'state' | 'urlParams'> & { data: Record<string, any> }

/**
 * Example web component using LIT (https://lit.dev)
 */
@customElement('lvl-side-nav-item')
export class SideNavItem extends LitElement implements SideNavItemType {

	static styles = [
		styles.base,
		styles.color,
		styles.animation,
		styles.skeleton,
		styles.icon,
		fontAwesome,
		css`
			:host-context(lvl-side-nav[show-state]) {
				--show-state: inline-block;
			}

			:host {
				--background-color: var(--cp-clr-background-lvl-1);
				--color: current;
			}

			/* selected state */

			:host([selected]) {
				--background-color: var(--cp-clr-background-lvl-0);

				& .nav-item.selected:before {
					content: '';
					position: absolute;
					inset: 0.8rem auto 0.8rem 0;
					width: 3px;
					background-color: var(--cp-clr-state-active);
					border-radius: var(--size-radius-m);
				}
			}

			:host([disabled]) {
				--color: var(--cp-clr-state-inactive);
			}

			:host(:not([disabled])) .nav-item:hover {
				--background-color: var(--cp-clr-background-lvl-2);
			}

			.nav-item__wrapper {
				display: contents;
			}

			.nav-item {
				position: relative;
				display: flex;
				align-items: center;
				padding: 0 var(--size-spacing-m, 0.5rem) 0 var(--size-spacing-m, 0.5rem);
				margin-right: var(--size-spacing-m);
				height: 3.2rem;
				max-width: var(--side-nav-item-max-width, 100%);
				transition: max-width ease-in-out var(--animation-time);

				color: var(--color);
				background-color: var(--background-color);
				border-radius: var(--size-radius-m);

				& span {
					flex-grow: 1;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					padding: 0 var(--size-spacing-m, 0.5rem);
				}
			}

			.nav-item.skeleton__block {
				--clr-skeleton-block: var(--cp-clr-background-lvl-2);
				--skeleton-inset: var(--size-spacing-xxs) var(--size-spacing-l) var(--size-spacing-xxs) var(--size-spacing-m);
				pointer-events: none;
			}

			.nav-item.skeleton__block * {
				opacity: 0;
			}

			.nav-item__icon {
				flex-shrink: 0;
			}

			.nav-item__state {
				display: var(--show-state, none);
				vertical-align: middle;
			}

			/* sub items */

			.nav-item__children {
				display: grid;
				grid-template-rows: 0fr;
				margin-left: var(--size-spacing-l);

				transition: grid-template-rows ease-in-out var(--animation-time-medium), margin-left ease-in-out var(--animation-time-medium);
			}
			
			:host([minimized]) .nav-item__children {
				margin-left: 0;
			}

			:host([open]) .nav-item__children {
				grid-template-rows: 1fr;
			}

			slot {
				display: grid;
				overflow: hidden;
			}

			.nav-item__toggle {
				display: none;
			}

			:host([data-toggle-node]) .nav-item__toggle {
				display: block;
				rotate: 0;
				transition: rotate linear var(--animation-time-medium);
			}

			:host([data-toggle-node][open]) .nav-item__toggle {
				rotate: 90deg;
			}

			:host([data-toggle-node][open][child-selected]) .nav-item {
				pointer-events: none;

				& .nav-item__toggle {
					color: var(--cp-clr-state-inactive)
				}
			}
		`,
	]

	//#region attributes

	@property()
	label!: string

	@property()
	value?: string

	@property()
	icon?: string

	@property()
	state?: ColorState

	@property({ type: Boolean, reflect: true })
	selected: boolean = false

	@property({ type: Boolean, reflect: true, attribute: 'child-selected' })
	childSelected: boolean = false

	@property({ type: Boolean, reflect: true })
	disabled: boolean = false

	@property({ type: Boolean, reflect: true })
	open: boolean = false
	
	@property({ type: Boolean, reflect: true })
	minimized: boolean = false

	@property({ type: Boolean, reflect: true })
	skeleton: boolean = false

	_urlParams: Record<string, any> = {}

	// optional additional url params specifically for this menu entry
	@property({ attribute: 'url-params' })
	set urlParams(value: any) {
		if (!value || [ 'string', 'object' ].indexOf(typeof value) == -1)
			return

		if (typeof value == 'string') {
			try {
				this._urlParams = JSON.parse(value)
			} catch (e) {
				console.warn(`failed to set urlParams to ${value}`)
			}
		} else
			this._urlParams = value
	}

	get urlParams(): Record<string, any> {
		return this._urlParams
	}

	private _data: Record<string, any> = {}

	@property({ type: Object })
	set data(newData: Record<string, any>) {
		if (newData != null)
			this._data = newData
	}

	get data() {
		return this._data
	}

	//#endregion

	//#region states	
	//#endregion states

	//#region private properties

	@queryAssignedElements()
	_subNavItems!: SideNavItem[]

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		const classes = {
			'nav-item': true,
			clickable: true,
			selected: this.selected && !this.skeleton,
			disabled: this.disabled,
			skeleton__block: this.skeleton,
		}

		return html`
			<div class="nav-item__wrapper">
				<div class="${classMap(classes)}">
					${this.icon ? renderIcon(this.icon, { additionalClasses: [ 'nav-item__icon' ], inline: false }) : ''}
					<span>${this.label}</span>
					<i class="state-icon nav-item__state" data-state="${this.state ?? ColorState.Inactive}"></i>
					${renderIcon('chevron-right', {
						additionalClasses: [ 'nav-item__toggle' ],
						inline: false,
						onClick: this.handleToggle,
					})}
				</div>
				<div class="nav-item__children">
					<slot></slot>
				</div>
			</div>
		`
	}

	//#region lifecycle callbacks

	connectedCallback() {
		super.connectedCallback()
		
		// just a node? 
		if (this.querySelector('lvl-side-nav-item')) {
			this.dataset['toggleNode'] = ''
			
			// if a child is selected before first load than it should be opened
			if (this.querySelector('lvl-side-nav-item[selected]')) {
				this.setAttribute('open', '')
				this.setAttribute('child-selected', '')
			}
		}

		this.addEventListener('click', this.handleClick)
		this.querySelectorAll('& > lvl-side-nav-item').forEach(item => {
			item.addEventListener('nav-item:click', (event: Event) => this.handleChildClick(event))
		})
	}

	//#endregion

	//#region public methods	

	/**
	 * Checks that the menu item has one selected child
	 */
	public hasSelectedChild(): boolean {
		return this._subNavItems.some(item => item.selected || item.hasSelectedChild())
	}
	
	//#endregion

	//#region private methods

	private handleChildClick(_: Event) {
		this.childSelected = true
	}

	private handleClick(event: MouseEvent) {
		// if the item is disabled, we don't handle any click events
		if (this.disabled)
			return
		
		if (this.hasAttribute('data-toggle-node')) {
			this.open = true
			this.childSelected = true
			this._subNavItems[0].dispatchEvent(new MouseEvent('click'))
			return
		}

		// a nav-item cannot deselect itself
		if (this.selected)
			return

		// define callbacks for resolve (and cancel)
		const callback = () => {
			this.handleClick(new MouseEvent('click'))
		}
		
		// define event and put callbacks in detail
		const pageLeaveEvent = new CustomEvent('initiate-page-leave', {
			cancelable: true,
			detail: {
				resolveCallback: callback
			}
		})

		// dispatch the page-leave-event and stop if it gets canceled
		if(!window.dispatchEvent(pageLeaveEvent))
			return

		// toggle select an trigger custom click to parent
		this.selected = true

		let customEvent = new CustomEvent<SideNavItemDetail>('nav-item:click', {
			bubbles: true,
			detail: { value: this.value, state: this.state, data: this.data, urlParams: this.urlParams },
		})

		this.dispatchEvent(customEvent)
		event.stopImmediatePropagation()
		event.preventDefault()
	}

	private handleToggle = (event: MouseEvent) => {
		this.open = !this.open

		let customEvent = new CustomEvent<SideNavItemDetail>('nav-item:toggle', { bubbles: true })
		this.dispatchEvent(customEvent)
		event.stopImmediatePropagation()
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-side-nav-item': SideNavItem
	}
}