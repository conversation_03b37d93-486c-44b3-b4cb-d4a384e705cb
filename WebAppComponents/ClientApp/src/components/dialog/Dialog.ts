import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { renderIcon } from '@/shared/component-html.ts'
import { query } from 'lit/decorators/query.js'
import { fontAwesome } from '@/shared/font-awesome.ts'
import { DialogPreset } from '@/enums/dialog-preset.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import { Button, ButtonType } from '@/components/atomics/button/Button.ts'
import { ColorState } from '@/enums/color-state'
import { IconStyle } from '@/enums/icon-style.ts'

export type DialogType = {
	heading: string
	text: string
	name: string
	icon?: string
	color?: ColorState
	modal: boolean
	open: boolean
	width?: string
	skeleton?: boolean
	hideHeader?: boolean
	includesSection?: boolean
}

/**
 * The Dialog web component using LIT (https://lit.dev)
 */
@customElement('lvl-dialog')
export class Dialog extends LitElement implements DialogType {

	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		styles.vanishingScrollbar,
		fontAwesome,
		css`
			:host {
				--width: 48rem;
				--font-size-header: var(--size-text-l);
			}

			:host([color]) main {
				border-top: 1px solid var(--cp-clr-border-medium);
				border-bottom: 1px solid var(--cp-clr-border-medium);
			}

			:host([icon-color=warning]) header {
				--icon-color: var(--cp-clr-signal-warning);
			}

			:host([icon-color=error]) header {
				--icon-color: var(--cp-clr-signal-error);
			}

			#dialog {
				position: fixed;
				top: 50%;
				left: 50%;
				width: var(--width);
				max-width: calc(100vw - 8rem);
				max-height: calc(100vh - 8rem);
				translate: -50% -50%;
				padding: 0;
				margin: 0;
				outline: 0;
				background-color: var(--cp-clr-background-lvl-1);
				border: 1px solid var(--cp-clr-border-medium);
				border-radius: var(--size-radius-m);
				cursor: default;
				overflow: visible; /* needed for tooltips inside the dialog */
				flex-direction: column;

				& .button-set__wrapper:not(.hidden) {
					display: flex;
					align-items: center;
					justify-content: space-between;
					gap: var(--size-spacing-m);

					.button-set {
						display: flex;
						gap: var(--size-spacing-m);
					}
				}

				& .button-set__wrapper.hidden {
					display: none;
				}
			}

			#dialog[open] {
				display: grid;
				grid-template-rows: max-content 1fr max-content;
			}

			#dialog::backdrop {
				background-color: #00000080;
			}

			header {
				display: flex;
				align-items: center;
				column-gap: var(--size-spacing-s);
				font-size: var(--font-size-header);
				padding: var(--size-spacing-m) var(--size-spacing-m) var(--size-spacing-m) 1.2rem;
				height: 4rem;
				background-color: var(--cp-clr-background-lvl-0);
				border-top-left-radius: var(--size-radius-m);
				border-top-right-radius: var(--size-radius-m);

				& h1 {
					flex-grow: 1;
					font-size: var(--font-size-header);
					font-weight: normal;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				& i {
					width: 2.4rem;
					text-align: center;
					font-size: 1.4rem;

					&:not([data-action=close]) {
						font-size: var(--size-text-l);
						color: var(--icon-color, inherit);
					}
				}

				.navigation-buttons {
					padding: 0 var(--size-spacing-m);
					border-right: 2px solid var(--cp-clr-border-medium);
					user-select: none;
					white-space: nowrap;
				}
			}

			footer {
				height: 6.4rem;
				padding: var(--size-spacing-l);
				background-color: var(--cp-clr-background-lvl-0);
				border-bottom-left-radius: var(--size-radius-m);
				border-bottom-right-radius: var(--size-radius-m);
			}

			header, main, footer {
				width: 100%;
			}

			.no-header {
				border-top-left-radius: var(--size-radius-m);
				border-top-right-radius: var(--size-radius-m);
			}

			main {
				overflow-x: hidden;
				overflow-y: auto;
			}

			.dialog__section {
				--content-padding: 0;
				display: block;
				height: calc(100% - 2 * var(--size-spacing-l));
				margin: var(--size-spacing-l);
			}

			.content-unsavedChanges, .content-confirm{
				margin: var(--size-spacing-l);
				line-height: 160%;

				& > label {
					display: block;
					font-size: 1.4rem;
					font-weight: 600;
					line-height: 160%;
					margin: 0 0 var(--size-spacing-m);
				}
			}

			.content-file {
				display: block;
				padding: var(--size-spacing-l) var(--size-spacing-xs) var(--size-spacing-l) var(--size-spacing-l);
				background-color: var(--clr-background-lvl-1);

				h3{
					font-size: var(--size-text-l);
					margin: var(--size-spacing-l) 0 var(--size-spacing-l) 0;
				}
			}
		`,
	]

	//#region attributes

	@property()
	heading: string = ''

	@property({ reflect: true })
	name: string = ''

	@property()
	text: string = ''

	@property()
	icon?: string

	@property({attribute: 'icon-style'})
	iconStyle: IconStyle = IconStyle.Light

	@property({attribute: 'icon-color', reflect: true})
	iconColor?: string

	@property({ type: Boolean })
	modal: boolean = true

	@property({ type: Boolean, reflect: true })
	open: boolean = false

	@property()
	width: string = '480'

	@property({ type: Boolean, reflect: true })
	skeleton: boolean = false

	@property({ type: Boolean, attribute: 'custom-keydown-handler', reflect: true })
	customKeydownHandler: boolean = false

	@property({ type: Boolean, attribute: 'ignore-overflow' })
	ignoreOverflow: boolean = false

	@property({ type: Boolean, attribute: 'hide-header' })
	hideHeader: boolean = false

	@property({ type: Boolean, attribute: 'hide-footer' })
	hideFooter: boolean = false

	@property({ type: Boolean, attribute: 'includes-section' })
	includesSection: boolean = false

	@property()
	preset?: DialogPreset

	//#endregion

	//#region private properties

	@query('#dialog')
	public dialog!: HTMLDialogElement

	private abortController?: AbortController

	private removeOnClose: boolean = false

	private static readonly _localizer: StringLocalizer = new StringLocalizer('Dialog')

	private localize(key: string) {
		return Dialog._localizer.localize(key)
	}

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		const iconHtml = this.icon ? renderIcon(this.icon, { inline: true, iconStyle: this.iconStyle }) : html``
		const closeIconHtml = renderIcon('xmark', { onClick: () => this.open = false, data: { action: 'close' } })


		return html`
			<dialog id="dialog" @click="${(event: MouseEvent) => event.stopPropagation()}">
				${this.hideHeader ? '' : html`
					<header>
						${iconHtml}
						<h1 title="${this.heading}">${this.heading}</h1>
						${closeIconHtml}
					</header>`}
				${this.includesSection ? html`
					<main class="${this.hideHeader ? 'no-header': ''}">
						<lvl-section class="dialog__section">
							<slot></slot>
						</lvl-section>
					</main>
				` : html`
					<main class="${this.ignoreOverflow ? '' : 'vanishing-scrollbar static-scrollbar'} ${this.hideHeader ? 'no-header': ''}">
						<slot></slot>
						${this.getPresetContent()}
					</main>
				`}
				${this.hideFooter ? '' : html`
				<footer class="button-set__wrapper">
					<div class="button-set">
						<slot name="button-left"></slot>
						${this.getPresetButtonsLeft()}
					</div>
					<div class="button-set">
						<slot name="button"></slot>
						<slot name="button-right"></slot>
						${this.getPresetButtonsRight()}
					</div>
				</footer>`}
			</dialog>
		`
	}

	//#region states
	//#endregion states

	//#region lifecycle callbacks
	protected updated(changedProperties: PropertyValues<DialogType>) {
		// react on width change
		if (changedProperties.has('width')) {
			const widthParsed = Number(this.width)
			const hostWidth = this.width ? !isNaN(widthParsed) && widthParsed > 0 ? `${this.width}px` : this.width : 'unset' // parseInt parses "60abc" to 60 so we need isNaN before!
			this.style.setProperty('--width', hostWidth)
		}

		// react on 'open' change
		if (changedProperties.has('open')) {
			if (this.open)
				this.openDialog()
			// and close it only if its not the first setting
			else if (changedProperties.get('open') != null)
				this.closeDialog()
		}

		// show/hide skeleton loading animations on supported elements
		if (changedProperties.has('skeleton')) {
			for (const form of this.querySelectorAll('lvl-form')) {
				form.skeleton = this.skeleton
			}

			for (const button of this.querySelectorAll('lvl-button')) {
				button.skeleton = this.skeleton
			}

			// TODO: Is there even a tab bar?
			const tabBar = this.querySelector('lvl-tab-bar')
			if (tabBar != undefined)
				tabBar.skeleton = this.skeleton
		}

		// react on 'modal' change - if the dialog is open, we have to reopen it again as modal
		if (this.open && (changedProperties.get('modal') != null)) {
			this.closeDialog()
			this.openDialog()
		}
	}

	// Event listener handling always after the first rendering
	protected firstUpdated(changedProperties: PropertyValues<this>) {
		super.firstUpdated(changedProperties)

		// set some default values according to presets
		switch (this.preset) {
			case DialogPreset.UnsavedChanges:
				this.name = 'unsavedChanges'
				this.icon = this.icon || 'exclamation-triangle'
				this.iconColor = ColorState.Warning
				this.iconStyle = IconStyle.Solid
				this.heading = this.heading || this.localize('unsavedChanges.title')
				this.ignoreOverflow = true
				break
			case DialogPreset.Confirm:
				this.name = 'confirm'
				this.ignoreOverflow = true
				break
			case DialogPreset.FileUpdate:
				this.name = 'update'
				this.heading = this.localize('fileDialog.startUpdate')
				this.icon="exclamation-triangle"
				this.iconColor="warning"
				this.iconStyle=IconStyle.Solid
				break
		}

		// add cancel buttons clicks event
		this.querySelectorAll('[data-action=cancel]')?.forEach(cancelButton => {
			cancelButton.addEventListener('click', (event: Event) => {
				this.closeDialog(true)
				event.stopPropagation()
			})
		})

		// dispatch reset event
		this.querySelector('[data-action=reset]')?.addEventListener('click', () => {
			this.dispatchEvent(new CustomEvent('dialog-reset'))
		})

		// lvl-dialog has to be display: none while starting (animation problem - translateX is changed in the init which triggers an animation)
		this.style.setProperty('display', 'block')
		this.setAttribute('tabindex', '-1')
	}

	//#endregion

	//#region public methods

	openDialog() {
		this.abortController = new AbortController
		this.removeAttribute('tabindex')

		if (!this.customKeydownHandler)
			this.dialog.addEventListener('keydown', (event: KeyboardEvent) => this.handleKeyDown(event), { signal: this.abortController.signal })
		else
			this.dialog.addEventListener('keydown', (event: KeyboardEvent) => {
				if (event.key == 'Escape') {
					event.preventDefault()
				}
			}, { signal: this.abortController.signal })

		if (this.modal || this.preset != null) {
			this.dialog.showModal()
			return
		}

		this.dialog.show()
	}

	/*
	 * closes the dialog and removes the event listener
	 */
	closeDialog(force: boolean = false) {
		// ignore calls if abort controller was already aborted
		if (this.abortController?.signal?.aborted)
			return

		let allowClose = this.dispatchEvent(new CustomEvent('dialog-close', { bubbles: true, cancelable: !force }))
		if (!force && !allowClose)
			return

		this.abortController?.abort()
		this.setAttribute('tabindex', '-1')

		this.open = false
		this.dialog.close()

		if (this.removeOnClose)
			this.remove()
	}

	getButton(name: string) {
		return this.dialog.querySelector<Button>(`lvl-button[name='${name}']`)
	}

	// show dialog once -> closing the dialog removes it from the DOM
	showOnce() {
		this.removeOnClose = true
		document.documentElement.appendChild<Dialog>(this)
		this.open = true

		// focus primary button
		setTimeout(() => {
			this.dialog.querySelector<Button>("lvl-button[type='primary']")?.focus()
		})
	}

	//#endregion

	//#region private methods

	private handleKeyDown(keyEvent: KeyboardEvent) {
		switch (keyEvent.key) {
			case 'Escape':
				this.open = false
				keyEvent.stopPropagation()
				break
		}
	}

	private handleButtonClick(event: MouseEvent) {
		const button = event.target as Button
		const allowClose = this.dispatchEvent(new CustomEvent(`dialog-${button.getAttribute('name')}`, { bubbles: true, cancelable: true }))
		if (!allowClose)
			return

		this.open = false
	}

	private getPresetContent() {
		switch (this.preset) {
			case DialogPreset.UnsavedChanges:
				return html `
					<div class="content-unsavedChanges">
						<label>${this.localize('unsavedChanges.headline')}</label>
						${this.localize('unsavedChanges.question')}
					</div>
				`
			case DialogPreset.Confirm:
				return html `
					<div class="content-confirm">${this.text}</div>
				`
			case DialogPreset.FileUpdate:
				return html `
					<div class="content-file">
						<h3>${this.localize('fileDialog.updateHeader')}</h3>
						<label>${this.localize('fileDialog.updateExplanation')}</label>
					</div>
				`
			default:
				return null
		}
	}

	private getPresetButtonsLeft() {
		switch (this.preset) {
			case DialogPreset.UnsavedChanges:
				return html `<lvl-button name="abort" label="${this.localize('abort')}" type="tertiary" color="active" @click="${this.handleButtonClick}"></lvl-button>`
			case DialogPreset.Confirm:
				return html `<lvl-button name="abort" data-action="abort" label="${this.localize('abort')}" @click="${this.handleButtonClick}"></lvl-button>`
			case DialogPreset.FileUpdate:
				return html `<lvl-button name="abort" data-action="abort" color="info" label="${this.localize('abort')}" @click="${this.handleButtonClick}"></lvl-button>`
			default:
				return null
		}
	}

	private getPresetButtonsRight() {
		switch (this.preset) {
			case DialogPreset.UnsavedChanges:
				return html `
					<lvl-button name="discard" label="${this.localize('discard')}" type="${ButtonType.Secondary}" color="${ColorState.Active}" @click="${this.handleButtonClick}"></lvl-button>
					<lvl-button name="save" label="${this.localize('saveChanges')}" type="${ButtonType.Primary}" color="${ColorState.Active}" @click="${this.handleButtonClick}"></lvl-button>
				`
			case DialogPreset.Confirm:
				return html `
					<lvl-button name="confirm" data-action="confirm" label="${this.localize('confirm')}" type="${ButtonType.Primary}" @click="${this.handleButtonClick}"></lvl-button>
				`
			case DialogPreset.FileUpdate:
				return html `
					<lvl-button name="update" type="${ButtonType.Primary}" data-action="update" label="${this.localize('fileDialog.startUpdate')}" @click="${this.handleButtonClick}"></lvl-button>
				`
			default:
				return null
		}
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-dialog': Dialog
	}
}