import type { <PERSON><PERSON>, StoryObj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { LevelStory } from '@story-home/support/commands'
import { PinType } from '@/components/atomics/pin/Pin.ts'
import { ifDefined } from 'lit/directives/if-defined.js'

import('./Pin')
import('../../popup/Popup')
import('../../atomics/button/Button')

/* 
 * More on how to set up stories at: https://storybook.js.org/docs/web-components/writing-stories/introduction
 */

type PinProperties = Partial<PinType>
type PinStory = Story<PinProperties>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-pin',
	tags: [ 'autodocs' ],
	render: (_args: PinProperties) => html`
		<lvl-pin color="${ifDefined(_args.color)}" zoom="${ifDefined(_args.zoom)}" ?disabled="${_args.disabled}"
						 text="${ifDefined(_args.text)}" icon="${ifDefined(_args.icon)}" style="margin-top:155px;"></lvl-pin>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		color: {
			control: 'select',
			options: [ '', '#22C55E', '#FDBA74', '#F87171' ],
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: 'empty' },
			},
			description: 'pin background color',
		},
		text: {
			control: 'text',
			table: {
				type: { summary: 'only visible if icon is not set!' },
				defaultValue: { summary: 'empty' },
			},
			description: 'Text content of the pin',
		},
		icon: {
			control: 'select',
			options: [ '', 'dog', 'user', 'hammer-crash', 'circle' ],
			table: {
				type: { summary: 'select' },
				defaultValue: { summary: 'none' },
			},
			description: 'Icon which is located to the left of the chip',
		},
		zoom: {
			control: 'number',
			table: {
				type: { summary: 'value between 0 and 100' },
				defaultValue: { summary: '100' },
			},
			description: 'should the pin be displayed in a smaller scale?',
		},
		disabled: {
			control: 'boolean',
			table: {
				type: { summary: 'boolean' },
				defaultValue: { summary: 'false' },
			},
			description: 'should the pin be grayed out?',
		},
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Default
 */
export const Default: PinStory = {
	args: {
		text: "99",
		color: "#22C55E",
		zoom: 100,
	  disabled: false
	}
}

/**
 * Icon
 */
export const Icon: PinStory = {
	args: {
		icon: "car",
		color: "#FDBA74"
	}
}

/**
 * Disabled
 */
export const Disasbled: PinStory = {
	args: {
		icon: "hammer-crash",
		color: "#F87171",
		disabled: true
	}
}

//#endregion

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default),
	icon: new LevelStory(meta, Icon)
} as const