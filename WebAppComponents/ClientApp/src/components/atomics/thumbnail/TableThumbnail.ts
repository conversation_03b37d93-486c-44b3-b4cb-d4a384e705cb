import { customElement, property, state } from 'lit/decorators.js'
import { css, html, PropertyValues } from 'lit'
import { renderIcon } from '@/shared/component-html.ts'
import { IconStyle } from '@/enums/icon-style.ts'
import { Dialog, PopupPlacement, Thumbnail } from '@/components'
import { ColorState } from '@/enums/color-state.ts'
import { ButtonType } from '@/components/atomics/button/Button.ts'
import { query } from 'lit/decorators/query.js'
import { Queue, Queueable } from '@/shared/priorityQueue.ts'
import { DataType, formatData } from '@/enums/data-type.ts'
import { when } from 'lit/directives/when.js'
import { classMap } from 'lit/directives/class-map.js'
import { ThumbnailState, ThumbnailType } from '@/components/atomics/thumbnail/Thumbnail.ts'
import { getIconByType, parseFileSize } from '@/enums/file-types.ts'

const previewQueueName: string = 'preview'

export type TableThumbnailType = ThumbnailType & {
	previewUrl?: string
	fileSize?: number
	fileDate?: string
	fileCreateUser?: string
	fileChangedUser?: string
	onOpenElementClick: (event: MouseEvent, newTab: boolean) => void
}

@customElement('lvl-table-thumbnail')
export class TableThumbnail extends Thumbnail implements Queueable {

	static styles = [
		...Thumbnail.styles,
		css`
			:host {
				--size: 2.8rem;
				
				display: inline-block;
			}

			.thumbnail-wrapper {
				height: var(--size);
				aspect-ratio: 1;
				border-radius: var(--size-radius-s);
			}

			.image-wrapper::after {
				content: "";
				position: absolute;
				inset: 0;
				box-shadow: 0 0 var(--size-spacing-xs) 0 hsla(0, 0%, 0%, 0.5) inset;
				pointer-events: none;
			}

			.preview-content {
				width: 24.8rem;
				border-radius: var(--size-radius-m);
				padding: var(--size-spacing-m);
				background-color: var(--cp-clr-background-lvl-0-tooltip);

				> img {
					max-width: 100%;
				}

				.preview-content-header {
					margin: var(--size-spacing-m) 0 var(--size-spacing-m) 0;
					text-align: left;
					color: var(--cp-clr-text-primary-negativ);
				}

				.preview-content-details {
					display: grid;
					grid-template-columns: auto auto;
					gap: var(--size-spacing-s);
					color: var(--cp-clr-text-tertiary);

					.preview-content-details-type {
						text-align: left;
					}

					.preview-content-details-content {
						text-align: right;
					}
				}
			}

			lvl-viewer {
				height: 70rem;
				max-height: calc(100vh - 8rem - 6.4rem - 0.2rem); /* consider: padding to viewport border + dialog button set + dialog border */
				display: block;
				border-radius: var(--size-radius-m) 0 0 var(--size-radius-m);
			}

			lvl-dialog {
				position: absolute;
			}
		`,
	]

	@property({ attribute: 'preview-url' })
	previewUrl?: string

	@property({ attribute: 'file-size' })
	fileSize?: number

	@property({ attribute: 'file-date'})
	fileDate?: string

	@property({ attribute: 'file-changed-user' })
	fileChangedUser?: string

	@property({ attribute: 'file-create-user' })
	fileCreateUser?: string

	@state()
	private previewVisible: boolean = false
	
	@state()
	private injectDialog: boolean = false

	@state()
	private startPreviewFetch: boolean = false

	@query('lvl-dialog')
	private _htmlViewerDialog!: Dialog

	render() {
		const viewerAllowed = this.hasFile()
		const icon = getIconByType(this.fileType, viewerAllowed)		
		const classes = classMap({
			'thumbnail-wrapper': true,
			'clickable': viewerAllowed,
			'skeleton__block': this.skeleton,
		})
		
		// no thumbnail?
		if (this.iconOnly || this.state == ThumbnailState.Failed) {
			return html`
				<div class="${classes}" @click="${(event: MouseEvent) => viewerAllowed && this.handleClick(event)}">
					${renderIcon(icon, { iconStyle: IconStyle.Light })}
				</div>
				${when(this.hasFile(), this.renderViewerDialog)}
			`
		}
		
		return html`
			<div class="${classes}" data-popup="thumbnail-popup" @click="${(event: MouseEvent) => viewerAllowed && this.handleClick(event)}" 
					 @mouseover="${this.handleMouseover}" @mouseleave="${this.handleMouseleave}">
				<div class="image-wrapper">
					${when(this.startThumbnailFetch, () => html`
						<img src="${this.url}" alt="" @load="${this.handleThumbnailLoaded}" @error="${this.handleThumbnailError}"/>
					`)}
				</div>
			</div>
			${this.renderPreviewPopup()}
			${when(viewerAllowed, this.renderViewerDialog)}
		`
	}
	
	private renderPreviewPopup = () => {
		const renderImage = () => html`
			<img src="${this.previewUrl}" alt="" @load="${() => this.handlePreviewLoaded()}" @error="${() => this.handlePreviewLoaded()}"/>
		`
		
		return html`
			<lvl-popup name="thumbnail-popup" offset-inline-x="5" offset-inline-y="35" placement="${PopupPlacement.RightStart}" ?open="${this.previewVisible}"
								 shift>
				<div class="preview-content">
					${when(this.startPreviewFetch, renderImage)}
					<div class="preview-content-header">${this.localize('details')}</div>
					<div class="preview-content-details">
						<div class="preview-content-details-type">${this.localize('type')}</div>
						<div class="preview-content-details-content">${this.fileType != undefined ? `${this.fileType.toUpperCase()}-${this.localize('file')}` : '-'}</div>
						<div class="preview-content-details-type">${this.localize('size')}</div>
						<div class="preview-content-details-content">${parseFileSize(this.fileSize)}</div>
						<div class="preview-content-details-type">${this.localize('lastEdit')}</div>
						<div class="preview-content-details-content">${this.fileDate ? formatData(new Date(this.fileDate), DataType.DateTime) : '-'}</div>
						<div class="preview-content-details-type">${this.localize('changedBy')}</div>
						<div class="preview-content-details-content">${this.fileChangedUser ?? '-'}</div>
						<div class="preview-content-details-type">${this.localize('createdBy')}</div>
						<div class="preview-content-details-content">${this.fileCreateUser ?? '-'}</div>
					</div>
				</div>
			</lvl-popup>
		`
	}
	
	private renderViewerDialog = () => html`
		${when(this.injectDialog, () => html`
			<lvl-dialog hide-header open custom-keydown-handler="true" ignore-overflow width="700">
				<lvl-viewer .dataSourceId="${this.dataSourceId}" .fileId="${this.fileId}" .fileExtension="${this.fileType}"></lvl-viewer>
				<lvl-button slot="button-left" color="${ColorState.Info}" data-action="cancel"
										label="${this.localize('cancel')}" @click="${(event: MouseEvent) => this.handleDialogButtonClick(event, false)}"></lvl-button>
				<lvl-button slot="button-right" type="${ButtonType.Secondary}" color="${ColorState.Info}" icon="arrow-up-right-from-square" data-action="newTab"
										label="${this.localize('newTab')}" @click="${(event: MouseEvent) => this.handleDialogButtonClick(event, true)}"></lvl-button>
				<lvl-button slot="button-right" type="${ButtonType.Primary}" icon="arrow-right-to-bracket" data-action="open" label="${this.localize('open')}"
										@click="${(event: MouseEvent) => this.handleDialogButtonClick(event, false)}"></lvl-button>
			</lvl-dialog>
		`)}
	`

	//#region lifecycle callbacks
	
	disconnectedCallback() {
		super.disconnectedCallback()

		const previewImage = this.renderRoot.querySelector('.preview-content')?.querySelector('img')
		if (previewImage)
			previewImage.src = ''

		Queue.unregister(this, previewQueueName)
	}

	protected willUpdate(_changedProperties: PropertyValues) {
		super.willUpdate(_changedProperties)

		if(_changedProperties.has('fileType')) {
			const isFileTypeAllowed: boolean = !!this.fileType && this._thumbnailWhiteList.includes(this.fileType.toLowerCase())
			if (!isFileTypeAllowed)
				Queue.unregister(this, previewQueueName)
		}
	}

	protected firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)

		if (!this.iconOnly && this.state == ThumbnailState.Loading) {
			Queue.register(this, previewQueueName, 2)
		}
	}

	//#endregion
	
	//#region public methods

	/**
	 * will be called to start a new image fetch
	 * @param queueName
	 */
	public upNext(queueName: string) {
		super.upNext(queueName)
		
		if (queueName === previewQueueName)
			this.startPreviewFetch = true
	}

	//#endregion
	
	//#region private methods

	private handlePreviewLoaded() {
		Queue.unregister(this, previewQueueName)
	}

	// show on mouseover
	private handleMouseover() {
		if(!this._htmlViewerDialog?.open){
			this.previewVisible = true
		}
	}

	// hide on mouseleave
	private handleMouseleave() {
		this.previewVisible = false
	}

	//open document viewer dialog
	private handleClick = (event: MouseEvent) => {
		event.stopPropagation()
		this.previewVisible = false
		this.injectDialog = true
	}

	private handleDialogButtonClick = (event: MouseEvent, newTab: boolean) => {
		event.stopPropagation()
		const button = event.target as HTMLElement
		this.injectDialog = false
		if (button.dataset.action === 'cancel')
			return
		this.dispatchEvent(new CustomEvent('thumbnail:click', { detail: newTab, bubbles: true }))
	}
	
	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-table-thumbnail': Thumbnail
	}
}