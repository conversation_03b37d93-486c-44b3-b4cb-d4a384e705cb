import { css, LitElement } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'
import { renderIcon } from '@/shared/component-html.ts'
import { IconStyle } from '@/enums/icon-style.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'

export type FavoriteButtonType = {
	enabled?: boolean
	disabled?: boolean
}

const _localizer: StringLocalizer = new StringLocalizer('FavoriteButton')

/**
 * Example web component using LIT (https://lit.dev)
 */
@customElement('lvl-fav-button')
export class FavoriteButton extends LitElement implements FavoriteButtonType {

	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		fontAwesome,
		css`
			:host {
				--color: var(--cp-clr-border-strong);
				font-size: var(--size-text-l);

				& .icon:hover {
					--color: var(--cp-clr-border-medium);
				}
			}
			
			:host([enabled]) {
				--color: var(--cp-clr-signal-favorite);

				& .icon:hover {
					--color: var(--cp-clr-signal-favorite-hover);
				}
			}
			
			:host([disabled]) {
				--color: var(--cp-clr-state-inactive);
				pointer-events: none;
			}
			
			.icon {
				color: var(--color);
			}
		`,
	]

	//#region attributes

	@property({ type: Boolean, reflect: true })
	enabled: boolean = false

	@property({ type: Boolean, reflect: true })
	disabled: boolean = false

	//#endregion

	//#region states
	//#endregion states

	//#region private properties
	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		return renderIcon('star', {
			iconStyle: this.enabled ? IconStyle.Solid : IconStyle.Light,
			onClick: () => this.enabled = !this.enabled,
			title: _localizer.localize(this.enabled ? 'removeFromFavorites' : 'addToFavorites')
		})
	}

	//#region lifecycle callbacks
	//#endregion

	//#region public methods
	//#endregion

	//#region private methods
	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-fav-button': FavoriteButton
	}
}