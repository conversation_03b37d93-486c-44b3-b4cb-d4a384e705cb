import { getLocalTimeHR, storyTest } from '@test-home/support/advanced-functions.ts'
import { stories } from './ValueFormatter.stories.ts'
import { DataType } from '@/enums/data-type.ts'

describe('<lvl-value-formatter />', () => {

	storyTest('checks numeric values', stories.default, () => {
		cy.mountStory(stories.default)
		cy.mockBrowserLanguage('en')

		// formatter exists
		cy.get('lvl-value-formatter').as('formatter').should('exist')

		// set numeric value and decimal places
		cy.get('@formatter').invoke('attr', 'type', DataType.Double)
		cy.get('@formatter').invoke('attr', 'value', '123456789')
		cy.get('@formatter').invoke('attr', 'decimal-places', '2')

		// check output
		cy.get('@formatter').shadow().should('contain.text', '123,456,789.00')

		// add 4 decimal places -> check rounding
		cy.get('@formatter').invoke('attr', 'value', '123456789.1256')
		cy.get('@formatter').shadow().should('contain.text', '123,456,789.13')

		// check again with one more visible decimal place
		cy.get('@formatter').invoke('attr', 'decimal-places', '3')
		cy.get('@formatter').shadow().should('contain.text', '123,456,789.126')

		// change to integer -> decimal places should disappear
		cy.get('@formatter').invoke('attr', 'type', DataType.Integer)
		cy.get('@formatter').shadow().should('contain.text', '123,456,789')
	})

	storyTest('checks date values', stories.default, () => {
		cy.mountStory(stories.default)
		cy.mockBrowserLanguage('en')

		const date = new Date('2023-02-01T08:23:52Z')
		const timeWithCurrentTimezone = getLocalTimeHR(date)

		// formatter exists
		cy.get('lvl-value-formatter').as('formatter').should('exist')

		// set numeric value and decimal places
		cy.get('@formatter').invoke('attr', 'type', DataType.Date)
		cy.get('@formatter').invoke('attr', 'value', '2023-02-01 08:23:52')

		// check output
		cy.get('@formatter').shadow().should('contain.text', '02/01/2023')
		cy.get('@formatter').shadow().should('not.contain.text', timeWithCurrentTimezone)

		// set to datetime and check again
		cy.get('@formatter').invoke('attr', 'type', DataType.DateTime)
		cy.get('@formatter').shadow().should('contain.text', '02/01/2023 ' + timeWithCurrentTimezone)

		// set to time and check again
		cy.get('@formatter').invoke('attr', 'type', DataType.Time)
		cy.get('@formatter').shadow().should('contain.text', timeWithCurrentTimezone)
		cy.get('@formatter').shadow().should('not.contain.text', '02/01/2023')
	})

	storyTest('checks rich text output', stories.richText, () => {
		cy.mountStory(stories.richText)
		
		// formatter exists
		cy.get('lvl-value-formatter').as('formatter').should('exist')

		cy.get('@formatter').find('p').should('exist')
		cy.get('@formatter').shadow().should('contain.text', 'Chuck Norris')
		cy.get('@formatter').find('p').contains('Chuck Norris').closest('span').should('have.css', 'color', 'rgb(255, 0, 0)')
	})
})