import { getExpectedColor, storyTest } from '@test-home/support/advanced-functions.ts'
import { stories } from './Textarea.stories.ts'

import('./Textarea.ts')

describe('<lvl-textarea />', () => {

	describe('Check textarea functions', () => {

		storyTest('checks default textarea', stories.default, () => {
			cy.mountStory(stories.default)

			// textarea exists
			cy.get('textarea').should("exist")
			
			// settings rows of lvl-textarea should set rows of real textarea
			cy.get('lvl-textarea').invoke('attr', 'rows', 10)
			cy.get('textarea').should('have.attr', 'rows', 10)
			
			// focused textarea should have blue border
			cy.get('textarea').focus()
			cy.get('.textarea-wrapper').should('have.css', 'border-color', getExpectedColor('rgb(175, 191, 202)', 'rgb(0, 151, 253)'))
		
			// legend should be visible and red
			let legend = cy.get('legend')
			legend.should('exist')
			legend.should('have.text', 'This is a legend')
			legend.should('have.css', 'color', getExpectedColor('rgb(222, 88, 88)', 'rgb(248, 113, 113)'))
			
			// changing textarea value gets reflected to lvl-textarea.value
			cy.get('textarea').focus().clear().type('this is a little test').blur({force: true})
			cy.get('lvl-textarea').then((element) => {
				expect(element[0].value).to.equal('this is a little test')
			})
		})

		storyTest('checks empty textarea', stories.empty, () => {
			cy.mountStory(stories.empty)

			// textarea exists
			cy.get('textarea').should("exist")

			// shows placeholder
			cy.get('textarea').should("have.attr", "placeholder", "I am a placeolder")
		})

		storyTest('checks required textarea', stories.required, () => {
			cy.mountStory(stories.required)

			// textarea exists
			cy.get('textarea').should("exist")

			// shows 3px grey border
			cy.get('.textarea-wrapper').invoke('css', 'border-left-width')
				.then(str => parseFloat(str.toString()))
				.should('be.gt', 2)
			cy.get('.textarea-wrapper').should('have.css', 'border-left-color', getExpectedColor('rgb(175, 191, 202)', 'rgb(69, 90, 104)'))
			cy.get('.textarea-wrapper').should('have.css', 'background-color', getExpectedColor('rgb(255, 255, 255)', 'rgb(20, 27, 31)'))
			// focus shows 3px blue border
			cy.get('textarea').focus()
			cy.get('.textarea-wrapper').invoke('css', 'border-left-width')
				.then(str => parseFloat(str.toString()))
				.should('be.gt', 2)
			cy.get('.textarea-wrapper').should('have.css', 'border-left-color', getExpectedColor('rgb(0, 151, 253)', 'rgb(0, 151, 253)'))
			// empty shows 3px red border
			cy.get('textarea').focus().clear().blur()
			cy.get('.textarea-wrapper').invoke('css', 'border-left-width')
				.then(str => parseFloat(str.toString()))
				.should('be.gt', 2)
			cy.get('.textarea-wrapper').should('have.css', 'border-left-color', getExpectedColor('rgb(222, 88, 88)', 'rgb(248, 113, 113)'))
		})

		storyTest('checks readonly textarea', stories.readonly, () => {
			cy.mountStory(stories.readonly)

			// textarea exists
			cy.get('textarea').should("exist")

			// textarea has to be readonly
			cy.get('textarea').should('have.attr', 'readonly')
			cy.get('textarea').should('have.css', 'color', 'rgb(94, 122, 141)')
			cy.get('.textarea-wrapper').should('have.css', 'background-color', getExpectedColor('rgb(237, 240, 243)', 'rgb(32, 42, 49)'))
		})
	})
})