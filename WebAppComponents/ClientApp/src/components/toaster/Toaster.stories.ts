import type { <PERSON><PERSON>, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { LevelStory } from '@story-home/support/commands'
import { ToasterType } from './Toaster'
import { MessageType, ToastType } from '@/components/toaster/toast/Toast.ts'
import { ButtonType } from '@/components/atomics/button/Button.ts'
import { ColorState } from '@/enums/color-state.ts'

import('@/components/atomics/button/Button')
import('./toast/Toast')
import('./toast-mini/ToastMini')
import('./Toaster')

export type ToastConfig = (ToastType & { label: string })

type ToasterProperties = Partial<ToasterType & {
	toastConfigs: ToastConfig[]
}>
type ToasterStory = Story<ToasterProperties>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-toaster',
	render: (_args: ToasterProperties) => html`
		<section style="display: grid; justify-items: start; width: 100%;">
			<div style="display: flex; gap: 4px; flex-direction: column;">
			${_args.toastConfigs?.map(toastConfig => html`
				<lvl-button style="background: #888; color: white; border-radius: 4px; padding: 4px;" label="${toastConfig.label}"
										@click="${async () => {
											let toaster = document.querySelector('lvl-toaster')
											if (toaster == null)
												return
											if (customElements.get(toaster.localName) == undefined)
												await customElements.whenDefined(toaster.localName)
											toastConfig.content ? toaster.notify(toastConfig) : toaster.notifySimple(toastConfig)
										}}"></lvl-button>
			`)}
			</div>
			<lvl-toaster></lvl-toaster>
		</section>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Appearance of a Toaster
 */
export const Default: ToasterStory = {
	args: {
		toastConfigs: [
			{
				label: 'Info Toast',
				type: MessageType.Info,
				heading: 'Breaking News',
				permanent: true,
				content: 'Roy, I spoke to the Elders of the Internet not one hour ago. I told them about Jen winning Employee of the Month and they were so impressed that they wanted to do whatever they could to help.',
				buttons: [{
					label: 'OK',
					closeOnClick: true,
					onclick: () => { 
						console.info('Oh look, Richmond\'s still alive.');
					}
				}]
			}, {
				label: 'Close Toast',
				type: MessageType.Info,
				heading: 'If someone is really close with you, your getting upset or them getting upset is okay, and they don\'t change because of it. It\'s just part of the relationship.',
				permanent: true,
			}, {
				label: 'Warning Toast',
				type: MessageType.Warning,
				heading: 'Slippery sidewalks in winter are dangerous!',
			}, {
				label: 'Error Toast',
				type: MessageType.Error,
				heading: 'Internal IT is loosing their minds',
				content: 'With this picture, everyone knows that a certain Mr Moss might be looking for his cup.',
			}, {
				label: 'Success Toast',
				type: MessageType.Success,
				heading: 'Winter arrives successfully',
				content: 'What good is the warmth of summer, without the cold of winter to give it sweetness.',
				permanent: true,
				buttons: [
					{
						label: 'Best Xmas movies',
						type: ButtonType.Secondary,
						icon: 'gift',
						trailingIcon: 'sleigh',
						color: ColorState.Active,
						onclick: () => window.open('https://www.imdb.com/list/ls000096828/', '_blank')
					}, {
				 		label: 'OK',
						type: ButtonType.Primary,
						closeOnClick: true,
					}
				]
			}, {
				label: 'Stacktrace Toast',
				type: MessageType.Error,
				heading: 'Stacktrace',
				permanent: true,
				content: `Stack Trace:
at Levelbuild.Core.WebAppCoreTests.StorageInterface.StorageInterfaceTest.DataSourceCrudTest() in /builds/levelspace/WebAppCore/WebAppCoreTests/StorageInterface/StorageInterfaceTest.cs:line 217
at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)`,
				buttons: [{
					label: 'OK',
					closeOnClick: true,
					onclick: () => {
						console.info('Oh look, Richmond\'s still alive.');
					}
				}]
			}
		]
	}
}


//#endregion

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default),
} as const