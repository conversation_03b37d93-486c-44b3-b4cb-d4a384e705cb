import { storyTest } from '@test-home/support/advanced-functions.ts'
import { stories } from './ColorPicker.stories.ts'
import { ColorConverter } from '@/shared/color-converter-helper.ts'
import { ColorFormat } from '@/enums/color-format.ts'
import { Rgb } from '@/shared/types.ts'

import('@/components/color-picker/ColorPicker')

describe('<lvl-color-picker />', () => {
	storyTest('checks color picker options', stories.default, () => {
		cy.mountStory(stories.default)

		// color picker is showing
		cy.get('#testPicker', {timeout: 10000}).should('have.attr', 'open')
		cy.get('#testPicker').find('.color-picker').should('be.visible')

		// expert Mode should not be visible
		cy.get('#testPicker').find('.canvas, .expert-hue').should('not.exist')

		// clicking a color-box should close the color picker
		cy.get('#testPicker').find('.palette .color-box').first().click()

		cy.get('#testPicker').should('not.have.attr', 'open')
	})

	storyTest('toggle mode & test navigation', stories.default, () => {
		cy.mountStory(stories.default)
		cy.get('#toggle-expert-mode').find('input[type="checkbox"]').click()

		cy.wait(50)
		cy.get('#testPicker').find('.canvas, .expert-hue').should('be.visible')
		cy.get('#testPicker').find('.color-box-container').should('not.exist')

		// toggle expert mode again and check for color palette and default colors
		cy.get('#testPicker').find('#toggle-expert-mode').click()
		cy.wait(50)
		cy.get('#testPicker').find('.canvas, .expert-hue').should('not.exist')
		cy.get('#testPicker').find('.color-select, .default-colors').should('be.visible')

		// test key navigation
		cy.get('#testPicker').find('.color-picker').realPress('Tab')
		cy.get('#testPicker').find('.color-picker').realPress('ArrowUp').realPress('ArrowDown').realPress('ArrowDown').realPress('ArrowDown')
			.realPress('ArrowUp').realPress('ArrowLeft').realPress('ArrowRight').realPress('ArrowRight').realPress('ArrowRight').realPress('ArrowRight').realPress('ArrowLeft')
		cy.get('#testPicker').find('.color-box.palette:nth-child(11)').should('have.class', 'selected')

		cy.get('#testPicker').find('.color-picker').realPress('Tab')

		cy.get('#testPicker').find('.color-picker').realPress('ArrowLeft').realPress('ArrowRight').realPress('ArrowRight').realPress('ArrowRight').realPress('ArrowRight').realPress('ArrowLeft')
		cy.get('#testPicker').find('.color-box.default:nth-child(3)').should('have.class', 'selected')

		// test move drag-ables
		cy.get('#testPicker').find('#toggle-expert-mode').click()
		cy.wait(50)
		cy.get('#testPicker').find('.canvas').click()
		cy.get('#testPicker').find('.drag.circle').then(dragCircle => {
			cy.expectNumbersInRange(Number(dragCircle.css('top').replace('px', '')), 62.5, 3)
			cy.expectNumbersInRange(Number(dragCircle.css('left').replace('px', '')), 103.8, 3)
		})

		cy.get('#testPicker').find('.slider-content').click()
		cy.get('#testPicker').find('.slider-thumb').then(slider => {
			cy.expectNumbersInRange(Number(slider.css('left').replace('px', '')), 85, 5)
		})

		cy.get('#testPicker').find('.slider-content').trigger('mousedown')
		cy.wait(50)
		cy.window().trigger('mousemove', {clientX: 100})
		cy.wait(50)
		cy.window().trigger('mouseup', {clientX: 100})
		cy.wait(50)

		cy.get('#testPicker').find('.canvas').trigger('mousedown')
		cy.wait(50)
		cy.window().trigger('mousemove', {clientX: 120, clientY: 120})
		cy.wait(50)
		cy.window().trigger('mouseup', {clientX: 120, clientY: 120})
		cy.wait(50)
		cy.get('#testPicker').find('.color-preview').then(preview => {
			const color = ColorConverter.convert(preview.css('background-color'), ColorFormat.Rgb) as Rgb
			cy.expectNumbersInRange(color.red, 93, 5)
			cy.expectNumbersInRange(color.green, 162, 5)
			cy.expectNumbersInRange(color.blue, 116, 5)
		})
	})

	//describe('actions when in Chrome', {browser: 'chrome'}, () => {
		storyTest('loading with colors', stories.default, () => {
			cy.mountStory(stories.default)

			// find color in palette
			cy.get('#testPicker').invoke('attr', 'value', '#FF0000')
			cy.get('#testPicker').find('.canvas, .expert-hue').should('not.exist')
			cy.get('#testPicker').find('.color-box.palette:nth-child(3)').should('have.class', 'selected')

			// find Color in default Colors
			cy.get('#testPicker').invoke('attr', 'value', 'hsl(0, 91%, 36%)')
			cy.get('#testPicker').find('.canvas, .expert-hue').should('not.exist')
			cy.get('#testPicker').find('.color-box.default:nth-child(1)').should('have.class', 'selected')

			// find color in expert mode
			cy.get('#testPicker').invoke('attr', 'value', 'rgb(83, 183, 232)')
			cy.get('#testPicker').find('.canvas, .expert-hue').should('exist')

			cy.get('#testPicker').find('.button-ok').click()
			cy.get('#testPicker').invoke('attr', 'open', true)
			cy.wait(50)
			cy.get('#testPicker').find('#toggle-expert-mode').click()
			cy.wait(50)
			cy.get('#testPicker').find('.palette .color-box:nth-child(5)').click()
			cy.get('#testPicker').invoke('attr', 'open', true)
			cy.wait(50)
			cy.get('#testPicker').find('.palette .color-box:nth-child(18)').click()

			cy.get('#testPicker').invoke('attr', 'value', 'rgb(83, 183, 232)')
			cy.get('#testPicker').invoke('attr', 'open', true)

			cy.get('#testPicker').find('.color-box.recently:nth-child(3)').should('have.class', 'selected')

			cy.get('#testPicker').find('.color-picker').realPress('Tab').realPress('Tab')
			cy.get('#testPicker').find('.color-picker').realPress('ArrowRight').realPress('ArrowLeft').realPress('ArrowLeft').realPress('ArrowLeft').realPress('ArrowRight')
			cy.get('#testPicker').find('.color-box.recently:nth-child(2)').should('have.class', 'selected')

			cy.get('#testPicker').find('.color-picker').realPress('Enter')
			cy.get('#testPicker').invoke('attr', 'open', true)
		})
	//})

	storyTest('test different color formats', stories.default, () => {
		cy.mountStory(stories.default)

		cy.get('#testPicker').invoke('attr', 'format', 'hsl')
		cy.get('#testPicker').invoke('attr', 'value', '#FF0000')
		cy.get('#testPicker').find('.color-box.palette:nth-child(3)').should('have.class', 'selected')

		cy.get('#testPicker').invoke('attr', 'format', 'rgb')
		cy.get('#testPicker').invoke('attr', 'value', '#FF0000')
		cy.get('#testPicker').find('.color-box.palette:nth-child(3)').should('have.class', 'selected')

		cy.get('#testPicker').invoke('attr', 'format', 'hex')
		cy.get('#testPicker').invoke('attr', 'value', 'red')
		cy.get('#testPicker').find('.color-box.palette:nth-child(3)').should('have.class', 'selected')

		cy.get('#testPicker').invoke('attr', 'value', '')
		cy.get('#testPicker').find('.color-box.palette:first-child').should('have.class', 'selected')
	})
})