import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, queryAssignedElements, state } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import CommunicationServiceProvider, { CommunicationResponseType } from '@/shared/communication-service.ts'
import { ItemChangeEventType } from '@/components/list-views/data-list/List.ts'
import { renderIcon } from '@/shared/component-html.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import { QueryViewNavigationInterface } from '@/components/list-views/QueryViewNavigation.ts'
import { query } from 'lit/decorators/query.js'
import { ifDefined } from 'lit/directives/if-defined.js'
import { Button } from '@/components/atomics/button/Button.ts'
import { CheckboxState } from '@/enums/checkbox-state.ts'
import { FilterField, ListFilter } from '@/components/data-organizers/list-filter/ListFilter.ts'
import { ListDataColumnInterface } from '@/components/list-views/data-list/ListDataColumn.ts'
import { ItemType } from '@/components/list-views/types.ts'
import { QueryView, QueryViewProperties } from '@/components/list-views/QueryView.ts'
import * as DataTypeColumnModule from '@/enums/data-column-type.ts'
import { DataColumnType } from '@/enums/data-column-type.ts'
import { classMap } from 'lit/directives/class-map.js'
import { fontAwesome } from '@/shared/font-awesome.ts'
import { QueryViewContent } from '@/components/list-views/QueryViewContent.ts'
import { guard } from 'lit/directives/guard.js'
import { replacePlaceholders } from '@/shared/data-operations.ts'

export type EnumerationType = QueryViewProperties & {
	withNavigation: boolean
	withPagination: boolean
	withSorting: boolean
}

export type EnumerationContentInterface = QueryViewContent & {
	withExternalNavigation: boolean
	get selectedItems(): ItemType[]
	get dataColumns(): ListDataColumnInterface[]
	hasSelectColumn: () => boolean
	countSelectedItems: () => number
	selectAll: () => void
	unselectAll: () => void
}

/**
 * Navigation and Filtering wrapper for lists and card web component using LIT (https://lit.dev)
 */
@customElement('lvl-enumeration')
export class Enumeration extends QueryView(LitElement) implements EnumerationType {

	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		styles.icon,
		styles.skeleton,
		styles.animation,
		fontAwesome,
		css`
			:host {
				position: relative;
				display: block;
				width: 100%;
				height: 100%;
			}

			:host([skeleton]) .skeleton__block > * {
				opacity: 0;
			}

			.no-data {
				position: absolute;
				inset: 0;

				display: flex;
				justify-content: center;
				align-items: center;
				align-content: center;
				justify-items: center;
				gap: var(--size-spacing-m);
				padding: var(--size-spacing-m);
				color: var(--cp-clr-text-tertiary);

				& .subtitle {
					font-size: 0.75em;
				}

				& .subtitle.hidden {
					display: none;
				}
			}
			
			.enumeration {
				display: flex;
				flex-direction: column;
				height: inherit;
				min-height: 80px;
			}
			
			::slotted(lvl-list), .enumeration__content {
				height: 100%;
			}

			.enumeration__content:not(:first-child) {
				position: relative;
				display: block;
				flex-grow: 1;
				overflow-y: hidden;
			}

			/* possible own component select bar */

			.select-all {
				display: flex;
				align-items: center;
				color: var(--cp-clr-text-secondary);
				padding-left: var(--size-spacing-m);
			}

			.control {
				flex-shrink: 0;
				
				display: grid;
				column-gap: var(--size-spacing-m);
				grid-auto-flow: column;
				align-items: center;
				
				height: 4rem;
				margin: var(--size-spacing-m);
				margin-bottom: 0;
				border-bottom: 1px solid var(--cp-clr-border-medium);
				
				white-space: nowrap;
				overflow-x: auto;
			}

			.control:has(.control__left + .control__center + .control__right) {
				grid-template-columns: 1fr max-content 1fr;
			}

			.control__left {
				justify-self: start;

				display: flex;
				align-items: center;
				gap: var(--size-spacing-m);
			}

			.control__center {
				justify-self: center;
			}

			.control__right {
				justify-self: end;
			}

			#custom-actions {
				display: flex;
				column-gap: 0.5rem;
				margin-right: var(--size-spacing-m);
			}
			
			.enumeration__navigation {
				height: 3.2rem;
				flex-shrink: 0;
				margin-bottom: var(--size-spacing-s);
			}
		`,
	]

	//#region attributes

	@property({ type: Boolean, attribute: 'with-navigation' })
	withNavigation: boolean = false

	@property({ type: Boolean, attribute: 'with-pagination' })
	withPagination: boolean = false

	@property({ type: Boolean, attribute: 'with-sorting' })
	withSorting: boolean = false

	@property({ type: Boolean, attribute: 'allow-new-entries' })
	allowNewEntries: boolean = false

	@property({ type: String, attribute: 'no-entries-message' })
	noEntriesMessage?: string

	//#endregion

	//#region states

	@state()
	selectable: boolean = false

	@state()
	selectAllState: CheckboxState = CheckboxState.Off

	@state()
	selectedCount: number = 0

	//#endregion states

	//#region private properties

	@queryAssignedElements({ slot: 'select-action' })
	private _selectActions?: HTMLElement[]

	private get selectActions() {
		return this._selectActions != null && this._selectActions.length > 0 ? this._selectActions : this.querySelectorAll('[slot="select-action"]')
	}

	@query('.enumeration')
	private _htmlRoot!: HTMLElement

	@query('lvl-query-view-navigation')
	private _htmlNavigation?: QueryViewNavigationInterface

	@query('lvl-list-filter')
	private _htmlFilter?: ListFilter

	protected get filterComponent() {
		return this._htmlFilter ?? null
	}

	// if we want to make a slot out of it
	protected get navigationComponent() {
		return this._htmlNavigation
	}

	@queryAssignedElements({ selector: 'lvl-list[initDone]' })
	private _htmlListComponents!: EnumerationContentInterface[]


	protected get contentComponent() : EnumerationContentInterface | null {
		return this._htmlListComponents.length > 0 ? this._htmlListComponents[0] : this.querySelector(`:is(${this.validContentComponents.join(',')})[initDone]`)
	}

	//@TODO: Remove this and use a slot attribute instead
	protected get validContentComponents(): string[] {
		return ['lvl-list']
	}

	private static readonly _baseLocalizer: StringLocalizer = new StringLocalizer('DataEnumeration')

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		let rootClasses = {
			enumeration: true,
			skeleton__block: this.skeleton,
		}
		
		return html`
			<div class="${classMap(rootClasses)}">
				${this.renderSortingLayer()}
				${this.renderSelectLayer()}
				<div class="enumeration__content">
					<slot></slot>
					${this.renderNoDataFallback()}
				</div>
				${this.withNavigation ? html`
					<lvl-query-view-navigation class="enumeration__navigation" limit="${ifDefined(this.limit)}" ?with-pagination="${this.withPagination}" 
																		 count="${ifDefined(this.totalCount)}"></lvl-query-view-navigation>` : ''}
			</div>
		`
	}

	// No data in data table
	private renderNoDataFallback = () => {
		let hasData = this.totalCount > 0 || this.apiController.isLoading
		if (hasData)
			return ''

		return guard([this.totalCount, this.lastDataFetch], () => html`
			<div class="no-data">
				${renderIcon('file-magnifying-glass', { cssStyle: 'font-size: 1.6rem;', inline: true })}
				<span>${Enumeration._baseLocalizer.localize('noEntriesTitle')}</span>
				<span class="subtitle${this.allowNewEntries ? "" : " hidden"}">${this.noEntriesMessage || Enumeration._baseLocalizer.localize('noEntriesText')}</span>
			</div>
		`)
	}

	renderSortingLayer = () => {
		if (!this.withSorting || !this.contentComponent)
			return ''

		// Get the first 10 non-hidden columns for the filter dialog
		const filterFields: Array<FilterField> = this.contentComponent.dataColumns
																								 .filter(column => !column.hidden && column.type != DataColumnType.Enum)
																								 .map(column => ({
																									 label: column.label || column.name,
																									 name: column.ref || column.name,
																									 type: DataTypeColumnModule.toDataType(column.type),
																									 ref: column.ref,
																									 values: column.values,
																								 }))
		return html`
			<div class="control">
				<div class="control__left">
					<lvl-list-filter id="${this.id || 'list'}-filter" url="${ifDefined(this.url ?? undefined)}"
													 .columns="${ifDefined(filterFields)}"
													 .sortings="${ifDefined(this.sorting)}"></lvl-list-filter>
				</div>
			</div>
		`
	}

	private renderSelectLayer = () => {
		if (!this.selectable)
			return ''

		return html`
			<div class="control">
				<div class="control__left">
					<div class="select-all">
						<lvl-checkbox class="clickable" data-action="select-all"
													@click="${this.handleSelectAll}"
													?checked="${this.selectAllState === CheckboxState.On}"
													?indeterminate="${this.selectAllState === CheckboxState.Inderterminate}"></lvl-checkbox>
						<span>${Enumeration._baseLocalizer.localize('selectedEntriesText', this.selectedCount, this.itemCount)}</span>
					</div>
				</div>
				<div id="custom-actions" class="control__right">
					<slot name="select-action"></slot>
				</div>
			</div>`
	}

	//#region lifecycle callbacks

	connectedCallback() {
		super.connectedCallback()
		this.addEventListener('line:toggle:change', async (event: Event) => {
			if (!this.url)
				return

			const customEvent = event as CustomEvent<ItemChangeEventType>
			const detail = customEvent.detail
			let payload = this.payload ? { ...this.payload } : {}
			
			// replace all placeholders in payload
			Object.keys(payload).forEach(parameterKey => {
				if (typeof payload[parameterKey] === 'string' && payload[parameterKey].toString().includes('##'))
					payload[parameterKey] = replacePlaceholders(payload[parameterKey].toString(), detail.row)
			})
			const response = await CommunicationServiceProvider.patch(this.url, detail.slug, {
				body: JSON.stringify({ ...payload, ...detail.data }),
				headers: { 'Content-Type': 'application/json' },
			})
			
			// reset input value to the origin value
			if (response.state == CommunicationResponseType.Error) {
				if (detail.errorCallback != null)
					detail.errorCallback()
				return false
			}

			return true
		})

		this.addEventListener('query-view:registered', () => {
			this.updateSelectState()

			if (this.withNavigation && this.contentComponent)
				this.contentComponent.withExternalNavigation = true
		})
	}

	protected firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)

		this._htmlRoot.addEventListener('query-view:select', (event: Event) => {
			const customEvent = event as CustomEvent<CheckboxState>
			this.selectAllState = customEvent.detail
			this.selectedCount = this.contentComponent?.countSelectedItems() ?? 0
			event.stopPropagation()
		})

		this.addSelectActionEventListener()
		this.addFilterDialogListener()
		this.addChipClickListener()

		this.updateSelectState()
	}

	protected async updated(_changedProperties: PropertyValues<this>) {
		super.updated(_changedProperties)
		if (this.contentComponent != null) {
			// tell the content component to display/show their own navigation button
			if (_changedProperties.has('withNavigation'))
				this.contentComponent.withExternalNavigation = this.withNavigation
		}

		// enable/disable item actions 
		if (_changedProperties.has('selectAllState'))
			this.setSelectActionsDisabled(this.selectAllState === CheckboxState.Off)
	}

	//#endregion

	//#region public methods

	//#endregion

	//#region private methods

	private addChipClickListener() {
		this._htmlRoot.addEventListener('chip-close:click', (event: Event) => {
			const chip = event.target as HTMLElement
			this.filters = this.filters.filter(filter => !(filter.filterColumn == chip.dataset['name'] && filter.compareValue?.toString() == chip.dataset['value']))
		})
	}

	private addFilterDialogListener() {
		this._htmlRoot.addEventListener('list-filter:filter:changed', (event: Event) => {
			const filterDialog = event.target as ListFilter
			// prevent unnecessary component updates
			if (JSON.stringify(this.filters) !== JSON.stringify(filterDialog.filters))
				this.filters = [ ...filterDialog.filters ]
		})

		this._htmlRoot.addEventListener('list-filter:sorting:changed', (event: Event) => {
			const filterDialog = event.target as ListFilter
			this.sorting = [ ...filterDialog.sortings ]
		})
	}

	// add a new custom event to all custom actions for selected rows
	private addSelectActionEventListener() {
		this.selectActions?.forEach(customAction => {
			customAction.addEventListener('click', (event: Event) => {
				if (this.selectedCount > 0 && this.contentComponent != null) {
					let customEvent = new CustomEvent('select-action:click', {
						bubbles: true,
						detail: { selectedItems: [ ...this.contentComponent.selectedItems ] },
					})
					this.dispatchEvent(customEvent)
				}
				event.stopPropagation()
			})
		})
	}

	// (un)select all displayed rows
	private handleSelectAll() {
		if (this.selectAllState == CheckboxState.On) {
			this.contentComponent?.unselectAll()
			this.selectAllState = CheckboxState.Off
			this.selectedCount = 0
			return
		}

		this.contentComponent?.selectAll()
		this.selectAllState = CheckboxState.On
		this.selectedCount = this.itemCount
	}

	// enable/disable all custom actions
	private setSelectActionsDisabled(disable: boolean) {
		this.selectActions?.forEach(customAction => {
			const button = customAction as Button
			button.disabled = disable
		})
	}

	private updateSelectState() {
		this.selectable = this.contentComponent?.hasSelectColumn() ?? false
		this.selectedCount = this.contentComponent?.countSelectedItems() ?? 0
		this.selectAllState = this.selectedCount === 0 ? CheckboxState.Off : (this.selectedCount < this.itemCount ? CheckboxState.Inderterminate : CheckboxState.On)
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-enumeration': Enumeration
	}
}