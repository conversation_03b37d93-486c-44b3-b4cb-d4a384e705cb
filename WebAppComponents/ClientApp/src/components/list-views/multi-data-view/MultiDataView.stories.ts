import type { <PERSON><PERSON>, <PERSON><PERSON>onte<PERSON><PERSON>, <PERSON>Obj as Story } from '@storybook/web-components'
import { html, TemplateResult } from 'lit'
import { ifDefined } from 'lit/directives/if-defined.js'
import { LevelStory } from '@story-home/support/commands'
import { MultiDataViewType } from './MultiDataView'
import { http, HttpResponse } from 'msw'
import { getSuccessListResolver } from '@test-home/support/advanced-functions.ts'
import { mockServerFailureResponse, mockServerSuccessResponse } from '@story-home/support/mock-service.ts'
import { QueryResponseData } from '@/shared/types.ts'
import fruits from '@test-home/fixtures/fruits.json'
import { generateSampleData } from '@/components/list-views/multi-data-view/table/Table.stories.ts'
import tableStoryMeta, { stories as tableStories } from './table/Table.stories.ts'
import galleryStoryMeta, { stories as galleryStories } from './gallery/Gallery.stories.ts'
import actionBarMeta, { QueryViewActionBarProperties } from '@/components/list-views/action-bar/QueryViewActionBar.stories.ts'
import { DataType } from '@/enums/data-type.ts'
import { BooleanFormat } from '@/enums/boolean-format.ts'

import('@/components/atomics/button/Button')
import('./../QueryViewNavigation')
import('./table/TableSelectColumn')
import('./MultiDataView')

/* 
 * More on how to set up stories at: https://storybook.js.org/docs/web-components/writing-stories/introduction
 */

type MultiDataViewProperties = Partial<MultiDataViewType & {
	actionBar: QueryViewActionBarProperties
	content: TemplateResult<1>, 
	width: string,
	height: string
}>
type MultiDataViewStory = Story<MultiDataViewProperties>

const LIMIT_STEPS = [ undefined, 10, 25, 50, 100, 250 ] as const

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-multi-data-view',
	render: (_args: MultiDataViewProperties) => html`
		${_args.actionBar ? actionBarMeta.render!(_args.actionBar, {} as StoryContext) : ''}
		<lvl-multi-data-view url="${ifDefined(_args.url)}" limit="${ifDefined(_args.limit)}" offset="${ifDefined(_args.startOffset)}"
												 filters="${ifDefined(JSON.stringify(_args.filters))}" sorting="${ifDefined(JSON.stringify(_args.sorting))}"
												 ?embedded="${_args.embedded}" ?skeleton="${_args.skeleton}" style="width: ${_args.width ?? '100%'};height:${_args.height ?? '100%'};">
			${_args.content}
		</lvl-multi-data-view>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		content: { table: { disable: true } },
		url: {
			control: 'text', description: 'Should the data be fetched from an url?',
			table: {
				type: { summary: 'text' },
			},
		},
		limit: {
			control: 'select', options: LIMIT_STEPS, description: 'How many records per page do you want to display?',
			table: {
				type: { summary: 'number' },
				defaultValue: { summary: '10' },
			},
		},
		offset: {
			control: 'number', description: 'Starting record which is used for paging.',
			table: {
				type: { summary: 'number' },
				defaultValue: { summary: '0' },
			},
		},
		filters: {
			control: 'object', description: 'Default filtering',
			table: {
				type: { summary: 'array' },
			},
		},
		sorting: { control: 'object', description: 'Default sorting (Multi sort is also possible)' },
		width: {
			control: 'text', description: 'Width of the list with unit',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: '100%' },
			},
		},
		embedded: {
			control: 'boolean',
			description: 'Makes the appearance more light weighted to use it within sections',
			table: {
				type: { summary: 'boolean' },
			},
		},
		height: {
			control: 'text', description: 'Height of the list with unit',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: '100%' },
			},
		},
		skeleton: {
			control: 'boolean',
			description: 'Is the list line still loading?',
			table: {
				type: { summary: 'boolean' },
			},
		},
	},
	parameters: {
		msw: {
			handlers: [
				http.get('/success-list-with-success-patch', async ({ request }) => {
					return getSuccessListResolver(request, fruits)
				}),
				http.get('/success-list-with-error-patch', async ({ request }) => {
					return getSuccessListResolver(request, fruits)
				}),
				http.patch('/success-list-with-success-patch/Tomato/*', async () => {
					let responseJson = await mockServerFailureResponse({
						errorCode: 500,
						errorMessage: 'Row update could not be stored to the database',
					}, 300)
					return HttpResponse.json(responseJson)
				}),
				http.patch('/success-list-with-success-patch/*', async () => {
					let responseJson = await mockServerSuccessResponse(null, 300)
					return HttpResponse.json(responseJson)
				}),
				http.patch('/success-list-with-error-patch/*', async () => {
					let responseJson = await mockServerFailureResponse({
						errorCode: 500,
						errorMessage: 'Row update could not be stored to the database',
					}, 300)
					return HttpResponse.json(responseJson)
				}),
				http.get('/no-data', async () => {
					let responseJson = await mockServerSuccessResponse<QueryResponseData>({
						rows: [],
						countTotal: 0,
					}, 500)
					return HttpResponse.json(responseJson)
				}),
				http.get('/infinite', async ({ request }) => {
					const rows = generateSampleData(3, 200).rows as Array<Record<string, any>>
					return getSuccessListResolver(request, rows)
				}),
			],
		},
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Appearance of a MultiDataView with data from an api endpoint
 */
export const UrlData: MultiDataViewStory = {
	args: {
		content: tableStoryMeta.render!({
			height: '600px',
			identityColumn: 'name',
			columns: [
				{ name: 'id', label: 'ID' },
				{ name: 'name', label: 'NAME' },
				{ name: 'enabled', label: 'ENABLED', type: DataType.Boolean, displayFormat: BooleanFormat.Toggle, liveEditable: true },
			],
		}, {} as StoryContext) as TemplateResult<1>,
		url: '/success-list-with-success-patch',
	},
}

/**
 * Appearance of a MultiDataView with data from an api endpoint
 */
export const UrlGalleryData: MultiDataViewStory = {
	args: {
		content: galleryStoryMeta.render!({
			height: '600px',
			identityColumn: 'name',
			allowFavorite: true,
			infos: [
				{ name: 'name' },
				{ name: 'enabled', type: DataType.Boolean, displayFormat: BooleanFormat.Toggle, liveEditable: true },
			],
		}, {} as StoryContext) as TemplateResult<1>,
		url: '/success-list-with-success-patch',
	},
}

/**
 * Appearance of a MultiDataView with data from an api endpoint
 */
export const UrlWithNoData: MultiDataViewStory = {
	args: {
		content: tableStoryMeta.render!({
			height: '600px',
			columns: [
				{ position: 0, name: 'id', label: 'ID' },
				{ position: 1, name: 'name', label: 'NAME' },
			],
		}, {} as StoryContext) as TemplateResult<1>,
		url: '/no-data',
	},
}

/**
 * Appearance of a MultiDataView with infinite scrolling because the request size is limited
 */
export const InfiniteScrolling: MultiDataViewStory = {
	args: {
		url: '/infinite',
		limit: 50,
		content: tableStoryMeta.render!({
			columns: generateSampleData(3, 1).columns,
			height: '700px',
		}, {} as StoryContext) as TemplateResult<1>,
	},
}

/**
 * Appearance of a MultiDataView with infinite scrolling because the request size is limited
 */
export const InfiniteGalleryScrolling: MultiDataViewStory = {
	args: {
		url: '/infinite',
		limit: 50,
		content: galleryStoryMeta.render!({
			infos: generateSampleData(3, 1).columns,
			height: '600px',
		}, {} as StoryContext) as TemplateResult<1>,
	},
}

/**
 * Appearance of a MultiDataView with local data and all data types
 */
export const DataTypes: MultiDataViewStory = {
	args: {
		content: tableStories.dataType.render() as TemplateResult<1>,
		width: tableStories.dataType.getAttribute<string>('width') ?? '',
	},
}

/**
 * Appearance of an embedded MultiDataView
 */
export const Embedded: MultiDataViewStory = {
	args: {
		content: tableStories.default.render() as TemplateResult<1>,
		embedded: true,
		height: '200px',
	},
}

/**
 * Appearance of an embedded MultiDataView of type Gallery
 */
export const EmbeddedGallery: MultiDataViewStory = {
	args: {
		content: galleryStories.default.render() as TemplateResult<1>,
		embedded: true,
		height: '200px',
	},
}

/**
 * Appearance of an embedded MultiDataView with selectable items
 */
export const SelectColumn: MultiDataViewStory = {
	args: {
		actionBar: {
			withSelectAll: true,
			withFavoriteAction: true,
			withInactiveAction: true,
		},
		content: tableStoryMeta.render!({
			preStaticColumns: [
				html`
					<lvl-table-select-column></lvl-table-select-column>`,
			],
			allowFavorite: true,
			allowInactive: true,
			columns: generateSampleData(3, 15).columns,
			rows: generateSampleData(3, 15).rows,
		}, {} as StoryContext) as TemplateResult<1>,
	},
}


/**
 * Appearance of an embedded MultiDataView with selectable items
 */
export const SelectColumnGallery: MultiDataViewStory = {
	args: {
		actionBar: {
			withSelectAll: true,
			withFavoriteAction: true,
			withInactiveAction: true,
		},
		content: galleryStoryMeta.render!({
			selectable: true,
			allowFavorite: true,
			allowInactive: true,
			rows: generateSampleData(3, 15).rows,
		}, {} as StoryContext) as TemplateResult<1>,
	},
}

//#endregion

// An array that may be imported into cypress tests
export const stories = {
	url: new LevelStory(meta, UrlData, 'Data from Url'),
	urlGallery: new LevelStory(meta, UrlGalleryData, 'Gallery Data from Url'),
	noData: new LevelStory(meta, UrlWithNoData, 'No Data Story'),
	infinite: new LevelStory(meta, InfiniteScrolling, 'Infinite Scrolling Story'),
	infiniteGallery: new LevelStory(meta, InfiniteGalleryScrolling, 'Infinite Scrolling Gallery Story'),
	dataType: new LevelStory(meta, DataTypes, 'Local data with all data Types'),
	embedded: new LevelStory(meta, Embedded, 'Embedded View'),
	select: new LevelStory(meta, SelectColumn, 'Multi select with actions'),
	selectGallery: new LevelStory(meta, SelectColumnGallery, 'Gallery multi select with actions'),
} as const