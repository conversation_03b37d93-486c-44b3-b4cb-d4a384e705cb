import { stories, TableStory } from './Table.stories'
import { getLocalTimeHR, storyTest } from '@test-home/support/advanced-functions'
import { LevelStory } from '@story-home/support/commands.ts'
import { Align } from '@/enums/align.ts'
import { ListDataColumn } from '@/components/list-views/data-list/ListDataColumn.ts'
import { Table } from '@/components/list-views/multi-data-view/table/Table.ts'
import { TableDataColumnProperties } from '@/components/list-views/multi-data-view/table/TableDataColumn.ts'
import { Data } from '@/shared/types'
import { ContentData } from '@/components/list-views/types.ts'

import('@/components/inputs/checkbox/Checkbox')
import('@/components/atomics/button/Button')
import('@/components/dropdown/DropdownMenuItem')
import('@/components/dropdown/DropdownMenu')
import('@/components/dropdown/Dropdown')
import('./Table')

// Test suite for the example web component
describe('<lvl-table />', () => {
	beforeEach(() => {
		cy.stub(window, 'setTimeout', (callback: () => void, timeout: number) => {
			if (timeout > 0)
				setTimeout(callback, 0)
			else
				callback()
		})
	})

	const findColumnDefinition = (levelStory: LevelStory<TableStory>, comparator: (column: TableDataColumnProperties) => boolean) => {
		const config = levelStory.config as TableStory
		let position = 0
		const foundColumn = config.args?.columns?.find(column => {
			if (!column.hidden)
				position++
			return comparator(column)
		})
		return foundColumn ? { position, column: foundColumn } : null
	}

	// delivers the column of the story configuration + real position in table
	const getColumnDefinitionByName = (name: string, levelStory: LevelStory<TableStory>) => {
		return findColumnDefinition(levelStory, (column) => column.name === name)
	}

	const multiLineThreshold = ($element: JQuery) => {
		const fontSize = $element[0].computedStyleMap().get('font-size') as CSSUnitValue
		return Number(fontSize?.value?.toString()) * 2
	}

	storyTest('changes column properties afterwards', stories.default, () => {
		cy.mountStory(stories.default)

		cy.get('.table__head .table__row:first-child .table__cell:first-child .column__label').should('have.text', 'Employee')
		cy.get('lvl-table-data-column:first-child').then($column => {
			const column = $column[0] as ListDataColumn
			column.label = 'FooBar'
			column.textAlign = Align.Right
			column.hideLabel = false
		})
		cy.get('.table__head .table__row:first-child .table__cell:first-child .column__label')
			.should('contain.text', 'FooBar')
		cy.get('.table__body lvl-table-row:first-child').find('.table__cell:first-child .cell__content')
			.should('have.class', 'align-right')
	})

	storyTest('auto formats all cell values in rows to human readable notation', stories.dataType, () => {

		cy.mockBrowserLanguage('de')
		cy.mountStory(stories.dataType)

		cy.get('.table__head .table__row:first-child').as('headerRow')
		cy.get('.table__body lvl-table-row:first-child').as('firstRow')
		cy.get('.table__body lvl-table-row:nth-child(2)').as('secondRow')
		let columnDefinition

		const checkLabel = (columnDefinition: any) => {
			cy.get('@headerRow').find(`.table__cell:nth-of-type(${columnDefinition.position}) .column__label`).should('contain.text', columnDefinition.column.label)
		}

		// Boolean
		columnDefinition = getColumnDefinitionByName('boolean', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).find(`.fa-check`).should('exist')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '-')

		// Checkbox
		columnDefinition = getColumnDefinitionByName('checkbox', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).find(`.fa-check`).should('exist')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '-')

		// Integer
		columnDefinition = getColumnDefinitionByName('integer', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '1')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '3000')

		// Long with thousand separator 
		columnDefinition = getColumnDefinitionByName('long', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '2')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '15.999.333')

		// Double with 2 decimal places
		columnDefinition = getColumnDefinitionByName('double', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '45,30')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '49,99')

		// Double with 1 decimal place
		columnDefinition = getColumnDefinitionByName('doubleCut', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '123,5')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '123,0')

		// Currency
		columnDefinition = getColumnDefinitionByName('currency', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '50,00 $')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '49,99 $')

		// Date
		columnDefinition = getColumnDefinitionByName('date', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '17.08.2023')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '24.12.2023')

		// Datetime
		columnDefinition = getColumnDefinitionByName('datetime', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '17.08.2023 10:30')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '24.12.2023 13:59')

		// DatetimeUTC
		columnDefinition = getColumnDefinitionByName('datetimeUTC', stories.dataType)!
		checkLabel(columnDefinition)
		let timeWithCurrentTimezone = getLocalTimeHR(new Date('2023-08-17T10:30Z'), 'de')
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '17.08.2023 ' + timeWithCurrentTimezone)
		timeWithCurrentTimezone = getLocalTimeHR(new Date('2023-12-24T13:59Z'), 'de')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '24.12.2023 ' + timeWithCurrentTimezone)

		// Time
		columnDefinition = getColumnDefinitionByName('time', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '10:30')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', '23:59')

		// TimeUTC
		columnDefinition = getColumnDefinitionByName('timeUTC', stories.dataType)!
		checkLabel(columnDefinition)
		timeWithCurrentTimezone = getLocalTimeHR(new Date('1970-01-01T10:30:00+02:00'), 'de')
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', timeWithCurrentTimezone)
		timeWithCurrentTimezone = getLocalTimeHR(new Date('1970-01-01T23:59Z'), 'de')
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).should('contain.text', timeWithCurrentTimezone)

		// String
		columnDefinition = getColumnDefinitionByName('string', stories.dataType)!
		checkLabel(columnDefinition)
		const expectSingleLine = ($element: JQuery) => {
			expect($element.innerHeight()).to.be.greaterThan(0).and.to.be.lessThan(multiLineThreshold($element))
		}
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position}) .cell__content`).then(expectSingleLine)
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position}) .cell__content`).then(expectSingleLine)

		// Text
		columnDefinition = getColumnDefinitionByName('text', stories.dataType)!
		checkLabel(columnDefinition)
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position}) .cell__content`).then(expectSingleLine)
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position}) .cell__content`).then(expectSingleLine)

		// Integer
		columnDefinition = getColumnDefinitionByName('multiInteger', stories.dataType)!
		checkLabel(columnDefinition)
		let integerValues = stories.dataType!.getAttribute<any[]>('rows')![0]['multiInteger'] as number[]
		cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position})`).find('lvl-fry').should('have.length', integerValues.length)
		integerValues.forEach((integer, index) => {
			cy.get('@firstRow').find(`.table__cell:nth-child(${columnDefinition.position}) lvl-fry`).eq(index).find('.fry label').should('contain.text', integer.toString())
		})
		integerValues = stories.dataType!.getAttribute<any[]>('rows')![1]['multiInteger'] as number[]
		cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position})`).find('lvl-fry').should('have.length', integerValues.length)
		integerValues.forEach((integer, index) => {
			cy.get('@secondRow').find(`.table__cell:nth-child(${columnDefinition.position}) lvl-fry`).eq(index).find('.fry label').should('contain.text', integer)
		})
	})

	storyTest('measures performance for big data', stories.big, () => {

		// measure mounting time of a tiny table
		cy.window().its('performance').invoke('mark', 'mountStoryLow')
		cy.mountStory(stories.tiny)
		cy.get('.table__body lvl-table-row:last-child')
		cy.window().its('performance').invoke('measure', 'mountStoryLow')
			.then(measureTiny => {

				// remove created datatable
				cy.get('lvl-table').invoke('remove')

				// ...and compare its time with the measure mounting time of a big table
				cy.window().its('performance').invoke('mark', 'mountStoryBig')
				cy.mountStory(stories.big)
				cy.get('.table__body lvl-table-row:last-child')
				cy.window().its('performance').invoke('measure', 'mountStoryBig')
					.then(measureBig => {
						expect(measureBig.duration - measureTiny.duration).to.be.lessThan(2000)
					})
			})

	})

	storyTest('clicks on row', stories.clickRow, () => {

		cy.spyConsole('log')

		cy.mountStory(stories.clickRow)

		cy.get('.table__body lvl-table-row:first-child').find('.table__cell:first-child').click()

		// compare console lo output with expected value
		cy.getLog((calls) => {
			expect(calls.length).to.be.greaterThan(0)
			expect(calls[calls.length - 1].args[0]).to.equal(`Row 1 was clicked.`)
		})
	})

	storyTest('checks hidden column label on first column', stories.default, () => {
		cy.mountStory(stories.default)
		cy.get('.table__head .table__row:first-child .table__cell:first-child .column__label')
			.should('not.have.text')
	})

	storyTest('checks changing of column sort icon', stories.default, () => {
		cy.mountStory(stories.default)
		cy.get('.table__body').find('lvl-table-row:nth-child(1)').as('firstRow')

		cy.get(`.table__head .table__cell:first-child`).as('column').click()
		cy.get('[data-column="name"] lvl-menu-item[sorting="asc"]').click({force:true}).should('have.attr', 'checked')
		cy.get(`@column`).click()
		cy.get('[data-column="name"] lvl-menu-item[sorting="desc"]').click({force:true}).should('have.attr', 'checked')
		cy.get('[data-column="name"] lvl-menu-item[sorting="asc"]').should('not.have.attr', 'checked')
		cy.get(`@column`).click()
		cy.get('[data-column="name"] lvl-menu-item[sorting="desc"]').click({force:true}).should('not.have.attr', 'checked')
	})

	storyTest('with thumbnails', stories.withThumbnail, () => {
		cy.intercept('/Api/DataSources/12345/Files/**', {
			statusCode: 200,
			headers: {
				contentType: 'image/png',
			},
			fixture: 'thumbnailFile.png,null',
		})

		cy.intercept('/Api/DataSources/00000/Files/*/Thumbnail*', {
			statusCode: 415,
			headers: {
				contentType: 'image/png',
			},
		})

		cy.intercept('/Api/DataSources/00000/Files/*', {
			statusCode: 200,
			headers: {
				contentType: 'image/png',
			},
			fixture: 'thumbnailFile.png,null',
		})
		
		cy.mountStory(stories.withThumbnail)
		
		cy.get('.table__body lvl-table-row').find('.table__cell lvl-table-thumbnail').should('have.length', 7).should('be.visible')
		cy.get('.table__body lvl-table-row').find('.table__cell lvl-table-thumbnail').shadow().find('img').should('have.length', 4).should('be.visible')
		cy.get('.table__body lvl-table-row').find('.table__cell lvl-table-thumbnail').shadow().find('.thumbnail-wrapper').find('i').should('have.length', 5).should('be.visible')
		
		// check no image with file thumbnail
		cy.get('lvl-table-thumbnail').eq(2).as('iconOnlyThumbnail')
		cy.get('@iconOnlyThumbnail').find('i').should('exist')
		cy.get('@iconOnlyThumbnail').find('.thumbnail-wrapper.clickable').should('exist')
		cy.get('@iconOnlyThumbnail').should('have.attr', 'state', 'failed')
		cy.get('@iconOnlyThumbnail').click()
		cy.get('@iconOnlyThumbnail').find('lvl-dialog').as('dialog').should('have.attr', 'open')
		cy.get('@dialog').find('[data-action="cancel"]').click({ force: true })
		cy.get('@dialog').should('not.exist')
		
		// check normal thumbnail
		cy.get('lvl-table-thumbnail').eq(0).as('thumbnail')
		cy.get('@thumbnail').find('img').should('exist')
		cy.get('@thumbnail').find('.thumbnail-wrapper.clickable').should('exist')
		cy.get('@thumbnail').should('have.attr', 'state', 'finished')
		cy.get('@thumbnail').click()
		cy.get('@thumbnail').find('lvl-dialog').as('dialog').should('have.attr', 'open')
		cy.get('@dialog').find('[data-action="cancel"]').click({ force: true })
		cy.get('@dialog').should('not.exist')
		
		// check no file thumbnail
		cy.get('lvl-table-thumbnail').eq(3).as('noFile')
		cy.get('@noFile').find('img').should('not.exist')
		cy.get('@noFile').find('i').should('exist')
		cy.get('@noFile').find('.thumbnail-wrapper.clickable').should('not.exist')
		cy.get('@noFile').should('have.attr', 'state', 'finished')
		cy.get('@noFile').click()
		cy.get('@noFile').find('lvl-dialog').should('not.exist')
	})
	
	storyTest('check stickiness', stories.stickyWithThumbnail, () => {
		cy.viewport(800, 500)
		cy.intercept('**/Thumbnail?*', {
			statusCode: 200,
			headers: {
				contentType: 'image/png',
			},
			fixture: 'thumbnailFile.png,null',
		})

		cy.mountStory(stories.stickyWithThumbnail)
		
		cy.get('.table__head').as('head')
		cy.get('.table__body').as('body')
		cy.get('@body').find('lvl-table-row').should('have.length', 7)
		cy.get('@body').scrollTo(500, 0)
		
		// check sticky head columns
		for(let i = 0; i < 2; i++) {
			cy.get('@head').find('.table__cell').eq(i).invoke('position').its('left').should('be.gte', 0)
		}
		cy.get('@head').find('.table__cell').eq(2).invoke('position').its('left').should('be.lessThan', 0)
		
		// set sticky = true for data column 2
		cy.get('lvl-table-data-column').eq(1).invoke('attr', 'sticky', '')
		for(let i = 0; i < 3; i++) {
			cy.get('@head').find('.table__cell').eq(i).invoke('position').its('left').should('be.gte', 0)
		}
	})
	
	storyTest('minimize the table to provoke column hiding', stories.dynamic, () => {
		
		cy.mountStory(stories.dynamic)
		
		cy.viewport(1100, 500)
		cy.get('lvl-table').find('.table__head').as('header')
		cy.get('@header').find('.table__cell').should('have.length', 7)

		cy.viewport(700, 500)
		cy.get('@header').find('.table__cell').should('have.length', 6)
		
		cy.viewport(650, 500)
		cy.get('@header').find('.table__cell').should('have.length', 5)
		
		cy.viewport(450, 500)
		cy.get('@header').should('not.exist')
		cy.get('lvl-card').should('have.length', 3)

		cy.viewport(650, 500)
		cy.get('@header').find('.table__cell').should('have.length', 5)

		cy.viewport(700, 500)
		cy.get('@header').find('.table__cell').should('have.length', 6)

		cy.viewport(800, 500)
		cy.get('@header').find('.table__cell').should('have.length', 7)
	})

	describe('scrolling behaviour', () => {
		storyTest('scrolls y to the last row', stories.big, () => {
			cy.mountStory(stories.big)
			cy.get('.table__body').then($main => {
				const mainRect = $main[0].getBoundingClientRect()

				cy.get('.table__body lvl-table-row:last-child').then($cellContent => {
					const rowRect = $cellContent[0].getBoundingClientRect()
					expect(rowRect.bottom).greaterThan(mainRect.height + mainRect.bottom)
				})
				cy.get('.table__body').scrollTo('bottomLeft', { duration: 200 })
				cy.wait(500)
				cy.get('.table__body lvl-table-row:last-child').then($cellContent => {
					const rowRect = $cellContent[0].getBoundingClientRect()
					expect(rowRect.bottom).lessThan(mainRect.height + mainRect.bottom)
				})
			})
		})

		storyTest('scrolls x to the last row', stories.big, () => {
			cy.mountStory(stories.big)
			cy.get('.table__body').then($main => {
				const mainRect = $main[0].getBoundingClientRect()

				cy.get('.table__body lvl-table-row:last-child').then($cellContent => {
					const rowRect = $cellContent[0].getBoundingClientRect()
					expect(rowRect.bottom).greaterThan(mainRect.height + mainRect.bottom)
				})
				cy.get('.table__body').scrollTo('bottomLeft', { duration: 200 })
				cy.wait(500)
				cy.get('.table__body lvl-table-row:last-child').then($cellContent => {
					const rowRect = $cellContent[0].getBoundingClientRect()
					expect(rowRect.bottom).lessThan(mainRect.height + mainRect.bottom)
				})
			})
		})

		storyTest('scrolls x to the last column and checks table header sync', stories.big, () => {
			const VIEWER_WIDTH = 500 as const
			cy.viewport(VIEWER_WIDTH, 700)
			cy.mountStory(stories.big)
			cy.get('.table__head .table__cell:nth-last-of-type(-n + 1)')
				.invoke('get', 0)
				.invoke('getBoundingClientRect')
				.its('left')
				.should('be.greaterThan', VIEWER_WIDTH)
			cy.get('.table__body').scrollTo('topRight', { duration: 200 })
			cy.get('.table__head .table__cell:nth-last-of-type(-n + 1)')
				.invoke('get', 0)
				.invoke('getBoundingClientRect')
				.its('left')
				.should('be.lessThan', VIEWER_WIDTH)
			cy.get('.table__head').then($headElement => {
				cy.get('.table__body').then($bodyElement => {
					expect(
						$headElement.offset() != null &&
						$bodyElement.offset() != null &&
						$headElement.offset()!.left === $bodyElement.offset()!.left,
					).to.be.true
				})
			})
		})

		storyTest('check workflow icons', stories.workflow, () => {
			const rows = stories.workflow.getAttribute<ContentData[]>('rows')!
			cy.mountStory(stories.workflow)
			rows.forEach((row, index) => {
				cy.get('.table__body lvl-table-row').eq(index).find('lvl-workflow-state-icon').as('workflowIcon').should('exist')
				cy.get('@workflowIcon').find('.workflows').find('.workflow').should('have.length', row.workflowInfos.length)
				cy.get('@workflowIcon').find('.legend').as('legend')
				cy.get('@legend').find('.legend__item').eq(0).find('.item__name').should('have.text', row.workflowInfos[0].workflowName)
				cy.get('@legend').find('.legend__item').eq(0).find('.item__state').should('have.text', row.workflowInfos[0].nodeName)
			})
		})
	})

	describe('tests with every story', () => {
		Object.keys(stories).forEach(storyKey => {
			const story: LevelStory<TableStory> = (stories as Record<string, LevelStory<TableStory>>)[storyKey]

			storyTest('counts render() call', story, () => {
				cy.spy(Table.prototype, 'render').as('spy')
				cy.viewport(1100, 500)
				cy.mountStory(story)

				// wait for timeouts which were set to 0 in beforeEach
				cy.wait(0)

				// cause of resizing the header, all tables with header must be called twice
				cy.get('@spy').its('length').should('be.lessThan', 2)
			})
		})
	})

	describe('data loading', () => {

		storyTest('loads data client side', stories.default, () => {
			cy.mountStory(stories.default)
			cy.get('lvl-table-row').its('length').should('be.greaterThan', 0)
		})

		storyTest('checks label is rendered correctly', stories.converter, () => {
			cy.mountStory(stories.converter)

			const labelColumn = 'isMale'
			const rows = stories.converter.getAttribute<Record<string, object>[]>('rows')
			const columnIndex = stories.converter.getAttribute<TableDataColumnProperties[]>('columns')?.findIndex(column => column.name === 'isMaleSign') ?? 0
			rows?.forEach((row, index) => {
				cy.get(`.table__body lvl-table-row:nth-child(${index + 1})`).find(`.table__cell:nth-child(${columnIndex + 1}) .icon`)
					.should('exist')
					.should('have.class', `fa-${row[labelColumn!] ? 'mars' : 'venus'}`)
			})
		})
	})

	describe('check item actions', () => {

		storyTest('toggle favorite', stories.actions, () => {
			cy.mountStory(stories.actions)
			cy.get('lvl-table').as('table')

			const rows = stories.actions.getAttribute<ContentData<Data>[]>('rows')!
			
			// enable favorite
			cy.get('@table').invoke('attr', 'allow-favorite', '')
			rows.forEach((row, index) => {
				cy.get('.table__body lvl-table-row').eq(index).should(row!.favorite ? 'have.attr' : 'not.have.attr', 'favorite')
				cy.get('.table__body lvl-table-row').eq(index).find('[data-action=favorite]').should(row!.favorite ? 'exist' : 'not.exist')
			})

			// disable favorite
			cy.get('@table').invoke('removeAttr', 'allow-favorite')
			rows.forEach((_, index) => {
				cy.get('.table__body lvl-table-row').eq(index).should('not.have.attr', 'favorite')
				cy.get('.table__body lvl-table-row').eq(index).find('[data-action=favorite]').should('not.exist')
			})

			// toggle button favorite = true
			cy.get('@table').invoke('attr', 'allow-favorite', '')
			
			cy.get('.table__body lvl-table-row:first-child').as('row')
			cy.get('@row').trigger('mouseenter')
			cy.get('@row').find('.row__actions').as('actions')
			cy.get('@actions').find('lvl-fav-button').as('starIcon')
			cy.get('@starIcon').should('have.prop', 'enabled', false)
			cy.get('@starIcon').click()
			cy.get('@starIcon').should('have.prop', 'enabled', true)
			cy.get('@row').trigger('mouseleave')
			cy.get('@row').find('[data-action=favorite]').should('exist')
			cy.get('@row').find('[data-action=favorite]').should('have.prop', 'enabled', true)

			// favorite = false
			cy.get('@row').find('[data-action=favorite]').click()
			cy.get('@row').find('[data-action=favorite]').should('not.exist')
			cy.get('@row').trigger('mouseenter')
			cy.get('@row').find('.row__actions').as('actions')
			cy.get('@actions').find('lvl-fav-button').as('starIcon')
			cy.get('@starIcon').should('have.prop', 'enabled', false)
		})

		storyTest('toggle inactive', stories.actions, () => {
			cy.mountStory(stories.actions)
			cy.get('lvl-table').as('table')

			const rows = stories.actions.getAttribute<ContentData<Data>[]>('rows')!

			// enable inactive
			cy.get('@table').invoke('attr', 'allow-inactive', '')
			rows.forEach((row, index) => {
				cy.get('.table__body lvl-table-row').eq(index).should(row!.inactive ? 'have.attr' : 'not.have.attr', 'inactive')
			})

			// disable inactive
			cy.get('@table').invoke('removeAttr', 'allow-inactive')

			// toggle button inactive = true
			cy.get('@table').invoke('attr', 'allow-inactive', '')
			cy.get('.table__body lvl-table-row:first-child').as('row')
			cy.get('@row').trigger('mouseenter')
			cy.get('@row').find('.row__actions').as('actions')
			cy.get('@actions').find('[data-dropdown="action-dropdown"][initdone]').as('dropdownButton')
			cy.get('@dropdownButton').click()
			cy.get('@actions').find('[data-action=inactive]').as('inactiveMenuItem')
			cy.get('@inactiveMenuItem').should('contain.text', 'Discard')
			cy.get('@row').should('not.have.attr', 'inactive')
			cy.get('@inactiveMenuItem').click()
			cy.get('@row').should('have.attr', 'inactive')
			cy.get('@row').trigger('mouseleave')

			// inactive = false
			cy.get('@row').trigger('mouseenter')
			cy.get('@row').find('.row__actions').as('actions')
			cy.get('@actions').find('[data-dropdown="action-dropdown"][initdone]').as('dropdownButton')
			cy.get('@dropdownButton').click()
			cy.get('@actions').find('[data-action=inactive]').as('inactiveMenuItem')
			cy.get('@inactiveMenuItem').should('contain.text', 'Reactivate')
			// (other actions should be invisible)
			cy.get('@actions').find('[data-action=favorite]').should('not.be.visible')
			cy.get('@inactiveMenuItem').click()
		})
	})
})