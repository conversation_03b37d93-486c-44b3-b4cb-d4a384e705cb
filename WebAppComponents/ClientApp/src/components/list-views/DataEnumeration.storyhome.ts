import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>j as Story } from '@storybook/web-components'
import { <PERSON><PERSON> } from '@/enums/align.ts'
import { DataType } from '@/enums/data-type'
import { DataEnumerationRow, DataEnumerationType, LIMIT_STEPS } from '@/components/list-views/DataEnumeration'
import { queryData } from '@/shared/data-operations'
import fruits from '@test-home/fixtures/fruits.json'
import { LevelStory } from '@story-home/support/commands.ts'
import { http, HttpResponse } from 'msw'
import { mockServerFailureResponse, mockServerSuccessResponse } from '@story-home/support/mock-service.ts'
import { QueryResponseData, SelectParameters } from '@/shared/types.ts'
import { SortDirection } from '@/enums/sort-direction.ts'

import('@/components/inputs/toggle/Toggle')
import('@/components/inputs/checkbox/Checkbox')
import('@/components/inputs/input/Input')
import('@/components/atomics/button/Button')
import('@/components/popup/Popup')
import('@/components/dropdown/Dropdown')
import('@/components/dropdown/DropdownMenu')
import('@/components/dropdown/DropdownMenuItem')
import('@/components/dropdown/DropdownMenuDivider')
import('@/components/data-organizers/search-field-menu/SearchFieldMenu')
import('@/components/data-organizers/list-filter/ListFilter')
import('@/components/atomics/chip/Chip')
import('@/components/atomics/tooltip/Tooltip')

type OuterProperties = {
	width?: string
	height?: string
	selectable?: boolean
}

export type DataEnumerationProperties = Partial<DataEnumerationType> & OuterProperties
export type DataEnumerationStory = Story<DataEnumerationProperties>

const REQUEST_TIMEOUT = 500 as const

//#region meta properties

export const argTypes = {
	columns: { table: { disable: true } },
	height: {
		control: 'text', description: 'Height of the enumeration with unit',
		table: {
			type: { summary: 'text' },
		},
	},
	paging: {
		control: 'boolean',
		description: 'Do you want to use paging?',
		table: {
			type: { summary: 'boolean' },
		},
	},
	limit: {
		control: 'select', options: LIMIT_STEPS, description: 'How many records per page do you want to display?',
		table: {
			type: { summary: 'number' },
			defaultValue: { summary: 10 },
		},
		if: {
			arg: 'paging',
			eq: true,
		},
	},
	offset: {
		control: 'number', defaultValue: 0, description: 'Starting record which is used for paging.',
		table: {
			type: { summary: 'number' },
			defaultValue: { summary: 0 },
		},
		if: {
			arg: 'paging',
			eq: true,
		},
	},
	filters: {
		control: 'array', description: 'Default filtering',
		table: {
			type: { summary: 'array' },
		},
	},
	sortings: { control: 'array', description: 'Default sorting (Multi sort is also possible)' },
	selectable: {
		control: 'boolean',
		description: 'Enables the multi select option of enumerations',
		table: {
			type: { summary: 'boolean' },
		},
	},
	url: {
		control: 'text', description: 'Should the data be fetched from an url?',
		table: {
			type: { summary: 'text' },
		},
	},
	rows: {
		control: 'object', description: 'Used when only client-side data is displayed.',
		table: {
			type: { summary: 'array' },
		},
	},
}

export const parameters: any = {
	msw: {
		handlers: [
			http.get('/success-list-with-success-patch', getSuccessListResolver),
			http.get('/success-list-with-error-patch', getSuccessListResolver),
			http.patch('/success-list-with-success-patch/*', async () => {
				let responseJson = await mockServerSuccessResponse(null, 300)
				return HttpResponse.json(responseJson)
			}),
			http.patch('/success-list-with-error-patch/*', async () => {
				let responseJson = await mockServerFailureResponse({
					errorCode: 500,
					errorMessage: 'Row update could not be stored to the database',
				}, 300)
				return HttpResponse.json(responseJson)
			}),
			http.get('/no-data', async () => {
				let responseJson = await mockServerSuccessResponse<QueryResponseData>({
					rows: [],
					countTotal: 0,
				}, 500)
				return HttpResponse.json(responseJson)
			}),
			http.get('/address-book', async ({ request }) => {
				return getSuccessListResolver(request, true, 50)
			}),
			http.get('/infinite', async ({ request }) => {
				return getSuccessListResolver(request, false, 200)
			}),
		],
	},
}

//#endregion

//#region Stories

export const Default: DataEnumerationStory = {
	args: {
		columns: [
			{ position: 1, name: 'name', label: 'Employee' },
			{ position: 2, name: 'age', label: 'Age', textAlign: Align.Right },
			{ position: 3, name: 'isMale', label: 'Male', type: DataType.Boolean },
			{
				position: 4,
				name: 'salary',
				label: 'Salary',
				type: DataType.Double,
				withThousandSeparators: true,
				sign: '€',
				textAlign: Align.Right,
			},
		],
		rows: [
			{ name: 'Uwe Gärtner', age: 33, isMale: true, salary: 1 },
			{ name: 'Martin Walter', age: 40, isMale: true, salary: 5 },
			{ name: 'Tom Weihe', age: 27, isMale: false, salary: 2 },
			{ name: 'Philipp Wolff', age: 31, isMale: true, salary: 2 },
			{ name: 'André Riebau', age: 39, isMale: true, salary: 4 },
			{ name: 'Marcel Kühne', age: 32, isMale: true, salary: 2 },
			{ name: 'Lukas Ramm', age: 23, isMale: true, salary: 1 },
		],
		sortings: [
			{
				orderColumn: 'salary',
				direction: SortDirection.Asc,
			},
			{
				orderColumn: 'age',
				direction: SortDirection.Desc,
			},
		],
	},
}

export const FixedWidth: DataEnumerationStory = {
	args: {
		width: '200px',
		columns: [
			{ position: 1, name: 'firstName', label: 'First name' },
			{ position: 2, name: 'lastName', label: 'Last name' },
			{ position: 3, name: 'age', label: 'Age', textAlign: Align.Right },
			{ position: 4, name: 'fullName', label: 'Full name' },
			{ position: 5, name: 'salary', label: 'Salary', textAlign: Align.Right },
		],
		rows: [
			{ firstName: 'Uwe', lastName: 'Gärtner', age: 33, fullName: 'Uwe Gärtner', salary: '1€' },
			{ firstName: 'Martin', lastName: 'Walter', age: 40, fullName: 'Martin Walter', salary: '5€' },
			{ firstName: 'Tom', lastName: 'Weihe', age: 27, fullName: 'Tom Weihe', salary: '2€' },
			{ firstName: 'Philipp', lastName: 'Wolff', age: 30, fullName: 'Philipp Wolff', salary: '2€' },
			{ firstName: 'André', lastName: 'Riebau', age: 39, fullName: 'Uwe Gärtner', salary: '4€' },
			{ firstName: 'Marcel', lastName: 'Kühne', age: 32, fullName: 'Marcel Kühne', salary: '2€' },
			{ firstName: 'Lukas', lastName: 'Ramm', age: 23, fullName: 'Lukas Ramm', salary: '1€' },
		],
	},
}

export const FixedHeight: DataEnumerationStory = {
	args: {
		height: '400px',
		columns: [
			{ position: 1, name: 'firstName', label: 'First name' },
			{ position: 2, name: 'lastName', label: 'Last name' },
			{ position: 3, name: 'age', label: 'Age', textAlign: Align.Right },
			{ position: 4, name: 'fullName', label: 'Full name' },
			{ position: 5, name: 'salary', label: 'Salary', textAlign: Align.Right },
		],
		rows: [
			{ firstName: 'Uwe', lastName: 'Gärtner', age: 33, fullName: 'Uwe Gärtner', salary: '1€' },
			{ firstName: 'Martin', lastName: 'Walter', age: 40, fullName: 'Martin Walter', salary: '5€' },
			{ firstName: 'Tom', lastName: 'Weihe', age: 27, fullName: 'Tom Weihe', salary: '2€' },
		],
	},
}

export const MinColumn: DataEnumerationStory = {
	args: {
		columns: [
			{ position: 1, name: 'firstName', label: 'First name', minWidth: 200 },
			{ position: 2, name: 'lastName', label: 'Last name' },
			{ position: 3, name: 'age', label: 'Age', textAlign: Align.Right },
			{ position: 4, name: 'fullName', label: 'Full name' },
			{ position: 5, name: 'salary', label: 'Salary', textAlign: Align.Right },
		],
		rows: [
			{ firstName: 'Uwe', lastName: 'Gärtner', age: 33, fullName: 'Uwe Gärtner', salary: '1€' },
			{ firstName: 'Martin', lastName: 'Walter', age: 40, fullName: 'Martin Walter', salary: '5€' },
			{ firstName: 'Tom', lastName: 'Weihe', age: 27, fullName: 'Tom Weihe', salary: '2€' },
		],
	},
}

export const MaxColumn: DataEnumerationStory = {
	args: {
		columns: [
			{ position: 1, name: 'firstName', label: 'First name' },
			{ position: 2, name: 'lastName', label: 'Last name' },
			{ position: 3, name: 'age', label: 'Age', textAlign: Align.Right },
			{ position: 4, name: 'fullName', label: 'Full name', maxWidth: 250 },
			{ position: 5, name: 'salary', label: 'Salary', textAlign: Align.Right },
		],
		rows: [
			{ firstName: 'Uwe', lastName: 'Gärtner', age: 33, fullName: 'Uwe Gärtner', salary: '1€' },
			{ firstName: 'Martin', lastName: 'Walter', age: 40, fullName: 'Martin Walter', salary: '5€' },
			{ firstName: 'Tom', lastName: 'Weihe', age: 27, fullName: 'Tom Weihe', salary: '2€' },
		],
	},
}

export const DataTypes: DataEnumerationStory = {
	args: {
		columns: [
			{ position: 1, name: 'boolean', label: 'Boolean', type: DataType.Boolean, textAlign: Align.Center },
			{ position: 2, name: 'checkbox', label: 'Checkbox', type: DataType.Boolean, liveEditable: true },
			{ position: 3, name: 'integer', label: 'Integer', type: DataType.Integer },
			{ position: 4, name: 'long', label: 'Long with 1000s', type: DataType.Long, withThousandSeparators: true },
			{ position: 5, name: 'double', label: 'Double', type: DataType.Double },
			{ position: 6, name: 'doubleCut', label: 'Double with 1 DP', decimalPlaces: 1, type: DataType.Double },
			{ position: 7, name: 'currency', label: 'Currency', sign: '$', decimalPlaces: 2, type: DataType.Double },
			{ position: 8, name: 'date', label: 'Date', type: DataType.Date },
			{ position: 9, name: 'datetime', label: 'Datetime', type: DataType.DateTime },
			{ position: 10, name: 'time', label: 'Time', type: DataType.Time },
			{ position: 11, name: 'string', label: 'String', type: DataType.String },
			{ position: 12, name: 'text', label: 'Text', type: DataType.Text, maxWidth: 400, minWidth: 400 },
		],
		rows: [
			{
				boolean: true,
				checkbox: true,
				integer: 1,
				long: 2,
				double: 45.3,
				doubleCut: 123.456,
				currency: 50,
				date: '2023-08-17',
				datetime: '2023-08-17 10:30:00',
				time: '10:30',
				string: 'Vermilion City',
				text: 'Aw, c\'mon Rick. That doesn\'t seem so bad.\n' +
					'Can somebody just let me out of here? If I die in a cage I lose a bet.',
			},
			{
				boolean: false,
				checkbox: false,
				integer: 3000,
				long: 15999333,
				double: 49.99,
				doubleCut: 123.0,
				currency: 49.99,
				date: '2023-12-24',
				datetime: '2023-12-24 23:59:59',
				time: '23:59:59',
				string: 'HI! I\'M MR MEESEEKS! LOOK AT ME!',
				text: `Get off the high road Summer. We all got pink eye because you wouldn't stop texting on the toilet.
He's not a hot girl. He can't just bail on his life and set up shop in someone else's.
I don't get it and I don't need to.
I'm more than just a hammer.
Keep Summer safe.
Let me out, what you see is not the same person as me. My life's a lie. I'm not who you're looking. Let me out. Set me free. I'm really old. This isn't me. My real body is slowly dieing in a vat. Is anybody listening? Can anyone understand? Stop looking at me like that and actually help me. Help me. Help me I'm gunna die.`,
			},
		],
	},
}

export const Paging: DataEnumerationStory = {
	args: {
		columns: [
			{ position: 0, name: 'id', label: 'ID', type: DataType.Integer },
			{ position: 1, name: 'name', label: 'NAME' },
		],
		rows: [
			{ id: 1, name: 'a' },
			{ id: 2, name: 'b' },
			{ id: 3, name: 'c' },
			{ id: 4, name: 'd' },
			{ id: 5, name: 'e' },
			{ id: 6, name: 'f' },
			{ id: 7, name: 'g' },
			{ id: 8, name: 'h' },
			{ id: 9, name: 'i' },
			{ id: 10, name: 'j' },
			{ id: 11, name: 'k' },
			{ id: 12, name: 'l' },
			{ id: 13, name: 'm' },
			{ id: 14, name: 'n' },
			{ id: 15, name: 'o' },
			{ id: 16, name: 'p' },
			{ id: 17, name: 'q' },
			{ id: 18, name: 'r' },
			{ id: 19, name: 's' },
			{ id: 20, name: 't' },
			{ id: 21, name: 'u' },
			{ id: 22, name: 'v' },
			{ id: 23, name: 'w' },
			{ id: 24, name: 'x' },
			{ id: 25, name: 'y' },
			{ id: 26, name: 'z' },
		],
		paging: true,
		limit: 10,
		height: '450px',
	},
}

export const UrlData: DataEnumerationStory = {
	args: {
		height: '600px',
		identityColumn: 'id',
		columns: [
			{ position: 0, name: 'id', label: 'ID' },
			{ position: 1, name: 'name', label: 'NAME' },
			{ position: 2, name: 'enabled', label: 'ENABLED', type: DataType.Boolean, liveEditable: true },
		],
		url: '/success-list-with-success-patch',
		paging: true,
	},
}

export const UrlDataWithToggleFailure: DataEnumerationStory = {
	args: {
		height: '600px',
		identityColumn: 'id',
		columns: [
			{ position: 0, name: 'id', label: 'ID' },
			{ position: 1, name: 'name', label: 'NAME' },
			{ position: 2, name: 'enabled', label: 'ENABLED', type: DataType.Boolean, liveEditable: true },
		],
		url: '/success-list-with-error-patch',
		paging: true,
	},
}

export const UrlWithNoData: DataEnumerationStory = {
	args: {
		height: '600px',
		columns: [
			{ position: 0, name: 'id', label: 'ID' },
			{ position: 1, name: 'name', label: 'NAME' },
		],
		url: '/no-data',
	},
}

export const DataCallback: DataEnumerationStory = {
	args: {
		height: '500px',
		columns: [
			{ position: 0, name: 'id', label: 'ID' },
			{ position: 1, name: 'name', label: 'NAME' },
		],
		loadData: (selectParameters: SelectParameters) => {
			return new Promise((resolve) => {
				setTimeout(() => {
					const { rows, countTotal } = getRowsData(selectParameters)
					resolve({ rows, countTotal })
				}, REQUEST_TIMEOUT)
			})
		},
		paging: true,
	},
}

export const BigData: DataEnumerationStory = {
	args: { height: '500px', limit: 250, ...generateSampleData(30, 50), paging: true },
}

export const LittleData: DataEnumerationStory = {
	args: generateSampleData(1, 1),
}

export const OnClickRow: DataEnumerationStory = {
	args: {
		columns: [
			{ position: 1, name: 'name' },
			{ position: 2, name: 'type', label: 'Type' },
			{ position: 3, name: 'dataSourceCount', label: 'Data Sources', type: DataType.Integer },
			{ position: 4, name: 'enabledLive', type: DataType.Boolean, liveEditable: true },
			{ position: 5, name: 'enabled', type: DataType.Boolean, maxWidth: 100 },
		],
		rows: [
			{ name: 'Bernburg', type: 'DaguWeb', dataSourceCount: 4, enabled: true, enabledLive: true },
			{ name: 'DaguRemoteAccess', type: 'DaguWeb', dataSourceCount: 6, enabled: false, enabledLive: false },
			{ name: 'DaguStorage_Kunde', type: 'DaguStorage', dataSourceCount: 12, enabled: true, enabledLive: true },
		],
		onRowClick: (record: DataEnumerationRow, index: number) => {
			console.log(`Row ${index + 1} was clicked.`, record)
		},
	},
}

export const HiddenColumn: DataEnumerationStory = {
	args: {
		columns: [
			{ position: 0, name: 'id', label: 'ID', type: DataType.Integer, hidden: true },
			{ position: 1, name: 'name', label: 'NAME' },
		],
		rows: [
			{ id: 1, name: 'a' },
			{ id: 2, name: 'b' },
			{ id: 3, name: 'c' },
		],
	},
}

export const SelectColumn: DataEnumerationStory = {
	args: {
		...generateSampleData(3, 15),
		selectable: true,
		paging: true,
	},
}

export const ShowAll: DataEnumerationStory = {
	args: {
		...generateSampleData(3, 15),
	},
}

export const InfiniteScrolling: DataEnumerationStory = {
	args: {
		url: '/infinite',
		columns: generateSampleData(3, 1).columns,
		height: '300px',
		selectable: true,
		paging: true,
	},
}

//#endregion

//#region methods

export const getBaseLevelStories = (definition: Meta): Record<string, LevelStory<any>> => {
	return {
		default:
			new LevelStory(definition, Default),
		dimensionWidth:
			new LevelStory(definition, FixedWidth, 'Fixed Width Story'),
		dimensionHeight:
			new LevelStory(definition, FixedHeight, 'Fixed Height Story'),
		minColumn:
			new LevelStory(definition, MinColumn, 'Maximum Column Story'),
		maxColumn:
			new LevelStory(definition, MaxColumn, 'Minimum Column Story'),
		dataType:
			new LevelStory(definition, DataTypes, 'Data Type Story'),
		url:
			new LevelStory(definition, UrlData, 'URL Story'),
		urlWithToggleFailure:
			new LevelStory(definition, UrlDataWithToggleFailure, 'URL with Post Error Story'),
		noData:
			new LevelStory(definition, UrlWithNoData, 'No Data Story'),
		callback:
			new LevelStory(definition, DataCallback, 'Callback Story'),
		paging:
			new LevelStory(definition, Paging, 'Paging Story'),
		big:
			new LevelStory(definition, BigData, 'Heavy Loaded Story'),
		tiny:
			new LevelStory(definition, LittleData, 'Tiny Story'),
		clickRow:
			new LevelStory(definition, OnClickRow, 'Click Row Story'),
		hiddenColumn:
			new LevelStory(definition, HiddenColumn, 'Hidden Column Story'),
		selectColumn:
			new LevelStory(definition, SelectColumn, 'Multi Selection Story'),
		showAll:
			new LevelStory(definition, ShowAll, 'No Paging Story'),
		infinite:
			new LevelStory(definition, InfiniteScrolling, 'Infinite Scrolling Story'),
	}
}

export function generateSampleData(columnCount: number, rowCount: number): Partial<DataEnumerationProperties> {
	const data: Partial<DataEnumerationProperties> = { columns: [], rows: [] }

	// create sample columns 
	for (let columnNumber = 1; columnNumber <= columnCount; columnNumber++) {
		data.columns!.push({ position: columnNumber, name: `column-${columnNumber}`, label: `Column ${columnNumber}` })
	}

	// create sample rows in relation to column
	for (let rowNumber = 1; rowNumber <= rowCount; rowNumber++) {
		const rowData: Record<string, any> = {}
		data.columns?.forEach((column) => {
			rowData[column.name] = `r:${rowNumber}, c:${column.position}`
		})

		data.rows!.push(rowData)
	}

	return data
}

async function getSuccessListResolver(request: any, useFruits: boolean = true, rowCount: number = 50) {
	const searchParameters = request.url.searchParams
	const mockResponse = await getMockedSuccessResponse(searchParameters, useFruits, rowCount)
	return HttpResponse.json(mockResponse)
}

export async function getMockedSuccessResponse(searchParameters: any, useFruits: boolean = true, rowCount: number, timeout: number = 500) {
	const selectParameters: SelectParameters = searchParameters === null ? {} : {
		limit: searchParameters.has('limit') ? parseInt(searchParameters.get('limit')!) : undefined,
		offset: searchParameters.has('offset') ? parseInt(searchParameters.get('offset')!) : undefined,
		filters: searchParameters.has('filters') ? JSON.parse(searchParameters.get('filters')!) : undefined,
		sortings: searchParameters.has('sortings') ? JSON.parse(searchParameters.get('sortings')!) : undefined,
		groupBy: searchParameters.has('groupBy') ? JSON.parse(searchParameters.get('groupBy')!) : undefined,
	}

	const data = getRowsData(selectParameters, useFruits, rowCount)
	return mockServerSuccessResponse<QueryResponseData>(data, timeout)
}

function getRowsData(selectParameters: SelectParameters, useFruits: boolean = true, rowCount: number = 50): QueryResponseData {
	let rows = (useFruits ? fruits.slice(0, rowCount) : generateSampleData(3, rowCount).rows) as Array<Record<string, any>>
	return queryData(rows, selectParameters)
}

//#endregion