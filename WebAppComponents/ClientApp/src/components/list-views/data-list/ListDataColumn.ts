import { css } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { Align } from '@/enums/align.ts'
import { BaseColumnInterface, BaseColumnProperties } from '@/components/list-views/BaseColumnElement.ts'
import { DataTypeOptions } from '@/shared/types.ts'
import { DataEntryType } from '@/shared/dtos.ts'
import { DataColumnType } from '@/enums/data-column-type.ts'
import { BooleanFormat } from '@/enums/boolean-format'
import { ListBaseColumn } from '@/components/list-views/data-list/ListBaseColumn.ts'

type ListDataColumnType = {
	type?: DataColumnType
	textAlign?: Align
	hidden?: boolean
	hideLabel?: boolean
	ref?: string
	values?: DataEntryType[]
	icon?: string
	iconTooltipYes?: string
	iconTooltipNo?: string
} & DataTypeOptions

export type ListDataColumnProperties = BaseColumnProperties & ListDataColumnType

export type ListDataColumnInterface = BaseColumnInterface & ListDataColumnType

/**
 * Example web component using LIT (https://lit.dev)
 */
@customElement('lvl-list-data-column')
export class ListDataColumn extends ListBaseColumn {

	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		css`
			:host {

			}
		`,
	]

	//#region attributes

	@property()
	type: DataColumnType = DataColumnType.String

	@property({ attribute: 'text-align' })
	textAlign: Align = Align.Left

	@property({ type: Boolean })
	hidden: boolean = false

	@property({ type: Boolean, attribute: 'hide-label' })
	hideLabel: boolean = false

	@property()
	ref?: string

	@property({ type: Array<DataEntryType> })
	values?: DataEntryType[] = []

	/* Type: Number options */

	@property({ type: Number, attribute: 'decimal-places' })
	decimalPlaces: number = 2

	@property()
	sign: string = ''

	@property({ type: Boolean, attribute: 'with-thousand-separators' })
	withThousandSeparators: boolean = false

	@property({ type: Boolean, attribute: 'live-editable' })
	liveEditable: boolean = false

	/* Type: Text options */
	@property({ type: Boolean, attribute: 'rich-text' })
	richText: boolean = false

	/* Type: Boolean options */
	@property({ attribute: 'display-format' })
	displayFormat: BooleanFormat = BooleanFormat.Active
	
	/* Type: Icon options */
	@property()
	icon?: string = 'check'
	
	@property({ attribute: 'icon-tooltip-yes' })
	iconTooltipYes?: string
	
	@property({ attribute: 'icon-tooltip-no' })
	iconTooltipNo?: string

	isData: boolean = true

	//#endregion

	//#region states
	//#endregion states

	//#region private properties
	//#endregion

	//#region lifecycle callbacks

	//#endregion

	//#region public methods

	/**
	 * grid-template-column config of the list data column
	 * @param defaultValue uses this if value of min and max are null
	 */
	getCssDimension(defaultValue?: string): string {
		return super.getCssDimension(defaultValue ?? 'auto')
	}

	protected get minimum() {
		if (!this.minWidth)
			return '0px'
		
		return super.minimum
	}
	
	//#endregion

	//#region private methods
	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-list-data-column': ListDataColumn
	}
}