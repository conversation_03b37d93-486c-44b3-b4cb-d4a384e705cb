import fs from 'fs'
import yaml from 'js-yaml'
import { CssIcon, CssIconCategory } from '@/shared/types.ts'

console.log('Start: Getting icon library for WebApp')

let icons
let categories
try {
	const iconData = fs.readFileSync('./node_modules/@fortawesome/fontawesome-pro/metadata/icons.yml','utf-8')
	icons = yaml.load(iconData) as any
	const categoryData = fs.readFileSync('./node_modules/@fortawesome/fontawesome-pro/metadata/categories.yml','utf-8')
	categories = yaml.load(categoryData) as any
} catch (error) {
	console.error(error)
}

let iconLibrary: CssIcon[] = []
let categoryLibrary: CssIconCategory[] = []

for (const iconName in icons) {
	const iconProperties = icons[iconName]
	const allowedStyles = ['light', 'solid']
	if(iconProperties.styles.some((style: string) => allowedStyles.includes(style))) {
		let label = iconProperties.label
		let terms = iconProperties.search.terms.map((term:string) => term.trim())
		let styles = iconProperties.styles.filter((style: string) => allowedStyles.includes(style))
		iconLibrary.push({icon:iconName, label: label, terms: terms, styles: styles})
	}
}

for (const categoryName in categories) {
	const categoryProperties = categories[categoryName]
	let label = categoryProperties.label
	let icons = categoryProperties.icons.map((icon:string) => icon.trim()).filter((icon:string) => iconLibrary.some(libraryIcon => libraryIcon.icon === icon))
	categoryLibrary.push({category: categoryName, label: label, icons: icons})
}

let jsonIconString = JSON.stringify(iconLibrary, null, 2)
fs.writeFileSync('../ClientApp/public/assets/fontawesome/iconLibrary.json', jsonIconString)

let jsonCategoryString = JSON.stringify(categoryLibrary, null, 2)
fs.writeFileSync('../ClientApp/public/assets/fontawesome/iconCategoryLibrary.json', jsonCategoryString)

console.log('Done: Getting icon library for WebApp')