using System.Globalization;
using System.Net.Http.Headers;
using Levelbuild.Core.GeoDataInterface;
using Levelbuild.Core.GeoDataInterface.Dto;
using Levelbuild.Domain.OpenStreetMap.Dto;
using Newtonsoft.Json;

namespace Levelbuild.Domain.OpenStreetMap;

/// <summary>
/// OpenStreetMap implementation of a GeoDtaProvider
/// </summary>
public class OsmGeoDataProvider : IGeoDataProvider
{
	/// <inheritdoc />
	public static IGeoDataProvider GetInstance(GeoDataConfig? config = null)
	{
		return new OsmGeoDataProvider();
	}

	/// <inheritdoc />
	public async Task<GeoLocation?> GetLocation(string address)
	{
		var requestUri = $"https://nominatim.openstreetmap.org/search?q={Uri.EscapeDataString(address)}&format=json";
		var request = new HttpRequestMessage(HttpMethod.Get, requestUri);
		request.Headers.UserAgent.Add(new ProductInfoHeaderValue("OSMGeoDataProvider", "1.0"));
		request.Headers.UserAgent.Add(new ProductInfoHeaderValue("(+https://www.Levelbuild.com/)"));

		HttpResponseMessage responseMessage;
		using (var client = new HttpClient())
		{
			responseMessage = await client.SendAsync(request);
		}

		var jsonResponse = await responseMessage.Content.ReadAsStringAsync();
		var response = JsonConvert.DeserializeObject<List<OsmGeoResult>>(jsonResponse);

		if (response == null || response.Count == 0)
			return null;
		
		var firstResult = response.First();
		
		double.TryParse(firstResult.Latitude, CultureInfo.InvariantCulture, out var latitude);
		double.TryParse(firstResult.Longitude, CultureInfo.InvariantCulture, out var longitude);

		if (firstResult.BoundingBox.Length != 4)
			return new GeoLocation(latitude, longitude, firstResult.DisplayName);
		
		double.TryParse(firstResult.BoundingBox[0], CultureInfo.InvariantCulture, out var latitudeFrom);
		double.TryParse(firstResult.BoundingBox[1], CultureInfo.InvariantCulture, out var latitudeTo);
		double.TryParse(firstResult.BoundingBox[2], CultureInfo.InvariantCulture, out var longitudeFrom);
		double.TryParse(firstResult.BoundingBox[3], CultureInfo.InvariantCulture, out var longitudeTo);
		
		return new GeoLocation(latitude, longitude, firstResult.DisplayName)
		{
			Area = new GeoArea(latitudeFrom, latitudeTo, longitudeFrom, longitudeTo)
		};
	}
}