import type { Overlay } from '@/components'
import I18n from './translations.ts'

class OverlayService {
	private _overlay?: Overlay
	
	async showWait(text?: string) {
		let overlay = await this.getOverlay()
		if (!overlay)
			return

		overlay.text = text || I18n.translate('oneMomentPlease')
		overlay.open = true
	}

	async hideWait() {
		let overlay = await this.getOverlay()
		if (!overlay)
			return

		overlay.open = false
	}

	private async getOverlay(): Promise<Overlay> {
		if (this._overlay)
			return this._overlay

		const overlayElement = document.getElementById('overlay')
		if (overlayElement)
			await window.Component.waitForComponentInitialization(overlayElement)
		return this._overlay = overlayElement as unknown as Overlay
	}
}

declare global {
	interface Window {
		Overlay: OverlayService
	}
}

const OverlayInstance = new OverlayService()
window.Overlay = OverlayInstance
export default OverlayInstance