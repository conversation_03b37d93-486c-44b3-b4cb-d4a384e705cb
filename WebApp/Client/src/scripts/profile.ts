import type { Autocomplete, Button, Toaster } from 'level-components'
import { MessageType } from 'level-components'
import Component from '../services/component-service.ts'

let mobileQrReady = false
const qrSwitch = document.getElementById('mobile-qr-switch') as Button
const container = document.getElementById('mobile-qr-container')


/* Add Mobile QR Code event listener */
qrSwitch.addEventListener('click', async () => {
	if (!mobileQrReady)
		await fetchMobileQrCode()

	if (!container)
		return

	if (container.classList.contains('hide')) {
		container.classList.remove('hide')
		qrSwitch.label = qrSwitch.dataset['hideLabel']
	} else {
		container.classList.add('hide')
		qrSwitch.label = qrSwitch.dataset['showLabel']
	}
})

/* Add customer switching */
document.getElementById('customer-switch')?.addEventListener('change', async (event) => {
	const toaster = document.getElementById('toaster') as Toaster
	await Component.waitForComponentInitialization(toaster)
	const autocomplete = event.target as Autocomplete
	const response = await fetch(`/Api/Users/<USER>/${autocomplete.value}`, { method: 'POST' })
	if (!response.ok) {
		toaster.notifySimple({ heading: autocomplete.dataset['errorMessage'] ?? '', type: MessageType.Error })
		return
	}

	window.location.reload()
})

async function fetchMobileQrCode() {
	if (!container)
		return

	const result = await fetch(`/Api/Users/<USER>'GET' })
	if (!result.ok) {
		console.error(`${result.status} - unexpected server error received: ${result.statusText}`)
		return null
	}

	const json = await result.json()
	if (json.error) {
		console.error(`Server response contains an error: ${json.error}`)
		return
	}

	container.insertAdjacentHTML('beforeend', json.data)
	mobileQrReady = true
}