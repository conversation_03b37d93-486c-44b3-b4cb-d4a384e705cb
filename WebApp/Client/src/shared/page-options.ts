import type { DataFilter, DataSorting } from 'level-components/types'
import { Operator } from '@/enums/operator.ts'

export type NavigationOptionsType = {
	elementIds: string[]
	index: number
	navigationOffset: number
	sortings: DataSorting[]
	filters: DataFilter[]
	listUrl: string
	count: number
}

type QueryParameters = {
	filters: DataFilter[]
	sorting: DataSorting[]
	fulltext: string
}

export default class PageOptions {
	private navigationOptions?: NavigationOptionsType
	
	private queryParameterDictionary: Record<string, QueryParameters> = {}
	
	public setNavigationOptions(navigationOptions?: NavigationOptionsType): PageOptions {
		this.navigationOptions = navigationOptions
		return this
	}
	
	constructor(json?: any) {
		this.navigationOptions = json?.navigationOptions ?? null
		this.queryParameterDictionary = json?.queryParameterDictionary ?? {}
	}
	
	public getNavigationOptions() : NavigationOptionsType | null {
		return this.navigationOptions ?? null
	}
	
	public updateNavigationOptions(toUpdate: Partial<NavigationOptionsType>) : void {
		if(this.navigationOptions != null)
			this.navigationOptions = { ... this.navigationOptions, ...toUpdate }
	}
	
	public getQueryParameters(key: string): QueryParameters {
		if (this.queryParameterDictionary[key] == null)
			return { filters: [], sorting: [], fulltext: '' }
		return this.queryParameterDictionary[key]
	}
	
	public updateQueryParameterSorting(key: string, sorting: DataSorting[]) {
		if (this.queryParameterDictionary[key] == null)
			this.queryParameterDictionary[key] = { filters: [], sorting: [], fulltext: '' }
		this.queryParameterDictionary[key].sorting = sorting
	}
	
	public updateQueryParameterFilters(key: string, filters: DataFilter[]) {
		if (this.queryParameterDictionary[key] == null)
			this.queryParameterDictionary[key] = { filters: [], sorting: [], fulltext: '' }
		this.queryParameterDictionary[key].filters = filters.filter(filter => !filter.force && filter.operator != Operator.FulltextSearch)
		this.queryParameterDictionary[key].fulltext = filters.find(filter => filter.operator == Operator.FulltextSearch)?.compareValue ?? ''
	}
}