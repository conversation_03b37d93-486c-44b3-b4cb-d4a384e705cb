(window.webpackJsonp=window.webpackJsonp||[]).push([[230],{1447:function(_,a,t){_.exports=function(_){"use strict";var a=function(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}(_),t={name:"tzl",weekdays:"Súladi_Lúneçi_<PERSON>tz<PERSON>_<PERSON>árc<PERSON>_Xhúadi_Viénerçi_Sáturi".split("_"),months:"Januar_Fevraglh_Març_Avrïu_Mai_Gün_<PERSON>_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar".split("_"),weekStart:1,weekdaysShort:"S<PERSON>_Lún_Mai_Már_Xhú_Vié_Sát".split("_"),monthsShort:"Jan_Fev_Mar_Avr_Mai_Gün_Jul_Gus_Set_Lis_Noe_Zec".split("_"),weekdaysMin:"Sú_Lú_Ma_Má_Xh_Vi_Sá".split("_"),ordinal:function(_){return _},formats:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"D. MMMM [dallas] YYYY",LLL:"D. MMMM [dallas] YYYY HH.mm",LLLL:"dddd, [li] D. MMMM [dallas] YYYY HH.mm"}};return a.default.locale(t,null,!0),t}(t(103))}}]);
//# sourceMappingURL=chunk.230.js.map