{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/cv.js"], "names": ["module", "exports", "_", "t", "default", "e", "n", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,oEAAoEC,MAAM,KAAKC,OAAO,gEAAgED,MAAM,KAAKE,UAAU,EAAEC,cAAc,6BAA6BH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,sCAAsCC,IAAI,6CAA6CC,KAAK,qDAAqD,OAAOnB,EAAEC,QAAQmB,OAAOjB,EAAE,MAAK,GAAIA,EAAz4BD,CAAE,EAAQ", "file": "chunks/chunk.124.js", "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_cv=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),n={name:\"cv\",weekdays:\"вырсарникун_тунтикун_ытларикун_юнкун_кӗҫнерникун_эрнекун_шӑматкун\".split(\"_\"),months:\"кӑрлач_нарӑс_пуш_ака_май_ҫӗртме_утӑ_ҫурла_авӑн_юпа_чӳк_раштав\".split(\"_\"),weekStart:1,weekdaysShort:\"выр_тун_ытл_юн_кӗҫ_эрн_шӑм\".split(\"_\"),monthsShort:\"кӑр_нар_пуш_ака_май_ҫӗр_утӑ_ҫур_авн_юпа_чӳк_раш\".split(\"_\"),weekdaysMin:\"вр_тн_ыт_юн_кҫ_эр_шм\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD-MM-YYYY\",LL:\"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ]\",LLL:\"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm\",LLLL:\"dddd, YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm\"}};return t.default.locale(n,null,!0),n}));"], "sourceRoot": ""}