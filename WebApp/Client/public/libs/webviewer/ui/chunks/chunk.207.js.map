{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/ru.js"], "names": ["module", "exports", "_", "e", "default", "t", "n", "split", "s", "r", "o", "i", "d", "mm", "hh", "dd", "MM", "yy", "u", "test", "month", "f", "a", "m", "name", "weekdays", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "yearStart", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "h", "M", "y", "ordinal", "meridiem", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,oFAAoFC,MAAM,KAAKC,EAAE,kFAAkFD,MAAM,KAAKE,EAAE,gEAAgEF,MAAM,KAAKG,EAAE,gEAAgEH,MAAM,KAAKI,EAAE,+BAA+B,SAASC,EAAEV,EAAEG,EAAEF,GAAG,IAAIG,EAAEE,EAAE,MAAM,MAAML,EAAEE,EAAE,SAAS,SAASH,EAAE,KAAKI,GAAGJ,EAAEM,EAAE,CAACK,GAAGR,EAAE,sBAAsB,sBAAsBS,GAAG,iBAAiBC,GAAG,gBAAgBC,GAAG,uBAAuBC,GAAG,gBAAgBd,GAAGI,MAAM,KAAKD,EAAE,IAAI,GAAGA,EAAE,KAAK,GAAGE,EAAE,GAAGF,EAAE,IAAI,GAAGA,EAAE,IAAI,IAAIA,EAAE,IAAI,IAAIA,EAAE,KAAK,IAAIE,EAAE,GAAGA,EAAE,IAAI,IAAIU,EAAE,SAAShB,EAAEG,GAAG,OAAOM,EAAEQ,KAAKd,GAAGC,EAAEJ,EAAEkB,SAASZ,EAAEN,EAAEkB,UAAUF,EAAEV,EAAEA,EAAEU,EAAEG,EAAEf,EAAE,IAAIgB,EAAE,SAASpB,EAAEG,GAAG,OAAOM,EAAEQ,KAAKd,GAAGI,EAAEP,EAAEkB,SAASV,EAAER,EAAEkB,UAAUE,EAAEd,EAAEE,EAAEY,EAAED,EAAEZ,EAAE,IAAIc,EAAE,CAACC,KAAK,KAAKC,SAAS,gEAAgElB,MAAM,KAAKmB,cAAc,8BAA8BnB,MAAM,KAAKoB,YAAY,uBAAuBpB,MAAM,KAAKqB,OAAOV,EAAEW,YAAYP,EAAEQ,UAAU,EAAEC,UAAU,EAAEC,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,iBAAiBC,IAAI,uBAAuBC,KAAK,8BAA8BC,aAAa,CAACC,OAAO,WAAWC,KAAK,WAAWjC,EAAE,mBAAmBe,EAAEX,EAAEC,GAAGD,EAAE8B,EAAE,MAAM5B,GAAGF,EAAEA,EAAE,OAAOG,GAAGH,EAAE+B,EAAE,QAAQ3B,GAAGJ,EAAEgC,EAAE,MAAM3B,GAAGL,GAAGiC,QAAQ,SAAS3C,GAAG,OAAOA,GAAG4C,SAAS,SAAS5C,GAAG,OAAOA,EAAE,EAAE,OAAOA,EAAE,GAAG,OAAOA,EAAE,GAAG,MAAM,WAAW,OAAOC,EAAEC,QAAQ2C,OAAOxB,EAAE,MAAK,GAAIA,EAAztDlB,CAAE,EAAQ", "file": "chunks/chunk.207.js", "sourcesContent": ["!function(_,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_ru=t(_.dayjs)}(this,(function(_){\"use strict\";function t(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var e=t(_),n=\"января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря\".split(\"_\"),s=\"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь\".split(\"_\"),r=\"янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.\".split(\"_\"),o=\"янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.\".split(\"_\"),i=/D[oD]?(\\[[^[\\]]*\\]|\\s)+MMMM?/;function d(_,t,e){var n,s;return\"m\"===e?t?\"минута\":\"минуту\":_+\" \"+(n=+_,s={mm:t?\"минута_минуты_минут\":\"минуту_минуты_минут\",hh:\"час_часа_часов\",dd:\"день_дня_дней\",MM:\"месяц_месяца_месяцев\",yy:\"год_года_лет\"}[e].split(\"_\"),n%10==1&&n%100!=11?s[0]:n%10>=2&&n%10<=4&&(n%100<10||n%100>=20)?s[1]:s[2])}var u=function(_,t){return i.test(t)?n[_.month()]:s[_.month()]};u.s=s,u.f=n;var a=function(_,t){return i.test(t)?r[_.month()]:o[_.month()]};a.s=o,a.f=r;var m={name:\"ru\",weekdays:\"воскресенье_понедельник_вторник_среда_четверг_пятница_суббота\".split(\"_\"),weekdaysShort:\"вск_пнд_втр_срд_чтв_птн_сбт\".split(\"_\"),weekdaysMin:\"вс_пн_вт_ср_чт_пт_сб\".split(\"_\"),months:u,monthsShort:a,weekStart:1,yearStart:4,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY г.\",LLL:\"D MMMM YYYY г., H:mm\",LLLL:\"dddd, D MMMM YYYY г., H:mm\"},relativeTime:{future:\"через %s\",past:\"%s назад\",s:\"несколько секунд\",m:d,mm:d,h:\"час\",hh:d,d:\"день\",dd:d,M:\"месяц\",MM:d,y:\"год\",yy:d},ordinal:function(_){return _},meridiem:function(_){return _<4?\"ночи\":_<12?\"утра\":_<17?\"дня\":\"вечера\"}};return e.default.locale(m,null,!0),m}));"], "sourceRoot": ""}