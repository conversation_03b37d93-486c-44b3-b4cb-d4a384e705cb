/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[22],{634:function(ya,ua,n){n.r(ua);var na=n(0),ma=n(7),oa=n(2);ya=n(53);var ka=n(36),ia=n(18);n=function(){function fa(){this.init()}fa.prototype.init=function(){this.qsa=!1;this.Zg=this.Hp=this.connection=null;this.Sl={};this.ha=this.OP=null};fa.prototype.HP=function(x){for(var y=this,r=0;r<x.length;++r){var e=x[r];switch(e.at){case "create":this.Sl[e.author]||(this.Sl[e.author]=e.aName);this.ADa(e);break;case "modify":this.ha.dt(e.xfdf).then(function(a){y.ha.Gb(a[0])});
break;case "delete":e="<delete><id>".concat(e.aId,"</id></delete>"),this.ha.dt(e)}}};fa.prototype.ADa=function(x){var y=this;this.ha.dt(x.xfdf).then(function(r){r=r[0];r.authorId=x.author;y.ha.Gb(r);y.ha.trigger(ma.d.UPDATE_ANNOTATION_PERMISSION,[r])})};fa.prototype.uCa=function(x,y,r){this.Hp&&this.Hp(x,y,r)};fa.prototype.preloadAnnotations=function(x){this.addEventListener("webViewerServerAnnotationsEnabled",this.uCa.bind(this,x,"add",{imported:!1}),{once:!0})};fa.prototype.initiateCollaboration=
function(x,y,r){var e=this;if(x){e.Zg=y;e.ha=r.ia();r.addEventListener(ma.i.DOCUMENT_UNLOADED,function(){e.disableCollaboration()});e.ySa(x);var a=new XMLHttpRequest;a.addEventListener("load",function(){if(200===a.status&&0<a.responseText.length)try{var f=JSON.parse(a.responseText);e.connection=exports.da.cUa(Object(ka.l)(e.Zg,"blackbox/"),"annot");e.OP=f.id;e.Sl[f.id]=f.user_name;e.ha.o_(f.id);e.connection.HXa(function(h){h.t&&h.t.startsWith("a_")&&h.data&&e.HP(h.data)},function(){e.connection.send({t:"a_retrieve",
dId:x});e.trigger(fa.Events.WEBVIEWER_SERVER_ANNOTATIONS_ENABLED,[e.Sl[f.id],e.OP])},function(){e.disableCollaboration()})}catch(h){Object(oa.f)(h.message)}});a.open("GET",Object(ka.l)(this.Zg,"demo/SessionInfo.jsp"));a.withCredentials=!0;a.send();e.qsa=!0;e.ha.mga(function(f){return e.Sl[f.Author]||f.Author})}else Object(oa.f)("Document ID required for collaboration")};fa.prototype.disableCollaboration=function(){this.Hp&&(this.ha.removeEventListener(ia.a.Events.ANNOTATION_CHANGED,this.Hp),this.Hp=
null);this.connection&&this.connection.zv();this.ha&&this.ha.o_("Guest");this.init();this.trigger(fa.Events.WEBVIEWER_SERVER_ANNOTATIONS_DISABLED)};fa.prototype.ySa=function(x){var y=this;this.Hp&&this.ha.removeEventListener(ia.a.Events.ANNOTATION_CHANGED,this.Hp);this.Hp=function(r,e,a){return Object(na.b)(this,void 0,void 0,function(){var f,h,b,w,z,aa,ea,ba,ca;return Object(na.d)(this,function(ha){switch(ha.label){case 0:if(a.imported)return[2];f={t:"a_".concat(e),dId:x,annots:[]};return[4,y.ha.o8()];
case 1:h=ha.aa();"delete"!==e&&(b=(new DOMParser).parseFromString(h,"text/xml"),w=new XMLSerializer);for(z=0;z<r.length;z++)aa=r[z],ba=ea=void 0,"add"===e?(ea=b.querySelector('[name="'.concat(aa.Id,'"]')),ba=w.serializeToString(ea),ca=null,aa.InReplyTo&&(ca=y.ha.yj(aa.InReplyTo).authorId||"default"),f.annots.push({at:"create",aId:aa.Id,author:y.OP,aName:y.Sl[y.OP],parent:ca,xfdf:"<add>".concat(ba,"</add>")})):"modify"===e?(ea=b.querySelector('[name="'.concat(aa.Id,'"]')),ba=w.serializeToString(ea),
f.annots.push({at:"modify",aId:aa.Id,xfdf:"<modify>".concat(ba,"</modify>")})):"delete"===e&&f.annots.push({at:"delete",aId:aa.Id});0<f.annots.length&&y.connection.send(f);return[2]}})})}.bind(y);this.ha.addEventListener(ia.a.Events.ANNOTATION_CHANGED,this.Hp)};fa.Events={WEBVIEWER_SERVER_ANNOTATIONS_ENABLED:"webViewerServerAnnotationsEnabled",WEBVIEWER_SERVER_ANNOTATIONS_DISABLED:"webViewerServerAnnotationsDisabled"};return fa}();Object(ya.a)(n);ua["default"]=n}}]);}).call(this || window)
