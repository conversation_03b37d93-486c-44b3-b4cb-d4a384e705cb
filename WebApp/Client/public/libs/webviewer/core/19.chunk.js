/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[19],{632:function(ya){(function(){ya.exports={Oa:function(){function ua(f,h){this.scrollLeft=f;this.scrollTop=h}function n(f){if(null===f||"object"!==typeof f||void 0===f.behavior||"auto"===f.behavior||"instant"===f.behavior)return!0;if("object"===typeof f&&"smooth"===f.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+f.behavior+" is not a valid value for enumeration ScrollBehavior.");}function na(f,h){if("Y"===
h)return f.clientHeight+a<f.scrollHeight;if("X"===h)return f.clientWidth+a<f.scrollWidth}function ma(f,h){f=fa.getComputedStyle(f,null)["overflow"+h];return"auto"===f||"scroll"===f}function oa(f){var h=na(f,"Y")&&ma(f,"Y");f=na(f,"X")&&ma(f,"X");return h||f}function ka(f){var h=(e()-f.startTime)/468;var b=.5*(1-Math.cos(Math.PI*(1<h?1:h)));h=f.pr+(f.x-f.pr)*b;b=f.fH+(f.y-f.fH)*b;f.method.call(f.KO,h,b);h===f.x&&b===f.y||fa.requestAnimationFrame(ka.bind(fa,f))}function ia(f,h,b){var w=e();if(f===x.body){var z=
fa;var aa=fa.scrollX||fa.pageXOffset;f=fa.scrollY||fa.pageYOffset;var ea=r.scroll}else z=f,aa=f.scrollLeft,f=f.scrollTop,ea=ua;ka({KO:z,method:ea,startTime:w,pr:aa,fH:f,x:h,y:b})}var fa=window,x=document;if(!("scrollBehavior"in x.documentElement.style&&!0!==fa.DUa)){var y=fa.HTMLElement||fa.Element,r={scroll:fa.scroll||fa.scrollTo,scrollBy:fa.scrollBy,f8:y.prototype.scroll||ua,scrollIntoView:y.prototype.scrollIntoView},e=fa.performance&&fa.performance.now?fa.performance.now.bind(fa.performance):Date.now,
a=RegExp("MSIE |Trident/|Edge/").test(fa.navigator.userAgent)?1:0;fa.scroll=fa.scrollTo=function(f,h){void 0!==f&&(!0===n(f)?r.scroll.call(fa,void 0!==f.left?f.left:"object"!==typeof f?f:fa.scrollX||fa.pageXOffset,void 0!==f.top?f.top:void 0!==h?h:fa.scrollY||fa.pageYOffset):ia.call(fa,x.body,void 0!==f.left?~~f.left:fa.scrollX||fa.pageXOffset,void 0!==f.top?~~f.top:fa.scrollY||fa.pageYOffset))};fa.scrollBy=function(f,h){void 0!==f&&(n(f)?r.scrollBy.call(fa,void 0!==f.left?f.left:"object"!==typeof f?
f:0,void 0!==f.top?f.top:void 0!==h?h:0):ia.call(fa,x.body,~~f.left+(fa.scrollX||fa.pageXOffset),~~f.top+(fa.scrollY||fa.pageYOffset)))};y.prototype.scroll=y.prototype.scrollTo=function(f,h){if(void 0!==f)if(!0===n(f)){if("number"===typeof f&&void 0===h)throw new SyntaxError("Value could not be converted");r.f8.call(this,void 0!==f.left?~~f.left:"object"!==typeof f?~~f:this.scrollLeft,void 0!==f.top?~~f.top:void 0!==h?~~h:this.scrollTop)}else h=f.left,f=f.top,ia.call(this,this,"undefined"===typeof h?
this.scrollLeft:~~h,"undefined"===typeof f?this.scrollTop:~~f)};y.prototype.scrollBy=function(f,h){void 0!==f&&(!0===n(f)?r.f8.call(this,void 0!==f.left?~~f.left+this.scrollLeft:~~f+this.scrollLeft,void 0!==f.top?~~f.top+this.scrollTop:~~h+this.scrollTop):this.scroll({left:~~f.left+this.scrollLeft,top:~~f.top+this.scrollTop,behavior:f.behavior}))};y.prototype.scrollIntoView=function(f){if(!0===n(f))r.scrollIntoView.call(this,void 0===f?!0:f);else{for(f=this;f!==x.body&&!1===oa(f);)f=f.parentNode||
f.host;var h=f.getBoundingClientRect(),b=this.getBoundingClientRect();f!==x.body?(ia.call(this,f,f.scrollLeft+b.left-h.left,f.scrollTop+b.top-h.top),"fixed"!==fa.getComputedStyle(f).position&&fa.scrollBy({left:h.left,top:h.top,behavior:"smooth"})):fa.scrollBy({left:b.left,top:b.top,behavior:"smooth"})}}}}}})()}}]);}).call(this || window)
