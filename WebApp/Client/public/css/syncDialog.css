#syncDialog > lvl-form {
	display: grid;
	width: 100%;
	max-height: 50rem;
	padding: var(--size-spacing-l) var(--size-spacing-l) var(--size-spacing-l) var(--size-spacing-l);
	grid-template-columns: 1fr;
	gap: var(--size-spacing-l);
	overflow: auto;
	
	& > label:not(.heading):not(.sourceName) {
		margin-bottom: var(--size-spacing-m)
	}

	& > lvl-checkbox-group {
		margin: 0 0 var(--size-spacing-s) var(--size-spacing-m);
	}

	& > .heading {
		font-size: var(--size-text-l);
		border-bottom: 1px solid var(--clr-border-strong);
		padding-bottom: var(--size-spacing-s);
	}

	& > .sourceName {
		font-size: var(--size-text-l);
		font-weight: bold;
		display: flex;

		& > label {
			font-size: var(--size-text-m);
			font-weight: lighter;
			color: var(--clr-text-secondary);
			padding-left: var(--size-spacing-m);
			align-self: center
		}
	}
}



