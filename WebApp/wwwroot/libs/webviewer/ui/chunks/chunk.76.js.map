{"version": 3, "sources": ["webpack:///./src/ui/src/components/ScaleModal/ScaleModal.scss?7790", "webpack:///./src/ui/src/components/ScaleModal/ScaleModal.scss", "webpack:///./src/ui/src/components/ScaleModal/ScaleCustom.js", "webpack:///./src/ui/src/components/ScaleModal/ScaleModal.js", "webpack:///./src/ui/src/components/ScaleModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "Scale", "Core", "ScaleCustomProps", "scale", "PropTypes", "array", "onScaleChange", "func", "precision", "number", "ScaleCustom", "measurementUnits", "useSelector", "state", "selectors", "getMeasurementUnits", "shallowEqual", "useState", "pageValueDisplay", "setPageValueDisplay", "worldValueDisplay", "setWorldValueDisplay", "isFractionalPrecision", "setIsFractionalPrecision", "pageWarningMessage", "setPageWarningMessage", "worldWarningMessage", "setWorldWarningMessage", "scaleValueBlurFlag", "setScaleValueBlurFlag", "leftDropdownWidth", "setLeftDropdownWidth", "pageValueInput", "useRef", "worldValueInput", "leftContainerRef", "t", "useTranslation", "filterFractionalUnits", "units", "filter", "unit", "fractionalUnits", "includes", "unitFromOptions", "from", "unitToOptions", "to", "useEffect", "onScaleUnitChange", "formatDecimal", "value", "toFixed", "toString", "current", "activeElement", "getFormattedValue", "ifFractionalPrecision", "hintValues", "isWorldValueValid", "onInputValueChange", "isPageValueValid", "pageScale", "newScale", "worldScale", "_onScaleChange", "clientWidth", "pageValueClass", "classNames", "worldValueClass", "getPrecision", "isPageValue", "getNewScale", "updateScaleValue", "scaleValue", "inputValue", "trim", "scaleUnit", "inFractionalRegex", "test", "result", "parseInFractional", "ftInFractionalRegex", "parseFtInFractional", "floatRegex", "parseFloat", "ftInDecimalRegex", "parseFtInDecimal", "undefined", "newUnit", "isPageUnit", "newPageScale", "newWorldScale", "convertUnit", "getInputPlaceholder", "onInputBlur", "flag", "className", "ref", "id", "type", "min", "aria-label", "data-element", "onChange", "e", "target", "placeholder", "onBlur", "Icon", "glyph", "<PERSON><PERSON><PERSON>", "Dropdown", "labelledById", "dataElement", "items", "onClickItem", "currentSelectionKey", "width", "aria-live", "propTypes", "scaleOptions", "ScaleModal", "annotations", "selectedTool", "dispatch", "useDispatch", "isElementDisabled", "DataElements", "SCALE_MODAL", "isElementOpen", "isElementHidden", "getCurrentToolbarGroup", "getSelectedScale", "getActiveToolName", "getIsAddingNewScale", "getMeasurementScalePreset", "getCalibrationInfo", "getIsMultipleScalesMode", "isDisabled", "isOpen", "isHidden", "currentToolbarGroup", "selectedScale", "activeToolName", "isAddingNewScale", "measurementScalePreset", "tempScale", "isFractionalUnit", "isMultipleScalesMode", "precisionOptions", "PrecisionType", "DECIMAL", "precisionOption", "setPrecisionOption", "scaleOption", "setScaleOption", "PresetMeasurementSystems", "METRIC", "presetScale", "setPresetScale", "customScale", "setCustomScale", "hasScaleChanged", "setHasScaleChanged", "totalScalesCount", "Object", "keys", "core", "getScales", "precisionType", "presetMeasurementSystem", "getScaleRatioAsArray", "getScalePrecision", "isFractional", "precisionItem", "getPrecisionType", "find", "item", "setTimeout", "useDidUpdate", "presetPrecisionOption", "scalePresetPrecision", "closeModal", "newPrecisionOption", "actions", "updateCalibrationInfo", "isCalibration", "initialScale", "closeElement", "getCurrentScale", "getPrecisionsValue", "temp", "precisionValue", "Math", "round", "isCustomOption", "modalClass", "open", "closed", "IMPERIAL", "FRACTIONAL", "isCurrentScaleValid", "<PERSON><PERSON><PERSON><PERSON>", "isFractionalUnitsToggleDisabled", "ModalWrapper", "title", "onCloseClick", "<PERSON><PERSON><PERSON><PERSON>", "swipeToClose", "Choice", "radio", "name", "checked", "label", "center", "onClick", "setToolMode", "previousToolName", "defaultUnit", "setIsElementHidden", "map", "_item", "aria<PERSON><PERSON><PERSON>", "isSwitch", "leftLabel", "disabled", "DataElementWrapper", "originalScales", "replaceScales", "applyTo", "createAndApplyScale"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,8jhBAA+jhB,KAGxlhB0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,w2CCavB,IAAMC,EAAQzB,OAAO0B,KAAKD,MAEpBE,EAAmB,CACvBC,MAAOC,IAAUC,MACjBC,cAAeF,IAAUG,KACzBC,UAAWJ,IAAUK,QAGvB,SAASC,EAAY,GAAqC,IAAnCP,EAAK,EAALA,MAAOG,EAAa,EAAbA,cAAeE,EAAS,EAATA,UACpCG,EAAgG,EAA5EC,aAAY,SAACC,GAAK,MAAK,CAACC,IAAUC,oBAAoBF,MAASG,KAAa,GAAhF,GACqC,IAAZC,mBAAS,IAAG,GAArDC,EAAgB,KAAEC,EAAmB,KACkB,IAAZF,mBAAS,IAAG,GAAvDG,EAAiB,KAAEC,EAAoB,KAC2B,IAAfJ,oBAAS,GAAM,GAAlEK,EAAqB,KAAEC,EAAwB,KACU,IAAZN,mBAAS,IAAG,GAAzDO,EAAkB,KAAEC,EAAqB,KACkB,IAAZR,mBAAS,IAAG,GAA3DS,EAAmB,KAAEC,EAAsB,KACiB,IAAfV,oBAAS,GAAM,GAA5DW,EAAkB,KAAEC,EAAqB,KAEa,IAAXZ,mBAAS,GAAE,GAAtDa,EAAiB,KAAEC,EAAoB,KACxCC,EAAiBC,iBAAO,MACxBC,EAAkBD,iBAAO,MACzBE,EAAmBF,iBAAO,MAEzBG,EAAqB,EAAhBC,cAAgB,GAApB,GAEFC,EAAwB,SAACC,GAAK,OAAKA,EAAMC,QAAO,SAACC,GAAI,OAAKC,IAAgBC,SAASF,OACnFG,EAAkBtB,EAAwBgB,EAAsB3B,EAAiBkC,MAAQlC,EAAiBkC,KAC1GC,EAAgBxB,EAAwBgB,EAAsB3B,EAAiBoC,IAAMpC,EAAiBoC,GAI5GC,qBAAU,WACHJ,EAAgBD,SAASxC,EAAM,GAAG,KACrC8C,EAAkBL,EAAgB,IAAI,KAEvC,CAACzC,EAAM,GAAG,KAKb6C,qBAAU,WACJJ,EAAgBD,SAASxC,EAAM,GAAG,MAAQ2C,EAAcH,SAASxC,EAAM,GAAG,KAC5E8C,EAAkBH,EAAc,IAAI,KAErC,CAAC3C,EAAM,GAAG,GAAIA,EAAM,GAAG,KAE1B6C,qBAAU,WACR,IAAME,EAAgB,SAACC,GACrB,OAAOA,aAAK,EAALA,EAAOC,SAAS,EAAI5C,GAAW6C,WAAWvE,OAAS,IAGxDqB,EAAM,GAAG,KAAM6B,aAAc,EAAdA,EAAgBsB,WAAY7E,SAAS8E,eAIpDpC,EAHGG,EAGiBtB,EAAMwD,kBAAkBrD,EAAM,GAAG,GAAIA,EAAM,GAAG,GAAIK,GAAW,GAAO,IAAS,GAF7E0C,EAAc/C,EAAM,GAAG,KAAO,KAKlDA,EAAM,GAAG,IAAQ+B,GAAmBA,EAAgBoB,UAAY7E,SAAS8E,gBACtEjC,GAAyC,UAAhBnB,EAAM,GAAG,GAGrCkB,EAAqBrB,EAAMwD,kBAAkBrD,EAAM,GAAG,GAAIA,EAAM,GAAG,GAAIK,GAAW,GAAO,IAAS,IAFlGa,EAAqB6B,EAAc/C,EAAM,GAAG,KAAO,OAKtD,CAACA,EAAOK,EAAW0B,EAAiBF,EAAgBV,EAAuBM,IAE9EoB,qBAAU,WACRzB,EAAyBkC,YAAsBjD,MAC9C,CAACA,IAEJwC,qBAAU,WACJ1B,GACFG,EAAsBiC,IAAWvD,EAAM,GAAG,KAC1CwB,EAAuB+B,IAAWvD,EAAM,GAAG,MAClB,UAAhBA,EAAM,GAAG,IAClBsB,EAAsB,IACtBE,EAAuB+B,IAAW,oBAElCjC,EAAsB,IACtBE,EAAuB,OAExB,CAACxB,EAAOmB,IAGX0B,qBAAU,YACPW,GAAqBC,EAAmB1B,EAAgBoB,QAAQH,OAAO,KACvE,CAAChD,EAAM,GAAG,KAGb6C,qBAAU,WACR,GAAKa,GAAqBF,GAkBvBE,GAAoBD,EAAmB5B,EAAesB,QAAQH,OAAO,IACrEQ,GAAqBC,EAAmB1B,EAAgBoB,QAAQH,OAAO,OAnB7B,CAC3C,IAAIW,EAAY,CACdX,MAAOhD,EAAM,GAAG,GAChBsC,KAAMtC,EAAM,GAAG,IAEjByD,EAAmB5B,EAAesB,QAAQH,OAAO,GAAM,SAACY,GACtDD,EAAYC,EAASD,aAEvB,IAAIE,EAAa,CACfb,MAAOhD,EAAM,GAAG,GAChBsC,KAAMtC,EAAM,GAAG,IAEjByD,EAAmB1B,EAAgBoB,QAAQH,OAAO,GAAO,SAACY,GACxDC,EAAaD,EAASC,cAGxBC,EAAe,IAAIjE,EAAM,CAAE8D,YAAWE,mBAKvC,CAAC1C,IAEJ0B,qBAAU,WACJb,EAAiBmB,SACnBvB,GAAsBI,EAAiBmB,QAAQY,YAAc,GAAK,KAEnE,CAAC/B,IAEJ,IAAM0B,IAAqB1D,EAAM,GAAG,GAC9BwD,IAAsBxD,EAAM,GAAG,GAE/BgE,EAAiBC,IAAW,cAAe,CAC/C,iBAAkBP,IAEdQ,EAAkBD,IAAW,cAAe,CAChD,iBAAkBT,IAIdM,EAAiB,SAACF,GACtB,IAAMO,EAAe,SAAC7B,GAAI,MAAe,UAATA,EAAmBjC,EAAY,GAAKA,GAEhEuD,EAASD,UAAUX,OAASY,EAASD,UAAUX,MAAQ3C,IACzDuD,EAASD,UAAUX,MAAQmB,EAAaP,EAASD,UAAUrB,OAEzDsB,EAASC,WAAWb,OAASY,EAASC,WAAWb,MAAQ3C,IAC3DuD,EAASC,WAAWb,MAAQmB,EAAaP,EAASC,WAAWvB,OAE/DnC,EAAcyD,IAGVH,EAAqB,SAACT,EAAOoB,EAAaC,GAC9C,IAAMC,EAAmB,SAACC,GACxB,GAAKH,GAAeG,IAAevE,EAAM,GAAG,KAASoE,GAAeG,IAAevE,EAAM,GAAG,GAAK,CAC/F,IAAM4D,EAAW,IAAI/D,EAAM,CACzB8D,UAAW,CAAEX,MAAOoB,EAAcG,EAAavE,EAAM,GAAG,GAAIsC,KAAMtC,EAAM,GAAG,IAC3E6D,WAAY,CAAEb,MAAQoB,EAA2BpE,EAAM,GAAG,GAAtBuE,EAA0BjC,KAAMtC,EAAM,GAAG,MAE3EqE,EACFA,EAAYT,GAEZE,EAAeF,KAKjBQ,EACFpD,EAAoBgC,GAEpB9B,EAAqB8B,GAEvB,IAAMwB,EAAaxB,EAAMyB,OACzB,GAAKtD,EAcE,CACL,IAAMuD,EAAYN,EAAcpE,EAAM,GAAG,GAAKA,EAAM,GAAG,GACvD,GAAkB,OAAd0E,GACF,GAAIC,IAAkBC,KAAKJ,GAAa,CACtC,IAAMK,EAASC,YAAkBN,GACjC,GAAIK,EAAS,EAEX,YADAP,EAAiBO,SAIhB,GAAkB,UAAdH,GACLK,IAAoBH,KAAKJ,GAAa,CACxC,IAAMK,EAASG,YAAoBR,GACnC,GAAIK,EAAS,EAEX,YADAP,EAAiBO,SA3BvB,GAAKT,GAA+B,UAAhBpE,EAAM,GAAG,IAQtB,GAAIiF,IAAWL,KAAKJ,GAAa,CAGtC,YADAF,EADmBY,WAAWV,IAAe,SAR7C,GAAIW,IAAiBP,KAAKJ,GAAa,CACrC,IAAMK,EAASO,YAAiBZ,GAChC,GAAIK,EAAS,EAEX,YADAP,EAAiBO,GA6BzBP,OAAiBe,IAGbvC,EAAoB,SAACwC,EAASC,GAClC,IAAIC,EASAC,EAPFD,EADED,GAAcD,IAAYtF,EAAM,GAAG,GACtB,CACbgD,MAAOhD,EAAM,GAAG,GAAK0F,YAAY1F,EAAM,GAAG,GAAIA,EAAM,GAAG,GAAIsF,GAAWtF,EAAM,GAAG,GAC/EsC,KAAMgD,GAGO,CAAEtC,MAAOhD,EAAM,GAAG,GAAIsC,KAAMtC,EAAM,GAAG,IASpDyF,EANGF,GAAcD,IAAYtF,EAAM,GAAG,GAMtB,CAAEgD,MAAOhD,EAAM,GAAG,GAAIsC,KAAMtC,EAAM,GAAG,IALrC,CACdgD,MAAOhD,EAAM,GAAG,GAAK0F,YAAY1F,EAAM,GAAG,GAAIA,EAAM,GAAG,GAAIsF,GAAWtF,EAAM,GAAG,GAC/EsC,KAAMgD,GAMVxB,EAAe,IAAIjE,EAAM,CAAE8D,UAAW6B,EAAc3B,WAAY4B,MAG5DE,EAAsB,SAACvB,GAC3B,IAAM9B,EAAO8B,EAAcpE,EAAM,GAAG,GAAKA,EAAM,GAAG,GAClD,OAAOmB,EAAwBoC,IAAWjB,GAAkB,UAATA,EAAmBiB,IAAW,iBAAmB,IAGhGqC,EAAc,WAClBlE,GAAsB,SAACmE,GAAI,OAAMA,MAGnC,OACE,yBAAKC,UAAU,0BACb,yBAAKA,UAAU,+BACb,yBAAKA,UAAU,uBACb,yBAAKA,UAAU,iBAAiBC,IAAK/D,GACnC,yBAAK8D,UAAU,aAAaE,GAAG,8BAA8B/D,EAAE,6CAC/D,yBAAK6D,UAAU,iBACb,yBAAKA,UAAW7B,IAAW,CAAE,iBAAkBP,KAC7C,2BACEuC,KAAM9E,EAAwB,OAAS,SACvC+E,IAAI,IACJJ,UAAW9B,EACXhB,MAAOjC,EACPoF,aAAYlE,EAAE,wCACdmE,eAAa,uBACbC,SAAU,SAACC,GAAC,OAAK7C,EAAmB6C,EAAEC,OAAOvD,OAAO,IACpDwD,YAAab,GAAoB,GACjCI,IAAKlE,EACL4E,OAAQb,IAEV,kBAACc,EAAA,EAAI,CAACC,MAAM,aAAab,UAAU,wBAErC,kBAACc,EAAA,EAAO,CAAC/I,QAAS,4CAChB,yBAAKiI,UAAU,cACb,kBAACe,EAAA,EAAQ,CACPb,GAAG,uBACHc,aAAa,6BACbC,YAAY,sBACZC,MAAOvE,EACPwE,YAAa,SAACjE,GAAK,OAAKF,EAAkBE,GAAO,IACjDkE,oBAAqBlH,EAAM,GAAG,GAC9BmH,MAAOxF,QAMjB,yBAAKmE,UAAU,qBAAqB,OACpC,yBAAKA,UAAU,mBACb,yBAAKA,UAAU,aAAaE,GAAG,gCAAgC/D,EAAE,+CACjE,yBAAK6D,UAAU,iBACb,yBAAKA,UAAW7B,IAAW,CAAE,iBAAkBT,KAC7C,2BACEyC,KAAO9E,GAAyC,UAAhBnB,EAAM,GAAG,GAAkB,OAAS,SACpEkG,IAAI,IACJJ,UAAW5B,EACXlB,MAAO/B,EACPkF,aAAYlE,EAAE,wCACdmE,eAAa,0BACbC,SAAU,SAACC,GAAC,OAAK7C,EAAmB6C,EAAEC,OAAOvD,OAAO,IACpDwD,YAAab,GAAoB,GACjCI,IAAKhE,EACL0E,OAAQb,IAEV,kBAACc,EAAA,EAAI,CAACC,MAAM,aAAab,UAAU,wBAErC,kBAACc,EAAA,EAAO,CAAC/I,QAAS,8CAChB,yBAAKiI,UAAU,cACb,kBAACe,EAAA,EAAQ,CACPb,GAAG,yBACHc,aAAa,+BACbE,MAAOrE,EACPoE,YAAY,yBACZE,YAAa,SAACjE,GAAK,OAAKF,EAAkBE,GAAO,IACjDkE,oBAAqBlH,EAAM,GAAG,GAC9BmH,MAAOxF,UASrB,yBAAKmE,UAAU,mBAAmBsB,YAAU,cACxC1D,GACA,uBAAGoC,UAAU,aAAW,UAClB7D,EAAE,iDAAgD,YAAIZ,KAG5DmC,GACA,uBAAGsC,UAAU,iCAA+B,UACtC7D,EAAE,iDAAgD,YAAIV,MAQtEhB,EAAY8G,UAAYtH,EAETQ,Q,g7CC/Tf,IAAMV,EAAQzB,OAAO0B,KAAKD,MAEbyH,EACH,SADGA,EAEH,SA0TKC,EAvTI,SAAH,GAAsC,QAAhCC,EAAW,EAAXA,YAAaC,EAAY,EAAZA,aAC3BC,EAAWC,cACV1F,EAAqB,EAAhBC,cAAgB,GAApB,GAwBN,IAXEzB,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUiH,kBAAkBlH,EAAOmH,IAAaC,aAChDnH,IAAUoH,cAAcrH,EAAOmH,IAAaC,aAC5CnH,IAAUqH,gBAAgBtH,EAAOmH,IAAaC,aAC9CnH,IAAUsH,uBAAuBvH,GACjCC,IAAUuH,iBAAiBxH,GAC3BC,IAAUwH,kBAAkBzH,GAC5BC,IAAUyH,oBAAoB1H,GAC9BC,IAAU0H,0BAA0B3H,GACpCC,IAAU2H,mBAAmB5H,GAC7BC,IAAU4H,wBAAwB7H,OAClC,IArBA8H,EAAU,KACVC,EAAM,KACNC,EAAQ,KACRC,EAAmB,KACnBC,EAAa,KACbC,EAAc,KACdC,EAAgB,KAChBC,EAAsB,YACpBC,EAAS,EAATA,UAAWC,EAAgB,EAAhBA,iBACbC,EAAoB,KAcmD,IAAfpI,oBAAS,GAAM,GAAlEK,EAAqB,KAAEC,EAAwB,KAC4C,IAApDN,mBAASqI,IAAiBC,IAAcC,SAAS,IAAG,GAA3FC,EAAe,KAAEC,EAAkB,KACyB,IAA7BzI,mBAASwG,GAAoB,GAA5DkC,EAAW,KAAEC,EAAc,KACwE,IAApE3I,mBAASiI,EAAuBW,IAAyBC,QAAQ,IAAG,GAAnGC,EAAW,KAAEC,EAAc,KAC2B,KAAvB/I,mBAAS,IAAIjB,EAAM,KAAI,GAAtDiK,GAAW,MAAEC,GAAc,MAC2B,KAAfjJ,oBAAS,GAAM,GAAtDkJ,GAAe,MAAEC,GAAkB,MAEpCC,GAAmBC,OAAOC,KAAKC,IAAKC,aAAa3L,OAEvDkE,qBAAU,WACHsG,IAAiBoB,IAAe/H,SAAS8G,IAC5CC,EAAmBJ,IAAiBoB,IAAe,IAErDV,EAAed,EAAuByB,IAAyB,MAC9D,CAACrJ,IAEJ0B,qBAAU,WACR4G,EAAenC,GACfyC,GAAe,IAAIlK,EAAM+I,EAAc6B,yBAEvC,IAAMpK,EAAYgK,IAAKK,kBAAkB9B,GACzC,GAAKvI,EAAL,CAGA,IAAMsK,EAAerH,YAAsBjD,GAC3Ce,EAAyBuJ,GAEzB,IACMC,EADiBzB,IAAiB0B,GAAiBF,IACpBG,MAAK,SAACC,GAAI,OAAKA,EAAK,KAAO1K,KAChEkJ,EAAmBqB,GAGnBI,YAAW,WACTf,IAAmB,SAEpB,CAACrB,IAEJqC,aAAa,WACX,GAAIzB,IAAgBlC,EAClByC,GAAeH,EAAY,QACtB,CACL,IAAMsB,EAAwBC,IAAqBvB,EAAY,IAC3DsB,GAAyBA,IAA0B5B,GACrDC,EAAmB2B,MAGtB,CAAC1B,IAEJyB,aAAa,WACiB,yBAAxBtC,GACFyC,OAED,CAACzC,IAEJ9F,qBAAU,WACR,IAAMwI,EAAqBF,IAAqBvB,EAAY,IACxDyB,GAAsB7B,IAAgBlC,GACxCiC,EAAmB8B,KAEpB,CAACzB,IAEJ/G,qBAAU,YACH6F,GAAYM,IAEfe,GAAe,IAAIlK,EAAMmJ,IACzBtB,EAAS4D,IAAQC,sBAAsB,CAAEC,eAAe,KACxDpK,EAAyB6H,MAE1B,CAACP,IAEJ7F,qBAAU,WACRoH,IAAmB,KAClB,CAACX,EAAiBQ,GAAaF,EAAaJ,IAE/C3G,qBAAU,WAEJ4F,GAAUK,IAAqBE,IACjCS,EAAenC,GACfyC,GAAe0B,KACfrK,GAAyB,GACzBmI,EAAmBJ,IAAiBC,IAAcC,SAAS,OAE5D,CAACZ,EAAQK,IAEZ,IAAMsC,GAAa,WACjB1D,EAAS4D,IAAQI,aAAa7D,IAAaC,eAwBvC6D,GAAkB,WACtB,IAAMC,EAAqB,SAAC5I,EAAOV,GACjC,IAAIuJ,EAAO7I,EACP8I,EAAiBxC,EAAgB,GAarC,OAXKnI,GAKU,UAATmB,IACFwJ,GAAkB,IAEpBD,EAAOE,KAAKC,MAAMH,EAAOC,GAAkBA,GAP9B,UAATxJ,IACFuJ,EAAOA,EAAK5I,SAAS,EAAI6I,GAAgB5I,WAAWvE,OAAS,IASnD,EAAPkN,GAGT,GAAII,GAAgB,CAClB,IAAMjM,EAAQ8J,GAAYW,uBAG1B,OAFAzK,EAAM,GAAG,GAAK4L,EAAmB5L,EAAM,GAAG,GAAIA,EAAM,GAAG,IACvDA,EAAM,GAAG,GAAK4L,EAAmB5L,EAAM,GAAG,GAAIA,EAAM,GAAG,IAChDA,EAET,OAAO4J,EAAY,GAAG1G,YAiBlBgJ,GAAajI,IAAW,QAAS,aAAc,CACnDkI,MAAOzD,EACP0D,OAAQ1D,IAEJuD,GAAiBzC,IAAgBlC,EACjCkD,GAA0BrJ,EAAwBuI,IAAyB2C,SAAW3C,IAAyBC,OAC/GkB,GAAmB,SAACF,GAAY,OAAMA,EAAevB,IAAckD,WAAalD,IAAcC,SAC9FkB,GAAgBM,GAAiB1J,GACjCoL,IAAuBN,IAAkBnC,GAAY0C,UACrDC,GAAkCR,MAAoB1J,IAAgBC,SAA8B,QAAtB,EAACsH,GAAYnG,iBAAS,aAArB,EAAuBrB,OAASC,IAAgBC,SAA+B,QAAvB,EAACsH,GAAYjG,kBAAU,aAAtB,EAAwBvB,OAEtK,OAAQkG,GACN,yBAAK1C,UAAWoG,GAAY9F,eAAcyB,IAAaC,aACrD,kBAAC4E,EAAA,EAAY,CACXC,MAAM,iCACNlE,OAAQA,EACRmE,aAAcxB,GACdyB,aAAczB,GACd0B,cAAY,GAEZ,yBAAKhH,UAAU,qBACb,yBAAKA,UAAU,gBACb,yBAAKA,UAAU,yBACb,yBAAKA,UAAU,uBACb,kBAACiH,EAAA,EAAM,CACL3G,eAAa,oBACb4G,OAAK,EACLC,KAAK,UACL5G,SAAU,kBAAMoD,EAAenC,IAC/B4F,QAASjB,GACTkB,MAAK,UAAKlL,EAAE,wCAAuC,KACnDmL,QAAM,KAGTnB,IACC,4BAAQ7F,eAAa,YAAYN,UAAU,gBAAgBuH,QApF7C,WAAM,MAChChD,IAAKiD,YAAY,0CACjB,IAAMhL,EAAO2J,IAAwC,QAAtB,EAAAnC,GAAYjG,kBAAU,aAAtB,EAAwBvB,OAAQ,GAAMsH,EAAY,GAAG/F,WAAWvB,KAC/FoF,EAAS4D,IAAQC,sBAAsB,CAAEC,eAAe,EAAM+B,iBAAkB1E,EAAgB2E,YAAalL,KAC7GoF,EAAS4D,IAAQmC,mBAAmB5F,IAAaC,aAAa,MAiF/C7F,EAAE,6CAIRgK,GACC,kBAAC,EAAW,CACVjM,MAAO8J,GAAYW,uBACnBtK,cAAe4J,GACf1J,UAAWiJ,EAAgB,KAG7B,KAEF,yBAAKxD,UAAU,uBACb,kBAACiH,EAAA,EAAM,CACL3G,eAAa,oBACb4G,OAAK,EACL3G,SAAU,kBAAMoD,EAAenC,IAC/B2F,KAAK,UACLC,SAAUjB,GACVkB,MAAK,UAAKlL,EAAE,wCAAuC,KACnDmL,QAAM,MAGRnB,IACA,yBAAKnG,UAAU,gCACb,yBAAKA,UAAU,YACb,kBAACe,EAAA,EAAQ,CACPb,GAAG,wBACHe,YAAY,eACZC,MAAO+B,EAAuByB,IAAyBkD,KAAI,SAAC3C,GAAI,OAAKA,EAAK,MAC1E7D,oBAAqB0C,EAAY,GACjC3C,YAAa,SAAC0G,EAAO1P,GAAC,OAAK4L,EAAed,EAAuByB,IAAyBvM,SAMjGgO,GAAiB,KAChB,yBAAKnG,UAAU,WAGnB,yBAAKA,UAAU,uBACb,yBAAKA,UAAU,sBACb,yBAAKA,UAAU,kBAAkBE,GAAG,kCAAkC/D,EAAE,2BAA2B,KACnG,yBAAK6D,UAAU,YACb,kBAACe,EAAA,EAAQ,CACPb,GAAG,2BACHc,aAAa,iCACbC,YAAY,kBACZC,MAAOmC,IAAiBoB,IAAemD,KAAI,SAAC3C,GAAI,OAAKA,EAAK,MAC1D7D,oBAAqBoC,EAAgB,GACrCrC,YAAa,SAAC0G,EAAO1P,GAAC,OAAKsL,EAAmBJ,IAAiBoB,IAAetM,KAC9E2P,UAAW3L,EAAE,+BAInB,kBAAC2E,EAAA,EAAO,CAAC/I,QAASoE,EAAE,uDAClB,yBAAK6D,UAAU,8BACb,kBAACiH,EAAA,EAAM,CACLc,UAAQ,EACRC,WAAS,EACT9H,GAAG,+BACHmH,MAAOlL,EAAE,iDACTiL,QAAS/L,EACTkF,SA1JkB,WAChCjF,GAAyB,SAACD,GAAqB,OAAMA,MA0JvC4M,SAAUtB,SAMpB,yBAAK3G,UAAU,UACb,kBAACkI,EAAA,EAAkB,CACjB/H,KAAM,SACNoH,QA7HO,WA5CK,IAACY,EAAgBjO,EAAhBiO,EA8CnB,CAACrF,GA9CkC5I,EA+CnC,IAAIH,EAAM8L,KAAmBrC,EAAgB,IA9C/Ce,IAAK6D,cAAcD,EAAgBjO,GACnCoL,MAwKQtF,UAAU,eACViB,YAAY,cACZgH,SAAUjF,IAAqByD,KAAwBvC,IAEtD/H,EAAE,kBAEL,4BACEoL,QA9HO,WAxDW,IAACrN,EAAOmO,EAAPnO,EA0DzB,IAAIH,EAAM8L,KAAmBrC,EAAgB,IA1Db6E,EA0DgB,YAC5C3G,GAAW,CAAEC,IA1DnB4C,IAAK+D,oBAAoBpO,EAAOmO,GAChC/C,MAqLQtF,UAAU,eACVM,eAAa,cACb2H,UAAWxB,KAAyBrD,GAAwBgB,KAAuBpB,IAAqBkB,IAEvG/H,EAAE,sBChVAsF", "file": "chunks/chunk.76.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ScaleModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.ScaleModal{visibility:visible}.closed.ScaleModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ScaleModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.ScaleModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.ScaleModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.ScaleModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.ScaleModal .footer .modal-button.cancel:hover,.ScaleModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.ScaleModal .footer .modal-button.cancel,.ScaleModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.ScaleModal .footer .modal-button.cancel.disabled,.ScaleModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.ScaleModal .footer .modal-button.cancel.disabled span,.ScaleModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.ScaleModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.ScaleModal .modal-container .wrapper .modal-content{padding:10px}.ScaleModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.ScaleModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.ScaleModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.ScaleModal .footer .modal-button.confirm{margin-left:4px}.ScaleModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .footer .modal-button{padding:23px 8px}}.ScaleModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .swipe-indicator{width:32px}}.ScaleModal .modal-container{width:480px;overflow-y:visible}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container{width:100%;position:fixed;border-radius:0;left:0;bottom:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container{width:100%;position:fixed;border-radius:0;left:0;bottom:0}}.ScaleModal .modal-container .content-container{padding:24px 16px 16px}.ScaleModal .modal-container .content-container .ui__choice__label{font-weight:700;font-size:13px;line-height:16px;display:flex;align-items:center}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-option{padding:4px 0}.ScaleModal .modal-container .content-container .scaleSetting .custom-option-wrapper{display:flex;align-items:center;justify-content:space-between;margin-bottom:8px}.ScaleModal .modal-container .content-container .scaleSetting .custom-option-wrapper .calibrate-btn{font-weight:400;line-height:16px;display:flex;align-items:center;text-align:center;color:var(--blue-5);background-color:transparent;border:none;cursor:pointer}.ScaleModal .modal-container .content-container .scaleSetting .custom-option-wrapper .calibrate-btn:hover{color:var(--blue-6)}.ScaleModal .modal-container .content-container .scaleSetting .selector{margin-top:16px;width:100%;height:32px;margin-bottom:18px}.ScaleModal .modal-container .content-container .scaleSetting .selector .Dropdown__wrapper{width:100%;height:100%}.ScaleModal .modal-container .content-container .scaleSetting .selector .Dropdown__wrapper .Dropdown{height:100%;width:100%!important;text-align:left}.ScaleModal .modal-container .content-container .scaleSetting .selector .Dropdown__wrapper .Dropdown .arrow{flex:0 1 auto}.ScaleModal .modal-container .content-container .scaleSetting .selector .Dropdown__wrapper .Dropdown__items{width:100%}.ScaleModal .modal-container .content-container .scaleSetting .block{height:18px}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container{margin-top:8px;margin-bottom:8px}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display{display:flex;flex-direction:row;align-items:center;justify-content:space-between;grid-gap:8px;gap:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display .left-container,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display .left-container .input-wrapper>div,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display .right-container,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display .right-container .input-wrapper>div{flex:1}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display .left-container,.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display .left-container .input-wrapper>div,.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display .right-container,.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display .right-container .input-wrapper>div{flex:1}}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display .unit-label{padding-bottom:8px}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .scale-ratio-display .scale-ratio-equal{padding-top:24px;font-size:20px;width:28px;text-align:center}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper{display:flex;flex-direction:row}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .scale-input{border-radius:var(--border-radius);border:1px solid var(--border);width:94px;height:32px;margin-right:8px;padding-left:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .scale-input{width:79px;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .scale-input{width:79px;height:32px}}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .scale-input.invalid-value{border-color:var(--red)}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .scale-input:not(.invalid-value):focus{border:1px solid var(--blue-5)}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .unit-input{width:100px;height:32px}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .unit-input .Dropdown__wrapper{width:100%;height:100%}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .unit-input .Dropdown__wrapper .Dropdown{height:100%;text-align:left}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .unit-input .Dropdown__wrapper .Dropdown__items{width:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .unit-input{width:4rem;height:2rem}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper .unit-input{width:4rem;height:2rem}}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper input::-webkit-inner-spin-button,.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .input-wrapper input[type=number]{-moz-appearance:textfield}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .warning-alert{position:relative;margin-right:8px}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .warning-alert input{margin-right:0!important}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .warning-alert .Icon{text-align:center;position:absolute;top:50%;right:5px;margin:auto;transform:translateY(-50%);color:var(--red);display:block}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .warning-alert-icon{display:none}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .warning-messages{height:16px;font-weight:400;color:var(--red);margin-top:4px;position:relative;display:flex}.ScaleModal .modal-container .content-container .scaleSetting .custom-scale-container .warning-messages .world-value-warning{position:absolute;left:246px}.ScaleModal .modal-container .content-container .precision-container{display:flex;font-size:13px;font-weight:400;margin-top:16px;align-items:center;justify-content:space-between;border-top:1px solid var(--modal-stroke-and-border);padding-top:16px;grid-gap:16px;gap:16px}.ScaleModal .modal-container .content-container .precision-container .precision-selector{display:flex;justify-content:space-between;align-items:center;width:288px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .content-container .precision-container .precision-selector{flex:2}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .content-container .precision-container .precision-selector{flex:2}}.ScaleModal .modal-container .content-container .precision-container .precision-selector .precision-title{height:1rem;font-weight:400;font-size:13px;line-height:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .content-container .precision-container .precision-selector .precision-title{margin-right:8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .content-container .precision-container .precision-selector .precision-title{margin-right:8px}}.ScaleModal .modal-container .content-container .precision-container .precision-selector .selector{width:223px;height:32px;z-index:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .content-container .precision-container .precision-selector .selector{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .content-container .precision-container .precision-selector .selector{width:100%}}.ScaleModal .modal-container .content-container .precision-container .precision-selector .selector .Dropdown__wrapper{width:100%;height:100%}.ScaleModal .modal-container .content-container .precision-container .precision-selector .selector .Dropdown__wrapper .Dropdown{height:100%;width:100%!important;text-align:left}.ScaleModal .modal-container .content-container .precision-container .precision-selector .selector .Dropdown__wrapper .Dropdown .arrow{flex:0 1 auto}.ScaleModal .modal-container .content-container .precision-container .precision-selector .selector .Dropdown__wrapper .Dropdown__items{width:100%}.ScaleModal .modal-container .content-container .precision-container .ui__choice__label{font-weight:400}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .content-container .precision-container .fractional-units-container{flex:1}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .content-container .precision-container .fractional-units-container{flex:1}}.ScaleModal .modal-container .footer{display:flex;padding:16px;align-items:center;justify-content:flex-end;width:100%;box-shadow:inset 0 1px 0 var(--modal-stroke-and-border);margin:0}.ScaleModal .modal-container .footer .scale-update{height:32px;min-width:72px;display:flex;align-items:center;justify-content:center;color:var(--blue-5);background-color:transparent;border:1px solid var(--blue-5);box-sizing:border-box;border-radius:var(--border-radius);cursor:pointer;margin-right:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .footer .scale-update{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .footer .scale-update{font-size:13px}}.ScaleModal .modal-container .footer .scale-update:enabled:hover{border:1px solid var(--blue-6);color:var(--blue-6)}.ScaleModal .modal-container .footer .scale-update:disabled{opacity:.5;cursor:not-allowed}.ScaleModal .modal-container .footer .scale-create{border:none;background-color:transparent;background:var(--primary-button);border-radius:var(--border-radius);padding:0 8px;height:32px;min-width:72px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer}:host(:not([data-tabbing=true])) .ScaleModal .modal-container .footer .scale-create,html:not([data-tabbing=true]) .ScaleModal .modal-container .footer .scale-create{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleModal .modal-container .footer .scale-create{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleModal .modal-container .footer .scale-create{font-size:13px}}.ScaleModal .modal-container .footer .scale-create:enabled:hover{background:var(--primary-button-hover)}.ScaleModal .modal-container .footer .scale-create:disabled{opacity:.5;cursor:not-allowed}.ScaleModal .ui__choice__input__check.ui__choice__input__check--checked{border:3px solid var(--gray-0);outline:solid 1px var(--blue-5);background-color:var(--blue-5)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect, useRef } from 'react';\nimport PropTypes from 'prop-types';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport selectors from 'selectors';\nimport Tooltip from '../Tooltip';\nimport Dropdown from '../Dropdown';\nimport {\n  ifFractionalPrecision,\n  hintValues,\n  convertUnit,\n  fractionalUnits,\n  floatRegex,\n  inFractionalRegex,\n  ftInFractionalRegex,\n  ftInDecimalRegex,\n  parseFtInDecimal,\n  parseInFractional,\n  parseFtInFractional\n} from 'constants/measurementScale';\nimport classNames from 'classnames';\nimport Icon from 'components/Icon';\n\nconst Scale = window.Core.Scale;\n\nconst ScaleCustomProps = {\n  scale: PropTypes.array,\n  onScaleChange: PropTypes.func,\n  precision: PropTypes.number\n};\n\nfunction ScaleCustom({ scale, onScaleChange, precision }) {\n  const [measurementUnits] = useSelector((state) => [selectors.getMeasurementUnits(state)], shallowEqual);\n  const [pageValueDisplay, setPageValueDisplay] = useState('');\n  const [worldValueDisplay, setWorldValueDisplay] = useState('');\n  const [isFractionalPrecision, setIsFractionalPrecision] = useState(false);\n  const [pageWarningMessage, setPageWarningMessage] = useState('');\n  const [worldWarningMessage, setWorldWarningMessage] = useState('');\n  const [scaleValueBlurFlag, setScaleValueBlurFlag] = useState(false);\n\n  const [leftDropdownWidth, setLeftDropdownWidth] = useState(0);\n  const pageValueInput = useRef(null);\n  const worldValueInput = useRef(null);\n  const leftContainerRef = useRef(null);\n\n  const [t] = useTranslation();\n\n  const filterFractionalUnits = (units) => units.filter((unit) => fractionalUnits.includes(unit));\n  const unitFromOptions = isFractionalPrecision ? filterFractionalUnits(measurementUnits.from) : measurementUnits.from;\n  const unitToOptions = isFractionalPrecision ? filterFractionalUnits(measurementUnits.to) : measurementUnits.to;\n\n  // If our scale has a unit that is not in the current 'from' measurement units, change it\n  // to the first unit in the list.\n  useEffect(() => {\n    if (!unitFromOptions.includes(scale[0][1])) {\n      onScaleUnitChange(unitFromOptions[0], true);\n    }\n  }, [scale[0][1]]);\n\n  // If our scale has a unit that is not in the current 'to' measurement units, change it\n  // to the first unit in the list. We want to wait until the 'from' unit is valid before\n  // setting the 'to' unit. Otherwise, we will reset the 'from' unit.\n  useEffect(() => {\n    if (unitFromOptions.includes(scale[0][1]) && !unitToOptions.includes(scale[1][1])) {\n      onScaleUnitChange(unitToOptions[0], false);\n    }\n  }, [scale[0][1], scale[1][1]]);\n\n  useEffect(() => {\n    const formatDecimal = (value) => {\n      return value?.toFixed((1 / precision).toString().length - 1);\n    };\n\n    if (scale[0][0] && pageValueInput?.current !== document.activeElement) {\n      if (!isFractionalPrecision) {\n        setPageValueDisplay(formatDecimal(scale[0][0]) || '');\n      } else {\n        setPageValueDisplay(Scale.getFormattedValue(scale[0][0], scale[0][1], precision, false, true) || '');\n      }\n    }\n    if (scale[1][0] && !(worldValueInput && worldValueInput.current === document.activeElement)) {\n      if (!isFractionalPrecision && scale[1][1] !== 'ft-in') {\n        setWorldValueDisplay(formatDecimal(scale[1][0]) || '');\n      } else {\n        setWorldValueDisplay(Scale.getFormattedValue(scale[1][0], scale[1][1], precision, false, true) || '');\n      }\n    }\n  }, [scale, precision, worldValueInput, pageValueInput, isFractionalPrecision, scaleValueBlurFlag]);\n\n  useEffect(() => {\n    setIsFractionalPrecision(ifFractionalPrecision(precision));\n  }, [precision]);\n\n  useEffect(() => {\n    if (isFractionalPrecision) {\n      setPageWarningMessage(hintValues[scale[0][1]]);\n      setWorldWarningMessage(hintValues[scale[1][1]]);\n    } else if (scale[1][1] === 'ft-in') {\n      setPageWarningMessage('');\n      setWorldWarningMessage(hintValues['ft-in decimal']);\n    } else {\n      setPageWarningMessage('');\n      setWorldWarningMessage('');\n    }\n  }, [scale, isFractionalPrecision]);\n\n  // Re-validate invalid world value input when world unit changes\n  useEffect(() => {\n    !isWorldValueValid && onInputValueChange(worldValueInput.current.value, false);\n  }, [scale[1][1]]);\n\n  // Re-validate invalid scale value input when isFractionalPrecision value changes\n  useEffect(() => {\n    if (!isPageValueValid && !isWorldValueValid) {\n      let pageScale = {\n        value: scale[0][0],\n        unit: scale[0][1]\n      };\n      onInputValueChange(pageValueInput.current.value, true, (newScale) => {\n        pageScale = newScale.pageScale;\n      });\n      let worldScale = {\n        value: scale[1][0],\n        unit: scale[1][1]\n      };\n      onInputValueChange(worldValueInput.current.value, false, (newScale) => {\n        worldScale = newScale.worldScale;\n      });\n\n      _onScaleChange(new Scale({ pageScale, worldScale }));\n    } else {\n      !isPageValueValid && onInputValueChange(pageValueInput.current.value, true);\n      !isWorldValueValid && onInputValueChange(worldValueInput.current.value, false);\n    }\n  }, [isFractionalPrecision]);\n\n  useEffect(() => {\n    if (leftContainerRef.current) {\n      setLeftDropdownWidth((leftContainerRef.current.clientWidth - 8) / 2);\n    }\n  }, [leftContainerRef]);\n\n  const isPageValueValid = !!scale[0][0];\n  const isWorldValueValid = !!scale[1][0];\n\n  const pageValueClass = classNames('scale-input', {\n    'invalid-value': !isPageValueValid\n  });\n  const worldValueClass = classNames('scale-input', {\n    'invalid-value': !isWorldValueValid\n  });\n\n  // If scale value is smaller than the current precision, replace it with precision value to prevent 0 value.\n  const _onScaleChange = (newScale) => {\n    const getPrecision = (unit) => (unit === 'ft-in' ? precision / 12 : precision);\n\n    if (newScale.pageScale.value && newScale.pageScale.value < precision) {\n      newScale.pageScale.value = getPrecision(newScale.pageScale.unit);\n    }\n    if (newScale.worldScale.value && newScale.worldScale.value < precision) {\n      newScale.worldScale.value = getPrecision(newScale.worldScale.unit);\n    }\n    onScaleChange(newScale);\n  };\n\n  const onInputValueChange = (value, isPageValue, getNewScale) => {\n    const updateScaleValue = (scaleValue) => {\n      if ((isPageValue && scaleValue !== scale[0][0]) || (!isPageValue && scaleValue !== scale[1][0])) {\n        const newScale = new Scale({\n          pageScale: { value: isPageValue ? scaleValue : scale[0][0], unit: scale[0][1] },\n          worldScale: { value: !isPageValue ? scaleValue : scale[1][0], unit: scale[1][1] }\n        });\n        if (getNewScale) {\n          getNewScale(newScale);\n        } else {\n          _onScaleChange(newScale);\n        }\n      }\n    };\n\n    if (isPageValue) {\n      setPageValueDisplay(value);\n    } else {\n      setWorldValueDisplay(value);\n    }\n    const inputValue = value.trim();\n    if (!isFractionalPrecision) {\n      if (!isPageValue && scale[1][1] === 'ft-in') {\n        if (ftInDecimalRegex.test(inputValue)) {\n          const result = parseFtInDecimal(inputValue);\n          if (result > 0) {\n            updateScaleValue(result);\n            return;\n          }\n        }\n      } else if (floatRegex.test(inputValue)) {\n        const scaleValue = parseFloat(inputValue) || 0;\n        updateScaleValue(scaleValue);\n        return;\n      }\n    } else {\n      const scaleUnit = isPageValue ? scale[0][1] : scale[1][1];\n      if (scaleUnit === 'in') {\n        if (inFractionalRegex.test(inputValue)) {\n          const result = parseInFractional(inputValue);\n          if (result > 0) {\n            updateScaleValue(result);\n            return;\n          }\n        }\n      } else if (scaleUnit === 'ft-in') {\n        if (ftInFractionalRegex.test(inputValue)) {\n          const result = parseFtInFractional(inputValue);\n          if (result > 0) {\n            updateScaleValue(result);\n            return;\n          }\n        }\n      }\n    }\n    updateScaleValue(undefined);\n  };\n\n  const onScaleUnitChange = (newUnit, isPageUnit) => {\n    let newPageScale;\n    if (isPageUnit && newUnit !== scale[0][1]) {\n      newPageScale = {\n        value: scale[0][0] ? convertUnit(scale[0][0], scale[0][1], newUnit) : scale[0][0],\n        unit: newUnit\n      };\n    } else {\n      newPageScale = { value: scale[0][0], unit: scale[0][1] };\n    }\n    let newWorldScale;\n    if (!isPageUnit && newUnit !== scale[1][1]) {\n      newWorldScale = {\n        value: scale[1][0] ? convertUnit(scale[1][0], scale[1][1], newUnit) : scale[1][0],\n        unit: newUnit\n      };\n    } else {\n      newWorldScale = { value: scale[1][0], unit: scale[1][1] };\n    }\n\n    _onScaleChange(new Scale({ pageScale: newPageScale, worldScale: newWorldScale }));\n  };\n\n  const getInputPlaceholder = (isPageValue) => {\n    const unit = isPageValue ? scale[0][1] : scale[1][1];\n    return isFractionalPrecision ? hintValues[unit] : (unit === 'ft-in' ? hintValues['ft-in decimal'] : '');\n  };\n\n  const onInputBlur = () => {\n    setScaleValueBlurFlag((flag) => !flag);\n  };\n\n  return (\n    <div className=\"custom-scale-container\">\n      <div className=\"scale-ratio-input-container\">\n        <div className=\"scale-ratio-display\">\n          <div className=\"left-container\" ref={leftContainerRef}>\n            <div className=\"unit-label\" id=\"paper-units-dropdown-label\">{t('option.measurement.scaleModal.paperUnits')}</div>\n            <div className=\"input-wrapper\">\n              <div className={classNames({ 'warning-alert': !isPageValueValid })}>\n                <input\n                  type={isFractionalPrecision ? 'text' : 'number'}\n                  min=\"0\"\n                  className={pageValueClass}\n                  value={pageValueDisplay}\n                  aria-label={t('insertPageModal.pageDimensions.units')}\n                  data-element=\"customPageScaleValue\"\n                  onChange={(e) => onInputValueChange(e.target.value, true)}\n                  placeholder={getInputPlaceholder(true)}\n                  ref={pageValueInput}\n                  onBlur={onInputBlur}\n                />\n                <Icon glyph=\"icon-alert\" className=\"warning-alert-icon\" />\n              </div>\n              <Tooltip content={'option.measurement.scaleModal.paperUnits'}>\n                <div className=\"unit-input\">\n                  <Dropdown\n                    id=\"paper-units-dropdown\"\n                    labelledById='paper-units-dropdown-label'\n                    dataElement=\"customPageScaleUnit\"\n                    items={unitFromOptions}\n                    onClickItem={(value) => onScaleUnitChange(value, true)}\n                    currentSelectionKey={scale[0][1]}\n                    width={leftDropdownWidth}\n                  />\n                </div>\n              </Tooltip>\n            </div>\n          </div>\n          <div className=\"scale-ratio-equal\">{' = '}</div>\n          <div className=\"right-container\">\n            <div className=\"unit-label\" id=\"display-units-dropdown-label\">{t('option.measurement.scaleModal.displayUnits')}</div>\n            <div className=\"input-wrapper\">\n              <div className={classNames({ 'warning-alert': !isWorldValueValid })}>\n                <input\n                  type={(isFractionalPrecision || scale[1][1] === 'ft-in') ? 'text' : 'number'}\n                  min='0'\n                  className={worldValueClass}\n                  value={worldValueDisplay}\n                  aria-label={t('insertPageModal.pageDimensions.units')}\n                  data-element=\"customDisplayScaleValue\"\n                  onChange={(e) => onInputValueChange(e.target.value, false)}\n                  placeholder={getInputPlaceholder(false)}\n                  ref={worldValueInput}\n                  onBlur={onInputBlur}\n                />\n                <Icon glyph=\"icon-alert\" className=\"warning-alert-icon\" />\n              </div>\n              <Tooltip content={'option.measurement.scaleModal.displayUnits'}>\n                <div className=\"unit-input\">\n                  <Dropdown\n                    id=\"display-units-dropdown\"\n                    labelledById='display-units-dropdown-label'\n                    items={unitToOptions}\n                    dataElement=\"customDisplayScaleUnit\"\n                    onClickItem={(value) => onScaleUnitChange(value, false)}\n                    currentSelectionKey={scale[1][1]}\n                    width={leftDropdownWidth}\n                  />\n                </div>\n              </Tooltip>\n            </div>\n          </div>\n\n        </div>\n      </div>\n      <div className=\"warning-messages\" aria-live=\"assertive\" >\n        {!isPageValueValid && (\n          <p className=\"no-margin\">\n            {`${t('option.measurement.scaleModal.incorrectSyntax')} ${pageWarningMessage}`}\n          </p>\n        )}\n        {!isWorldValueValid && (\n          <p className=\"world-value-warning no-margin\">\n            {`${t('option.measurement.scaleModal.incorrectSyntax')} ${worldWarningMessage}`}\n          </p>\n        )}\n      </div>\n    </div>\n  );\n}\n\nScaleCustom.propTypes = ScaleCustomProps;\n\nexport default ScaleCustom;\n", "import React, { useEffect, useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport classNames from 'classnames';\nimport {\n  precisionOptions,\n  PrecisionType,\n  scalePresetPrecision,\n  PresetMeasurementSystems,\n  fractionalUnits,\n  ifFractionalPrecision,\n  initialScale\n} from 'constants/measurementScale';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport ScaleCustom from './ScaleCustom';\nimport DataElements from 'constants/dataElement';\nimport useDidUpdate from 'hooks/useDidUpdate';\nimport Dropdown from 'components/Dropdown';\nimport Tooltip from 'components/Tooltip';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport ModalWrapper from 'components/ModalWrapper';\n\nimport './ScaleModal.scss';\nimport '../Choice/Choice.scss';\n\nconst Scale = window.Core.Scale;\n\nexport const scaleOptions = {\n  CUSTOM: 'custom',\n  PRESET: 'preset'\n};\n\nconst ScaleModal = ({ annotations, selectedTool }) => {\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const [\n    isDisabled,\n    isOpen,\n    isHidden,\n    currentToolbarGroup,\n    selectedScale,\n    activeToolName,\n    isAddingNewScale,\n    measurementScalePreset,\n    { tempScale, isFractionalUnit },\n    isMultipleScalesMode\n  ] = useSelector((state) => [\n    selectors.isElementDisabled(state, DataElements.SCALE_MODAL),\n    selectors.isElementOpen(state, DataElements.SCALE_MODAL),\n    selectors.isElementHidden(state, DataElements.SCALE_MODAL),\n    selectors.getCurrentToolbarGroup(state),\n    selectors.getSelectedScale(state),\n    selectors.getActiveToolName(state),\n    selectors.getIsAddingNewScale(state),\n    selectors.getMeasurementScalePreset(state),\n    selectors.getCalibrationInfo(state),\n    selectors.getIsMultipleScalesMode(state)\n  ]);\n\n  const [isFractionalPrecision, setIsFractionalPrecision] = useState(false);\n  const [precisionOption, setPrecisionOption] = useState(precisionOptions[PrecisionType.DECIMAL][0]);\n  const [scaleOption, setScaleOption] = useState(scaleOptions.CUSTOM);\n  const [presetScale, setPresetScale] = useState(measurementScalePreset[PresetMeasurementSystems.METRIC][0]);\n  const [customScale, setCustomScale] = useState(new Scale(''));\n  const [hasScaleChanged, setHasScaleChanged] = useState(false);\n\n  const totalScalesCount = Object.keys(core.getScales()).length;\n\n  useEffect(() => {\n    if (!precisionOptions[precisionType].includes(precisionOption)) {\n      setPrecisionOption(precisionOptions[precisionType][0]);\n    }\n    setPresetScale(measurementScalePreset[presetMeasurementSystem][0]);\n  }, [isFractionalPrecision]);\n\n  useEffect(() => {\n    setScaleOption(scaleOptions.CUSTOM);\n    setCustomScale(new Scale(selectedScale.getScaleRatioAsArray()));\n\n    const precision = core.getScalePrecision(selectedScale);\n    if (!precision) {\n      return;\n    }\n    const isFractional = ifFractionalPrecision(precision);\n    setIsFractionalPrecision(isFractional);\n\n    const precisionItems = precisionOptions[getPrecisionType(isFractional)];\n    const precisionItem = precisionItems.find((item) => item[1] === precision);\n    setPrecisionOption(precisionItem);\n\n    // Update/Create button should be disabled until the user makes a change\n    setTimeout(() => {\n      setHasScaleChanged(false);\n    });\n  }, [selectedScale]);\n\n  useDidUpdate(() => {\n    if (scaleOption === scaleOptions.CUSTOM) {\n      setCustomScale(presetScale[1]);\n    } else {\n      const presetPrecisionOption = scalePresetPrecision[presetScale[0]];\n      if (presetPrecisionOption && presetPrecisionOption !== precisionOption) {\n        setPrecisionOption(presetPrecisionOption);\n      }\n    }\n  }, [scaleOption]);\n\n  useDidUpdate(() => {\n    if (currentToolbarGroup === 'toolbarGroup-Measure') {\n      closeModal();\n    }\n  }, [currentToolbarGroup]);\n\n  useEffect(() => {\n    const newPrecisionOption = scalePresetPrecision[presetScale[0]];\n    if (newPrecisionOption && scaleOption === scaleOptions.PRESET) {\n      setPrecisionOption(newPrecisionOption);\n    }\n  }, [presetScale]);\n\n  useEffect(() => {\n    if (!isHidden && tempScale) {\n      // Triggered when calibration is applied\n      setCustomScale(new Scale(tempScale));\n      dispatch(actions.updateCalibrationInfo({ isCalibration: false }));\n      setIsFractionalPrecision(isFractionalUnit);\n    }\n  }, [isHidden]);\n\n  useEffect(() => {\n    setHasScaleChanged(true);\n  }, [precisionOption, customScale, presetScale, scaleOption]);\n\n  useEffect(() => {\n    // Reset component state when adding new scale\n    if (isOpen && isAddingNewScale && !tempScale) {\n      setScaleOption(scaleOptions.CUSTOM);\n      setCustomScale(initialScale);\n      setIsFractionalPrecision(false);\n      setPrecisionOption(precisionOptions[PrecisionType.DECIMAL][0]);\n    }\n  }, [isOpen, isAddingNewScale]);\n\n  const closeModal = () => {\n    dispatch(actions.closeElement(DataElements.SCALE_MODAL));\n  };\n\n  const createAndApplyScale = (scale, applyTo) => {\n    core.createAndApplyScale(scale, applyTo);\n    closeModal();\n  };\n\n  const replaceScales = (originalScales, scale) => {\n    core.replaceScales(originalScales, scale);\n    closeModal();\n  };\n\n  const toggleFractionalPrecision = () => {\n    setIsFractionalPrecision((isFractionalPrecision) => !isFractionalPrecision);\n  };\n\n  const openCalibrationTool = () => {\n    core.setToolMode('AnnotationCreateCalibrationMeasurement');\n    const unit = isCustomOption ? (customScale.worldScale?.unit || '') : presetScale[1].worldScale.unit;\n    dispatch(actions.updateCalibrationInfo({ isCalibration: true, previousToolName: activeToolName, defaultUnit: unit }));\n    dispatch(actions.setIsElementHidden(DataElements.SCALE_MODAL, true));\n  };\n\n  const getCurrentScale = () => {\n    const getPrecisionsValue = (value, unit) => {\n      let temp = value;\n      let precisionValue = precisionOption[1];\n\n      if (!isFractionalPrecision) {\n        if (unit !== 'ft-in') {\n          temp = temp.toFixed((1 / precisionValue).toString().length - 1);\n        }\n      } else {\n        if (unit === 'ft-in') {\n          precisionValue /= 12;\n        }\n        temp = Math.round(temp / precisionValue) * precisionValue;\n      }\n\n      return temp * 1;\n    };\n\n    if (isCustomOption) {\n      const scale = customScale.getScaleRatioAsArray();\n      scale[0][0] = getPrecisionsValue(scale[0][0], scale[0][1]);\n      scale[1][0] = getPrecisionsValue(scale[1][0], scale[1][1]);\n      return scale;\n    }\n    return presetScale[1].toString();\n  };\n\n  const onUpdate = () => {\n    replaceScales(\n      [selectedScale],\n      new Scale(getCurrentScale(), precisionOption[1])\n    );\n  };\n\n  const onCreate = () => {\n    createAndApplyScale(\n      new Scale(getCurrentScale(), precisionOption[1]),\n      [...annotations, selectedTool]\n    );\n  };\n\n  const modalClass = classNames('Modal', 'ScaleModal', {\n    open: !isHidden,\n    closed: isHidden\n  });\n  const isCustomOption = scaleOption === scaleOptions.CUSTOM;\n  const presetMeasurementSystem = isFractionalPrecision ? PresetMeasurementSystems.IMPERIAL : PresetMeasurementSystems.METRIC;\n  const getPrecisionType = (isFractional) => (isFractional ? PrecisionType.FRACTIONAL : PrecisionType.DECIMAL);\n  const precisionType = getPrecisionType(isFractionalPrecision);\n  const isCurrentScaleValid = !isCustomOption || customScale.isValid();\n  const isFractionalUnitsToggleDisabled = isCustomOption && !(fractionalUnits.includes(customScale.pageScale?.unit) && fractionalUnits.includes(customScale.worldScale?.unit));\n\n  return !isDisabled && (\n    <div className={modalClass} data-element={DataElements.SCALE_MODAL}>\n      <ModalWrapper\n        title=\"option.measurementOption.scale\"\n        isOpen={isOpen}\n        onCloseClick={closeModal}\n        closeHandler={closeModal}\n        swipeToClose\n      >\n        <div className=\"content-container\">\n          <div className=\"scaleSetting\">\n            <div className=\"custom-option-wrapper\">\n              <div className=\"custom-scale-option\">\n                <Choice\n                  data-element=\"customScaleOption\"\n                  radio\n                  name=\"setting\"\n                  onChange={() => setScaleOption(scaleOptions.CUSTOM)}\n                  checked={isCustomOption}\n                  label={`${t('option.measurement.scaleModal.custom')}:`}\n                  center\n                />\n              </div>\n              {isCustomOption && (\n                <button data-element=\"calibrate\" className=\"calibrate-btn\" onClick={openCalibrationTool}>\n                  {t('option.measurement.scaleModal.calibrate')}\n                </button>\n              )}\n            </div>\n            {isCustomOption ? (\n              <ScaleCustom\n                scale={customScale.getScaleRatioAsArray()}\n                onScaleChange={setCustomScale}\n                precision={precisionOption[1]}\n              />\n            ) : (\n              null\n            )}\n            <div className=\"custom-scale-option\">\n              <Choice\n                data-element=\"presetScaleOption\"\n                radio\n                onChange={() => setScaleOption(scaleOptions.PRESET)}\n                name=\"setting\"\n                checked={!isCustomOption}\n                label={`${t('option.measurement.scaleModal.preset')}:`}\n                center\n              />\n            </div>\n            {!isCustomOption && (\n              <div className=\"scaleModal__preset-container\">\n                <div className=\"selector\">\n                  <Dropdown\n                    id=\"preset-scale-dropdown\"\n                    dataElement=\"presetScales\"\n                    items={measurementScalePreset[presetMeasurementSystem].map((item) => item[0])}\n                    currentSelectionKey={presetScale[0]}\n                    onClickItem={(_item, i) => setPresetScale(measurementScalePreset[presetMeasurementSystem][i])}\n                  />\n                </div>\n              </div>\n            )}\n\n            {isCustomOption ? null : (\n              <div className=\"block\" />\n            )}\n          </div>\n          <div className=\"precision-container\">\n            <div className=\"precision-selector\">\n              <div className=\"precision-title\" id=\"scale-precision-dropdown-label\">{t('option.shared.precision')}:</div>\n              <div className=\"selector\">\n                <Dropdown\n                  id=\"scale-precision-dropdown\"\n                  labelledById='scale-precision-dropdown-label'\n                  dataElement=\"scalePrecisions\"\n                  items={precisionOptions[precisionType].map((item) => item[0])}\n                  currentSelectionKey={precisionOption[0]}\n                  onClickItem={(_item, i) => setPrecisionOption(precisionOptions[precisionType][i])}\n                  ariaLabel={t('option.shared.precision')}\n                />\n              </div>\n            </div>\n            <Tooltip content={t('option.measurement.scaleModal.fractionUnitsTooltip')}>\n              <div className=\"fractional-units-container\">\n                <Choice\n                  isSwitch\n                  leftLabel\n                  id=\"scale-modal-fractional-units\"\n                  label={t('option.measurement.scaleModal.fractionalUnits')}\n                  checked={isFractionalPrecision}\n                  onChange={toggleFractionalPrecision}\n                  disabled={isFractionalUnitsToggleDisabled}\n                />\n              </div>\n            </Tooltip>\n          </div>\n        </div>\n        <div className=\"footer\">\n          <DataElementWrapper\n            type={'button'}\n            onClick={onUpdate}\n            className=\"scale-update\"\n            dataElement=\"updateScale\"\n            disabled={isAddingNewScale || !isCurrentScaleValid || !hasScaleChanged}\n          >\n            {t('action.update')}\n          </DataElementWrapper>\n          <button\n            onClick={onCreate}\n            className=\"scale-create\"\n            data-element=\"createScale\"\n            disabled={!isCurrentScaleValid || (!isMultipleScalesMode && totalScalesCount) || (!isAddingNewScale && !hasScaleChanged)}\n          >\n            {t('action.create')}\n          </button>\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default ScaleModal;\n", "import ScaleModal from './ScaleModal';\n\nexport default ScaleModal;"], "sourceRoot": ""}