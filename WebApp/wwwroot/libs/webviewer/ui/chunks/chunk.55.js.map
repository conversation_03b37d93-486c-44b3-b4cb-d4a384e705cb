{"version": 3, "sources": ["webpack:///./src/ui/src/components/PrintModal/PrintModal.scss?ad31", "webpack:///./src/ui/src/components/PrintModal/WatermarkModal/WatermarkModal.scss?4a7e", "webpack:///./src/ui/src/components/PrintModal/WatermarkModal/WatermarkModal.scss", "webpack:///./src/ui/src/components/PrintModal/PrintModal.scss", "webpack:///./src/ui/src/components/PrintModal/WatermarkModal/web-fonts.js", "webpack:///./src/ui/src/components/PrintModal/WatermarkModal/WatermarkModal.js", "webpack:///./src/ui/src/components/PrintModal/WatermarkModal/index.js", "webpack:///./src/ui/src/components/PrintModal/PrintModal.js", "webpack:///./src/ui/src/components/PrintModal/PrintModalContainer.js", "webpack:///./src/ui/src/components/PrintModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "FONTS", "WATERMARK_LOCATIONS", "CENTER", "TOP_LEFT", "TOP_RIGHT", "TOP_CENTER", "BOT_LEFT", "BOT_RIGHT", "BOT_CENTER", "FORM_FIELD_KEYS", "DEFAULT_VALS", "Core", "Annotations", "Color", "WATERMARK_API_LOCATIONS", "WatermarkModal", "props", "isVisible", "setState", "locationSettings", "state", "previousLocationSettings", "core", "getWatermark", "preExistingWatermark", "addWatermarks", "removeWatermarkCreatedByModal", "setWatermark", "watermarkOptions", "createWatermarks", "t", "pageHeight", "getPageHeight", "pageIndexToView", "desiredZoomForWidth", "getPageWidth", "desiredZoomForHeight", "desiredZoom", "Math", "min", "pageNumber", "getDocument", "loadCanvas", "zoom", "drawComplete", "canvas", "nodes", "canvasContainerRef", "current", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "style", "border", "height", "backgroundColor", "setAttribute", "value", "fontStyles", "isBolded", "getFontStyles", "BOLD", "isItalic", "ITALIC", "isUnderlined", "UNDERLINE", "fontSize", "fontFamily", "font", "color", "toString", "opacity", "text", "watermarks", "Object", "keys", "key", "temp", "constructWatermarkOption", "modalClosed", "currLocationSettings", "currSelectedLocation", "getCurrentSelectedLocation", "event", "preventDefault", "resetLocationSettings", "formSubmitted", "visible", "isColorPaletteVisible", "locationKey", "locationSetting", "isSelected", "location", "watermarkLocations", "watermarkStrVal", "tempWatermarkProps", "colorArray", "slice", "replace", "split", "includes", "trim", "object", "find", "newColor", "currLocation", "currLocationSetting", "R", "G", "B", "initializeLocationSettings", "undefined", "lockFocus", "React", "createRef", "prevProps", "addEventListener", "this", "closeModal", "handleWatermarkOnVisibilityChanged", "isCustomizableUI", "formInfo", "hexColor", "toHexString", "dropdownHalfWidth", "isMobile", "dropdownFullWidth", "DataElementWrapper", "className", "id", "data-element", "ModalWrapper", "isOpen", "title", "closeButtonDataElement", "onCloseClick", "swipeToClose", "<PERSON><PERSON><PERSON><PERSON>", "ref", "onSubmit", "e", "htmlFor", "Dropdown", "labelledById", "dataElement", "items", "getTranslationLabel", "currentSelectionKey", "onClickItem", "onLocationChanged", "width", "onChange", "handleInputChange", "target", "type", "FontSizeDropdown", "fontUnit", "onFontSizeChange", "val", "Number", "parseInt", "maxFontSize", "initialFontValue", "initialMaxFontValue", "Slide<PERSON>", "property", "displayProperty", "max", "step", "getDisplayValue", "round", "withInputField", "inputFieldType", "onSliderChange", "onStyleChange", "getLocalValue", "<PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "onClick", "setColorPaletteVisibility", "img", "isActive", "ColorPalette", "onColorChanged", "resetForm", "onOkPressed", "PureComponent", "PropTypes", "bool", "number", "func", "isRequired", "withTranslation", "PrintModal", "isDisabled", "isApplyWatermarkDisabled", "isFullAPIEnabled", "currentPage", "printQuality", "isGrayscale", "setIsGrayscale", "setIsCurrentView", "isCurrentViewDisabled", "includeAnnotations", "setIncludeAnnotations", "includeComments", "setIncludeComments", "isWatermarkModalVisible", "setIsWatermarkModalVisible", "watermarkModalOptions", "existingWatermarksRef", "setAllowWatermarkModal", "closePrintModal", "createPagesAndPrint", "pagesToPrint", "setPagesToPrint", "count", "isPrinting", "layoutMode", "useEmbeddedPrint", "pageLabe<PERSON>", "propTypes", "array", "string", "dispatch", "useDispatch", "useTranslation", "allPages", "useRef", "currentPageRef", "customPages", "includeCommentsRef", "current<PERSON>iew", "useState", "embedPrintValid", "setEmbedPrintValid", "specifiedPages", "setSpecifiedPages", "pageNumberError", "setPageNumberError", "isCustomPagesChecked", "setIsCustomPagesChecked", "customizableUI", "useSelector", "selectors", "getFeatureFlags", "printQualityOptions", "1", "2", "setWatermarkModalVisibility", "getClassName", "customPagesLabelElement", "classNames", "error", "PageNumberInput", "selectedPageNumbers", "pageCount", "getTotalPages", "onSelectedPageNumbersChange", "pageNumbers", "onBlurHandler", "onError", "customPageLabels", "enablePageLabels", "useEffect", "checked", "LayoutMode", "FacingCover", "FacingCoverContinuous", "FacingContinuous", "Facing", "then", "watermark", "getType", "openWaterMarkModalWithFocusTransfer", "useFocusHandler", "actions", "setWatermarkModalOptions", "DataElements", "PRINT_MODAL", "containerOnClick", "stopPropagation", "Choice", "name", "radio", "label", "defaultChecked", "disabled", "center", "prevState", "PRINT_QUALITY", "item", "quality", "setPrintQuality", "PRINT_WATERMARK", "PrintModalContainer", "isElementDisabled", "isElementOpen", "getCurrentPage", "getPrintQuality", "getDefaultPrintOptions", "getPageLabels", "getSortStrategy", "getColorMap", "getDisplayMode", "getPrintedNoteDateFormat", "getCurrentLanguage", "getWatermarkModalOptions", "getTimezone", "isEmbedPrintSupported", "shallowEqual", "defaultPrintOptions", "sortStrategy", "colorMap", "printedNoteDateFormat", "language", "timezone", "allowWatermarkModal", "setCount", "maintainPageOrientation", "setMaintainPageOrientation", "is<PERSON><PERSON>rent<PERSON>iew", "setIsCurrentViewDisabled", "checkCurrentView", "closeElements", "SIGNATURE_MODAL", "LOADING_MODAL", "PROGRESS_MODAL", "ERROR_MODAL", "visiblePagesArray", "getDisplayModeObject", "getVisiblePages", "embeddedPrinting", "printingOptions", "annotManager", "getAnnotationManager", "printEmbeddedPDF", "processEmbeddedPrintOptions", "rasterPrinting", "printOptions", "createCanvases", "createPages", "createRasterizedPrintPages", "pagePromise", "Promise", "all", "pages", "printPages", "closePrintModalAfterPrint", "console", "closeElement", "useFocusOnClose", "isFullPDFEnabled", "fileType", "warn"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,qBClEnC,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,s2fAAu2f,KAGh4f0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,sBCVvBD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,mwRAAowR,KAG7xR0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,sfCTVC,EAAQ,CACnB,QACA,kBACA,UACA,eACA,UACA,UACA,UACA,WACA,gBACA,cACA,U,g/BCXF,8lGAAA5B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,0lBAAAA,EAAA,EAAAA,EAAA,SAAAA,IAAA,SAAAA,GAAA,ugDAgBA,IASM6B,EAAsB,CAC1BC,OAAQ,SACRC,SAAU,UACVC,UAAW,WACXC,WAAY,YACZC,SAAU,aACVC,UAAW,cACXC,WAAY,gBAGRC,EAEM,WAFNA,EAGE,OAHFA,EAIG,QAJHA,EAKK,UALLA,EAME,OANFA,EAOM,WAPNA,EAQM,WARNA,EASU,eAGVC,IAAY,OAXN,WAYkBT,EAAoBC,QAAM,IACrDO,EA9BuB,IA8BqB,IAC5CA,EAAuB,IAAE,IAEzBA,EAAwB,IAAIlC,OAAOoC,KAAKC,YAAYC,MAAM,IAAK,GAAI,KAAG,IACtEJ,EAA0B,KAAG,IAC7BA,EAAuBT,EAAM,IAAE,IAC/BS,GAA2B,GAAK,IAChCA,GAA2B,GAAK,IAChCA,GAA+B,GAAK,GAIjCK,IAAuB,OAC1Bb,EAAoBC,OAAS,YAAU,IACvCD,EAAoBE,SAAW,cAAY,IAC3CF,EAAoBG,UAAY,eAAa,IAC7CH,EAAoBI,WAAa,gBAAc,IAC/CJ,EAAoBK,SAAW,cAAY,IAC3CL,EAAoBM,UAAY,eAAa,IAC7CN,EAAoBO,WAAa,gBAAc,GAG5CO,GAAc,aAvEpB,qRAuEoB,UAvEpB,MAuEoB,OAWlB,WAAYC,GAAO,OAlFrB,4FAkFqB,SACJ,IAAb,cAAMA,IAAO,sCAiCsB,WAC/B,EAAKA,MAAMC,UACb,EAAKC,SACH,CACEC,iBAAkB,EAAKC,MAAMC,0BAC9B,YACD,8FAEoCC,IAAKC,eAAc,OAArD,EAAKC,qBAAuB,EAAH,KACzB,EAAKC,gBAAgB,6CAIzB,EAAKC,gCACLJ,IAAKK,aAAa,EAAKH,0BAE1B,wBAEe,WACd,IAAMI,EAAmB,EAAKC,mBACtBC,EAAM,EAAKd,MAAXc,EACRR,IAAKK,aAAaC,GAElB,IAAMG,EAAaT,IAAKU,cAAc,EAAKhB,MAAMiB,gBAAkB,GAG7DC,EA9HY,IA4HAZ,IAAKa,aAAa,EAAKnB,MAAMiB,gBAAkB,GAG3DG,EA9Ha,IA8H2BL,EAExCM,EAAcC,KAAKC,IAAIH,EAAsBF,GAC7CM,EAAa,EAAKxB,MAAMiB,gBAAkB,EAEhDX,IAAKmB,cAAcC,WAAW,CAC5BF,WAAYA,EACZG,KAAMN,EACNO,aAAc,SAACC,GACb,IAAMC,EAAQ,EAAKC,mBAAmBC,QAAQC,WAC1CH,GAASA,EAAMhE,OAAS,GAC1B,EAAKiE,mBAAmBC,QAAQE,YAAYJ,EAAM,IAEpDD,EAAOM,MAAMC,OAAS,EAAKL,mBAAmBC,QAAQG,MAAMC,OAC5DP,EAAOM,MAAME,OAAS,EAAKN,mBAAmBC,QAAQG,MAAME,OAC5DR,EAAOM,MAAMG,gBAAkB,EAAKP,mBAAmBC,QAAQG,MAAMG,gBACrET,EAAOU,aAAa,OAAQ,OAC5BV,EAAOU,aAAa,aAAc,GAAF,OAAKzB,EAAE,eAAc,YAAIU,IACzD,EAAKO,mBAAmBC,QAAQrE,YAAYkE,SAKjD,mCAG0B,SAACW,GAC1B,IAAMC,EAAa,GAmBnB,OAlBID,EAAME,UACRD,EAAWnE,KAAKgC,IAAKqC,gBAAgBC,MAEnCJ,EAAMK,UACRJ,EAAWnE,KAAKgC,IAAKqC,gBAAgBG,QAEnCN,EAAMO,cACRN,EAAWnE,KAAKgC,IAAKqC,gBAAgBK,WAEf,CACtBC,SAAUT,EAAMS,SAChBC,WAAYV,EAAMW,KAClBC,MAAOZ,EAAMY,MAAMC,WACnBC,QAASd,EAAMc,QACfC,KAAMf,EAAMe,KACZd,iBAIH,2BAEkB,WACjB,IAAMe,EAAa,GASnB,OAPAC,OAAOC,KAAKzE,GAAqBb,SAAQ,SAACuF,GACxC,IAAMC,EAAO,EAAKC,yBAChB,EAAKzD,MAAMD,iBAAiBwD,IAG9BH,EAAW1D,GADGb,EAAoB0E,KACWC,KAExCJ,KACR,wCAG+B,WAC9BlD,IAAKK,aAAa,OACnB,qBAEY,WACX,EAAKX,MAAM8D,iBACZ,4BAEmB,SAACH,EAAKnB,GACxB,IAAMuB,EAAuB,EAAH,GACrB,EAAK3D,MAAMD,kBAEV6D,EAAuB,EAAKC,6BAClCF,EAAqBC,GAAwB,EAAH,KACrCD,EAAqBC,IAAqB,QAC5CL,EAAMnB,IAGT,EAAKtC,SACH,CACEC,iBAAkB4D,IAEpB,WACE,EAAKtD,sBAGV,oBAEW,SAACyD,GACXA,EAAMC,iBACN,IAAMhE,EAAmB,EAAKiE,wBAC9B,EAAKlE,SACH,CACEC,qBAEF,kBAAM,EAAKM,sBAEd,sBAEa,WACZ,EAAKP,SACH,CACEG,yBAA0B,EAAKD,MAAMD,mBAEvC,WAEE,EAAKH,MAAM8D,cACX,IAAMlD,EAAmB,EAAKC,mBAC9B,EAAKb,MAAMqE,cAAczD,SAG9B,oCAE2B,SAAC0D,GAC3B,EAAKpE,SAAS,CAAEqE,sBAAuBD,OACxC,4BAEmB,SAACX,GACnB,IAAMI,EAAuB,EAAH,GACrB,EAAK3D,MAAMD,kBAEhBsD,OAAOC,KAAKK,GAAsB3F,SAAQ,SAACoG,GACzC,IAAIC,EAAkBV,EAAqBS,GAC3CC,EAAkB,EAAH,KACVA,GAAe,IAClBC,WAAYf,IAAQa,IAEtBT,EAAqBS,GAAeC,KAGtC,EAAKvE,SACH,CACEC,iBAAkB4D,IAEpB,WACE,EAAKtD,sBAGV,gCAGuB,WACtB,IAAMN,EAAmB,GAWzB,OAVAsD,OAAOC,KAAKzE,GAAqBb,SAAQ,SAACuF,GAExC,IACMC,EAAO,EAAH,KADO,kB,oEAAA,CAAKlE,UAEX,IACTgF,WAAYzF,EAAoB0E,KAASjE,GAAaiF,WAExDxE,EAAiBwD,GAAOC,KAGnBzD,KACR,qCAG4B,WAC3B,IAAMA,EAAmB,EAAKiE,wBA2C9B,OAzCI,EAAKpE,MAAM4E,oBACbnB,OAAOC,KAAKzE,GAAqBb,SAAQ,SAACuF,GAAQ,QAC1CkB,EAAkB5F,EAAoB0E,GACtCmB,EAA4F,QAA1E,EAAG,EAAK9E,MAAM4E,mBAAmB9E,GAAwB+E,WAAiB,SAClG,GAAKC,EAAL,CAIA,IAAMlB,EAAO,EAAKC,yBAChBiB,GAGF3E,EAAiBwD,GAAKJ,KAAOK,EAAKL,KAElC,IACMwB,EADWD,EAAmB1B,MACR4B,MAAM,GAAGC,QAAQ,IAAK,IAAIC,MAAM,KACtD9B,EAAQ,IAAI7F,OAAOoC,KAAKC,YAAYC,MACxCkF,EAAW,GACXA,EAAW,GACXA,EAAW,GACXA,EAAW,IAGb5E,EAAiBwD,GAAKP,MAAQA,EAC9BjD,EAAiBwD,GAAKL,QAAUM,EAAKN,QACrCnD,EAAiBwD,GAAKV,SAAWW,EAAKX,SAElC6B,EAAmBrC,aACrBtC,EAAiBwD,GAAKjB,SAAWoC,EAA+B,WAAEK,SAAS,QAC3EhF,EAAiBwD,GAAKd,SAAWiC,EAA+B,WAAEK,SAAS,UAC3EhF,EAAiBwD,GAAKZ,aAAe+B,EAA+B,WAAEK,SAAS,cAGjF,IAAMjC,EAA0C,QAAhC,EAAG4B,EAAmB5B,kBAAU,SAE3CA,GAA2C,IAA7BA,EAAWkC,OAAOtH,SACnCqC,EAAiBwD,GAAOjE,GAAayD,UAKpChD,KACR,wBAGe,SAACkF,EAAQ7C,GAAK,OAAKiB,OAAOC,KAAK2B,GAAQC,MAAK,SAAC3B,GAAG,OAAK0B,EAAO1B,KAASnB,QAAM,qCAE9D,kBAAMiB,OAAOC,KAAK,EAAKtD,MAAMD,kBAAkBmF,MAAK,SAACd,GAEhF,OADwB,EAAKpE,MAAMD,iBAAiBqE,GAC7BE,iBACvB,yBAEe,SAACa,GAChB,IAAMC,EAAe,EAAKvB,6BACpBwB,EAAsB,EAAKrF,MAAMD,iBAAiBqF,GACxDC,EAAoBhG,GAAyB,IAAIlC,OAAOoC,KAAKC,YAAYC,MACvE0F,EAASG,EACTH,EAASI,EACTJ,EAASK,GAEX,IAAMzF,EAAmB,EAAH,GACjB,EAAKC,MAAMD,kBAEXsF,EAAoBhG,IAEvBgE,OAAOC,KAAKzE,GAAqBb,SAAQ,SAACuG,GACxC,IAAMF,EAAkBtE,EAAiBwE,GACpCF,EAAgBhF,KACnBgF,EAAgBhF,GAAyB,IAAIlC,OAAOoC,KAAKC,YAAYC,MACnE0F,EAASG,EACTH,EAASI,EACTJ,EAASK,OAKjB,EAAK1F,SACH,CACEC,qBAEF,WACE,EAAKM,sBA/ST,IAAMN,EAAmB,EAAK0F,6BAQc,OAP5C,EAAKrF,0BAAuBsF,EAC5B,EAAK1F,MAAQ,CACXmE,uBAAuB,EACvBpE,mBACAE,yBAA0BF,EAC1B4F,WAAW,GAEb,EAAKhE,mBAAqBiE,IAAMC,YAAY,EA2f7C,OAvlBH,EA6FG,GA7FH,EA6FG,iCAED,SAAmBC,GAAW,WAC5B5F,IAAK6F,iBAAiB,iBAAkBC,KAAKC,YAEzCD,KAAKpG,MAAMC,YAAciG,EAAUjG,YAGjCmG,KAAKpG,MAAMC,UACbmG,KAAKlG,SAAS,CAAE6F,WAAW,IAE3BK,KAAKlG,SAAS,CAAE6F,WAAW,IAG7BK,KAAKlG,SACH,CACEqE,uBAAuB,IAEzB,kBAAM,EAAK+B,2CAGhB,oBAsRD,WAAS,WAEP,IADsBF,KAAKpG,MAAnBC,UAEN,OAAO,KAGT,MAAgCmG,KAAKpG,MAA7Bc,EAAC,EAADA,EAAGyF,EAAgB,EAAhBA,iBAELf,EAAeY,KAAKnC,6BACpBuC,EAAWJ,KAAKhG,MAAMD,iBAAiBqF,GACvCiB,EAAWD,EAAS/G,GAAuBiH,cAC3CC,EAAoBC,cA7XA,IADP,IA+XbC,EAAoBD,cA7XF,IAFL,IAgYnB,OACE,kBAACE,EAAA,EAAkB,CACjBC,UAAW,kBACXC,GAAG,iBACHC,eAAa,kBAEb,kBAACC,EAAA,EAAY,CACXC,OAAQf,KAAKhG,MAAM2F,UAAWqB,MAAO,gCACrCC,uBAAwB,4BACxBC,aAAclB,KAAKC,WACnBkB,cAAY,EACZC,aAAcpB,KAAKC,YAEnB,yBAAKU,UAAU,oBAEf,yBAAKA,UAAU,0BACb,yBACEA,UAAU,mBACVU,IAAKrB,KAAKrE,qBAGZ,yBAAKgF,UAAU,sBACb,0BAAMC,GAAG,OAAOU,SAAU,SAACC,GAAC,OAAKA,EAAExD,mBACjC,yBAAK4C,UAAU,cACb,2BAAOA,UAAU,4CAA4Ca,QAAQ,WAAWZ,GAAG,qCAAqClG,EAAE,8BAC1H,kBAAC+G,EAAA,EAAQ,CACPb,GAAG,WACHc,aAAa,oCACbC,YAAY,oBACZC,MAAOvE,OAAOC,KAAKzE,GACnBgJ,oBAAqB,SAACtE,GAAG,OAAK7C,EAAE,8BAAD,OAA+B7B,EAAoB0E,MAClFuE,oBAAqB1C,EACrB2C,YAAa/B,KAAKgC,kBAClBC,MAAOxB,IAET,yBAAKE,UAAU,uBAGjB,yBAAKA,UAAU,cACb,2BAAOa,QAAQ,aAAa9G,EAAE,0BAC9B,2BACEiG,UAAU,aACVC,GAAG,YACHxE,MAAOgE,EAAS/G,GAChB6I,SAAU,SAACpE,GAAK,OAAK,EAAKqE,kBACxB9I,EACAyE,EAAMsE,OAAOhG,QAGfiG,KAAK,UAGT,yBAAK1B,UAAU,oBACb,yBAAKA,UAAU,kBACb,2BAAOa,QAAQ,QAAQZ,GAAG,iCAAiClG,EAAE,0BAC7D,kBAAC+G,EAAA,EAAQ,CACPb,GAAG,QACHc,aAAa,gCACbC,YAAY,gBACZC,MAAOhJ,EACPkJ,oBAAqB1B,EAAS/G,GAC9B0I,YAAa,SAACxE,GAAG,OAAK,EAAK4E,kBACzB9I,EACAkE,IAEF0E,MAAO1B,KAGX,yBAAKI,UAAU,kBACb,2BAAOa,QAAQ,YAAY9G,EAAE,0BAC7B,kBAAC4H,EAAA,EAAgB,CACfzF,SAAUuD,EAAS/G,GACnBkE,IAAI,WACJgF,SAAS,KACTC,iBAAkB,SAACC,GAAG,OAAK,EAAKN,kBAAkB9I,EAA0BqJ,OAAOC,SAASF,KAC5FG,YAAa,KACbC,iBAAkB,EAClBC,oBAAqB,IACrBb,MAAO1B,MAIb,yBAAKI,UAAU,4BAA4BC,GAAG,iBAC5C,kBAACmC,EAAA,EAAM,CACLpB,YAAa,yBACbqB,SAAU,UACVC,gBAAiB,UACjB9H,IAAK,EACL+H,IAAK,IACLC,KAAM,EACN/G,MAAOgE,EAAS/G,GAChB+J,gBAAiB,SAAClG,GAAO,gBAAQhC,KAAKmI,MAAMnG,GAAQ,MACpDoG,eAAgBnD,EAChBoD,eAAgB,SAChBC,eAAgB,aAChBC,cAAe,SAACT,EAAU5G,GAAK,OAAK,EAAK+F,kBACvC9I,EACA6B,KAAKmI,MAAc,IAARjH,KAEbsH,cAAe,SAACxG,GAAO,OAAKyF,SAASzF,GAAW,QAGpD,yBAAKyD,UAAU,cACb,+BAAQjG,EAAE,2BACV,yBAAKiG,UAAU,mBACb,kBAACgD,EAAA,EAAM,CACL/C,GAAG,mBAEHD,UAAS,sBAA8B,YAAbN,EAAyB,cAAgB,IACnEuD,UAAU,oBACV7H,MAAO,CACLG,gBAAiBmE,GAEnBwD,QAAS,kBAAM,EAAKC,2BACjB,EAAK9J,MAAMmE,0BAIhB,yBAAKwC,UAAU,mBACb,kBAACgD,EAAA,EAAM,CACLhC,YAAY,WACZoC,IAAI,iBACJC,SAAU5D,EAAS/G,GACnBwK,QAAS,kBAAM,EAAK1B,kBAClB9I,GACC+G,EAAS/G,OAId,kBAACsK,EAAA,EAAM,CACLhC,YAAY,gBACZoC,IAAI,mBACJC,SAAU5D,EAAS/G,GACnBwK,QAAS,kBAAM,EAAK1B,kBAClB9I,GACC+G,EAAS/G,OAId,kBAACsK,EAAA,EAAM,CACLhC,YAAY,gBACZoC,IAAI,sBACJC,SAAU5D,EAAS/G,GACnBwK,QAAS,kBAAM,EAAK1B,kBAClB9I,GACC+G,EAAS/G,SAOjB2G,KAAKhG,MAAMmE,uBACV,yBACEwC,UAAW,mBACXC,GAAG,aACHiD,QAAS,kBAAM,EAAKC,2BAA0B,KAE9C,kBAACG,EAAA,EAAY,CACXjH,MAAOoD,EAAS/G,GAChB2J,SAAU,YACVS,cAAe,SAACT,EAAUhG,GACxB,EAAKkH,eAAelH,GACpB,EAAK8G,2BAA0B,WAW/C,yBAAKnD,UAAU,YACf,yBAAKA,UAAU,oBACb,4BACEA,UAAU,iBACVC,GAAG,QACHiD,QAAS7D,KAAKmE,WAEbzJ,EAAE,sCAEL,4BACEiG,UAAU,YACVC,GAAG,SACHiD,QAAS7D,KAAKoE,aAEb1J,EAAE,uBAjlBjB,8EAulBG,EAhhBiB,CAASkF,IAAMyE,eAAa,EAA1C1K,GAAc,YACC,CACjBE,UAAWyK,IAAUC,KACrB1J,gBAAiByJ,IAAUE,OAC3BhG,mBAAoB8F,IAAUrF,OAC9BvB,YAAa4G,IAAUG,KACvBxG,cAAeqG,IAAUG,KACzB/J,EAAG4J,IAAUG,KAAKC,WAClBvE,iBAAkBmE,IAAUC,OA2gBjBI,ICzlBAhL,GDylBAgL,cAAkBhL,I,klCEtkBjC,IA0ZeiL,GA1ZI,SAAbA,EAAU,GA6BV,IA5BJC,EAAU,EAAVA,WACA9D,EAAM,EAANA,OACA+D,EAAwB,EAAxBA,yBACAC,EAAgB,EAAhBA,iBACAC,EAAW,EAAXA,YACAC,EAAY,EAAZA,aACAC,EAAW,EAAXA,YACAC,EAAc,EAAdA,eACAC,EAAgB,EAAhBA,iBACAC,EAAqB,EAArBA,sBACAC,EAAkB,EAAlBA,mBACAC,EAAqB,EAArBA,sBACAC,EAAe,EAAfA,gBACAC,EAAkB,EAAlBA,mBACAC,EAAuB,EAAvBA,wBACAC,EAA0B,EAA1BA,2BACAC,EAAqB,EAArBA,sBACAC,EAAqB,EAArBA,sBACAC,EAAsB,EAAtBA,uBACAC,EAAe,EAAfA,gBACAC,EAAmB,EAAnBA,oBACAC,EAAY,EAAZA,aACAC,EAAe,EAAfA,gBACAC,EAAK,EAALA,MACAC,EAAU,EAAVA,WACAC,EAAU,EAAVA,WACAC,EAAgB,EAAhBA,iBACAC,EAAU,EAAVA,WAEA3B,EAAW4B,UAAY,CACrB3B,WAAYP,IAAUC,KACtBxD,OAAQuD,IAAUC,KAClBO,yBAA0BR,IAAUC,KACpCQ,iBAAkBT,IAAUC,KAC5BS,YAAaV,IAAUE,OACvBS,aAAcX,IAAUE,OACxBU,YAAaZ,IAAUC,KACvBY,eAAgBb,IAAUG,KAC1BW,iBAAkBd,IAAUG,KAC5BY,sBAAuBf,IAAUC,KACjCe,mBAAoBhB,IAAUC,KAC9BgB,sBAAuBjB,IAAUG,KACjCe,gBAAiBlB,IAAUC,KAC3BkB,mBAAoBnB,IAAUG,KAC9BiB,wBAAyBpB,IAAUC,KACnCoB,2BAA4BrB,IAAUG,KACtCmB,sBAAuBtB,IAAUrF,OACjC4G,sBAAuBvB,IAAUrF,OACjC6G,uBAAwBxB,IAAUG,KAClCsB,gBAAiBzB,IAAUG,KAC3BuB,oBAAqB1B,IAAUG,KAC/BwB,aAAc3B,IAAUmC,MACxBP,gBAAiB5B,IAAUG,KAC3B0B,MAAO7B,IAAUE,OACjB4B,WAAY9B,IAAUC,KACtB8B,WAAY/B,IAAUoC,OACtBJ,iBAAkBhC,IAAUC,KAC5BgC,WAAYjC,IAAUmC,OAGxB,IAAME,EAAWC,cACVlM,EAAqB,GAAhBmM,eAAgB,GAApB,GAEFC,EAAWC,mBACXC,EAAiBD,mBACjBE,EAAcF,mBACdG,EAAqBH,mBACrBI,EAAcJ,mBACyC,KAAfK,oBAAS,GAAM,GAAtDC,EAAe,KAAEC,GAAkB,KACc,MAAZF,mBAAS,IAAG,GAAjDG,GAAc,MAAEC,GAAiB,MACkB,MAAZJ,mBAAS,IAAG,GAAnDK,GAAe,MAAEC,GAAkB,MAC6B,MAAfN,oBAAS,GAAM,GAAhEO,GAAoB,MAAEC,GAAuB,MAE9CC,GAAiBC,aAAY,SAAC9N,GAAK,aAAqC,QAArC,EAAK+N,IAAUC,gBAAgBhO,UAAM,aAAhC,EAAkC6N,kBAE1EI,GAAsB,CAC1BC,EAAG,GAAF,OAAKxN,EAAE,+BACRyN,EAAG,GAAF,OAAKzN,EAAE,8BAGJ0N,GAA8B,SAAClK,GACnCyH,EAA2BzH,IAGvByC,GAAY0H,YAAa,mBAAoB,CAAEtH,WAe/CuH,GACJ,oCACE,2BAAO9G,QAAQ,oBAAoBb,UAAU,2BAC3C,8BAAOjG,EAAE,8BACRiN,IACC,0BAAMhH,UAAU,4BAA2B,KACtCjG,EAAE,mDAIViN,IACC,yBAAKhH,UAAW4H,IAAW,8BAA+B,CAAEC,QAASf,MACnE,kBAACgB,GAAA,EAAe,CACd7H,GAAG,oBACH8H,oBAAqBnB,GACrBoB,UAAWzO,IAAK0O,gBAChBC,4BAvBqB,SAACC,GAC1BA,EAAYpR,OAAS,IACvBgQ,GAAmB,IACnBF,GAAkBsB,KAqBZC,cAAevB,GACfwB,QA/BoB,SAAC5N,GACzBA,GACFsM,GAAmB,GAAD,OAAIhN,EAAE,2BAA0B,YAAIR,IAAK0O,mBA8BrDnB,gBAAiBA,GACjBwB,iBAAkB1C,EAClB2C,kBAAkB,MAO5BC,qBAAU,WACRjH,OACC,CAACqF,KAEJ,IAAMrF,GAAW,WAAM,kBACjB+D,EAAe,GAGnB,GAFAb,EAAoC,QAApB,EAAC+B,EAAYvL,eAAO,aAAnB,EAAqBwN,SACtCxB,GAA2C,QAApB,EAACX,EAAYrL,eAAO,aAAnB,EAAqBwN,SACzB,QAAhB,EAAAtC,EAASlL,eAAO,OAAhB,EAAkBwN,SAA+B,QAAnB,EAAAjC,EAAYvL,eAAO,OAAnB,EAAqBwN,SAAW/B,EAChE,IAAK,IAAIrQ,EAAI,EAAGA,GAAKkD,IAAK0O,gBAAiB5R,IACzCiP,EAAa/N,KAAKlB,QAEf,GAA0B,QAA1B,EAAIgQ,EAAepL,eAAO,OAAtB,EAAwBwN,QAAS,CAC1C,IAAMT,EAAYzO,IAAK0O,gBAGvB,OAAQvC,GACN,KAAKgD,IAAWC,YAChB,KAAKD,IAAWE,sBAEI,IAAhBvE,GACCA,IAAgB2D,GAAaA,EAAY,GAAM,EAGhD1C,EAAa/N,KAAK8M,GAElBiB,EACEjB,EAAc,EACV,CAACA,EAAc,EAAGA,GAClB,CAACA,EAAaA,EAAc,GAEpC,MACF,KAAKqE,IAAWG,iBAChB,KAAKH,IAAWI,OACVzE,IAAgB2D,GAAaA,EAAY,GAAM,EAEjD1C,EAAa/N,KAAK8M,GAElBiB,EACEjB,EAAc,EACV,CAACA,EAAaA,EAAc,GAC5B,CAACA,EAAc,EAAGA,GAE1B,MACF,QACEiB,EAAa/N,KAAK8M,SAGM,QAAvB,EAAIiC,EAAYrL,eAAO,OAAnB,EAAqBwN,QAC9BnD,EAAesB,GACa,QAAvB,EAAIJ,EAAYvL,eAAO,OAAnB,EAAqBwN,UAC9BnD,EAAe,CAACjB,IAGlBkB,EAAgBD,IAGlBkD,qBAAU,WAYR,OAXAjH,KAEAhI,IAAKC,eAAeuP,MAAK,SAACC,GACxB7D,EACE6D,SAEkC,IAAlCtM,OAAOC,KAAKqM,GAAWjS,QAEzBmO,EAAsBjK,QAAU+N,KAG3B,WACLzP,IAAKK,aAAasL,EAAsBjK,SACxC+J,GAA2B,MAE5B,IAEHwD,qBAAU,WAC0B,QAAjCjP,IAAKmB,cAAcuO,WAAuBtD,EAAoBgB,IAAmB,GAAQA,IAAmB,KAC5G,CAAChB,IAEJ,IAIMuD,GAAsCC,cAAgB,WACrD1D,GACHgC,IAA4B,MAIhC,OAAOvD,EAAa,KAClB,oCACE,kBAAC,GAAc,CACbhL,aAAckH,IAAU2E,GAExB7K,gBAAiBmK,EAAc,EAC/BtH,YAAa0K,GACbnK,cAAe,SAAC7B,GAAK,OAAKuK,EAASoD,IAAQC,yBAAyB5N,KACpEoC,mBAAoBoH,EACpBzF,iBAAkB0H,KAEpB,yBACElH,UAAWA,GACXE,eAAcoJ,IAAaC,aAE3B,kBAACpJ,EAAA,EAAY,CACXC,OAAQA,IAAW2E,EAAyB1E,MAAO,6BACnDmJ,iBAAkB,SAAC5I,GAAC,OAAKA,EAAE6I,mBAAmBlJ,aAAc6E,EAC5D9E,uBAAwB,wBACxBE,cAAY,EACZC,aAAc2E,GAEd,yBAAKpF,UAAU,oBACf,yBAAKA,UAAU,YACb,yBAAKA,UAAU,WACb,yBAAKA,UAAU,iBAAe,UAAKjG,EAAE,sBAAqB,MAC1D,0BACEiG,UAAU,gBACVuB,SAAUA,GACVZ,SAAU0E,GAEV,kBAACqE,GAAA,EAAM,CACL1I,YAAY,sBACZN,IAAKyF,EACLlG,GAAG,YACH0J,KAAK,QACLC,OAAK,EACLC,MAAO9P,EAAE,oBACT+P,gBAAc,EACdC,SAAUtE,EACVuE,QAAM,IAER,kBAACN,GAAA,EAAM,CACL1I,YAAY,yBACZN,IAAK2F,EACLpG,GAAG,eACH0J,KAAK,QACLC,OAAK,EACLC,MAAO9P,EAAE,wBACTgQ,SAAUtE,EACVuE,QAAM,IAER,kBAACN,GAAA,EAAM,CACL1I,YAAY,yBACZN,IAAK8F,EACLvG,GAAG,eACH0J,KAAK,QACLC,OAAK,EACLC,MAAO9P,EAAE,qBACTgQ,SAAUrF,EACVsF,QAAM,EACN3J,MAAOtG,EAAE,uCAEX,kBAAC2P,GAAA,EAAM,CACL1I,YAAY,yBACZN,IAAK4F,EACLrG,GAAG,eACH0J,KAAK,QACL3J,UAAU,uBACV4J,OAAK,EACLC,MAAOlC,GACPoC,SAAUtE,EACVuE,QAAM,IAER,kBAACN,GAAA,EAAM,CACL1I,YAAY,yBACZf,GAAG,sBACH0J,KAAK,cACLE,MAAO9P,EAAE,mCACTgQ,SAAUtE,EACVlE,SAAU,kBAAMqD,GAAsB,SAACqF,GAAS,OAAMA,MACtDxB,QAAS9D,EACTqF,QAAM,IAEPtD,GACC,oCAEItC,GACE,oCACE,kBAACsF,GAAA,EAAM,CACL1I,YAAY,uBACZf,GAAG,kBACH0J,KAAK,YACLE,MAAO9P,EAAE,+BACTgQ,SAAUtE,EACVlE,SAAU,kBAAMiD,GAAe,SAACyF,GAAS,OAAMA,MAC/CxB,QAASlE,EACTyF,QAAM,IAER,kBAACN,GAAA,EAAM,CACL1I,YAAY,sBACZN,IAAK6F,EACLtG,GAAG,mBACH0J,KAAK,WACLE,MAAO9P,EAAE,gCACTwH,SAAU,kBAAMuD,GAAmB,SAACmF,GAAS,OAAMA,MACnDF,SAAUtE,EACVgD,QAAS5D,EACTmF,QAAM,OAOhBtD,GACA,oCACE,kBAACgD,GAAA,EAAM,CACL1I,YAAY,uBACZf,GAAG,kBACH0J,KAAK,YACLE,MAAO9P,EAAE,+BACTgQ,SAAUtE,EACVlE,SAAU,kBAAMiD,GAAe,SAACyF,GAAS,OAAMA,MAC/CxB,QAASlE,EACTyF,QAAM,IAER,kBAACN,GAAA,EAAM,CACL1I,YAAY,sBACZN,IAAK6F,EACLtG,GAAG,mBACH0J,KAAK,WACLE,MAAO9P,EAAE,gCACTwH,SAAU,kBAAMuD,GAAmB,SAACmF,GAAS,OAAMA,MACnDF,SAAUtE,EACVgD,QAAS5D,EACTmF,QAAM,QAMdtD,GACA,kBAAC3G,EAAA,EAAkB,CAACC,UAAU,UAAUgB,YAAasI,IAAaY,eAChE,2BAAOlK,UAAU,4CAA4Ca,QAAQ,sBAAsBZ,GAAG,+BAA6B,UAAKlG,EAAE,4BAA2B,MAC7J,kBAAC+G,EAAA,EAAQ,CACPb,GAAG,sBACHc,aAAa,8BACbC,YAAY,sBACZC,MAAOvE,OAAOC,KAAK2K,IACnB7E,gBAAiB,SAAC0H,GAAI,OAAK7C,GAAoB6C,IAC/C/I,YAjKiB,SAACgJ,GAChCpE,EAASoD,IAAQiB,gBAAgBtI,OAAOqI,MAiK1BjJ,oBAAqBmD,aAAY,EAAZA,EAAchI,WACnCgF,MAAO,OAIb,yBAAKtB,UAAU,SACZyF,EACC,uCAAS1L,EAAE,sBAAqB,YAAIyL,EAAK,YAAIF,EAAavO,SAE1D,6BAAMgD,EAAE,8BAA+B,CAAEyL,MAAOF,EAAavO,YAG/DoN,GACA,kBAACpE,EAAA,EAAkB,CAACC,UAAU,4BAA4BgB,YAAasI,IAAagB,iBAClF,yBAAKtK,UAAU,iBAAiBjG,EAAE,2BAClC,4BACEmG,eAAa,iBACbF,UAAU,kBACV+J,SAAUtE,EACVvC,QAASgG,IAERnP,EAAE,8BAKX,yBAAKiG,UAAU,YACf,yBAAKA,UAAU,WACb,kBAACgD,EAAA,EAAM,CACLhD,UAAU,SACVkD,QAASmC,EACTwE,MAAO9P,EAAE,gBACTkJ,UAAWlJ,EAAE,uB,mZCra3B,gmGAAA1D,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,olBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAeA,IAgNekU,GAhNa,WAC1B,IAAMvE,EAAWC,cAoChB,KAnBGkB,aACF,SAAC9N,GAAK,MAAK,CACT+N,IAAUoD,kBAAkBnR,EAAOiQ,IAAaC,aAChDnC,IAAUqD,cAAcpR,EAAOiQ,IAAaC,aAC5CnC,IAAUoD,kBAAkBnR,EAAO,kBACnC+N,IAAUsD,eAAerR,GACzB+N,IAAUuD,gBAAgBtR,GAC1B+N,IAAUwD,uBAAuBvR,GACjC+N,IAAUyD,cAAcxR,EAAO,cAC/B+N,IAAU0D,gBAAgBzR,GAC1B+N,IAAU2D,YAAY1R,GACtB+N,IAAU4D,eAAe3R,GACzB+N,IAAU6D,yBAAyB5R,GACnC+N,IAAU8D,mBAAmB7R,GAC7B+N,IAAU+D,yBAAyB9R,GACnC+N,IAAUgE,YAAY/R,GACtB+N,IAAUiE,sBAAsBhS,EAAO,uBAEzCiS,KACD,IAlCCpH,EAAU,KACV9D,EAAM,KACN+D,EAAwB,KACxBE,EAAW,KACXC,EAAY,KACZiH,EAAmB,KACnB3F,EAAU,KACV4F,EAAY,KACZC,EAAQ,KACR/F,EAAU,KACVgG,EAAqB,MACrBC,EAAQ,MACR1G,EAAqB,MACrB2G,EAAQ,MACRjG,EAAgB,MAsBZT,EAAwBkB,mBAEuC,KAAfK,oBAAS,GAAM,GAA9DoF,EAAmB,KAAE1G,EAAsB,KACZ,KAAZsB,oBAAU,GAAE,GAA/BjB,EAAK,KAAEsG,EAAQ,KAEuD,KAAfrF,oBAAS,GAAM,GAAtEsF,EAAuB,KAAEC,EAA0B,KACN,KAAZvF,mBAAS,IAAG,GAA7CnB,EAAY,KAAEC,EAAe,KACiB,KAAfkB,oBAAS,GAAM,GAA9ClC,EAAW,KAAEC,EAAc,KAC2C,KAAfiC,oBAAS,GAAM,GAAtE1B,EAAuB,KAAEC,EAA0B,KACQ,KAAdyB,oBAAS,GAAK,GAA3D9B,EAAkB,KAAEC,EAAqB,KACa,KAAf6B,oBAAS,GAAM,GAAtD5B,EAAe,KAAEC,EAAkB,KACe,KAAf2B,oBAAS,GAAM,GAAlDwF,EAAa,KAAExH,GAAgB,KACmC,MAAfgC,oBAAS,GAAM,GAAlE/B,GAAqB,MAAEwH,GAAwB,MAEtD1D,qBAAU,WACiB,UAArB+C,IACF3G,EAA4D,QAAvC,EAAC2G,EAAoB5G,0BAAkB,QAAIA,GAChEG,EAAsD,QAApC,EAACyG,EAAoB1G,uBAAe,QAAIA,GAC1DmH,EAAsE,QAA5C,EAACT,EAAoBQ,+BAAuB,QAAIA,MAE3E,CAACR,IAEJ,IAAM9F,GAAaD,GAAS,EAE5BgD,qBAAU,WACJpI,IACEuF,GACFwG,KAGFnG,EAASoD,IAAQgD,cAAc,CAC7B9C,IAAa+C,gBACb/C,IAAagD,cACbhD,IAAaiD,eACbjD,IAAakD,kBAGhB,CAACpM,EAAQ4F,IAEZ,IAAMmG,GAAmB,WACvB,IAAIzH,GAAJ,CAIA,IACM+H,EADqBlT,IAAKmT,uBACaC,gBAAgB,EAAG,IAE5DF,aAAiB,EAAjBA,EAAmB1V,QAAS,EAC9BmV,IAAyB,GAEzBA,IAAyB,KAmBvBU,GAAgB,+BAAG,yFAIP,OAHVC,EAAkB,CAAEZ,gBAAetH,qBAAoBE,kBAAiBI,wBAAuBK,gBAC/F5O,EAAW6C,IAAKmB,cAChBoS,EAAevT,IAAKwT,uBAAsB,KAChDC,IAAgB,SAAOC,YAA4BJ,EAAiBnW,EAAUoW,GAAa,6EAC5F,kBALqB,mCAOhBI,GAAiB,SAACtM,GAGtB,GAFAA,EAAExD,mBAEEkI,EAAavO,OAAS,GAA1B,CAIA+U,EAAS,GAELD,EACFtS,IAAKK,aAAaqL,GAElB1L,IAAKK,aAAasL,EAAsBjK,SAG1C,IAAMkS,EAAe,CACnBtI,kBACAF,qBACAoH,0BACAzH,eACAkH,eACAC,SAAUA,EACVC,wBACAO,gBACAN,WACAC,WACAwB,gBAAgB,EAChB7I,eAGI8I,EAAcC,YAClBhI,EACA6H,OACApO,GAEFsO,EAAYhW,QAAO,+BAAC,WAAOkW,GAAW,kFAC9BA,EAAW,OACjBzB,EAAStG,EAAQF,EAAavO,UAAsB,IAAXyO,EAAeA,EAAQ,EAAIA,IAAQ,2CAC7E,mDAHkB,IAInBgI,QAAQC,IAAIJ,GACTtE,MAAK,SAAC2E,GACLC,YAAWD,GACXE,QACA,OACK,SAAChN,GACNiN,QAAQhG,MAAMjH,GACdkL,GAAU,QAKV1G,GAAkB,WACtB0G,GAAU,GACV9F,EAASoD,IAAQ0E,aAAaxE,IAAaC,eAGvCqE,GAA4BG,aAAgB3I,IAElD,OACE,kBAAC,GAAU,CACTlB,WAAYA,EACZ9D,OAAQA,EACR+D,yBAA0BA,EAC1BC,iBAAkB7K,IAAKyU,mBACvB3J,YAAaA,EACbC,aAAcA,EACdC,YAAaA,EACbC,eAAgBA,EAChBC,iBAAkBA,GAClBC,sBAAuBA,GACvByH,iBAAkBA,GAClBxH,mBAAoBA,EACpBC,sBAAuBA,EACvBC,gBAAiBA,EACjBC,mBAAoBA,EACpBC,wBAAyBA,EACzBC,2BAA4BA,EAC5BC,sBAAuBA,EACvBC,sBAAuBA,EACvBC,uBAAwBA,EACxBC,gBAAiBA,GACjBC,oBAvGwB,SAACzE,GAC3B,IACMqN,EADW1U,IAAKmB,cACIuO,UAEtBtD,GAAiC,QAAbsI,GACtBJ,QAAQK,KAAK,qFAGXvI,GAAiC,QAAbsI,EACtBrB,KAEAM,GAAetM,IA6Ff0E,aAAcA,EACdC,gBAAiBA,EACjBC,MAAOA,EACPC,WAAYA,GACZG,WAAYA,EACZF,WAAYA,EACZC,iBAAkBA,KCzNT4E", "file": "chunks/chunk.55.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./PrintModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./WatermarkModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".Watermark{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.Watermark .modal-container .wrapper .modal-content{padding:10px}.Watermark .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.Watermark .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.Watermark .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.Watermark .footer .modal-button.confirm{margin-left:4px}.Watermark .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .footer .modal-button{padding:23px 8px}}.Watermark .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .swipe-indicator{width:32px}}.open.Watermark{visibility:visible}.closed.Watermark{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Watermark .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.Watermark .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.Watermark .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.Watermark .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.Watermark .footer .modal-button.cancel:hover,.Watermark .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.Watermark .footer .modal-button.cancel,.Watermark .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.Watermark .footer .modal-button.cancel.disabled,.Watermark .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.Watermark .footer .modal-button.cancel.disabled span,.Watermark .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.Watermark{z-index:110}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .modal-container{overflow:visible}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .modal-container{overflow:visible}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .h2{font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .h2{font-size:16px}}.Watermark .StylePopup{position:absolute;margin-top:-140px;background:var(--preset-background);display:flex;flex-direction:column;justify-content:center;border-radius:0 0 4px 4px;box-shadow:0 0 3px 0 var(--box-shadow);align-items:center;z-index:85}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.Watermark .StylePopup{margin-top:0}}.Watermark .StylePopup .ColorPalette{padding:10px}.Watermark .style-container{display:flex}.Watermark .style-container .Button{margin-right:8px}.Watermark .style-container .Button:hover{background:var(--popup-button-hover)}.Watermark .style-container .Button.active{background:var(--popup-button-active)}.Watermark .form-content-container{display:flex;flex-direction:row;justify-content:space-between;align-items:center;padding:16px;width:100%}.Watermark .form-content-container form{width:auto}@media(min-width:315px)and (max-width:480px){.Watermark .form-content-container form{width:auto}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .form-content-container{flex-direction:column;padding:16px 16px 0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .form-content-container{flex-direction:column;padding:16px 16px 0}}.Watermark .font-form-fields{display:flex;flex-direction:column;margin:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields{flex-direction:row}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields{flex-direction:row}}.Watermark .font-form-fields .form-font-type{margin-top:16px;margin-left:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-type{width:160px;margin-left:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-type{width:160px;margin-left:0}}.Watermark .font-form-fields .form-font-type label{font-weight:700}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-type label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-type label{font-size:13px}}.Watermark .font-form-fields .form-font-type .Dropdown__wrapper{margin-top:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-type .Dropdown__wrapper{width:160px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-type .Dropdown__wrapper{width:160px}}.Watermark .font-form-fields .form-font-type .Dropdown__wrapper .Dropdown{text-align:left;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-type .Dropdown__wrapper .Dropdown{height:28px;width:160px;z-index:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-type .Dropdown__wrapper .Dropdown{height:28px;width:160px;z-index:0}}.Watermark .font-form-fields .form-font-type .Dropdown__wrapper .Dropdown__items{width:100%;top:0}.Watermark .font-form-fields .form-font-type .Dropdown__wrapper#location .Dropdown__items{z-index:101}.Watermark .font-form-fields .form-font-size{margin-top:16px;margin-left:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-size{width:160px;margin-left:8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-size{width:160px;margin-left:8px}}.Watermark .font-form-fields .form-font-size label{font-weight:700}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-size label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-size label{font-size:13px}}.Watermark .font-form-fields .form-font-size .FontSizeDropdown{padding:2px;height:32px;margin-top:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-size .FontSizeDropdown{height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-size .FontSizeDropdown{height:28px}}.Watermark .font-form-fields .form-font-size .FontSizeDropdown .icon-button{cursor:pointer;width:14px;height:14px}.Watermark .font-form-fields .form-font-size .FontSizeDropdown .icon-button>.Icon{color:var(--icon-color);width:12px;height:16px;margin-top:2px}.Watermark .watermark-settings{display:flex;flex-direction:column;width:330px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings{width:100%;height:374px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings{width:100%;height:374px}}.Watermark .watermark-settings .form-field{display:flex;flex-direction:column;margin:16px 0 0 16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field{margin:0;width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field{margin:0;width:100%}}.Watermark .watermark-settings .form-field label{margin-bottom:8px;font-weight:700}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field label{margin-top:16px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field label{margin-top:16px;font-size:13px}}.Watermark .watermark-settings .form-field .text-input,.Watermark .watermark-settings .form-field select{height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field .text-input,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field select{height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field .text-input,.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field select{height:28px}}.Watermark .watermark-settings .form-field.opacity-slider{display:flex;margin-bottom:0}.Watermark .watermark-settings .form-field.opacity-slider .slider-property{font-weight:700}.Watermark .watermark-settings .form-field.opacity-slider .slider-svg-container{margin:0;height:30px}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.Watermark .watermark-settings .form-field.opacity-slider{align-items:stretch}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field.opacity-slider{font-size:13px;margin-top:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field.opacity-slider{font-size:13px;margin-top:16px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field .Dropdown__wrapper{width:328px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field .Dropdown__wrapper{width:328px}}.Watermark .watermark-settings .form-field .Dropdown__wrapper button{width:314px}.Watermark .watermark-settings .form-field .Dropdown__wrapper .Dropdown{text-align:left;width:314px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field .Dropdown__wrapper .Dropdown{height:28px;z-index:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field .Dropdown__wrapper .Dropdown{height:28px;z-index:0}}.Watermark .watermark-settings .form-field .Dropdown__wrapper .Dropdown__items{width:100%;top:0}.Watermark .watermark-settings .form-field .Dropdown__wrapper#location .Dropdown__items{z-index:101}.Watermark .watermark-settings .colorSelect{width:18px;height:18px;margin:7px 8px 7px 7px;border-radius:10000000px}.Watermark .watermark-settings .white-color{border:1px solid var(--white-color-palette-border)}.Watermark .watermark-settings .slider-input-field{height:32px}.Watermark .canvas-container{display:flex;justify-content:center;align-items:center;width:500px;height:436px;background-color:var(--file-preview-background);border:1px solid var(--lighter-border)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .canvas-container{display:block;width:328px;height:261px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .canvas-container{display:block;width:328px;height:261px}}.Watermark .canvas-container canvas{width:325px!important}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .canvas-container canvas{width:328px;height:261px;-o-object-fit:contain;object-fit:contain;background-color:var(--file-preview-background)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .canvas-container canvas{width:328px;height:261px;-o-object-fit:contain;object-fit:contain;background-color:var(--file-preview-background)}}.Watermark .divider{height:1px;width:100%;background:var(--divider)}.Watermark .divider.separator{margin-top:16px}.Watermark .button-container{display:flex;justify-content:space-between;align-items:baseline;width:100%;padding:16px}.Watermark .button-container .button{display:flex;justify-content:center;align-items:center;padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;border:0;height:32px;cursor:pointer;font-size:13px}.Watermark .button-container .button.ok{color:var(--primary-button-text);background:var(--primary-button)}.Watermark .button-container .button.ok:hover{background:var(--primary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .button-container .button.ok{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .button-container .button.ok{font-size:13px}}.Watermark .button-container .button.cancel{color:var(--secondary-button-text);background:none}.Watermark .button-container .button.cancel:hover{color:var(--secondary-button-hover)}.Watermark .button-container .reset-settings{background-color:transparent;cursor:pointer;background:none;border:0;color:var(--secondary-button-text);display:flex;font-size:13px;padding:6px 18px}:host(:not([data-tabbing=true])) .Watermark .button-container .reset-settings,html:not([data-tabbing=true]) .Watermark .button-container .reset-settings{outline:none}.Watermark .button-container .reset-settings:hover{color:var(--secondary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .button-container .reset-settings{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .button-container .reset-settings{font-size:13px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.PrintModal{visibility:visible}.closed.PrintModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.PrintModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.PrintModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.PrintModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.PrintModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.PrintModal .footer .modal-button.cancel:hover,.PrintModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.PrintModal .footer .modal-button.cancel,.PrintModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.PrintModal .footer .modal-button.cancel.disabled,.PrintModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.PrintModal .footer .modal-button.cancel.disabled span,.PrintModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.PrintModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.PrintModal .modal-container .wrapper .modal-content{padding:10px}.PrintModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.PrintModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.PrintModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.PrintModal .footer .modal-button.confirm{margin-left:4px}.PrintModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PrintModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PrintModal .footer .modal-button{padding:23px 8px}}.PrintModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PrintModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .PrintModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PrintModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PrintModal .swipe-indicator{width:32px}}.PrintModal .modal-container{width:480px;overflow:hidden}.PrintModal .modal-container .settings{display:flex;flex-direction:column;width:100%;padding:24px}.PrintModal .modal-container .settings .ui__choice{margin:10px 4px 4px 0}.PrintModal .modal-container .settings .ui__choice--disabled{opacity:.5}.PrintModal .modal-container .settings .ui__input{border:none}.PrintModal .modal-container .settings .ui__input.ui__input--focused{box-shadow:none}.PrintModal .modal-container .settings .settings-form{margin-bottom:0}.PrintModal .modal-container .settings .specifyPagesChoiceLabel{display:flex}.PrintModal .modal-container .settings .specifyPagesChoiceLabel .specifyPagesExampleLabel{margin-left:4px;color:var(--faded-text)}.PrintModal .modal-container .settings .page-number-input-container{margin-top:8px}.PrintModal .modal-container .divider{height:1px;width:100%;background:var(--divider)}.PrintModal .modal-container .section{padding-bottom:16px}.PrintModal .modal-container .section.watermark-section{padding-bottom:0}.PrintModal .modal-container .section .section-label{font-weight:700}.PrintModal .modal-container .section .hidden{display:none}.PrintModal .modal-container label.section-label,.PrintModal .modal-container label.ui__choice__label{padding:2px 0 2px 4px}.PrintModal .modal-container .print-quality-section-label{padding:0;margin-bottom:8px}.PrintModal .modal-container #printQualityOptions button.Dropdown{height:32px;margin-top:8px}.PrintModal .modal-container #printQualityOptions.Dropdown__wrapper .Dropdown{text-align:left}.PrintModal .modal-container #printQualityOptions.Dropdown__wrapper .Dropdown__items{width:274px;left:0}.PrintModal .modal-container input[type=text]{width:100%;height:32px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 42px 6px 8px;margin-top:6px}.PrintModal .modal-container input[type=text]:focus{outline:none;border:1px solid var(--focus-border)}.PrintModal .modal-container input[type=text]::-moz-placeholder{color:var(--placeholder-text)}.PrintModal .modal-container input[type=text]::placeholder{color:var(--placeholder-text)}.PrintModal .modal-container input[type=text].page-number-input--error{border-color:var(--error-border-color)}.PrintModal .modal-container .total{display:flex;flex-direction:row;padding-bottom:16px;color:var(--text-color)}.PrintModal .modal-container .background{width:100%;height:10px;transform:translateY(-50%);fill:#e2f3fe}.PrintModal .modal-container .progress{height:10px;transform:translateY(-50%);fill:#077bc5}.PrintModal .modal-container .buttons{display:flex;flex-direction:row;justify-content:flex-end;width:100%;padding:20px}.PrintModal .modal-container .buttons .button{background-color:transparent;display:flex;justify-content:center;align-items:center;color:var(--primary-button-text);padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;background:var(--primary-button);border-radius:4px;border:0;height:32px;cursor:pointer;font-size:13px}:host(:not([data-tabbing=true])) .PrintModal .modal-container .buttons .button,html:not([data-tabbing=true]) .PrintModal .modal-container .buttons .button{outline:none}.PrintModal .modal-container .buttons .button:hover:not(:disabled){background:var(--primary-button-hover)}.PrintModal .modal-container .buttons .button:disabled{opacity:.5;cursor:auto}.PrintModal .modal-container .specify-pages-choice{margin-bottom:18px}.PrintModal .modal-container .specify-pages-choice input{width:195px;margin-top:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PrintModal .modal-container .specify-pages-choice input{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PrintModal .modal-container .specify-pages-choice input{width:100%}}.PrintModal .modal-container .specify-pages-choice label{display:grid}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PrintModal .modal-container .specify-pages-choice label{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PrintModal .modal-container .specify-pages-choice label{width:100%}}.PrintModal .modal-container .specify-pages-choice.ui__choice--checked{align-items:baseline;height:80px}.PrintModal .modal-container .specify-pages-choice.ui__choice--checked .ui__choice__input{top:3px}.PrintModal .modal-container .apply-watermark{border:none;background-color:transparent;cursor:pointer;margin-top:10px;background:none;border:1px solid var(--secondary-button-text);border-radius:4px;color:var(--secondary-button-text);padding:6px 16px;height:32px;display:flex;align-items:center;justify-content:center;font-size:13px}:host(:not([data-tabbing=true])) .PrintModal .modal-container .apply-watermark,html:not([data-tabbing=true]) .PrintModal .modal-container .apply-watermark{outline:none}.PrintModal .modal-container .apply-watermark:hover{color:var(--secondary-button-hover)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "// https://websitesetup.org/web-safe-fonts-html-css/\nexport const FONTS = [\n  'Arial',\n  'Times New Roman',\n  '<PERSON><PERSON><PERSON> ',\n  'Trebuchet MS',\n  'Courier',\n  '<PERSON>erd<PERSON>',\n  '<PERSON>',\n  '<PERSON><PERSON><PERSON>',\n  'Comic Sans MS',\n  'Arial Black',\n  'Impact',\n];", "import ColorPalette from 'components/ColorPalette';\nimport Slider from 'components/Slider';\nimport core from 'core';\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport { withTranslation } from 'react-i18next';\nimport Button from 'components/Button';\nimport FontSizeDropdown from 'components/FontSizeDropdown';\nimport { FONTS } from './web-fonts';\nimport './WatermarkModal.scss';\nimport ModalWrapper from '../../ModalWrapper';\n\nimport DataElementWrapper from 'src/components/DataElementWrapper';\nimport Dropdown from 'src/components/Dropdown';\n\nimport { isMobile } from 'helpers/device';\n\nconst DESIRED_WIDTH = 300;\nconst DESIRED_HEIGHT = 300;\n\nconst DEFAULT_FONT_SIZE = 48;\n\nconst DROPDOWN_WIDTH = 314;\nconst DROPDOWN_MOBILE_WIDTH = 160;\nconst DROPDOWN_WIDTH_LONG = 328;\n\nconst WATERMARK_LOCATIONS = {\n  CENTER: 'center',\n  TOP_LEFT: 'topLeft',\n  TOP_RIGHT: 'topRight',\n  TOP_CENTER: 'topCenter',\n  BOT_LEFT: 'bottomLeft',\n  BOT_RIGHT: 'bottomRight',\n  BOT_CENTER: 'bottomCenter',\n};\n\nconst FORM_FIELD_KEYS = {\n  location: 'location',\n  fontSize: 'fontSize',\n  text: 'text',\n  color: 'color',\n  opacity: 'opacity',\n  font: 'font',\n  isBolded: 'isBolded',\n  isItalic: 'isItalic',\n  isUnderlined: 'isUnderlined',\n};\n\nconst DEFAULT_VALS = {\n  [FORM_FIELD_KEYS.location]: WATERMARK_LOCATIONS.CENTER,\n  [FORM_FIELD_KEYS.fontSize]: DEFAULT_FONT_SIZE,\n  [FORM_FIELD_KEYS.text]: '',\n  // red\n  [FORM_FIELD_KEYS.color]: new window.Core.Annotations.Color(228, 66, 52),\n  [FORM_FIELD_KEYS.opacity]: 100,\n  [FORM_FIELD_KEYS.font]: FONTS[0],\n  [FORM_FIELD_KEYS.isBolded]: false,\n  [FORM_FIELD_KEYS.isItalic]: false,\n  [FORM_FIELD_KEYS.isUnderlined]: false,\n};\n\n// Values come from https://docs.apryse.com/api/web/Core.DocumentViewer.html#setWatermark__anchor\nconst WATERMARK_API_LOCATIONS = {\n  [WATERMARK_LOCATIONS.CENTER]: 'diagonal',\n  [WATERMARK_LOCATIONS.TOP_LEFT]: 'headerLeft',\n  [WATERMARK_LOCATIONS.TOP_RIGHT]: 'headerRight',\n  [WATERMARK_LOCATIONS.TOP_CENTER]: 'headerCenter',\n  [WATERMARK_LOCATIONS.BOT_LEFT]: 'footerLeft',\n  [WATERMARK_LOCATIONS.BOT_RIGHT]: 'footerRight',\n  [WATERMARK_LOCATIONS.BOT_CENTER]: 'footerCenter',\n};\n\nclass WatermarkModal extends React.PureComponent {\n  static propTypes = {\n    isVisible: PropTypes.bool,\n    pageIndexToView: PropTypes.number,\n    watermarkLocations: PropTypes.object,\n    modalClosed: PropTypes.func,\n    formSubmitted: PropTypes.func,\n    t: PropTypes.func.isRequired,\n    isCustomizableUI: PropTypes.bool,\n  };\n\n  constructor(props) {\n    super(props);\n    const locationSettings = this.initializeLocationSettings();\n    this.preExistingWatermark = undefined;\n    this.state = {\n      isColorPaletteVisible: false,\n      locationSettings,\n      previousLocationSettings: locationSettings,\n      lockFocus: false,\n    };\n    this.canvasContainerRef = React.createRef();\n  }\n\n  componentDidUpdate(prevProps) {\n    core.addEventListener('documentLoaded', this.closeModal);\n\n    if (this.props.isVisible !== prevProps.isVisible) {\n      // Sets focus with a slight delay after modal becomes visible in order to\n      // prevent stack overflow with competing print modal focus lock.\n      if (this.props.isVisible) {\n        this.setState({ lockFocus: true });\n      } else {\n        this.setState({ lockFocus: false });\n      }\n\n      this.setState(\n        {\n          isColorPaletteVisible: false,\n        },\n        () => this.handleWatermarkOnVisibilityChanged(),\n      );\n    }\n  }\n\n  handleWatermarkOnVisibilityChanged = () => {\n    if (this.props.isVisible) {\n      this.setState(\n        {\n          locationSettings: this.state.previousLocationSettings,\n        },\n        async () => {\n          // Store the pre-existing watermark (if any) before we overwrite it\n          this.preExistingWatermark = await core.getWatermark();\n          this.addWatermarks();\n        },\n      );\n    } else {\n      this.removeWatermarkCreatedByModal();\n      core.setWatermark(this.preExistingWatermark);\n    }\n  };\n\n  addWatermarks = () => {\n    const watermarkOptions = this.createWatermarks();\n    const { t } = this.props;\n    core.setWatermark(watermarkOptions);\n\n    const pageHeight = core.getPageHeight(this.props.pageIndexToView + 1);\n    const pageWidth = core.getPageWidth(this.props.pageIndexToView + 1);\n\n    const desiredZoomForWidth = DESIRED_WIDTH / pageWidth;\n    const desiredZoomForHeight = DESIRED_HEIGHT / pageHeight;\n\n    const desiredZoom = Math.min(desiredZoomForHeight, desiredZoomForWidth);\n    const pageNumber = this.props.pageIndexToView + 1;\n\n    core.getDocument().loadCanvas({\n      pageNumber: pageNumber,\n      zoom: desiredZoom,\n      drawComplete: (canvas) => {\n        const nodes = this.canvasContainerRef.current.childNodes;\n        if (nodes && nodes.length > 0) {\n          this.canvasContainerRef.current.removeChild(nodes[0]);\n        }\n        canvas.style.border = this.canvasContainerRef.current.style.border;\n        canvas.style.height = this.canvasContainerRef.current.style.height;\n        canvas.style.backgroundColor = this.canvasContainerRef.current.style.backgroundColor;\n        canvas.setAttribute('role', 'img');\n        canvas.setAttribute('aria-label', `${t('action.page')} ${pageNumber}`);\n        this.canvasContainerRef.current.appendChild(canvas);\n      },\n    });\n\n    // Note: do not update and refresh the doc else it may affect other docs as well\n  };\n\n  // eslint-disable-next-line class-methods-use-this\n  constructWatermarkOption = (value) => {\n    const fontStyles = [];\n    if (value.isBolded) {\n      fontStyles.push(core.getFontStyles().BOLD);\n    }\n    if (value.isItalic) {\n      fontStyles.push(core.getFontStyles().ITALIC);\n    }\n    if (value.isUnderlined) {\n      fontStyles.push(core.getFontStyles().UNDERLINE);\n    }\n    const watermarkOption = {\n      fontSize: value.fontSize,\n      fontFamily: value.font,\n      color: value.color.toString(),\n      opacity: value.opacity,\n      text: value.text,\n      fontStyles,\n    };\n\n    return watermarkOption;\n  };\n\n  createWatermarks = () => {\n    const watermarks = {};\n\n    Object.keys(WATERMARK_LOCATIONS).forEach((key) => {\n      const temp = this.constructWatermarkOption(\n        this.state.locationSettings[key],\n      );\n      const value = WATERMARK_LOCATIONS[key];\n      watermarks[WATERMARK_API_LOCATIONS[value]] = temp;\n    });\n    return watermarks;\n  };\n\n  // eslint-disable-next-line class-methods-use-this\n  removeWatermarkCreatedByModal = () => {\n    core.setWatermark({});\n  };\n\n  closeModal = () => {\n    this.props.modalClosed();\n  };\n\n  handleInputChange = (key, value) => {\n    const currLocationSettings = {\n      ...this.state.locationSettings,\n    };\n    const currSelectedLocation = this.getCurrentSelectedLocation();\n    currLocationSettings[currSelectedLocation] = {\n      ...currLocationSettings[currSelectedLocation],\n      [key]: value,\n    };\n\n    this.setState(\n      {\n        locationSettings: currLocationSettings,\n      },\n      () => {\n        this.addWatermarks();\n      },\n    );\n  };\n\n  resetForm = (event) => {\n    event.preventDefault();\n    const locationSettings = this.resetLocationSettings();\n    this.setState(\n      {\n        locationSettings,\n      },\n      () => this.addWatermarks(),\n    );\n  };\n\n  onOkPressed = () => {\n    this.setState(\n      {\n        previousLocationSettings: this.state.locationSettings,\n      },\n      () => {\n        // the order of these fxn calls matter\n        this.props.modalClosed();\n        const watermarkOptions = this.createWatermarks();\n        this.props.formSubmitted(watermarkOptions);\n      },\n    );\n  };\n\n  setColorPaletteVisibility = (visible) => {\n    this.setState({ isColorPaletteVisible: visible });\n  };\n\n  onLocationChanged = (key) => {\n    const currLocationSettings = {\n      ...this.state.locationSettings,\n    };\n    Object.keys(currLocationSettings).forEach((locationKey) => {\n      let locationSetting = currLocationSettings[locationKey];\n      locationSetting = {\n        ...locationSetting,\n        isSelected: key === locationKey,\n      };\n      currLocationSettings[locationKey] = locationSetting;\n    });\n\n    this.setState(\n      {\n        locationSettings: currLocationSettings,\n      },\n      () => {\n        this.addWatermarks();\n      },\n    );\n  };\n\n  // eslint-disable-next-line class-methods-use-this\n  resetLocationSettings = () => {\n    const locationSettings = {};\n    Object.keys(WATERMARK_LOCATIONS).forEach((key) => {\n      // ignore location as it is redundant as we already have location key\n      const { ...others } = DEFAULT_VALS;\n      const temp = {\n        ...others,\n        isSelected: WATERMARK_LOCATIONS[key] === DEFAULT_VALS.location,\n      };\n      locationSettings[key] = temp;\n    });\n\n    return locationSettings;\n  };\n\n  // eslint-disable-next-line class-methods-use-this\n  initializeLocationSettings = () => {\n    const locationSettings = this.resetLocationSettings();\n\n    if (this.props.watermarkLocations) {\n      Object.keys(WATERMARK_LOCATIONS).forEach((key) => {\n        const watermarkStrVal = WATERMARK_LOCATIONS[key];\n        const tempWatermarkProps = this.props.watermarkLocations[WATERMARK_API_LOCATIONS[watermarkStrVal]] ?? false;\n        if (!tempWatermarkProps) {\n          return;\n        }\n\n        const temp = this.constructWatermarkOption(\n          tempWatermarkProps\n        );\n\n        locationSettings[key].text = temp.text;\n\n        const colorStr = tempWatermarkProps.color;\n        const colorArray = colorStr.slice(5).replace(')', '').split(',');\n        const color = new window.Core.Annotations.Color(\n          colorArray[0],\n          colorArray[1],\n          colorArray[2],\n          colorArray[3]\n        );\n\n        locationSettings[key].color = color;\n        locationSettings[key].opacity = temp.opacity;\n        locationSettings[key].fontSize = temp.fontSize;\n\n        if (tempWatermarkProps.fontStyles) {\n          locationSettings[key].isBolded = tempWatermarkProps['fontStyles'].includes('BOLD');\n          locationSettings[key].isItalic = tempWatermarkProps['fontStyles'].includes('ITALIC');\n          locationSettings[key].isUnderlined = tempWatermarkProps['fontStyles'].includes('UNDERLINE');\n        }\n\n        const fontFamily = tempWatermarkProps.fontFamily ?? false;\n\n        if (!fontFamily || fontFamily.trim().length === 0) {\n          locationSettings[key] = DEFAULT_VALS.font;\n        }\n      });\n    }\n\n    return locationSettings;\n  };\n\n  // eslint-disable-next-line class-methods-use-this\n  getKeyByValue = (object, value) => Object.keys(object).find((key) => object[key] === value);\n\n  getCurrentSelectedLocation = () => Object.keys(this.state.locationSettings).find((locationKey) => {\n    const locationSetting = this.state.locationSettings[locationKey];\n    return locationSetting.isSelected;\n  });\n\n  onColorChanged = (newColor) => {\n    const currLocation = this.getCurrentSelectedLocation();\n    const currLocationSetting = this.state.locationSettings[currLocation];\n    currLocationSetting[FORM_FIELD_KEYS.color] = new window.Core.Annotations.Color(\n      newColor.R,\n      newColor.G,\n      newColor.B,\n    );\n    const locationSettings = {\n      ...this.state.locationSettings,\n    };\n    if (!currLocationSetting[FORM_FIELD_KEYS.text]) {\n      // if text is undefined, persist the changed color to other location settings (customer's request)\n      Object.keys(WATERMARK_LOCATIONS).forEach((location) => {\n        const locationSetting = locationSettings[location];\n        if (!locationSetting[FORM_FIELD_KEYS.text]) {\n          locationSetting[FORM_FIELD_KEYS.color] = new window.Core.Annotations.Color(\n            newColor.R,\n            newColor.G,\n            newColor.B,\n          );\n        }\n      });\n    }\n    this.setState(\n      {\n        locationSettings,\n      },\n      () => {\n        this.addWatermarks();\n      },\n    );\n  };\n\n  render() {\n    const { isVisible } = this.props;\n    if (!isVisible) {\n      return null;\n    }\n\n    const { t, isCustomizableUI } = this.props;\n\n    const currLocation = this.getCurrentSelectedLocation();\n    const formInfo = this.state.locationSettings[currLocation];\n    const hexColor = formInfo[FORM_FIELD_KEYS.color].toHexString();\n    const dropdownHalfWidth = isMobile() ? DROPDOWN_MOBILE_WIDTH : DROPDOWN_WIDTH;\n    const dropdownFullWidth = isMobile() ? DROPDOWN_WIDTH_LONG : DROPDOWN_WIDTH;\n    return (\n      <DataElementWrapper\n        className={'Modal Watermark'}\n        id=\"watermarkModal\"\n        data-element=\"watermarkModal\"\n      >\n        <ModalWrapper\n          isOpen={this.state.lockFocus} title={'option.watermark.addWatermark'}\n          closeButtonDataElement={'watermarkModalCloseButton'}\n          onCloseClick={this.closeModal}\n          swipeToClose\n          closeHandler={this.closeModal}\n        >\n          <div className=\"swipe-indicator\" />\n\n          <div className=\"form-content-container\">\n            <div\n              className=\"canvas-container\"\n              ref={this.canvasContainerRef}\n            ></div>\n\n            <div className=\"watermark-settings\">\n              <form id=\"form\" onSubmit={(e) => e.preventDefault()}>\n                <div className=\"form-field\">\n                  <label className=\"section-label print-quality-section-label\" htmlFor=\"location\" id=\"watermark-location-dropdown-label\">{t('option.watermark.location')}</label>\n                  <Dropdown\n                    id=\"location\"\n                    labelledById='watermark-location-dropdown-label'\n                    dataElement=\"watermarkLocation\"\n                    items={Object.keys(WATERMARK_LOCATIONS)}\n                    getTranslationLabel={(key) => t(`option.watermark.locations.${WATERMARK_LOCATIONS[key]}`)}\n                    currentSelectionKey={currLocation}\n                    onClickItem={this.onLocationChanged}\n                    width={dropdownFullWidth}\n                  />\n                  <div className=\"separator divider\"></div>\n                </div>\n\n                <div className=\"form-field\">\n                  <label htmlFor=\"textInput\">{t('option.watermark.text')}</label>\n                  <input\n                    className=\"text-input\"\n                    id=\"textInput\"\n                    value={formInfo[FORM_FIELD_KEYS.text]}\n                    onChange={(event) => this.handleInputChange(\n                      FORM_FIELD_KEYS.text,\n                      event.target.value,\n                    )\n                    }\n                    type=\"text\"\n                  />\n                </div>\n                <div className=\"font-form-fields\">\n                  <div className=\"form-font-type\">\n                    <label htmlFor=\"fonts\" id=\"watermark-font-dropdown-label\">{t('option.watermark.font')}</label>\n                    <Dropdown\n                      id=\"fonts\"\n                      labelledById='watermark-font-dropdown-label'\n                      dataElement=\"watermarkFont\"\n                      items={FONTS}\n                      currentSelectionKey={formInfo[FORM_FIELD_KEYS.font]}\n                      onClickItem={(key) => this.handleInputChange(\n                        FORM_FIELD_KEYS.font,\n                        key\n                      )}\n                      width={dropdownHalfWidth}\n                    />\n                  </div>\n                  <div className=\"form-font-size\">\n                    <label htmlFor=\"fontSize\">{t('option.watermark.size')}</label>\n                    <FontSizeDropdown\n                      fontSize={formInfo[FORM_FIELD_KEYS.fontSize]}\n                      key=\"fontSize\"\n                      fontUnit=\"pt\"\n                      onFontSizeChange={(val) => this.handleInputChange(FORM_FIELD_KEYS.fontSize, Number.parseInt(val))}\n                      maxFontSize={1600}\n                      initialFontValue={1}\n                      initialMaxFontValue={512}\n                      width={dropdownHalfWidth}\n                    />\n                  </div>\n                </div>\n                <div className=\"form-field opacity-slider\" id=\"opacitySlider\">\n                  <Slider\n                    dataElement={'watermarkOpacitySlider'}\n                    property={'Opacity'}\n                    displayProperty={'opacity'}\n                    min={0}\n                    max={100}\n                    step={1}\n                    value={formInfo[FORM_FIELD_KEYS.opacity]}\n                    getDisplayValue={(opacity) => `${Math.round(opacity)}%`}\n                    withInputField={isCustomizableUI}\n                    inputFieldType={'number'}\n                    onSliderChange={() => { }}\n                    onStyleChange={(property, value) => this.handleInputChange(\n                      FORM_FIELD_KEYS.opacity,\n                      Math.round(value * 100),\n                    )}\n                    getLocalValue={(opacity) => parseInt(opacity) / 100}\n                  />\n                </div>\n                <div className=\"form-field\">\n                  <label>{t('option.watermark.style')}</label>\n                  <div className=\"style-container\">\n                    <Button\n                      id=\"currentColorCell\"\n                      // eslint-disable-next-line custom/no-hex-colors\n                      className={`colorSelect ${hexColor === '#FFFFFF' ? 'white-color' : ''}`}\n                      ariaLabel=\"colorSelectButton\"\n                      style={{\n                        backgroundColor: hexColor,\n                      }}\n                      onClick={() => this.setColorPaletteVisibility(\n                        !this.state.isColorPaletteVisible,\n                      )\n                      }\n                    />\n                    <div className=\"style-container\">\n                      <Button\n                        dataElement=\"boldText\"\n                        img=\"icon-text-bold\"\n                        isActive={formInfo[FORM_FIELD_KEYS.isBolded]}\n                        onClick={() => this.handleInputChange(\n                          FORM_FIELD_KEYS.isBolded,\n                          !formInfo[FORM_FIELD_KEYS.isBolded],\n                        )\n                        }\n                      />\n                      <Button\n                        dataElement=\"italicizeText\"\n                        img=\"icon-text-italic\"\n                        isActive={formInfo[FORM_FIELD_KEYS.isItalic]}\n                        onClick={() => this.handleInputChange(\n                          FORM_FIELD_KEYS.isItalic,\n                          !formInfo[FORM_FIELD_KEYS.isItalic],\n                        )\n                        }\n                      />\n                      <Button\n                        dataElement=\"underlineText\"\n                        img=\"icon-text-underline\"\n                        isActive={formInfo[FORM_FIELD_KEYS.isUnderlined]}\n                        onClick={() => this.handleInputChange(\n                          FORM_FIELD_KEYS.isUnderlined,\n                          !formInfo[FORM_FIELD_KEYS.isUnderlined],\n                        )\n                        }\n                      />\n                    </div>\n                  </div>\n\n                  {this.state.isColorPaletteVisible && (\n                    <div\n                      className={'Popup StylePopup'}\n                      id=\"stylePopup\"\n                      onClick={() => this.setColorPaletteVisibility(false)}\n                    >\n                      <ColorPalette\n                        color={formInfo[FORM_FIELD_KEYS.color]}\n                        property={'TextColor'} // arbitrary property name. this property isn't used in this file\n                        onStyleChange={(property, color) => {\n                          this.onColorChanged(color);\n                          this.setColorPaletteVisibility(false);\n                        }}\n                      />\n                    </div>\n                  )}\n\n                </div>\n              </form>\n            </div>\n          </div>\n\n          <div className=\"divider\"></div>\n          <div className=\"button-container\">\n            <button\n              className=\"reset-settings\"\n              id=\"reset\"\n              onClick={this.resetForm}\n            >\n              {t('option.watermark.resetAllSettings')}\n            </button>\n            <button\n              className=\"ok button\"\n              id=\"submit\"\n              onClick={this.onOkPressed}\n            >\n              {t('action.add')}\n            </button>\n          </div>\n        </ModalWrapper>\n      </DataElementWrapper>\n    );\n  }\n}\n\nexport default withTranslation()(WatermarkModal);\n", "import WatermarkModal from './WatermarkModal';\n\nexport default WatermarkModal;\n", "import React, { useEffect, useRef, useState } from 'react';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport classNames from 'classnames';\nimport core from 'core';\nimport PropTypes from 'prop-types';\nimport getClassName from 'helpers/getClassName';\nimport LayoutMode from 'constants/layoutMode';\nimport WatermarkModal from 'components/PrintModal/WatermarkModal';\nimport Choice from 'components/Choice/Choice';\nimport ModalWrapper from 'components/ModalWrapper';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport DataElements from 'constants/dataElement';\nimport DataElementWrapper from '../DataElementWrapper';\nimport Dropdown from '../Dropdown';\nimport PageNumberInput from '../PageReplacementModal/PageNumberInput';\nimport useFocusHandler from 'hooks/useFocusHandler';\nimport './PrintModal.scss';\nimport Button from '../Button';\n\nconst PrintModal = ({\n  isDisabled,\n  isOpen,\n  isApplyWatermarkDisabled,\n  isFullAPIEnabled,\n  currentPage,\n  printQuality,\n  isGrayscale,\n  setIsGrayscale,\n  setIsCurrentView,\n  isCurrentViewDisabled,\n  includeAnnotations,\n  setIncludeAnnotations,\n  includeComments,\n  setIncludeComments,\n  isWatermarkModalVisible,\n  setIsWatermarkModalVisible,\n  watermarkModalOptions,\n  existingWatermarksRef,\n  setAllowWatermarkModal,\n  closePrintModal,\n  createPagesAndPrint,\n  pagesToPrint,\n  setPagesToPrint,\n  count,\n  isPrinting,\n  layoutMode,\n  useEmbeddedPrint,\n  pageLabels\n}) => {\n  PrintModal.propTypes = {\n    isDisabled: PropTypes.bool,\n    isOpen: PropTypes.bool,\n    isApplyWatermarkDisabled: PropTypes.bool,\n    isFullAPIEnabled: PropTypes.bool,\n    currentPage: PropTypes.number,\n    printQuality: PropTypes.number,\n    isGrayscale: PropTypes.bool,\n    setIsGrayscale: PropTypes.func,\n    setIsCurrentView: PropTypes.func,\n    isCurrentViewDisabled: PropTypes.bool,\n    includeAnnotations: PropTypes.bool,\n    setIncludeAnnotations: PropTypes.func,\n    includeComments: PropTypes.bool,\n    setIncludeComments: PropTypes.func,\n    isWatermarkModalVisible: PropTypes.bool,\n    setIsWatermarkModalVisible: PropTypes.func,\n    watermarkModalOptions: PropTypes.object,\n    existingWatermarksRef: PropTypes.object,\n    setAllowWatermarkModal: PropTypes.func,\n    closePrintModal: PropTypes.func,\n    createPagesAndPrint: PropTypes.func,\n    pagesToPrint: PropTypes.array,\n    setPagesToPrint: PropTypes.func,\n    count: PropTypes.number,\n    isPrinting: PropTypes.bool,\n    layoutMode: PropTypes.string,\n    useEmbeddedPrint: PropTypes.bool,\n    pageLabels: PropTypes.array\n  };\n\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const allPages = useRef();\n  const currentPageRef = useRef();\n  const customPages = useRef();\n  const includeCommentsRef = useRef();\n  const currentView = useRef();\n  const [embedPrintValid, setEmbedPrintValid] = useState(false);\n  const [specifiedPages, setSpecifiedPages] = useState([]);\n  const [pageNumberError, setPageNumberError] = useState('');\n  const [isCustomPagesChecked, setIsCustomPagesChecked] = useState(false);\n\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n\n  const printQualityOptions = {\n    1: `${t('option.print.qualityNormal')}`,\n    2: `${t('option.print.qualityHigh')}`\n  };\n\n  const setWatermarkModalVisibility = (visible) => {\n    setIsWatermarkModalVisible(visible);\n  };\n\n  const className = getClassName('Modal PrintModal', { isOpen });\n\n  const handlePageNumberError = (pageNumber) => {\n    if (pageNumber) {\n      setPageNumberError(`${t('message.errorPageNumber')} ${core.getTotalPages()}`);\n    }\n  };\n\n  const handlePageNumberChange = (pageNumbers) => {\n    if (pageNumbers.length > 0) {\n      setPageNumberError('');\n      setSpecifiedPages(pageNumbers);\n    }\n  };\n\n  const customPagesLabelElement = (\n    <>\n      <label htmlFor=\"specifyPagesInput\" className=\"specifyPagesChoiceLabel\">\n        <span>{t('option.print.specifyPages')}</span>\n        {isCustomPagesChecked && (\n          <span className=\"specifyPagesExampleLabel\">\n            - {t('option.thumbnailPanel.multiSelectPagesExample')}\n          </span>\n        )}\n      </label>\n      {isCustomPagesChecked && (\n        <div className={classNames('page-number-input-container', { error: !!pageNumberError })}>\n          <PageNumberInput\n            id=\"specifyPagesInput\"\n            selectedPageNumbers={specifiedPages}\n            pageCount={core.getTotalPages()}\n            onSelectedPageNumbersChange={handlePageNumberChange}\n            onBlurHandler={setSpecifiedPages}\n            onError={handlePageNumberError}\n            pageNumberError={pageNumberError}\n            customPageLabels={pageLabels}\n            enablePageLabels={true}\n          />\n        </div>\n      )}\n    </>\n  );\n\n  useEffect(() => {\n    onChange();\n  }, [specifiedPages]);\n\n  const onChange = () => {\n    let pagesToPrint = [];\n    setIsCurrentView(currentView.current?.checked);\n    setIsCustomPagesChecked(customPages.current?.checked);\n    if (allPages.current?.checked || (currentView.current?.checked && embedPrintValid)) {\n      for (let i = 1; i <= core.getTotalPages(); i++) {\n        pagesToPrint.push(i);\n      }\n    } else if (currentPageRef.current?.checked) {\n      const pageCount = core.getTotalPages();\n\n      // when displaying 2 pages, \"Current\" should print both of them\n      switch (layoutMode) {\n        case LayoutMode.FacingCover:\n        case LayoutMode.FacingCoverContinuous:\n          if (\n            currentPage === 1 ||\n            (currentPage === pageCount && pageCount % 2 === 0)\n          ) {\n            // first page or last page if single page\n            pagesToPrint.push(currentPage);\n          } else {\n            pagesToPrint =\n              currentPage % 2\n                ? [currentPage - 1, currentPage]\n                : [currentPage, currentPage + 1];\n          }\n          break;\n        case LayoutMode.FacingContinuous:\n        case LayoutMode.Facing:\n          if (currentPage === pageCount && pageCount % 2 === 1) {\n            // last page if single page\n            pagesToPrint.push(currentPage);\n          } else {\n            pagesToPrint =\n              currentPage % 2\n                ? [currentPage, currentPage + 1]\n                : [currentPage - 1, currentPage];\n          }\n          break;\n        default:\n          pagesToPrint.push(currentPage);\n          break;\n      }\n    } else if (customPages.current?.checked) {\n      pagesToPrint = specifiedPages;\n    } else if (currentView.current?.checked) {\n      pagesToPrint = [currentPage];\n    }\n\n    setPagesToPrint(pagesToPrint);\n  };\n\n  useEffect(() => {\n    onChange();\n\n    core.getWatermark().then((watermark) => {\n      setAllowWatermarkModal(\n        watermark === undefined ||\n        watermark === null ||\n        Object.keys(watermark).length === 0\n      );\n      existingWatermarksRef.current = watermark;\n    });\n\n    return () => {\n      core.setWatermark(existingWatermarksRef.current);\n      setIsWatermarkModalVisible(false);\n    };\n  }, []);\n\n  useEffect(() => {\n    (core.getDocument().getType() !== 'xod' && useEmbeddedPrint) ? setEmbedPrintValid(true) : setEmbedPrintValid(false);\n  }, [useEmbeddedPrint]);\n\n  const handlePrintQualityChange = (quality) => {\n    dispatch(actions.setPrintQuality(Number(quality)));\n  };\n\n  const openWaterMarkModalWithFocusTransfer = useFocusHandler(() => {\n    if (!isPrinting) {\n      setWatermarkModalVisibility(true);\n    }\n  });\n\n  return isDisabled ? null : (\n    <>\n      <WatermarkModal\n        isVisible={!!(isOpen && isWatermarkModalVisible)}\n        // pageIndex starts at index 0 and getCurrPage number starts at index 1\n        pageIndexToView={currentPage - 1}\n        modalClosed={setWatermarkModalVisibility}\n        formSubmitted={(value) => dispatch(actions.setWatermarkModalOptions(value))}\n        watermarkLocations={watermarkModalOptions}\n        isCustomizableUI={customizableUI}\n      />\n      <div\n        className={className}\n        data-element={DataElements.PRINT_MODAL}\n      >\n        <ModalWrapper\n          isOpen={isOpen && !isWatermarkModalVisible} title={'option.print.printSettings'}\n          containerOnClick={(e) => e.stopPropagation()} onCloseClick={closePrintModal}\n          closeButtonDataElement={'printModalCloseButton'}\n          swipeToClose\n          closeHandler={closePrintModal}\n        >\n          <div className=\"swipe-indicator\" />\n          <div className=\"settings\">\n            <div className=\"section\">\n              <div className=\"section-label\">{`${t('option.print.pages')}:`}</div>\n              <form\n                className=\"settings-form\"\n                onChange={onChange}\n                onSubmit={createPagesAndPrint}\n              >\n                <Choice\n                  dataElement=\"allPagesPrintOption\"\n                  ref={allPages}\n                  id=\"all-pages\"\n                  name=\"pages\"\n                  radio\n                  label={t('option.print.all')}\n                  defaultChecked\n                  disabled={isPrinting}\n                  center\n                />\n                <Choice\n                  dataElement=\"currentPagePrintOption\"\n                  ref={currentPageRef}\n                  id=\"current-page\"\n                  name=\"pages\"\n                  radio\n                  label={t('option.print.current')}\n                  disabled={isPrinting}\n                  center\n                />\n                <Choice\n                  dataElement=\"currentViewPrintOption\"\n                  ref={currentView}\n                  id=\"current-view\"\n                  name=\"pages\"\n                  radio\n                  label={t('option.print.view')}\n                  disabled={isCurrentViewDisabled}\n                  center\n                  title={t('option.print.printCurrentDisabled')}\n                />\n                <Choice\n                  dataElement=\"customPagesPrintOption\"\n                  ref={customPages}\n                  id=\"custom-pages\"\n                  name=\"pages\"\n                  className=\"specify-pages-choice\"\n                  radio\n                  label={customPagesLabelElement}\n                  disabled={isPrinting}\n                  center\n                />\n                <Choice\n                  dataElement=\"annotationsPrintOption\"\n                  id=\"include-annotations\"\n                  name=\"annotations\"\n                  label={t('option.print.includeAnnotations')}\n                  disabled={isPrinting}\n                  onChange={() => setIncludeAnnotations((prevState) => !prevState)}\n                  checked={includeAnnotations}\n                  center\n                />\n                {embedPrintValid && (\n                  <>\n                    {\n                      isFullAPIEnabled && (\n                        <>\n                          <Choice\n                            dataElement=\"grayscalePrintOption\"\n                            id=\"print-grayscale\"\n                            name=\"grayscale\"\n                            label={t('option.print.printGrayscale')}\n                            disabled={isPrinting}\n                            onChange={() => setIsGrayscale((prevState) => !prevState)}\n                            checked={isGrayscale}\n                            center\n                          />\n                          <Choice\n                            dataElement=\"commentsPrintOption\"\n                            ref={includeCommentsRef}\n                            id=\"include-comments\"\n                            name=\"comments\"\n                            label={t('option.print.includeComments')}\n                            onChange={() => setIncludeComments((prevState) => !prevState)}\n                            disabled={isPrinting}\n                            checked={includeComments}\n                            center\n                          />\n                        </>\n                      )\n                    }\n                  </>\n                )}\n                {!embedPrintValid && (\n                  <>\n                    <Choice\n                      dataElement=\"grayscalePrintOption\"\n                      id=\"print-grayscale\"\n                      name=\"grayscale\"\n                      label={t('option.print.printGrayscale')}\n                      disabled={isPrinting}\n                      onChange={() => setIsGrayscale((prevState) => !prevState)}\n                      checked={isGrayscale}\n                      center\n                    />\n                    <Choice\n                      dataElement=\"commentsPrintOption\"\n                      ref={includeCommentsRef}\n                      id=\"include-comments\"\n                      name=\"comments\"\n                      label={t('option.print.includeComments')}\n                      onChange={() => setIncludeComments((prevState) => !prevState)}\n                      disabled={isPrinting}\n                      checked={includeComments}\n                      center\n                    />\n                  </>\n                )}\n              </form>\n            </div>\n            {!embedPrintValid && (\n              <DataElementWrapper className=\"section\" dataElement={DataElements.PRINT_QUALITY}>\n                <label className=\"section-label print-quality-section-label\" htmlFor=\"printQualityOptions\" id=\"print-quality-options-label\">{`${t('option.print.pageQuality')}:`}</label>\n                <Dropdown\n                  id=\"printQualityOptions\"\n                  labelledById='print-quality-options-label'\n                  dataElement=\"printQualityOptions\"\n                  items={Object.keys(printQualityOptions)}\n                  getDisplayValue={(item) => printQualityOptions[item]}\n                  onClickItem={handlePrintQualityChange}\n                  currentSelectionKey={printQuality?.toString()}\n                  width={274}\n                />\n              </DataElementWrapper>\n            )}\n            <div className=\"total\">\n              {isPrinting ? (\n                <div>{`${t('message.processing')} ${count}/${pagesToPrint.length}`}</div>\n              ) : (\n                <div>{t('message.printTotalPageCount', { count: pagesToPrint.length })}</div>\n              )}\n            </div>\n            {!isApplyWatermarkDisabled && (\n              <DataElementWrapper className=\"section watermark-section\" dataElement={DataElements.PRINT_WATERMARK}>\n                <div className=\"section-label\">{t('option.watermark.title')}</div>\n                <button\n                  data-element=\"applyWatermark\"\n                  className=\"apply-watermark\"\n                  disabled={isPrinting}\n                  onClick={openWaterMarkModalWithFocusTransfer}\n                >\n                  {t('option.watermark.addNew')}\n                </button>\n              </DataElementWrapper>\n            )}\n          </div>\n          <div className=\"divider\"></div>\n          <div className=\"buttons\">\n            <Button\n              className=\"button\"\n              onClick={createPagesAndPrint}\n              label={t('action.print')}\n              ariaLabel={t('action.print')}\n            />\n          </div>\n        </ModalWrapper>\n      </div>\n    </>\n  );\n};\n\nexport default PrintModal;\n", "import React, { useState, useEffect, useRef } from 'react';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport DataElements from 'constants/dataElement';\n\nimport core from 'core';\n\nimport { printPages } from 'helpers/print';\nimport { createRasterizedPrintPages } from 'helpers/rasterPrint';\nimport { processEmbeddedPrintOptions, printEmbeddedPDF } from 'helpers/embeddedPrint';\nimport PrintModal from './PrintModal';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nimport './PrintModal.scss';\n\nconst PrintModalContainer = () => {\n  const dispatch = useDispatch();\n  const [\n    isDisabled,\n    isOpen,\n    isApplyWatermarkDisabled,\n    currentPage,\n    printQuality,\n    defaultPrintOptions,\n    pageLabels,\n    sortStrategy,\n    colorMap,\n    layoutMode,\n    printedNoteDateFormat,\n    language,\n    watermarkModalOptions,\n    timezone,\n    useEmbeddedPrint,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementDisabled(state, DataElements.PRINT_MODAL),\n      selectors.isElementOpen(state, DataElements.PRINT_MODAL),\n      selectors.isElementDisabled(state, 'applyWatermark'),\n      selectors.getCurrentPage(state),\n      selectors.getPrintQuality(state),\n      selectors.getDefaultPrintOptions(state),\n      selectors.getPageLabels(state, 'pageLabels'),\n      selectors.getSortStrategy(state),\n      selectors.getColorMap(state),\n      selectors.getDisplayMode(state),\n      selectors.getPrintedNoteDateFormat(state),\n      selectors.getCurrentLanguage(state),\n      selectors.getWatermarkModalOptions(state),\n      selectors.getTimezone(state),\n      selectors.isEmbedPrintSupported(state, 'useEmbeddedPrint')\n    ],\n    shallowEqual\n  );\n\n  const existingWatermarksRef = useRef();\n\n  const [allowWatermarkModal, setAllowWatermarkModal] = useState(false);\n  const [count, setCount] = useState(-1);\n\n  const [maintainPageOrientation, setMaintainPageOrientation] = useState(false);\n  const [pagesToPrint, setPagesToPrint] = useState([]);\n  const [isGrayscale, setIsGrayscale] = useState(false);\n  const [isWatermarkModalVisible, setIsWatermarkModalVisible] = useState(false);\n  const [includeAnnotations, setIncludeAnnotations] = useState(true);\n  const [includeComments, setIncludeComments] = useState(false);\n  const [isCurrentView, setIsCurrentView] = useState(false);\n  const [isCurrentViewDisabled, setIsCurrentViewDisabled] = useState(false);\n\n  useEffect(() => {\n    if (defaultPrintOptions) {\n      setIncludeAnnotations(defaultPrintOptions.includeAnnotations ?? includeAnnotations);\n      setIncludeComments(defaultPrintOptions.includeComments ?? includeComments);\n      setMaintainPageOrientation(defaultPrintOptions.maintainPageOrientation ?? maintainPageOrientation);\n    }\n  }, [defaultPrintOptions]);\n\n  const isPrinting = count >= 0;\n\n  useEffect(() => {\n    if (isOpen) {\n      if (useEmbeddedPrint) {\n        checkCurrentView();\n      }\n\n      dispatch(actions.closeElements([\n        DataElements.SIGNATURE_MODAL,\n        DataElements.LOADING_MODAL,\n        DataElements.PROGRESS_MODAL,\n        DataElements.ERROR_MODAL,\n      ]));\n    }\n  }, [isOpen, dispatch]);\n\n  const checkCurrentView = () => {\n    if (isCurrentViewDisabled) {\n      return;\n    }\n\n    const virtualDisplayMode = core.getDisplayModeObject();\n    const visiblePagesArray = virtualDisplayMode.getVisiblePages(0, 0);\n\n    if (visiblePagesArray?.length > 1) {\n      setIsCurrentViewDisabled(true);\n    } else {\n      setIsCurrentViewDisabled(false);\n    }\n  };\n\n  const createPagesAndPrint = (e) => {\n    const document = core.getDocument();\n    const fileType = document.getType();\n\n    if (useEmbeddedPrint && fileType === 'xod') {\n      console.warn('Falling back to raster printing, XOD files and Embedded Printing is not supported');\n    }\n\n    if (useEmbeddedPrint && fileType !== 'xod') {\n      embeddedPrinting();\n    } else {\n      rasterPrinting(e);\n    }\n  };\n\n  const embeddedPrinting = async () => {\n    const printingOptions = { isCurrentView, includeAnnotations, includeComments, watermarkModalOptions, pagesToPrint };\n    const document = core.getDocument();\n    const annotManager = core.getAnnotationManager();\n    printEmbeddedPDF(await processEmbeddedPrintOptions(printingOptions, document, annotManager));\n  };\n\n  const rasterPrinting = (e) => {\n    e.preventDefault();\n\n    if (pagesToPrint.length < 1) {\n      return;\n    }\n\n    setCount(0);\n\n    if (allowWatermarkModal) {\n      core.setWatermark(watermarkModalOptions);\n    } else {\n      core.setWatermark(existingWatermarksRef.current);\n    }\n\n    const printOptions = {\n      includeComments,\n      includeAnnotations,\n      maintainPageOrientation,\n      printQuality,\n      sortStrategy,\n      colorMap: colorMap,\n      printedNoteDateFormat,\n      isCurrentView,\n      language,\n      timezone,\n      createCanvases: false,\n      isGrayscale\n    };\n\n    const createPages = createRasterizedPrintPages(\n      pagesToPrint,\n      printOptions,\n      undefined\n    );\n    createPages.forEach(async (pagePromise) => {\n      await pagePromise;\n      setCount(count < pagesToPrint.length && (count !== -1 ? count + 1 : count));\n    });\n    Promise.all(createPages)\n      .then((pages) => {\n        printPages(pages);\n        closePrintModalAfterPrint();\n      })\n      .catch((e) => {\n        console.error(e);\n        setCount(-1);\n      });\n  };\n\n\n  const closePrintModal = () => {\n    setCount(-1);\n    dispatch(actions.closeElement(DataElements.PRINT_MODAL));\n  };\n\n  const closePrintModalAfterPrint = useFocusOnClose(closePrintModal);\n\n  return (\n    <PrintModal\n      isDisabled={isDisabled}\n      isOpen={isOpen}\n      isApplyWatermarkDisabled={isApplyWatermarkDisabled}\n      isFullAPIEnabled={core.isFullPDFEnabled()}\n      currentPage={currentPage}\n      printQuality={printQuality}\n      isGrayscale={isGrayscale}\n      setIsGrayscale={setIsGrayscale}\n      setIsCurrentView={setIsCurrentView}\n      isCurrentViewDisabled={isCurrentViewDisabled}\n      checkCurrentView={checkCurrentView}\n      includeAnnotations={includeAnnotations}\n      setIncludeAnnotations={setIncludeAnnotations}\n      includeComments={includeComments}\n      setIncludeComments={setIncludeComments}\n      isWatermarkModalVisible={isWatermarkModalVisible}\n      setIsWatermarkModalVisible={setIsWatermarkModalVisible}\n      watermarkModalOptions={watermarkModalOptions}\n      existingWatermarksRef={existingWatermarksRef}\n      setAllowWatermarkModal={setAllowWatermarkModal}\n      closePrintModal={closePrintModal}\n      createPagesAndPrint={createPagesAndPrint}\n      pagesToPrint={pagesToPrint}\n      setPagesToPrint={setPagesToPrint}\n      count={count}\n      isPrinting={isPrinting}\n      pageLabels={pageLabels}\n      layoutMode={layoutMode}\n      useEmbeddedPrint={useEmbeddedPrint}\n    />\n  );\n};\n\nexport default PrintModalContainer;\n", "import PrintModalContainer from './PrintModalContainer';\n\nexport default PrintModalContainer;\n"], "sourceRoot": ""}