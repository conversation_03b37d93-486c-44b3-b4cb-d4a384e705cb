{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/tl-ph.js"], "names": ["module", "exports", "e", "n", "default", "a", "t", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,yDAAyDC,MAAM,KAAKC,OAAO,0FAA0FD,MAAM,KAAKE,UAAU,EAAEC,cAAc,8BAA8BH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,YAAY,wBAAwBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,YAAYC,GAAG,eAAeC,IAAI,qBAAqBC,KAAK,6BAA6BC,aAAa,CAACC,OAAO,gBAAgBC,KAAK,mBAAmBC,EAAE,gBAAgBC,EAAE,eAAeC,GAAG,YAAYC,EAAE,aAAaC,GAAG,UAAUC,EAAE,aAAaC,GAAG,UAAUC,EAAE,cAAcC,GAAG,WAAWC,EAAE,aAAaC,GAAG,YAAY,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAA3jCD,CAAE,EAAQ", "file": "chunks/chunk.227.js", "sourcesContent": ["!function(e,a){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=a(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],a):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_tl_ph=a(e.dayjs)}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=a(e),t={name:\"tl-ph\",weekdays:\"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado\".split(\"_\"),months:\"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre\".split(\"_\"),weekStart:1,weekdaysShort:\"Lin_Lun_Mar_Miy_Huw_Biy_Sab\".split(\"_\"),monthsShort:\"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis\".split(\"_\"),weekdaysMin:\"Li_Lu_Ma_Mi_Hu_Bi_Sab\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"MM/D/YYYY\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY HH:mm\",LLLL:\"dddd, MMMM DD, YYYY HH:mm\"},relativeTime:{future:\"sa loob ng %s\",past:\"%s ang nakalipas\",s:\"ilang segundo\",m:\"isang minuto\",mm:\"%d minuto\",h:\"isang oras\",hh:\"%d oras\",d:\"isang araw\",dd:\"%d araw\",M:\"isang buwan\",MM:\"%d buwan\",y:\"isang taon\",yy:\"%d taon\"}};return n.default.locale(t,null,!0),t}));"], "sourceRoot": ""}