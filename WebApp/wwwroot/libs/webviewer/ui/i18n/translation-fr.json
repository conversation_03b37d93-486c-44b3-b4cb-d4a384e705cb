{"action": {"addParagraph": "Ajouter un paragraphe", "add": "Ajouter", "addSheet": "Ajouter une feuille", "apply": "Appliquer", "applyAll": "Appliquer tout", "calendar": "Calandre", "calibrate": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "confirm": "Confirmer", "currentPageIs": "La page actuelle est", "clear": "<PERSON><PERSON><PERSON><PERSON>", "clearAll": "Tout effacer", "close": "<PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "comment": "<PERSON><PERSON><PERSON>", "reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "cut": "Couper", "paste": "Pâ<PERSON>", "pasteWithoutFormatting": "Coller sans formater", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleted": "Supprimé", "group": "Grouper", "ungroup": "Dissocier", "download": "Télécharger", "edit": "Modifier", "collapse": "Effondrement", "expand": "Développer", "extract": "Extraire", "extractPage": "Extraire la page", "enterFullscreen": "Plein écran", "exitFullscreen": "<PERSON><PERSON><PERSON> le plein écran", "fit": "Ajuster", "fitToPage": "Ajuster à la page", "fitToWidth": "Ajuster à la largeur", "more": "Autres ...", "openFile": "<PERSON><PERSON><PERSON><PERSON><PERSON> un fichier", "showMoreFiles": "Afficher plus de fi<PERSON>ers", "page": "Page", "of": "de", "pagePrev": "<PERSON> p<PERSON>", "pageNext": "<PERSON> suivante", "pageSet": "Mise en page", "print": "<PERSON><PERSON><PERSON><PERSON>", "proceed": "<PERSON><PERSON><PERSON>", "name": "Nommer", "rename": "<PERSON>mmer", "remove": "<PERSON><PERSON><PERSON>", "ok": "OK", "rotate": "Rotation", "rotate3D": "<PERSON><PERSON>", "rotateClockwise": "Rotation horaire", "rotateCounterClockwise": "Rotation antihoraire", "rotatedClockwise": "tourné dans le sens des aiguilles d'une montre", "rotatedCounterClockwise": "tourné dans le sens inverse des aiguilles d'une montre", "rotationIs": "la rotation actuelle de la page est", "movedToBottomOfDocument": "déplacé au bas du document", "movedToTopofDocument": "déplacé en haut du document", "extracted": "extrait", "save": "Enregistrer", "post": "Publier", "create": "<PERSON><PERSON><PERSON>", "update": "Mise à jour", "showMoreResults": "Afficher plus de résultats", "sign": "Signer", "style": "Style", "submit": "So<PERSON><PERSON><PERSON>", "zoom": "Zoom", "zoomIn": "Zoom avant", "zoomOut": "Zoom arri<PERSON>", "zoomSet": "Définir le zoom", "zoomChanged": "Le zoom actuel est", "zoomControls": "Commandes de zoom", "draw": "<PERSON><PERSON><PERSON>", "type": "Type", "upload": "Télécharger", "link": "<PERSON><PERSON>", "unlink": "Supprimer le lien", "fileAttachmentDownload": "Télécharger le fichier joint", "prevResult": "Résultat précédent", "nextResult": "Résultat suivant", "prev": "p<PERSON><PERSON><PERSON>", "next": "suivant", "startFormEditing": "Commencer l'édition du formulaire", "exitFormEditing": "<PERSON><PERSON><PERSON> le mode d'édition de formulaire", "exit": "<PERSON><PERSON><PERSON>", "addOption": "Ajouter une option", "formFieldEdit": "Modifier le champ de formulaire", "formFieldEditMode": "Modifier les champs du formulaire", "contentEditMode": "Modifier le contenu", "viewShortCutKeysFor3D": "Afficher les touches de raccourci", "markAllRead": "<PERSON><PERSON> tout comme lu", "pageInsertion": "Insertion de pages", "insertPage": "Insertion de pages", "insert": "<PERSON><PERSON><PERSON><PERSON>", "pageManipulation": "Manipulation des pages", "replace": "<PERSON><PERSON>lace<PERSON>", "replacePage": "Remplace<PERSON> la page", "modal": "modal", "isOpen": "est ouvert", "setDestination": "Destination choisie", "showLess": "<PERSON><PERSON> moins", "showMore": "...Suite", "chooseFile": "Choisissez un fichier", "changeDate": "Date de modification", "browse": "Parcourir les fichiers", "selectYourOption": "Sélectionnez votre option", "open": "Ouvert", "deselectAll": "<PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moveToTop": "<PERSON><PERSON><PERSON><PERSON> vers le haut", "moveToBottom": "<PERSON><PERSON><PERSON><PERSON> vers le bas", "movePageToTop": "<PERSON><PERSON><PERSON><PERSON> la page vers le haut", "movePageToBottom": "<PERSON><PERSON><PERSON><PERSON> la page vers le bas", "moveUp": "<PERSON><PERSON>", "moveDown": "Descendre", "moveLeft": "<PERSON><PERSON><PERSON>r vers la gauche", "moveRight": "Déplacez-vous vers la droite", "backToMenu": "Retour au menu", "redactPages": "Masquer les pages", "playAudio": "Lecture audio", "pauseAudio": "Pause audio", "selectAll": "<PERSON><PERSON>", "unselect": "Désé<PERSON><PERSON>ner", "addMark": "Ajouter une marque", "viewFile": "Voir la fiche", "multiReplyAnnotations": "Répondre aux annotations sélectionnées ({{count}})", "comparePages": "Comparer les pages", "startComparison": "Commencer la comparaison", "showComparison": "Afficher la comparaison", "highlightChanges": "Mettre en évidence les changements", "back": "Retour", "clearSignature": "Signature claire", "clearInitial": "<PERSON>e claire", "readOnlySignature": "La signature en lecture seule ne peut pas être supprimée", "newDocument": "Nouveau document", "sideBySideView": "<PERSON>ue côte à côte", "pageNumberInput": "<PERSON><PERSON> du numéro de page", "addNewColor": "Ajouter une nouvelle couleur", "deleteColor": "Supprimer la couleur sélectionnée", "copySelectedColor": "<PERSON><PERSON>r la couleur sélectionnée", "showMoreColors": "Afficher plus de couleurs", "showLessColors": "Afficher moins de couleurs", "fromCustomColorPicker": "à partir du sélecteur de couleurs personnalisé", "newSpreadsheetDocument": "Nouvelle feuille de calcul"}, "annotation": {"areaMeasurement": "Surface mesurée", "arc": "Arc", "arcMeasurement": "Mesure d'arc", "arrow": "Flèche", "callout": "Faire appel à", "crop": "<PERSON><PERSON><PERSON>", "caret": "Insertion", "dateFreeText": "<PERSON><PERSON><PERSON>", "formFillCheckmark": "<PERSON><PERSON>", "formFillCross": "Traverser", "distanceMeasurement": "Mesure de distance", "rectangularAreaMeasurement": "Zone rectangulaire", "ellipseMeasurement": "Zone elliptique", "countMeasurement": "Mesure de décompte", "ellipse": "Ellipse", "eraser": "Gomme à effacer", "fileattachment": "Pièce jointe", "freehand": "<PERSON><PERSON><PERSON>", "freeHandHighlight": "Mise en évidence de la main libre", "freetext": "Texte libre", "markInsertText": "Insérer du texte", "markReplaceText": "<PERSON><PERSON><PERSON><PERSON> le texte", "highlight": "<PERSON><PERSON><PERSON><PERSON>", "image": "Image", "line": "Ligne", "perimeterMeasurement": "<PERSON><PERSON><PERSON><PERSON><PERSON> mesure", "polygon": "Polygone", "polygonCloud": "Nuage", "polyline": "Ligne polygonale", "rectangle": "Rectangle", "redact": "Caviardage", "formFillDot": "Point", "signature": "Signature", "snipping": "<PERSON><PERSON>", "squiggly": "Ligne ondulée", "stamp": "Tampon", "stickyNote": "Note collante", "strikeout": "<PERSON><PERSON>", "underline": "Soulignement", "custom": "Personnalisation", "rubberStamp": "Estampille", "note": "<PERSON><PERSON><PERSON>", "textField": "Champ de texte", "signatureFormField": "Champ de signature", "checkBoxFormField": "Champ de case à cocher", "radioButtonFormField": "Champ de bouton radio", "listBoxFormField": "Champ de zone de liste", "comboBoxFormField": "Champ de zone de liste déroulante", "link": "<PERSON><PERSON>", "other": "<PERSON><PERSON>", "3D": "3D", "sound": "<PERSON><PERSON>", "changeView": "Change de vue", "newImage": "Nouvelle image", "defaultCustomStampTitle": "<PERSON><PERSON><PERSON>"}, "rubberStamp": {"Approved": "A<PERSON><PERSON><PERSON><PERSON>", "AsIs": "Tel quel", "Completed": "Complet", "Confidential": "Confidentiel", "Departmental": "Départemental", "Draft": "Brouillon", "Experimental": "Expérimental", "Expired": "Expiré", "Final": "Final", "ForComment": "Pour commentaire", "ForPublicRelease": "Pour diffusion publique", "InformationOnly": "Information seulement", "NotApproved": "Non approuvé", "NotForPublicRelease": "Pas pour diffusion publique", "PreliminaryResults": "Résultats préliminaires", "Sold": "V<PERSON><PERSON>", "TopSecret": "Top Secret", "Void": "<PERSON><PERSON><PERSON>", "SHSignHere": "<PERSON><PERSON> ici", "SHWitness": "<PERSON><PERSON><PERSON><PERSON>", "SHInitialHere": "Initiales", "SHAccepted": "Accepté", "SBRejected": "<PERSON><PERSON><PERSON>"}, "component": {"attachmentPanel": "Pièces jointes", "leftPanel": "Panneau de gauche", "toolsHeader": "Outils", "searchOverlay": "Recherche", "searchPanel": "<PERSON><PERSON><PERSON>", "menuOverlay": "<PERSON><PERSON>", "notesPanel": "<PERSON><PERSON><PERSON>", "indexPanel": "Panneau d'index", "outlinePanel": "É<PERSON>ment", "outlinesPanel": "Plan", "newOutlineTitle": "Nouveau titre du plan", "outlineTitle": "Titre du plan", "destination": "Destination", "bookmarkPanel": "Signet", "bookmarksPanel": "Signets", "bookmarkTitle": "Titre du signet", "bookmarkPage": "Page", "signaturePanel": "Signatures", "layersPanel": "Couches", "thumbnailsPanel": "Vignettes", "toolsButton": "Outils", "redaction": "Caviardage", "viewControls": "Contrôles de l'affichage", "pageControls": "Contr<PERSON><PERSON> de page", "calibration": "Calibration", "zoomOverlay": "Superposition de zoom", "textPopup": "<PERSON>up texte", "createStampButton": "Créer un nouveau tampon", "filter": "Filtre", "multiSelectButton": "Sélection multiple", "pageReplaceModalTitle": "Remplace<PERSON> la page", "files": "Des dossiers", "file": "<PERSON><PERSON><PERSON>r", "editText": "É<PERSON><PERSON> le texte", "redactionPanel": "Panneau de rédaction", "tabLabel": "<PERSON><PERSON><PERSON>", "noteGroupSection": {"open": "Afficher toutes les annotations", "close": "<PERSON><PERSON><PERSON> toutes les annotations"}, "comparePanel": "Panneau de comparaison", "watermarkPanel": "Panneau Fi<PERSON>grane", "mainMenu": "<PERSON>u principal"}, "message": {"showMore": "Montre plus", "showLess": "<PERSON><PERSON> moins", "toolsOverlayNoPresets": "Aucun <PERSON>", "badDocument": "Échec du chargement du document. Le document est corrompu ou non valide.", "customPrintPlaceholder": "par ex. : 3 4-10", "encryptedAttemptsExceeded": "Échec du chargement du document chiffré. Trop de tentatives.", "encryptedUserCancelled": "Échec du chargement du document chiffré. Saisie du mot de passe annulée.", "enterPassword": "Ce document est protégé par un mot de passe. Veuillez saisir ce mot de passe.", "incorrectPassword": "<PERSON><PERSON> de passe incorrect. Tentatives restantes: {{remainingAttempts}}", "noAnnotations": "Ce document n'a pas d'annotations.", "noAnnotationsReadOnly": "Ce document n'a pas d'annotations.", "noAnnotationsFilter": "Commencez à créer des annotations et des filtres apparaîtront ici.", "noBookmarks": "Aucun signet disponible", "noOutlines": "Ce document n'a pas de plan.", "noAttachments": "Ce document n'a pas de pièces jointes.", "noResults": "Aucun résultat trouvé.", "numResultsFound": "Résultats trouvés", "loadError": "Erreur lors du chargement du document", "notSupported": "Ce type de fichier n'est pas pris en charge.", "passwordRequired": "Mot de passe requis", "enterPasswordPlaceholder": "Entrer le mot de passe", "preparingToPrint": "Préparation à l'impression...", "annotationReplyCount": "{{count}} réponse", "annotationReplyCount_plural": "{{count}} réponses", "printTotalPageCount": "{{count}} page", "printTotalPageCount_plural": "{{count}} pages", "processing": "Traitement...", "searching": "Recherche...", "searchCommentsPlaceholder": "Rechercher des commentaires", "searchDocumentPlaceholder": "Rechercher un document", "searchSettingsPlaceholder": "Paramètres de recherche", "searchSuggestionsPlaceholder": "Suggestions de recherche", "signHere": "Signer ici", "insertTextHere": "Insérer du texte ici", "imageSignatureAcceptedFileTypes": "Seuls {{acceptedFileTypes}} sont acceptés", "signatureRequired": "Une signature et une initiale sont requises pour continuer", "enterMeasurement": "Entrez la mesure entre les deux points", "errorEnterMeasurement": "Le nombre que vous avez entré est invalide, vous pouvez entrer des valeurs comme 7,5 ou 7 1/2", "linkURLorPage": "Lien URL ou une page", "warning": "Avertissement", "svgMalicious": "Script SVG ignoré pour des raisons de sécurité", "doNotShowAgain": "Ne me montre plus jamais ça", "doNotAskAgain": "Ne demande plus", "enterReplacementText": "Entrez le texte que vous souhaitez remplacer", "sort": "<PERSON><PERSON>", "sortBy": "Trier par", "emptyCustomStampInput": "Le texte du tampon ne peut pas être vide", "unpostedComment": "Commentaire non publié", "lockedLayer": "Le calque est verrouillé", "layerVisibililtyNoChange": "La visibilité de la couche ne peut pas être modifiée", "noLayers": "Ce document ne comporte aucun calque.", "noSignatureFields": "Ce document n'a pas de champs de signature.", "untitled": "Sans titre", "selectHowToLoadFile": "Sélectionnez comment charger votre document", "openFileByUrl": "<PERSON><PERSON><PERSON><PERSON><PERSON> le fichier par URL:", "enterUrlHere": "Entrez l'URL ici", "openLocalFile": "Ouvrir le fichier local:", "selectFile": "Choi<PERSON> le dossier", "selectPageToReplace": "Sélectionnez les pages du document que vous souhaitez remplacer.", "embeddedFiles": "Fichiers intégrés", "pageNum": "Page", "viewBookmark": "A<PERSON><PERSON><PERSON> le signet sur la page", "error": "<PERSON><PERSON><PERSON>", "errorPageNumber": "Numéro de page invalide. La limite est", "errorBlankPageNumber": "Veuillez indiquer un numéro de page", "errorLoadingDocument": "Un problème est survenu lors de la lecture de ce document et certaines pages peuvent ne pas s'afficher. Cela indique que le document est peut-être corrompu. Le nombre total de pages est de {{totalPageCount}} et le nombre de pages affichées est de {{displayedPageCount}}.", "noRevisions": "Ce document n'a aucune révision."}, "option": {"type": {"caret": "manque", "custom": "<PERSON><PERSON><PERSON><PERSON>", "ellipse": "Ellipse", "fileattachment": "Pièce jointe", "freehand": "Main libre", "callout": "Faire appel à", "freetext": "Texte libre", "line": "Ligne", "polygon": "Polygone", "polyline": "Polyligne", "rectangle": "Rectangle", "redact": "<PERSON><PERSON><PERSON><PERSON>", "signature": "Signature", "stamp": "Timbre", "stickyNote": "Note collante", "highlight": "<PERSON><PERSON><PERSON><PERSON>", "strikeout": "<PERSON><PERSON>", "underline": "<PERSON><PERSON><PERSON>", "squiggly": "<PERSON><PERSON><PERSON>", "3D": "3D", "other": "<PERSON><PERSON>", "initials": "Initiales", "saved": "Enregistré"}, "notesOrder": {"dropdownLabel": "Liste des ordres de tri", "position": "Position", "time": "Temps", "status": "Statut", "author": "<PERSON><PERSON><PERSON>", "type": "Type", "color": "<PERSON><PERSON><PERSON>", "createdDate": "Date de création", "modifiedDate": "Date modifiée"}, "toolbarGroup": {"dropdownLabel": "Groupes de barres d'outils", "flyoutLabel": "Ruban", "toolbarGroup-View": "Affichage", "toolbarGroup-Annotate": "<PERSON><PERSON>", "toolbarGroup-Shapes": "Formes", "toolbarGroup-Insert": "<PERSON><PERSON><PERSON><PERSON>", "toolbarGroup-Measure": "Mesure", "toolbarGroup-Edit": "É<PERSON>er", "toolbarGroup-EditText": "É<PERSON><PERSON> le texte", "toolbarGroup-FillAndSign": "Remp<PERSON><PERSON> et signer", "toolbarGroup-Forms": "Formulaire", "toolbarGroup-Redact": "<PERSON><PERSON><PERSON><PERSON>", "toolbarGroup-oe-Home": "Accueil", "toolbarGroup-oe-Insert": "<PERSON><PERSON><PERSON><PERSON>", "toolbarGroup-oe-Review": "Revoir"}, "annotationColor": {"StrokeColor": "Bordure", "FillColor": "Remplissage", "TextColor": "Texte"}, "colorPalette": {"colorLabel": "<PERSON><PERSON><PERSON>"}, "colorPalettePicker": {"addColor": "Ajouter une nouvelle couleur", "selectColor": "Choisissez la couleur"}, "displayMode": {"layout": "Disposition", "pageTransition": "Transition de page"}, "documentControls": {"selectTooltip": "Sélectionnez plusieurs pages", "closeTooltip": "Fermer la sélection multiple"}, "bookmarkOutlineControls": {"edit": "É<PERSON>er", "done": "Fait", "reorder": "Réorganiser"}, "layout": {"cover": "Couverture", "double": "Double", "single": "Simple"}, "mathSymbols": "Symboles mathématiques", "notesPanel": {"separator": {"today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er", "unknown": "Inconnu"}, "noteContent": {"noName": "(sans nom)", "noDate": "(pas de date)"}, "toggleMultiSelect": "Activer/désactiver la sélection multiple pour l'annotation"}, "pageTransition": {"continuous": "<PERSON><PERSON><PERSON>", "default": "Page par page", "reader": "Lecture"}, "print": {"all": "Toutes", "current": "Page actuelle", "pages": "Pages", "specifyPages": "Spécifier les pages", "view": "Vue actuelle", "pageQuality": "Qualité d'impression", "qualityNormal": "Ordinaire", "qualityHigh": "Haute", "includeAnnotations": "Inclure les annotations", "includeComments": "Inclure les commentaires", "printSettings": "Paramètres d'impression", "printGrayscale": "Imprimer en niveaux de gris", "printCurrentDisabled": "La vue actuelle n'est disponible que lors de l'affichage d'une seule page."}, "printInfo": {"author": "<PERSON><PERSON><PERSON>", "subject": "Sujet", "date": "Date"}, "redaction": {"markForRedaction": "Caviarder"}, "searchPanel": {"caseSensitive": "Sensible à la casse", "wholeWordOnly": "<PERSON>t complet", "wildcard": "Caractère générique", "replace": "<PERSON><PERSON>lace<PERSON>", "replaceAll": "Remplace tout", "replaceText": "<PERSON><PERSON><PERSON><PERSON> le texte", "confirmMessageReplaceAll": "Voulez-vous vraiment remplacer tout le texte ?", "confirmMessageReplaceOne": "Voulez-vous vraiment remplacer ce texte ?", "moreOptions": "Plus d'options", "lessOptions": "Moins d'options", "confirm": "Confirmer"}, "toolsOverlay": {"currentStamp": "Tampon Actuel", "currentSignature": "Signature Actuelle", "signatureAltText": "Signature"}, "stampOverlay": {"addStamp": "Ajouter un nouveau tampon"}, "signatureOverlay": {"addSignature": "Ajouter une signature", "addSignatureOrInitials": "Signature/initiales"}, "signatureModal": {"modalName": "<PERSON><PERSON><PERSON> une nouvelle signature", "dragAndDrop": "Faites glisser et déposez votre image ici", "or": "Ou", "pickImage": "Choisir l'image de signature", "selectImage": "Sélectionnez votre image ici", "typeSignature": "Tapez Signature*", "typeInitial": "Taper les initiales*", "drawSignature": "Tirage au sort Signature*", "drawInitial": "Dessiner des initiales*", "imageSignature": "Signature d'image", "imageInitial": "Initiales de l'image", "pickInitialsFile": "Choisissez les initiales", "noSignatures": "Il n'y a actuellement aucune signature enregistrée.", "fontStyle": "Police", "textSignature": {"dropdownLabel": "Famille de polices"}}, "pageReplacementModal": {"yourFiles": "<PERSON><PERSON>", "chooseFile": "Choisissez un fichier", "localFile": "Fichier local", "pageReplaceInputLabel": "Remplacer les pages", "pageReplaceInputFromSource": "avec page(s)", "warning": {"title": "Quitter la page de remplacement ?", "message": "Quitter annulera toutes les sélections que vous avez faites jusqu'à présent. Voulez-vous toujours quitter ?"}}, "filterAnnotModal": {"color": "<PERSON><PERSON><PERSON>", "includeReplies": "Inclure les réponses", "filters": "Filtres", "user": "Utilisa<PERSON>ur", "type": "Taper", "status": "Statut", "filterSettings": "Paramètres de filtre", "filterDocument": "Filtrer le document et le panneau de commentaires"}, "state": {"accepted": "Accepté", "rejected": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>", "set": "Définir le statut:", "setBy": "attribué par", "none": "Aucun", "marked": "<PERSON><PERSON><PERSON>", "unmarked": "Non marqué"}, "measurementOverlay": {"scale": "<PERSON><PERSON><PERSON>", "angle": "<PERSON><PERSON>", "distance": "Distance", "perimeter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "area": "Surface", "distanceMeasurement": "Mesure de distance", "perimeterMeasurement": "Mesure du périmètre", "arcMeasurement": "Mesure d'arc", "areaMeasurement": "Mesure de surface", "countMeasurement": "Mesure de décompte", "radius": "Rayon", "count": "<PERSON><PERSON><PERSON>", "length": "<PERSON><PERSON><PERSON>", "xAxis": "Axe X", "yAxis": "Axe Y"}, "freeTextOption": {"autoSizeFont": "Ajuster la taille de la police de manière dynamique"}, "measurementOption": {"scale": "<PERSON><PERSON><PERSON>", "selectScale": "Sélectionner l'échelle", "selectScaleDropdown": "Sélectionner la liste déroulante de l'échelle"}, "measurement": {"scaleModal": {"calibrate": "<PERSON><PERSON><PERSON><PERSON>", "custom": "Coutume", "fractionalUnits": "Unités fractionnaires", "precision": "Précision", "preset": "Préréglé", "paperUnits": "Unités papier", "displayUnits": "Unités d'affichage", "fractionUnitsTooltip": "Les unités de fraction ne s'appliquent qu'à in et ft-in", "incorrectSyntax": "Syntaxe incorrecte", "units": "Unités"}, "scaleOverlay": {"addNewScale": "Ajouter une nouvelle échelle", "selectTwoPoints": "Sélectionnez deux points d'une dimension connue à calibrer", "inputKnowDimension": "Entrez la dimension connue et les unités à calibrer", "multipleScales": "Échelles multiples"}, "deleteScaleModal": {"deleteScale": "Supprimer l'échelle", "scaleIsOn-delete-info": "Cette échelle est actuellement utilisée sur", "page-delete-info": "page", "appliedTo-delete-info": "et appliqué à", "measurement": "la mesure", "measurements": "des mesures", "deletionIs": "La suppression est", "irreversible": "irréversible", "willDeleteMeasurement": "et supprimera les mesures associées.", "confirmDelete": "Voulez-vous vraiment supprimer cette échelle ?", "thisCantBeUndone": " <PERSON><PERSON> ne peut pas être annulé.", "ifChangeScale": "Si vous modifiez l'échelle de la mesure ou de l'outil sélectionné, l'échelle", "notUsedWillDelete": " ne sera plus utilisé par aucune mesure ou outil et sera supprimé. La suppression est irréversible.", "ifToContinue": "Voulez-vous vraiment continuer ?"}}, "contentEdit": {"deletionModal": {"title": "Supp<PERSON>er le contenu", "message": "Voulez-vous vraiment supprimer le contenu sélectionné ? Cela ne peut pas être annulé."}, "digitalSign": {"title": "Avertissement Document scellé", "message": "Ce document a été signé et ne peut être modifié ou modifié."}}, "stylePopup": {"textStyle": "Style de texte", "colors": "Couleurs", "invalidFontSize": "La taille de la police doit être comprise dans la plage suivante", "labelText": "Texte de l'étiquette", "labelTextPlaceholder": "Ajouter un texte d'étiquette"}, "styleOption": {"style": "Style", "solid": "Solide", "cloudy": "<PERSON><PERSON><PERSON><PERSON>"}, "slider": {"opacity": "Opacité", "thickness": "Épaisseur", "text": "<PERSON>lle du texte"}, "shared": {"page": "Page", "precision": "Précision", "enableSnapping": "Activer le magnétisme pour les outils de mesure"}, "watermark": {"title": "Filigrane", "addWatermark": "Ajouter un filigrane", "size": "<PERSON><PERSON>", "location": "Choisissez un emplacement pour modifier les filigranes", "text": "Texte", "style": "Style", "resetAllSettings": "Réinitialiser tous les réglages", "font": "Police", "addNew": "Ajouter nouveau", "locations": {"center": "Centre", "topLeft": "Sup<PERSON>ur gauche", "topRight": "<PERSON><PERSON><PERSON><PERSON> droit", "topCenter": "Centre supérieur", "bottomLeft": "Inférieur gauche", "bottomRight": "Inférieur droit", "bottomCenter": "Centre inférieur"}}, "thumbnailPanel": {"delete": "<PERSON><PERSON><PERSON><PERSON>", "rotateClockwise": "Rotation horaire", "rotateCounterClockwise": "Rotation antihoraire", "rotatePageClockwise": "Faire pivoter la page dans le sens des aiguilles d'une montre", "rotatePageCounterClockwise": "Faire pivoter la page dans le sens inverse des aiguilles d'une montre", "moreOptions": "Plus d'options", "moreOptionsMenu": "Menu d'options Plus de vignettes", "enterPageNumbers": "Entrez les numéros de page à sélectionner", "multiSelectPages": "Pages à sélection multiple", "multiSelectPagesExample": "par exemple. 1, 3, 5-10"}, "thumbnailsControlOverlay": {"move": "<PERSON>é<PERSON>r des pages"}, "richText": {"bold": "Gras", "italic": "Italique", "underline": "<PERSON><PERSON><PERSON>", "strikeout": "<PERSON><PERSON><PERSON>", "alignLeft": "Texte aligné à gauche", "alignRight": "Texte aligné à droite", "alignCenter": "Centre d'alignement du texte", "justifyCenter": "Centre de justification du texte", "alignTop": "Aligner en haut", "alignMiddle": "Aligner au milieu", "alignBottom": "Aligner en bas"}, "customStampModal": {"modalName": "Créer un nouveau tampon", "stampText": "Texte du tampon", "timestampText": "Texte d'horodatage", "Username": "Nom d'utilisateur", "Date": "Date", "Time": "heure", "fontStyle": "Police", "dateFormat": "Format de date", "month": "<PERSON><PERSON>", "day": "Jour", "year": "An", "hour": "<PERSON><PERSON>", "minute": "Minute", "second": "Seconde", "textColor": "Couleur du texte", "backgroundColor": "Couleur de l'arrière plan", "dateToolTipLabel": "Plus d'informations sur le format de date", "previewCustomStamp": "Aperçu de"}, "pageRedactModal": {"addMark": "Ajouter une marque", "pageSelection": "Sélection de pages", "current": "Page actuelle", "specify": "Spécifier les pages", "odd": "Pages impaires seulement", "even": "Seulement les pages paires"}, "lineStyleOptions": {"title": "Style"}, "settings": {"settings": "Réglages", "searchSettings": "Paramètres de recherche", "general": "Général", "language": "<PERSON><PERSON>", "theme": "Thème", "darkMode": "Mode sombre", "lightMode": "Mode lumière", "advancedSetting": "Paramètre a<PERSON>", "viewing": "Affichage", "disableFadePageNavigationComponent": "Désactiver le composant de navigation de page de fondu", "disableFadePageNavigationComponentDesc": "Gardez toujours le composant de navigation de page à l'écran. Le comportement par défaut est de le faire disparaître après une certaine période d'inactivité.", "disableNativeScrolling": "Désactiver le défilement natif", "disableNativeScrollingDesc": "Désactivez le comportement de défilement natif des appareils mobiles s'il avait déjà été activé. Notez que le comportement de défilement natif des appareils mobiles est désactivé par défaut.", "annotations": "<PERSON><PERSON><PERSON>", "disableToolDefaultStyleUpdateFromAnnotationPopup": "Désactiver la mise à jour du style par défaut de l'outil à partir de la fenêtre contextuelle d'annotation", "disableToolDefaultStyleUpdateFromAnnotationPopupDesc": "Désactive la synchronisation des mises à jour de style d'annotation avec l'outil associé qui a créé l'annotation. <PERSON><PERSON>, si le style d'une annotation est modifié, les styles par défaut de l'outil ne seront pas mis à jour.", "notesPanel": "Panneau de commentaires", "disableNoteSubmissionWithEnter": "Désactiver la soumission de notes avec Entrée", "disableNoteSubmissionWithEnterDesc": "Désactivez la possibilité de soumettre des notes en appuyant uniquement sur Entrée si elle avait déjà été activée. Cela rétablira la soumission de note à la valeur par défaut qui est Ctrl/Cmd + Entrée.", "disableAutoExpandCommentThread": "Désactiver le fil de commentaires à développement automatique", "disableAutoExpandCommentThreadDesc": "Désactive l'expansion automatique de tous les fils de commentaires dans le panneau Notes.", "disableReplyCollapse": "Désactiver la réduction de la réponse", "disableReplyCollapseDesc": "Désactive la réduction des réponses dans le panneau Notes.", "disableTextCollapse": "Désactiver la réduction du texte", "disableTextCollapseDesc": "Désactive la réduction du texte de l'annotation dans le panneau Notes.", "search": "<PERSON><PERSON><PERSON>", "disableClearSearchOnPanelClose": "<PERSON>és<PERSON>r Effacer la recherche sur le panneau Fermer", "disableClearSearchOnPanelCloseDesc": "Désactivez l'effacement des résultats de recherche lorsque l'utilisateur ferme le panneau de recherche. Lorsqu'il est désactivé, les résultats de la recherche sont conservés même si l'utilisateur ferme et rouvre le panneau de recherche. Notez que les appareils mobiles n'effacent jamais les résultats de recherche, même si ce paramètre est activé. En effet, le panneau doit être fermé pour afficher les résultats de la recherche sur le document.", "pageManipulation": "Manipulation de pages", "disablePageDeletionConfirmationModal": "Désactiver la modalité de confirmation de suppression de page", "disablePageDeletionConfirmationModalDesc": "Désactiver le modal de confirmation lors de la suppression d'une page de la vue des vignettes", "disableMultiselect": "Désactiver la sélection multiple", "disableMultiselectDesc": "Désactiver la sélection multiple dans le panneau des vignettes de gauche", "miscellaneous": "Divers", "keyboardShortcut": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "command": "Commande", "description": "La description", "action": "Action", "rotateDocumentClockwise": "Faire pivoter le document dans le sens des aiguilles d'une montre", "rotateDocumentCounterclockwise": "Faire pivoter le document dans le sens antihoraire", "copyText": "Co<PERSON>r le texte ou les annotations sélectionnés", "pasteText": "Coller du texte ou des annotations", "undoChange": "Annuler une modification d'annotation", "redoChange": "Ré<PERSON>blir une modification d'annotation", "openFile": "<PERSON><PERSON><PERSON><PERSON><PERSON> le sélecteur de fichiers", "openSearch": "O<PERSON><PERSON><PERSON>r la superposition de recherche", "zoomOptions": "Options de zoom", "zoomIn": "<PERSON><PERSON><PERSON><PERSON>", "zoomOut": "Dézoomer", "setHeaderFocus": "Définit le focus sur l'en-tête", "fitScreenWidth": "Aju<PERSON>z le document à la largeur de l'écran dans un petit écran (< 640px), sinon adaptez-le à sa taille d'origine", "print": "<PERSON><PERSON><PERSON><PERSON>", "bookmarkOpenPanel": "<PERSON><PERSON> rapidement une page et ouvrez le panneau des signets", "goToPreviousPage": "Aller à la page précédente", "goToNextPage": "Aller à la page suivante", "goToPreviousPageArrowUp": "Aller à la page précédente en mode mise en page unique (ArrowUp)", "goToNextPageArrowDown": "Aller à la page suivante en mode mise en page simple (ArrowDown)", "holdSwitchPan": "Maintenez enfoncé pour passer en mode panoramique et relâchez pour revenir à l'outil précédent", "selectAnnotationEdit": "Sélectionnez l'outil AnnotationEdit", "selectPan": "Sélectionnez l'outil Panoramique", "selectCreateArrowTool": "Sélectionnez l'outil AnnotationCréerFlèche", "selectCreateCalloutTool": "Sélectionnez l'outil AnnotationCreateCallout", "selectEraserTool": "Sélectionnez l'outil AnnotationEraser", "selectCreateFreeHandTool": "Sélectionnez l'outil AnnotationCreateFreeHand", "selectCreateStampTool": "Sélectionnez l'outil AnnotationCreateStamp", "selectCreateLineTool": "Sélectionnez l'outil AnnotationCreateLine", "selectCreateStickyTool": "Sélectionnez l'outil AnnotationCreateSticky", "selectCreateEllipseTool": "Sélectionnez l'outil AnnotationCréerEllipse", "selectCreateRectangleTool": "Sélectionnez l'outil AnnotationCréerRectangle", "selectCreateRubberStampTool": "Sélectionnez l'outil AnnotationCreateRubberStamp", "selectCreateFreeTextTool": "Sélectionnez l'outil AnnotationCreateFreeText", "openSignatureModal": "<PERSON><PERSON><PERSON><PERSON>r la signature modale ou la superposition", "selectCreateTextSquigglyTool": "Sélectionnez l'outil AnnotationCreateTextSquiggly", "selectCreateTextHighlightTool": "Sélectionnez l'outil AnnotationCreateTextHighlight", "selectCreateTextStrikeoutTool": "Sélectionnez l'outil AnnotationCreateTextStrikeout", "selectCreateTextUnderlineTool": "Sélectionnez l'outil AnnotationCreateTextUnderline", "editKeyboardShorcut": "Modifier le raccourci clavier", "setShortcut": "Définir un raccourci", "editShortcut": "Modifier le raccourci", "shortcutAlreadyExists": "Le raccourci clavier ci-dessus existe déjà.", "close": "Fermer l'info-bulle"}}, "warning": {"deletePage": {"deleteTitle": "Supprimer la page", "deleteMessage": "Etes-vous sûr de vouloir supprimer la ou les pages sélectionnées ? Cela ne peut pas être annulé.", "deleteLastPageMessage": "Il ne resterait aucune page. Veuillez laisser au moins une page"}, "extractPage": {"title": "Extraire la page", "message": "Voulez-vous vraiment extraire la ou les pages sélectionnées?", "confirmBtn": "Extraire des pages", "secondaryBtn": "Extraire et supprimer des pages"}, "redaction": {"applyTile": "Appliquer le caviardage", "applyMessage": "Cette action supprimera définitivement tous les éléments sélectionnés pour caviardage. Ceci ne pourra pas être défait."}, "deleteBookmark": {"title": "Supprimer le signet ?", "message": "Voulez-vous vraiment supprimer ces favoris ? Vous ne pouvez pas annuler cette action."}, "deleteOutline": {"title": "Supprimer le contour ?", "message": "Voulez-vous vraiment supprimer ces contours ?\n\nLa suppression d'un contour qui a des contours imbriqués entraînera la suppression de toute la structure interne et, si nécessaire, devra être recréée."}, "selectPage": {"selectTitle": "Aucune page sélectionnée", "selectMessage": "Veuillez sélectionner des pages et réessayez."}, "colorPicker": {"deleteTitle": "Supprimer la couleur personnalisée", "deleteMessage": "Supprimer la couleur personnalisée sélectionnée? Elle sera supprimée de votre palette de couleurs."}, "colorPalettePicker": {"deleteTitle": "Supprimer la couleur personnalisée"}, "multiDeleteAnnotation": {"title": "Supprimer les annotations ?", "message": "La suppression supprimera tous les commentaires, réponses et regroupements et ne pourra pas être annulée.\n\n Voulez-vous vraiment supprimer ces annotations ?"}, "closeFile": {"title": "Fermer sans télécharger?", "message": "Des modifications ont été apportées à ce document, êtes-vous sûr de vouloir le fermer sans télécharger votre travail ? Vous ne pouvez pas annuler cette action.", "rejectDownloadButton": "Fermer sans téléchargement"}, "connectToURL": {"title": "Avertissement de sécurité", "message": "Ce document tente de se connecter à :\n\n{{- uri}}\n\n Si vous faites confiance à ce document, cliquez sur Confirmer pour l'ouvrir."}, "sheetTabRenameIssueOne": {"title": "Modifier l'avertissement", "message": "Ce nom de feuille existe déjà. Veuillez saisir un autre nom."}, "sheetTabRenameIssueTwo": {"title": "Modifier l'avertissement", "message": "Ce nom de feuille ne peut pas être vide."}, "sheetTabDeleteMessage": {"title": "Modifier l'avertissement", "message": "Etes-vous sûr de vouloir supprimer cette feuille ?"}}, "shortcut": {"arrow": "(A)", "callout": "(C)", "copy": "(Ctrl C)", "delete": "(Del)", "ellipse": "(O)", "eraser": "(E)", "freehand": "(F)", "freetext": "(T)", "highlight": "(H)", "line": "(L)", "pan": "(P)", "rectangle": "(R)", "rotateClockwise": "(Ctrl Shift +)", "rotateCounterClockwise": "(Ctrl Shift -)", "select": "(Esc)", "signature": "(S)", "squiggly": "(G)", "image": "(I)", "redo": "(Ctrl Shift Z)", "redo_windows": "(Ctrl Y)", "undo": "(Ctrl Z)", "stickyNote": "(N)", "strikeout": "(K)", "underline": "(U)", "zoomIn": "(Ctrl +)", "zoomOut": "(Ctrl -)", "richText": {"bold": "(Ctrl B)", "italic": "(Ctrl I)", "underline": "(Ctrl U)", "strikeout": "(Ctrl K)"}, "rotate3D": "<PERSON> + <PERSON><PERSON><PERSON>", "zoom3D": "Maj + <PERSON><PERSON><PERSON>lement"}, "tool": {"pan": "Panoramique", "select": "Sélection", "selectAnOption": "Choisir une option", "Marquee": "Zoom sur la sélection", "Link": "URL de lien ou page", "Standard": "Standard", "Custom": "<PERSON><PERSON><PERSON><PERSON>"}, "link": {"url": "URL", "page": "Page", "enterurl": "Entrez l'URL vers laquelle vous souhaitez créer un lien", "enterUrlAlt": "Entrer l'URL", "insertLink": "Insérer un lien", "insertLinkOrPage": "Insérer un lien ou une page", "enterpage": "Entrez le numéro de page vers lequel vous souhaitez créer un lien", "urlLink": "Lien URL"}, "Model3D": {"add3D": "Ajoutez une annotation 3D en saisissant une URL", "enterurl": "Entrez l'URL de l'objet 3D au format glTF", "enterurlOrLocalFile": "Entrez l'URL ou téléchargez un objet 3D au format glTF", "formatError": "Seul le format glTF (.glb) est pris en charge"}, "OpenFile": {"enterUrlOrChooseFile": "Entrez une URL ou choisissez un fichier à charger dans WebViewer", "enterUrl": "Entrez l'URL du fichier", "extension": "Extension de fichier", "existingFile": "Le fichier est déjà ouvert", "addTab": "Ajouter un onglet", "newTab": "Nouvel onglet"}, "datePicker": {"previousMonth": "<PERSON><PERSON>", "nextMonth": "<PERSON><PERSON> suivant", "months": {"0": "janvier", "1": "<PERSON><PERSON><PERSON><PERSON>", "2": "mars", "3": "avril", "4": "mai", "5": "juin", "6": "juillet", "7": "août", "8": "septembre", "9": "octobre", "10": "novembre", "11": "décembre"}, "monthsShort": {"0": "janv", "1": "févr", "2": "mars", "3": "avril", "4": "mai", "5": "juin", "6": "juil", "7": "août", "8": "sept", "9": "oct", "10": "nov", "11": "déc"}, "weekdays": {"0": "dimanche", "1": "lundi", "2": "mardi", "3": "merc<PERSON>i", "4": "jeudi", "5": "vend<PERSON>i", "6": "<PERSON>di"}, "weekdaysShort": {"0": "dim.", "1": "lun.", "2": "mar.", "3": "mer.", "4": "jeu.", "5": "ven.", "6": "sam."}, "today": "<PERSON><PERSON><PERSON>'hui", "invalidDateTime": "Date/heure non valide : l'entrée doit correspondre au format"}, "formField": {"indexPanel": {"formFieldList": "Liste des champs de formulaire", "notFields": "Ce document ne comporte aucun champ de formulaire"}, "formFieldPopup": {"fieldName": "Nom de champ", "fieldValue": "Valeur par défaut", "readOnly": "Lecture seule", "multiSelect": "Sélection multiple", "required": "Obligatoire", "multiLine": "Multiligne", "apply": "Appliquer", "cancel": "Annuler", "flags": "Caractéristiques", "fieldSize": "<PERSON><PERSON> du champ", "options": "Options", "properties": "Propriétés", "radioGroups": "Les boutons radio avec le même nom de champ appartiendront au même groupement.", "nameRequired": "Le nom du champ est obligatoire", "fieldIndicator": "Indicateurs de terrain", "documentFieldIndicator": "Indicateurs de champ de document", "includeFieldIndicator": "<PERSON><PERSON>re l'indicateur de champ", "indicatorPlaceHolders": {"TextFormField": "Ins<PERSON>rer le texte ici", "CheckBoxFormField": "Vérifier", "RadioButtonFormField": "<PERSON><PERSON><PERSON><PERSON>", "ListBoxFormField": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ComboBoxFormField": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SignatureFormField": {"fullSignature": "<PERSON><PERSON> ici", "initialsSignature": "Vos initiales ici"}}, "size": "<PERSON><PERSON>", "width": "<PERSON><PERSON>", "height": "<PERSON><PERSON>", "invalidField": {"duplicate": "Le nom du champ existe déjà", "empty": "Le nom du champ ne peut pas être vide"}}, "formFieldPanel": {"SignatureFormField": "Annotation du champ de signature", "CheckBoxFormField": "Annotation du champ de case à cocher", "RadioButtonFormField": "Annotation du champ du bouton radio", "ListBoxFormField": "Annotation du champ de la zone de liste", "ComboBoxFormField": "Annotation des champs de la zone de liste déroulante", "TextFormField": "Annotation du champ de texte"}, "apply": "Appliquer les champs", "type": "Type de champ", "types": {"text": "Texte", "signature": "Signature", "checkbox": "Case à cocher", "radio": "Bouton radio", "listbox": "Zone de liste", "combobox": "<PERSON>îte combo", "button": "Bouton"}}, "alignmentPopup": {"alignment": "<PERSON><PERSON><PERSON>", "alignLeft": "Alignez à gauche", "alignHorizontalCenter": "<PERSON><PERSON><PERSON> le centre horizontal", "alignVerticalCenter": "<PERSON><PERSON><PERSON> le centre vertical", "alignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "alignTop": "Aligner en haut", "alignBottom": "Aligner en bas", "distribute": "Distribuer", "distributeHorizontal": "Répartir horizontalement", "distributeVertical": "Distribuer verticalement"}, "digitalSignatureModal": {"certification": "certification", "Certification": "Certification", "signature": "Signature", "Signature": "Signature", "valid": "valide", "invalid": "invalide", "unknown": "inconnue", "title": "{{type}} propriétés", "header": {"documentIntegrity": "Intégrité des documents", "identitiesTrust": "Identités et confiance", "generalErrors": "Erreurs générales", "digestStatus": "État de l'empreinte"}, "documentPermission": {"noChangesAllowed": "Le {{editor}} a spécifié qu'aucune modification n'est autorisée pour ce document", "formfillingSigningAllowed": "Le {{editor}} a spécifié que le remplissage et la signature de formulaire sont autorisés pour ce document. Aucun autre changement n'est autorisé.", "annotatingFormfillingSigningAllowed": "Le {{editor}} a spécifié que le remplissage, la signature et les commentaires de formulaire sont autorisés pour ce document. Aucun autre changement n'est autorisé.", "unrestricted": "Le {{editor}} a spécifié qu'il n'y avait aucune restriction pour ce document."}, "digestAlgorithm": {"preamble": "L'algorithme d'empreinte utilisé pour chiffrer la signature:", "unknown": "L'algorithme d'empreinte utilisé pour chiffrer la signature est inconnu."}, "trustVerification": {"none": "Aucun résultat de vérification de confiance détaillé disponible.", "current": "Vérification de la confiance tentée par rapport à l'heure actuelle", "signing": "Vérification de la confiance tentée en ce qui concerne l'heure de signature: {{trustVerificationTime}}", "timestamp": "Tentative de vérification de la confiance en ce qui concerne l'horodatage intégré sécurisé: {{trustVerificationTime}}"}, "signerIdentity": {"preamble": "L'identité du signataire est", "valid": "valide.", "unknown": "inconnue."}, "summaryBox": {"summary": "Numérique {{type}} est {{status}}", "signedBy": ", signé par {{name}}"}}, "digitalSignatureVerification": {"certifier": "certificateur", "certified": "certifié", "Certified": "Certifié", "Certification": "Certificat", "signer": "signataire", "signed": "signé", "Signed": "<PERSON><PERSON>", "Signature": "Signature", "by": "par", "on": "le", "disallowedChange": "Modification interdite: {{type}}, objnum: {{objnum}}", "unsignedSignatureField": "Champ de signature non signé : {{fieldName}}", "signatureProperties": "Propriétés des signatures", "trustVerification": {"current": "L'heure de vérification utilisée était l'heure courante", "signing": "L'heure de vérification provient de l'horloge de l'ordinateur du signataire", "timestamp": "L'heure de vérification provient de l'horodatage sécurisé intégré dans le document", "verifiedTrust": "Résultat de la vérification de confiance: vérifié", "noTrustVerification": "Aucun résultat de vérification de confiance détaillé disponible."}, "permissionStatus": {"noPermissionsStatus": "Aucun statut d'autorisation à signaler.", "permissionsVerificationDisabled": "La vérification des autorisations a été désactivée.", "hasAllowedChanges": "Le document comporte des modifications autorisées par les paramètres d'autorisations de signatures.", "invalidatedByDisallowedChanges": "Le document comporte des modifications qui ne sont pas autorisées par les paramètres d'autorisations de signatures.", "unmodified": "Le document n'a pas été modifié depuis", "unsupportedPermissionsFeatures": "Des fonctionnalités d'autorisations non prises en charge ont été rencontrées."}, "trustStatus": {"trustVerified": "Confiance établie avec {{verificationType}} avec succès.", "untrusted": "La confiance n'a pas pu être établie.", "trustVerificationDisabled": "La vérification de la confiance a été désactivée.", "noTrustStatus": "Aucun statut de confiance à signaler."}, "digestStatus": {"digestInvalid": "L'empreinte est incorrecte.", "digestVerified": "L'empreinte est correcte.", "digestVerificationDisabled": "La vérification du résumé a été désactivée.", "weakDigestAlgorithmButDigestVerifiable": "L'empreinte est correcte, mais l'algorithme d'empreinte est faible et non sécurisé.", "noDigestStatus": "Aucun état d'empreinte détecté", "unsupportedEncoding": "<PERSON><PERSON><PERSON> entête de signature installée, impossible de connaître le codage de la signature", "documentHasBeenAltered": "Le document a été modifié ou corrompu depuis sa signature."}, "documentStatus": {"noError": "Aucune erreur générale à signaler.", "corruptFile": "L'entête de signature a détecté une corruption de fichier.", "unsigned": "La signature n'a pas encore été chiffée cryptographiquement.", "badByteRanges": "L'entête de signature a détecté une corruption dans les ByteRanges de la signature numérique.", "corruptCryptographicContents": "L'entête de signature a détecté une corruption dans le contenu de la signature numérique."}, "signatureDetails": {"signatureDetails": "<PERSON><PERSON><PERSON> de la signature", "contactInformation": "Informations de contact", "location": "Emplacement", "reason": "<PERSON>son", "signingTime": "Heure de signature", "noContactInformation": "Aucune information de contact fournie", "noLocation": "Aucun emplacement fourni", "noReason": "Aucune raison fournie", "noSigningTime": "Aucune heure de signature trouvée"}, "panelMessages": {"noSignatureFields": "Ce document n'a pas de champ de signature", "certificateDownloadError": "Erreur rencont<PERSON>e lors de la tentative de téléchargement d'un certificat de confiance", "localCertificateError": "Il y a quelques problèmes avec la lecture d'un certificat local"}, "verificationStatus": {"valid": "{{verificationType}} est valide.", "failed": "{{verificationType}} a échoué."}}, "cropPopUp": {"title": "Pages à recadrer", "allPages": "Tous", "singlePage": "Page actuelle", "multiPage": "Spécifier la page", "cropDimensions": "Dimensions du recadrage", "dimensionInput": {"unitOfMeasurement": "Unité", "autoTrim": "Trim automatique", "autoTrimCustom": "Coutume"}, "cropModal": {"applyTitle": "Appliquer le recadrage ?", "applyMessage": "Cette action recadrera de manière permanente toutes les pages sélectionnées. Cette action ne pourra pas être annulée.", "cancelTitle": "Annuler le recadrage?", "cancelMessage": "Voulez-vous vraiment annuler le recadrage de toutes les pages sélectionnées?"}}, "snippingPopUp": {"title": "<PERSON><PERSON>", "clipboard": "Copier dans le presse-papiers", "download": "Télécharger", "cropAndRemove": "Recadrer et supprimer", "snippingModal": {"applyTitle": "Appliquer le découpage ?", "applyMessage": "Cette action découpera définitivement la zone spécifiée et supprimera les autres pages. Elle est irréversible.", "cancelTitle": "Ann<PERSON>r le découpage ?", "cancelMessage": "Êtes-vous sûr de vouloir arrêter le découpage ?"}}, "textEditingPanel": {"paragraph": "Paragraphe", "text": "Texte"}, "redactionPanel": {"noMarkedRedactions": "Commencez à rédiger en marquant du texte, des régions, des pages ou en effectuant une recherche.", "redactionSearchPlaceholder": "Rédiger par clavier ou par modèles", "redactionCounter": "Marqué pour rédaction", "clearMarked": "Dégager", "redactAllMarked": "Tout expurger", "redactionItems": "Articles de rédaction", "redactionItem": {"regionRedaction": "Caviardage de région", "fullPageRedaction": "Caviardage pleine page", "fullVideoFrameRedaction": "Rédaction vidéo", "audioRedaction": "Rédaction audio", "fullVideoFrameAndAudioRedaction": "Rédaction vidéo et audio", "image": "Image"}, "expand": "Développer", "collapse": "<PERSON><PERSON>e<PERSON><PERSON><PERSON>", "searchResults": "Résultats de recherche", "search": {"creditCards": "Carte de <PERSON>", "phoneNumbers": "Les numéros de téléphone", "images": "Images", "emails": "E-mails", "pattern": "<PERSON><PERSON><PERSON>", "start": "Commencez à faire votre recherche"}}, "wv3dPropertiesPanel": {"propertiesHeader": "Propriétés", "miscValuesHeader": "Tous", "emptyPanelMessage": "Sélectionnez un élément pour afficher ses propriétés."}, "watermarkPanel": {"textWatermark": "Filigrane de texte", "uploadImage": "Télécharger une image", "browse": "Parcourir", "watermarkOptions": "Options de filigrane", "watermarks": "<PERSON>ligra<PERSON>"}, "portfolio": {"createPDFPortfolio": "Créer un portfolio PDF", "uploadFiles": "Télécharger des fichiers", "uploadFolder": "Télécharger le dossier", "addFiles": "Ajouter des fichiers", "addFile": "<PERSON><PERSON><PERSON> le fi<PERSON>er", "addFolder": "Ajouter le dossier", "createFolder": "<PERSON><PERSON><PERSON> le dossier", "portfolioPanelTitle": "Portfolio PDF", "portfolioNewFolder": "Nouveau dossier", "portfolioDocumentTitle": "Titre du document", "portfolioFolderPlaceholder": "Nom de dossier", "portfolioFilePlaceholder": "Nom de fi<PERSON>er", "folderNameAlreadyExists": "Le nom du dossier existe déjà", "fileNameAlreadyExists": "Le nom du fichier existe déjà", "openFile": "<PERSON><PERSON><PERSON><PERSON>r dans un nouvel onglet", "fileAlreadyExists": "Le fichier existe déjà", "fileAlreadyExistsMessage": "Le fichier \"{{fileName}}\" existe déjà dans le portfolio.", "deletePortfolio": "Voulez-vous vraiment supprimer \"{{fileName}}\" ?", "reselect": "Sélectionner de nouveau"}, "languageModal": {"selectLanguage": "Choisir la langue"}, "officeEditor": {"bold": "Gras", "italic": "Italique", "underline": "<PERSON><PERSON><PERSON>", "textColor": "Couleur du texte", "leftAlign": "Alignement à gauche", "centerAlign": "Aligner au centre", "rightAlign": "Alignement à droite", "justify": "Justifier", "lineSpacing": "Espacement des lignes et des paragraphes", "lineSpacingMenu": "Espacement des lignes", "bulletList": "Liste à puces", "numberList": "Liste numérotée", "decreaseIndent": "Diminuer le retrait", "increaseIndent": "Augmenter le retrait", "nonPrintingCharacters": "Caractères non imprimables", "insertLink": "Insérer un lien", "insertImage": "Insérer une image", "image": "Image", "table": "<PERSON><PERSON>", "insertRowAbove": "Insérer une ligne au-dessus", "insertRowBelow": "Insérer une ligne en dessous", "insertColumnRight": "Insérer une colonne à droite", "insertColumnLeft": "Insérer une colonne à gauche", "deleteRow": "Supprimer la ligne", "deleteColumn": "Supprimer la colonne", "deleteTable": "Su<PERSON><PERSON><PERSON> le tableau", "deleted": "Supprimé :", "added": "Ajoutée:", "editing": "Édition", "editingDescription": "Modifier le document", "reviewing": "Révision", "reviewingDescription": "Faire des suggestions", "viewOnly": "Afficher uniquement", "viewOnlyDescription": "Afficher sans suggestions", "notSupportedOnMobile": "L'édition Office n'est pas prise en charge sur les appareils mobiles.", "previewAllChanges": "Aperçu de toutes les modifications", "accept": "Accepter", "reject": "<PERSON><PERSON><PERSON>", "pastingTitle": "Collage non disponible", "pastingMessage": "Le collage n'est pas pris en charge dans votre navigateur. Au lieu de cela, vous pouvez utiliser un raccourci clavier", "pastingWithoutFormatTitle": "Collage sans formatage non disponible", "pastingWithoutFormatMessage": "Le collage sans formatage n'est pas pris en charge dans votre navigateur. Au lieu de cela, vous pouvez utiliser un raccourci clavier", "breaks": "Pauses", "pageBreak": "Saut de page", "pageBreakDescription": "Fin de page et début d'une nouvelle page", "sectionBreakNextPage": "Saut de section - <PERSON> suivante", "sectionBreakNextPageDescription": "Insérer un saut de section et commencer à la page suivante", "sectionBreakContinuous": "Saut de section - Continu", "sectionBreakContinuousDescription": "Insérer un saut de section et continuer sur la même page", "section": "Section", "header": {"0": "<PERSON>-tête", "1": "<PERSON>-t<PERSON><PERSON> de la première page", "2": "En-tête de page uniforme", "3": "En-tête de page impaire", "-1": "<PERSON><PERSON>t<PERSON><PERSON> invalide"}, "footer": {"0": "Pied de page", "1": "Pied de page de la première page", "2": "Pied de page pair", "3": "Pied de page de page impaire", "-1": "Pied de page invalide"}, "options": "Options", "pageOptions": "Options de page", "removeHeader": "Supp<PERSON>er l'en-tête", "removeFooter": "Supprimer le pied de page", "headerFooterOptionsModal": {"title": "Format d'en-tête et de pied de page", "margins": "Marges", "headerFromTop": "En-tête du haut", "footerFromBottom": "Pied de page depuis le bas", "layouts": {"layout": "Mise en page", "noSelection": "Aucune sélection", "differentFirstPage": "Première page différente", "differentEvenOddPages": "Différentes pages paires et impaires", "differentFirstEvenOddPages": "Premières pages, pages paires et pages impaires différentes"}}, "lineSpacingOptions": {"15": "1,5", "115": "1.15", "single": "Simple", "double": "Double", "custom": "Personnaliser"}, "numberDropdown": {"6": "Nombre latin romain 1", "7": "Nombre décimal", "8": "Nombre latin romain 2", "10": "Latin romain", "11": "Nombre latin romain", "dropdownLabel": "Préréglages de numérotation"}, "bulletDropdown": {"0": "<PERSON><PERSON>", "1": "<PERSON><PERSON> de balle", "2": "<PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON>", "4": "Vérifier", "5": "Flèche", "dropdownLabel": "Préréglages de balles"}, "fontSize": {"dropdownLabel": "Taille de la police"}, "fontStyles": {"dropdownLabel": "Styles de police"}, "fontFamily": {"dropdownLabel": "Famille de polices"}}, "spreadsheetEditor": {"editing": "Édition", "viewOnly": "Affichage", "editingDescription": "Modifier le document", "viewOnlyDescription": "Voir seulement", "bold": "Audacieux", "italic": "Italique", "underline": "<PERSON><PERSON><PERSON>", "strikethrough": "<PERSON><PERSON>", "cellBorderStyle": "Style de bordure", "merge": "<PERSON><PERSON>", "unmerge": "Annuler la fusion", "cellFormat": "Format de cellule", "automatic": "Automatique", "plainText": "Texte brut", "increaseDecimal": "Augmenter la décimale", "decreaseDecimal": "Diminuer la décimale", "number": "Nombre", "percent": "Pour cent", "accounting": "Comptabilité", "financial": "Financier", "currency": "<PERSON><PERSON>", "currencyRounded": "<PERSON><PERSON> a<PERSON>", "calendar": "Date", "clockHour": "Temps", "calendarTime": "Date Heure", "formatAsCurrency": "Formater comme devise", "formatAsPercent": "Format en pourcentage", "formatAsDecDecimal": "Diminuer le point décimal", "formatAsIncDecimal": "Augmenter le point décimal", "fontColor": "Couleur de police", "cellBackgroundColor": "Couleur d'arrière-plan de la cellule", "textAlignment": "Alignement du texte", "alignLeft": "<PERSON><PERSON><PERSON> à gauche", "alignCenter": "Aligner le centre", "alignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "alignTop": "<PERSON><PERSON><PERSON> le haut", "alignMiddle": "Aligner au milieu", "alignBottom": "<PERSON><PERSON><PERSON> le bas", "cellAdjustment": "Ajustement des cellules", "insertColLeft": "Insérer la colonne à gauche", "columnInsertLeft": "Insérer la colonne à gauche", "columnInsertRight": "Insérer la colonne à droite", "rowInsertTop": "Insérer une ligne au-dessus", "rowInsertBottom": "Insérer une ligne ci-dessous", "columnInsertShiftDown": "Insérer des cellules et décaler vers le bas", "columnInsertShiftRight": "Insérer des cellules et décaler vers la droite", "columnDelete": "Supprimer la colonne", "rowDelete": "Supprimer la ligne", "columnDeleteShiftUp": "Supprimer des cellules et décaler vers le haut", "columnDeleteShiftLeft": "Supprimer les cellules et décaler vers la gauche"}, "insertPageModal": {"title": "Insérer une nouvelle page", "tabs": {"blank": "<PERSON>", "upload": "Télécharger"}, "pagePlacements": {"header": "Positionnement des pages", "above": "Au-dessus de la page", "below": "Sous la page"}, "pageLocations": {"header": "Emplacement de la page", "specify": "Spécifier la page", "specifyLocation": "Spécifier l'emplacement de la page", "amount": "Nombre de pages", "total": "Total", "pages": "pages"}, "pageDimensions": {"header": "Dimensions des pages", "subHeader": "Préconfigurations", "presets": {"letter": "<PERSON><PERSON>", "halfLetter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juniorLegal": "<PERSON><PERSON><PERSON> junior", "custom": "<PERSON><PERSON><PERSON><PERSON>"}, "units": "Unités"}, "browse": "Parcourir les fichiers", "fileSelected": {"title": "Sélectionnez les pages à ajouter"}, "button": "Ajouter des pages", "selectPages": "Sélectionnez les pages à ajouter", "page": "Page", "warning": {"title": "<PERSON><PERSON><PERSON> insérer une nouvelle page ?", "message": "Quitter annulera toutes les sélections que vous avez faites jusqu'à présent. Voulez-vous toujours quitter ?"}}, "multiViewer": {"dragAndDrop": "Faites glisser et déposez votre fichier ici pour comparer", "or": "Ou", "browse": "Parcourir les fichiers", "startSync": "Démarrer la synchronisation", "stopSync": "Arrêter la synchronisation", "closeDocument": "<PERSON><PERSON><PERSON> le <PERSON>", "save": "Enregistrer le document", "comparePanel": {"Find": "Rechercher dans le document", "changesList": "Changer la liste", "change": "Changer", "old": "Agé de", "new": "Nouveau", "page": "Page", "textContent": "Contenu du texte", "delete": "effacer", "insert": "insérer", "edit": "É<PERSON>er"}}, "saveModal": {"close": "proche", "saveAs": "Enregistrer sous", "general": "Général", "fileName": "Nom de fi<PERSON>er", "fileType": "Type de fichier", "fileLocation": "Emplacement du fichier", "browse": "Parcourir", "pageRange": "Intervalle de pages", "all": "<PERSON>ut", "currentView": "Vue actuelle", "currentPage": "Page actuelle", "specifyPage": "Spécifier la page", "properties": "Propriétés", "includeAnnotation": "Inclure les annotations", "includeComments": "Inclure les commentaires", "watermark": "Filigrane", "addWatermark": "Ajouter un filigrane", "save": "Enregis<PERSON><PERSON> le fi<PERSON>er", "pageError": "Veuillez saisir une plage comprise entre 1 et ", "fileNameCannotBeEmpty": "Le nom du fichier ne peut pas être vide"}, "filePicker": {"dragAndDrop": "Faites glisser et déposez votre fichier ici", "selectFile": "Sélectionnez votre dossier ici", "or": "Ou"}, "stylePanel": {"headings": {"styles": "modes", "annotation": "Annotation", "annotations": "Annotations", "tool": "Outil", "textStyles": "Styles de texte", "currentColor": "Couleur actuelle", "customColors": "Couleurs personnalisées", "redactionTextLabel": "Étiquette de texte", "redactionMarkOutline": "Mar<PERSON> le contour", "redactionFill": "Couleur de rédaction", "redactionTextPlaceholder": "Insérer une étiquette de texte", "contentEdit": "Modifier le contenu"}, "lineStyles": {"startLineStyleLabel": "Style de la ligne de départ", "middleLineStyleLabel": "Style de ligne médiane", "endLineStyleLabel": "Style de la ligne de fin"}, "addColorToCustom": "Ajouter aux couleurs personnalisées", "noToolSelected": "Sélectionnez un outil pour afficher les propriétés de l'outil", "noToolStyle": "L'outil ne contient aucune propriété de style.", "lineEnding": {"start": {"dropdownLabel": "Début de la ligne"}, "end": {"dropdownLabel": "Fin de la ligne"}, "middle": {"dropdownLabel": "Ligne du milieu"}}, "borderStyle": {"dropdownLabel": "Frontière"}}, "signatureListPanel": {"header": "Liste des signatures", "newSignature": "Nouvelle Signature", "newSignatureAndInitial": "Nouvelle signature et initiale", "signatureList": {"signature": "Signature", "initials": "Initiales"}}, "rubberStampPanel": {"header": "Timbres", "standard": "Timbres standards"}, "colorPickerModal": {"modalTitle": "<PERSON><PERSON><PERSON><PERSON> de couleurs"}, "accessibility": {"landmarks": {"topHeader": "<PERSON><PERSON>tê<PERSON> supérieur", "leftHeader": "En-tête gauche", "rightHeader": "<PERSON>-tête droit", "bottomHeader": "En-tête inférieur", "documentContent": "Contenu du document"}, "label": "Accessibilité", "accessibilityMode": "Mode d'accessibilité", "skipTo": "Passer à", "document": "Document", "notes": "<PERSON><PERSON><PERSON>"}, "formulaBar": {"label": "Barre de formule", "range": "<PERSON><PERSON><PERSON>", "formulas": "Formules", "sumif": "Une somme conditionnelle sur une plage", "sumsq": "Renvoie la somme des carrés d'une plage", "sum": "Additionne tous les nombres d'une plage", "asinh": "Renvoie le sinus hyperbolique inverse d'un nombre", "acos": "Renvoie l'arc cosinus d'un nombre, en radians", "cosh": "Renvoie le cosinus hyperbolique d'un nombre", "iseven": "Vérifie si un nombre est pair", "isodd": "Vérifie si un nombre est impair"}}