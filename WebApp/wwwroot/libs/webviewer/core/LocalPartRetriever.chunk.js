/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[7],{616:function(ya,ua,n){n.r(ua);var na=n(0),ma=n(2),oa=n(211);ya=n(124);var ka=n(359);n=n(537);var ia=window;ya=function(fa){function x(y,r,e){r=fa.call(this,y,r,e)||this;if(y.name&&"xod"!==y.name.toLowerCase().split(".").pop())throw Error("Not an XOD file");if(!ia.FileReader||!ia.File||!ia.Blob)throw Error("File API is not supported in this browser");r.file=y;r.uK=[];r.AT=0;return r}Object(na.c)(x,fa);x.prototype.QX=function(y,
r,e){var a=this,f=new FileReader;f.onloadend=function(h){if(0<a.uK.length){var b=a.uK.shift();b.pKa.readAsBinaryString(b.file)}else a.AT--;if(f.error){h=f.error;if(h.code===h.ABORT_ERR){Object(ma.i)("Request for chunk ".concat(r.start,"-").concat(r.stop," was aborted"));return}return e(h)}if(h=f.content||h.target.result)return e(!1,h);Object(ma.i)("No data was returned from FileReader.")};r&&(y=(y.slice||y.webkitSlice||y.mozSlice||y.aWa).call(y,r.start,r.stop));0===a.uK.length&&50>a.AT?(f.readAsBinaryString(y),
a.AT++):a.uK.push({pKa:f,file:y});return function(){f.abort()}};x.prototype.gA=function(y){var r=this;r.qK=!0;var e=oa.a;r.QX(r.file,{start:-e,stop:r.file.size},function(a,f){if(a)return Object(ma.i)("Error loading end header: %s ".concat(a)),y(a);if(f.length!==e)throw Error("Zip end header data is wrong size!");r.$e=new ka.a(f);var h=r.$e.y9();r.QX(r.file,h,function(b,w){if(b)return Object(ma.i)("Error loading central directory: %s ".concat(b)),y(b);if(w.length!==h.stop-h.start)throw Error("Zip central directory data is wrong size!");
r.$e.Bea(w);r.bT=!0;r.qK=!1;return y(!1)})})};x.prototype.pZ=function(y,r){var e=this,a=e.Uj[y];if(e.$e.P6(y)){var f=e.$e.aE(y),h=e.QX(e.file,f,function(b,w){delete e.Uj[y];if(b)return Object(ma.i)('Error loading part "%s": %s, '.concat(y,", ").concat(b)),r(b);if(w.length!==f.stop-f.start)throw Error("Part data is wrong size!");r(!1,y,w,e.$e.Taa(y))});a.Sha=!0;a.cancel=h}else r(Error('File not found: "'.concat(y,'"')),y)};return x}(ya.a);Object(n.a)(ya);Object(n.b)(ya);ua["default"]=ya}}]);}).call(this || window)
