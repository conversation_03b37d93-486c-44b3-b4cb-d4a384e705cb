using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Text.Encodings.Web;
using System.Text.Unicode;
using HealthChecks.UI.Client;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.Auth.Extensions;
using Levelbuild.Frontend.WebApp.Features.Database.Extensions;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Services;
using Levelbuild.Frontend.WebApp.Features.Logging.Middlewares;
using Levelbuild.Frontend.WebApp.Shared.Localization;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Extensions;
using Levelbuild.Frontend.WebApp.Features.Viewer.Email.Services;
using Levelbuild.Frontend.WebApp.Shared.BackgroundTasks;
using Levelbuild.Frontend.WebApp.Shared.Constants;
using Levelbuild.Frontend.WebApp.Shared.Services.Files;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.WebEncoders;
using Microsoft.IdentityModel.Tokens;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Serilog;
using StackExchange.Redis;
using Vite.AspNetCore;

namespace Levelbuild.Frontend.WebApp;

internal static class Program
{
	private static readonly string? Environment = System.Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

	internal static bool IsDevelopment => Environment == ExtendedEnvironments.Development || IsTesting;

	internal static bool IsStaging => Environment == ExtendedEnvironments.Staging;

	internal static bool IsTesting => Environment == ExtendedEnvironments.Testing;
	
	internal static bool IsDevelopmentOrStaging => IsDevelopment || IsStaging;

	internal static bool IsRunningInContainer =>
		string.Equals(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER"), "True", StringComparison.InvariantCultureIgnoreCase);

	internal static bool IgnoreAuth => System.Environment.GetEnvironmentVariable("AUTHENTICATION") == "Ignore" && IsDevelopmentOrStaging;

	internal static string HostName => Dns.GetHostName();

	[ExcludeFromCodeCoverage]
	private static async Task Main(string[] args)
	{
		var builder = WebApplication.CreateBuilder(args);

		// Add the Vite Manifest Service.
		builder.Services.AddViteServices(options =>
		{
			options.Server.AutoRun = true;
			// if you want to change, also change it in appsettings.Development.json under Vite -> Server -> Https
			options.Server.Https = false;
		});

		// Configure Base Logger
		builder.Host.UseSerilog().ConfigureLogging((hostContext, logBuilder) =>
		{
			var loggerConfiguration = new LoggerConfiguration().ReadFrom.Configuration(hostContext.Configuration);

			Log.Logger = loggerConfiguration.CreateLogger();
			logBuilder.AddSerilog();
		});

		// Configure default thread pool
		var minThreads = 16;
		if(int.TryParse(builder.Configuration.GetSection("ThreadPoolConfig")["MinThreads"], out var threadResult) && threadResult > 0)
			minThreads = threadResult;
		ThreadPool.SetMinThreads(minThreads, minThreads);
		
		// Add in-memory datastore
		var redisMasterOptions = ConfigurationOptions.Parse(builder.Configuration.GetConnectionString("RedisMaster")!);
		builder.Services.AddKeyedSingleton<IConnectionMultiplexer>(RedisConstants.RedisWriteConnection, await ConnectionMultiplexer.ConnectAsync(redisMasterOptions));
		
		var redisReplicaOptions = ConfigurationOptions.Parse(builder.Configuration.GetConnectionString("RedisReplica")!);
		builder.Services.AddKeyedSingleton<IConnectionMultiplexer>(RedisConstants.RedisReadConnection, await ConnectionMultiplexer.ConnectAsync(redisReplicaOptions));

		// Add RedisAccessService
		builder.Services.AddSingleton<IRedisAccessService, RedisAccessService>();
		
		// Add EF Core context
		builder.Services.AddDatabaseContext(builder.Configuration);

		// Add LogManager
		builder.Services.AddSingleton<ILogManager, LogManager>();

		// Add VersionReader
		builder.Services.AddSingleton<IVersionReader, VersionReader>();

		// Add ThumbnailMicroService
		builder.Services.AddSingleton<IThumbnailMicroservice, ThumbnailMicroservice>();
		
		// Add FileCachingService
		builder.Services.AddSingleton<IFileCachingService, FileCachingService>();
		
		// Add DeepZoomMicroservice
		builder.Services.AddSingleton<IDeepZoomMicroservice, DeepZoomMicroservice>();
		
		// Add DeepZoomHelperService as a centralized way to generate and cache dzi's
		builder.Services.AddSingleton<IDeepZoomHelperService, DeepZoomHelperService>();
		
		// Add ThumbnailHelperService as a centralized way to generate and cache thumbnails
		builder.Services.AddSingleton<IThumbnailHelperService, ThumbnailHelperService>();
		
		// Add AssetService to use imports in razor pages
		builder.Services.AddSingleton<IAssetService, AssetService>();

		// Add EmailReader to work with Mails (.eml and .msg with and without rtf)
		builder.Services.AddSingleton<IEmailReader, EmailReader>();

		// Configure Zitadel
		builder.ConfigureZitadelAuthentication();

		// Add Controllers
		builder.Services.AddControllersWithViews();
		builder.Services.AddEndpointsApiExplorer();
		builder
			.AddSwaggerGen()
			.InitOdata();

		// Add view location expander
		builder.Services.Configure<RazorViewEngineOptions>(options => { options.ViewLocationExpanders.Add(new VerticalSliceViewLocationExpander()); });

		// don't encode UTF-8 Chars while rendering cshtml
		builder.Services.Configure<WebEncoderOptions>(options => { options.TextEncoderSettings = new TextEncoderSettings(UnicodeRanges.All); });

		// add Localization
		builder.Services.AddSingleton<StringLocalizerCache>();
		builder.Services.AddScoped<IExtendedStringLocalizerFactory, StringLocalizerFactory>();
		builder.Services.AddSingleton<ITranslationService, TranslationService>();

		builder.Services.AddLocalization();
		builder.Services.ConfigureOptions<CustomRequestLocalizationOptions>();
		
		builder.Services.AddProblemDetails();

		//add health checks
		builder.Services.AddHealthChecks()
			.AddNpgSql(
				connectionString: builder.Configuration.GetConnectionString("Postgres")!,
				name: "postgres-db",
				tags: new[] { "postgres" })
			.AddRedis(
				redisConnectionString: builder.Configuration.GetConnectionString("RedisReplica")!,
				name: "redis",
				tags: new[] { "redis" });

		builder.Services.AddResponseCompression(options => { options.EnableForHttps = true; });

		// Set max multipart body length to 10GB
		builder.Services.Configure<FormOptions>(x => { x.MultipartBodyLengthLimit = 10_000_000_000; });
		
		// add background services
		builder.Services.AddHostedService<FileCachingServiceCleanup>();

		//Todo: @TWE
		/* Below, we implemented the PoC config for OpenTelemetry.
		 * The DataSourceController was used as an Example Implementation.
		 * The GetFieldsToRemove function in particular
		 *
		 * You can Reach the JaegerUI with: http://localhost:16686
		 *
		 * OpenTelemtry package Registry:
		 * https://opentelemetry.io/ecosystem/registry/?language=dotnet&s=
		 *
		 * Useful Blog:
		 * https://www.mytechramblings.com/posts/getting-started-with-opentelemetry-and-dotnet-core/
		 *
		 * Number 3 and 4 are read worthy
		 * https://dev.to/rahul18789/distributed-tracing-best-practices-for-2023-4i4h
		 */


		/* The Activities used in Code are not going to fail, if no Endpoint is Provided.
		 *
		 *
		 */

		// builder.Services.AddSingleton(new ActivitySource("LevelStudio"));

		// Configure OpenTelemetry
		if (!builder.Configuration.GetSection("OpenTelemetry")["TracingEndpoint"].IsNullOrEmpty())
		{
			builder.Services.AddOpenTelemetry()
				.WithTracing(tb =>
				{
					tb.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService("LevelStudio"));

					tb.AddAspNetCoreInstrumentation(); // Captures incoming HTTP requests
					tb.AddHttpClientInstrumentation(); // Captures outgoing HTTP requests

					// zitadel?
					tb.AddGrpcClientInstrumentation();

					tb.AddEntityFrameworkCoreInstrumentation(options =>
					{
						options.EnrichWithIDbCommand = (activity, command) =>
						{
							activity.SetTag("db.query", command.CommandText);
						};
					});

					// for redis
					tb.AddRedisInstrumentation(RedisConstants.RedisWriteConnection);
					tb.AddRedisInstrumentation(RedisConstants.RedisReadConnection);
					// tb.AddRedisInstrumentation();

					/*
					 * add mongodb instrumentation needs DiagnosticsActivityEventSubscriber:
					 * 
					 * var mongoClientSettings = MongoClientSettings.FromUrl(new MongoUrl(connectionString));
					 *	mongoClientSettings.ClusterConfigurator = cb =>
					 *	{
					 *		cb.Subscribe<CommandStartedEvent>(e => { logger.Debug($"{e.CommandName} - {e.Command.ToJson()}"); });
					 *		var options = new InstrumentationOptions { CaptureCommandText = true };
					 *		cb.Subscribe(new DiagnosticsActivityEventSubscriber(options));
					 *	};
					 *	return new MongoClient(mongoClientSettings);
					 */ 
					tb.AddSource("MongoDB.Driver.Core.Extensions.DiagnosticSources");

					tb.AddOtlpExporter(otp => { otp.Endpoint = new Uri(builder.Configuration.GetSection("OpenTelemetry")["TracingEndpoint"]!); });
				});

			builder.Logging.AddOpenTelemetry(x =>
			{
				x.IncludeScopes = true;
				x.IncludeFormattedMessage = true;
			});

			await Console.Out.WriteLineAsync("Tracing: initialized");
			// Log.Logger.Information("Tracing initialized");
		}
		else
		{
			await Console.Out.WriteLineAsync("Tracing: not configured");
			// Log.Logger.Warning("Tracing not configured");
		}

		builder.Services.AddScoped<Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Utils.WopiDiscoveryService>();

		var app = builder.Build();

		// Configure the HTTP request pipeline.
		if (IsDevelopmentOrStaging)
		{
			app.UseDeveloperExceptionPage();
		}
		else
		{
			app.UseExceptionHandler("/Error/500");
			app.UseStatusCodePagesWithReExecute("/Error/{0}");
			// The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
			app.UseHsts();
		}

		app.AddSwagger();

		app.UseHttpsRedirection();

		var provider = new FileExtensionContentTypeProvider
		{
			Mappings =
			{
				[".res"] = "application/octet-stream",
				[".mem"] = "application/octet-stream",
				[".wasm"] = "application/wasm"
			}
		};
		app.UseStaticFiles(new StaticFileOptions
		{
			ContentTypeProvider = provider,
		});

		app.UseRouting();
		app.UseAuthentication();
		app.UseAuthorization();
		app.UseRequestLocalization();
		app.MapControllers();
		app.MapControllerRoute(
			name: "default",
			pattern: "{controller=Home}/{action=Index}/{id?}"
		).RequireAuthorization();

		app.UseMiddleware<UsernameLoggingMiddleware>();
		
		app.MapHealthChecks("Api/Health", new HealthCheckOptions()
		{
			ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
		}).AllowAnonymous();
		app.MapGet("Api/Health/Ready", () => "Ready")
			.AllowAnonymous()
			.ExcludeFromDescription();


		// We want to run the Migration only if it is specified or in develop mode (RIDER IDE with ENVIRONMENT=DEVELOPMENT)
		var isMigration = string.Equals(System.Environment.GetEnvironmentVariable("ISMIGRATION"), "True", StringComparison.InvariantCultureIgnoreCase);
		if (isMigration || IsDevelopmentOrStaging)
		{
			Log.Logger.Information("Run Migration");
			await using (var scope = app.Services.CreateAsyncScope())
			{
				await using (var context = await scope.ServiceProvider.GetRequiredService<IDbContextFactory<CoreDatabaseContext>>().CreateDbContextAsync())
				{
					await context.HandleStartupAsync(Log.Logger, app.Services);

					// import missing Component translations
					await TranslationService.ImportFromFileAsync(context, "Translations.i18n.json");
				}
			}

			// Exit Program if it is only for Migration
			if (isMigration)
			{
				await Console.Out.WriteLineAsync("Migration Finished. App is shutting down.");
				System.Environment.Exit(0);
			}
		}

		// Print to know which Env is used at App start
		await Console.Out.WriteLineAsync("Environment: " + System.Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"));

		app.UseResponseCompression();

		// Init Vite frontend server for development
		if (IsDevelopment && !IsRunningInContainer)
		{
			Log.Logger.Information("Initialize Vite Development Server");
			app.UseWebSockets();
			app.UseViteDevelopmentServer(true);
		}
		
		// remove response cache header on failure
		app.Use(async (context, next) =>
		{
			context.Response.OnStarting(() =>
			{
				if (context.Response.StatusCode >= 400)
				{
					context.Response.Headers.Remove("Cache-Control");
				}
				return Task.FromResult(0);
			});
			await next();
		});

		app.Lifetime.ApplicationStarted.Register(OnStartup);
		app.Lifetime.ApplicationStopping.Register(OnStopping);
		app.Lifetime.ApplicationStopped.Register(OnStopped);
		await app.RunAsync();
	}

	private static void OnStartup()
	{
		Log.Logger.Information("App started");
	}

	private static void OnStopping()
	{
		Log.Logger.Information("App stopping");
	}

	private static void OnStopped()
	{
		Log.Logger.Information("App stopped");
	}
}