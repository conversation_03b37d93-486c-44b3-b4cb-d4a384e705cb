using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Globalization;
using System.Linq.Expressions;
using System.Reflection;
using FluentMigrator.Infrastructure.Extensions;
using JetBrains.Annotations;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Extensions;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.Reflection;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.Utils;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using SharpCompress;
using DataType = Levelbuild.Core.SharedDtos.Enums.DataType;
using ILogger = Serilog.ILogger;
using Type = System.Type;

namespace Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;

/// <summary>
/// Base class for all frontend controllers
/// </summary>
[ApiExplorerSettings(IgnoreApi = true)]
public abstract class FrontendController : Controller
{
	/// <summary>
	/// The injected version reader.
	/// </summary>
	protected readonly IVersionReader VersionReader;
	
	/// <summary>
	/// The injected log manager.
	/// </summary>
	protected readonly ILogManager LogManager;
	
	/// <summary>
	/// The injected logger.
	/// </summary>
	protected readonly ILogger Logger;
	
	/// <summary>
	/// Id for log tracing.
	/// </summary>
	// ReSharper disable once ConditionalAccessQualifierIsNonNullableAccordingToAPIContract
	protected string? TraceId => Activity.Current?.Id ?? HttpContext?.TraceIdentifier;
	
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="logger"></param>
	/// <param name="versionReader">injected VersionReader</param>
	protected FrontendController(ILogManager logManager, ILogger logger, IVersionReader versionReader)
	{
		LogManager = logManager;
		Logger = logger;
		VersionReader = versionReader;
	}
	
	#region Response Handling
	
	/// <summary>
	/// Creates an OK (HTTP 200) response without payload.
	/// </summary>
	public ActionResult<FrontendResponse> GetOkResponse()
	{
		return Ok(new FrontendResponse(Program.HostName, VersionReader.GetVersion()));
	}
	
	/// <summary>
	/// Creates an OK (HTTP 200) response containing a payload of the given type T.
	/// </summary>
	/// <param name="data">The payload.</param>
	/// <typeparam name="T">The type of data contained in the FrontendResponse (Entity or DTO).</typeparam>
	public ActionResult<FrontendResponse> GetOkResponse<T>(T data) where T : IResponseObject
	{
		return Ok(new FrontendResponse<T>(Program.HostName, VersionReader.GetVersion(), data));
	}

	/// <summary>
	/// Creates an OK (HTTP 200) response containing a payload of the given type T.
	/// </summary>
	/// <param name="payload">The payload.</param>
	/// <typeparam name="T">The type of data contained in the FrontendResponse (Entity or DTO).</typeparam>
	// ReSharper disable once MemberCanBeProtected.Global
	public ActionResult<FrontendResponse> GetOkResponse<T>(ServerResponsePayload<T> payload)
	{
		return Ok(new FrontendResponse<T>(Program.HostName, VersionReader.GetVersion(), payload.Payload));
	}
	
	/// <summary>
	/// Creates a BadRequest (HTTP 400) response containing an error message.
	/// </summary>
	/// <param name="message">The error message.</param>
	public ActionResult<FrontendResponse> GetBadRequestResponse(string message)
	{
		var response = new FrontendResponse(Program.HostName, VersionReader.GetVersion())
		{
			Error = new(400, message, TraceId)
		};
		return BadRequest(response);
	}
	
	/// <summary>
	/// Creates a BadRequest (HTTP 400) response containing an error message.
	/// </summary>
	/// <param name="validationErrors">list of validation errors</param>
	public ActionResult<FrontendResponse> GetValidationFailedResponse(IList<ValidationError> validationErrors)
	{
		var response = new FrontendResponse(Program.HostName, VersionReader.GetVersion())
		{
			Error = new(400, "Validation failed", TraceId),
			ValidationErrors = validationErrors
		};
		return BadRequest(response);
	}
	
	/// <summary>
	/// Creates a NotFound (HTTP 404) response containing an error message.
	/// </summary>
	/// <param name="message">The error message.</param>
	public ActionResult<FrontendResponse> GetNotFoundResponse(string message)
	{
		var response = new FrontendResponse(Program.HostName, VersionReader.GetVersion())
		{
			Error = new(404, message, TraceId)
		};
		return NotFound(response);
	}
	
	/// <summary>
	/// Creates a Unprocessable Content (HTTP 422) response containing an error message.
	/// </summary>
	/// <param name="message">The error message.</param>
	public ActionResult<FrontendResponse> GetUnprocessableEntityResponse(string message)
	{
		var response = new FrontendResponse(Program.HostName, VersionReader.GetVersion())
		{
			Error = new(422, message, TraceId)
		};
		return UnprocessableEntity(response);
	}
	
	/// <summary>
	/// Creates a MethodNotAllowed (HTTP 405) response containing an error message.
	/// </summary>
	/// <param name="message">The error message.</param>
	public ActionResult<FrontendResponse> GetUnauthorizedResponse(string message)
	{
		var response = new FrontendResponse(Program.HostName, VersionReader.GetVersion())
		{
			Error = new(401, message, TraceId)
		};
		return Unauthorized(response);
	}
	
	/// <summary>
	/// Creates an Unauthorized (HTTP 401) response containing an error message.
	/// </summary>
	/// <param name="message">The error message.</param>
	public ActionResult<FrontendResponse> GetForbiddenRequestResponse(string message)
	{
		var response = new FrontendResponse(Program.HostName, VersionReader.GetVersion())
		{
			Error = new(403, message, TraceId)
		};
		return Unauthorized(response);
	}
	
	/// <summary>
	/// Creates an empty Unauthorized (HTTP 415) response.
	/// </summary>
	protected ActionResult GetUnsupportedMediaTypeResponse()
	{
		return this.UnsupportedMediaType(null);
	}
	
	#endregion
	
	#region View Handling
	
	/// <summary>
	/// Skips rendering the layout around the view if the request is part of a js fetch and is able to load a different base view and handover different
	/// properties to base- and target view so that the base view can be rendered first and can either integrate the target view or call it afterwards via ajax
	/// </summary>
	/// <param name="baseView">Should a different view be rendered if this is no fetch request? (for example render list instead of create and integrate create into list view)</param>
	/// <param name="baseViewData">Dies the base view need some additional information to render properly?</param>
	/// <returns></returns>
	protected IActionResult RenderPartial([AspMvcView] string? baseView = null,
										  IDictionary<string, object>? baseViewData = null)
	{
		// if CacheControl is not set until this point, explicitly forbid caching
		if (Response.Headers.CacheControl.IsNullOrEmpty())
			Response.Headers.CacheControl = "no-cache, no-store, must-revalidate";
		
		string currentMenu = "";
		if (Request.RouteValues.TryGetValue("menu", out var menuRoutValue))
			currentMenu = menuRoutValue != null ? (string)menuRoutValue : "";
		
		if (!string.IsNullOrEmpty(currentMenu))
			ViewData["targetMenu"] = currentMenu;
		
		// check if we are allowed to do a partial rendering (check if the current request is a fetch request)
		if (IsPartialRequest())
			return PartialView();
		
		// is this an inline menu request?
		if (IsMenuRequest() && !string.IsNullOrEmpty(currentMenu))
			return PartialView($"_{currentMenu}");
		
		// do we have a different base view which we need to render?
		if (baseView.IsNullOrEmpty())
			return View();
		
		// Step 1: remember view data to later hand it over to the target action
		//IDictionary<string, object?> targetData = new Dictionary<string, object?>();
		ViewDataDictionary targetData = new ViewDataDictionary(new EmptyModelMetadataProvider(), ControllerContext.ModelState);
		foreach (var entry in ViewData)
		{
			targetData[entry.Key] = entry.Value;
		}
		
		// Step 2: clear view data and set target data
		ViewData.Clear();
		ViewData["targetMenu"] = currentMenu;
		ViewData["targetAction"] = ControllerContext.RouteData.Values["action"]?.ToString();
		ViewData["targetViewData"] = targetData;
		
		
		// Step 3: set new base view data (if supplied)
		if (baseViewData?.Count > 0)
		{
			foreach (var entry in baseViewData)
			{
				ViewData[entry.Key] = entry.Value;
			}
		}
		
		return View(baseView);
	}
	
	/// <summary>
	/// Skips rendering the layout around the view if the request is part of a js fetch 
	/// </summary>
	/// <param name="baseView">the view which has to be loaded first before we can render the child view</param>
	/// <param name="baseViewModel">the model needed for the child view to render properly</param>
	/// <param name="viewModel">dto to hand over to the view</param>
	/// <returns></returns>
	protected IActionResult RenderPartial(object viewModel, [AspMvcView] string? baseView = null,
										  [AspMvcModelType] object? baseViewModel = null)
	{
		// if CacheControl is not set until this point, explicitly forbid caching
		if (Response.Headers.CacheControl.IsNullOrEmpty())
			Response.Headers.CacheControl = "no-cache, no-store, must-revalidate";
		
		string currentMenu = "";
		if (Request.RouteValues.TryGetValue("menu", out var menuRoutValue))
			currentMenu = menuRoutValue != null ? (string)menuRoutValue : "";
		
		var viewDirectoryProperty = viewModel.GetType().GetProperty("ViewDirectory");
		var viewDirectory = viewDirectoryProperty?.GetValue(viewModel)?.ToString() ?? "";
		
		if (!string.IsNullOrEmpty(currentMenu))
			ViewData["targetMenu"] = currentMenu;
		
		// check if we are allowed to do a partial rendering (check if the current request is a fetch request)
		if (IsPartialRequest())
			return PartialView(viewModel);
		
		// is this an inline menu request?
		if (IsMenuRequest() && !string.IsNullOrEmpty(currentMenu))
			return PartialView($"_{currentMenu}", viewModel, viewDirectory);
		
		// do we have a different base view which we need to render?
		if (baseView.IsNullOrEmpty())
			return View(viewModel);
		
		ViewData.Clear();
		ViewData["targetAction"] = ControllerContext.RouteData.Values["action"]?.ToString();
		ViewData["targetViewModel"] = viewModel;
		ViewData["targetMenu"] = currentMenu;
		
		return View(baseView, baseViewModel);
	}
	
	// ReSharper disable once MemberCanBePrivate.Global
	/// <summary>
	/// For partial requests of a (empty) view.
	/// </summary>
	/// <param name="name"></param>
	/// <param name="model"></param>
	/// <param name="viewDirectory"></param>
	/// <returns></returns>
	protected PartialViewResult PartialView([AspMvcPartialView] string? name, [AspMvcModelType] object? model,
											string viewDirectory)
	{
		IViewEngine? viewEngine = HttpContext.RequestServices.GetService(typeof(ICompositeViewEngine)) as ICompositeViewEngine;
		if (viewDirectory != "" && viewEngine?.FindView(ControllerContext, $"{viewDirectory}{name}", false).Success == true)
			return PartialView($"{viewDirectory}{name}", model);
		return PartialView($"{name}", model);
	}
	
	/// <summary>
	/// For partial requests of a (empty) view, set the correct eTag Headers and return 304 if the browser already has the current version
	/// </summary>
	/// <param name="eTag">ETag of the item which gets represented by the view (defaults to the build version if not specified)</param>
	/// <param name="maxAge">How long is the browser allowed to cache the view without needing to revalidate (seconds)</param>
	/// <returns></returns>
	protected IActionResult? CachedPartial(string? eTag = null, int maxAge = 300)
	{
		if (!IsPartialRequest())
			return null;
		
		// set eTag to build version if not specified
		eTag ??= Assembly.GetEntryAssembly()?.GetName().Version?.ToString();
		
		// language is important part of the etag!
		// TODO: take last config change date for this culture into account 
		eTag += ":" + CultureInfo.CurrentCulture;
		
		var requestTag = Request.Headers.IfNoneMatch;
		if (!eTag.IsNullOrEmpty() && eTag == requestTag)
			return StatusCode(304);
		
		Response.Headers.CacheControl = $"max-age={maxAge}";
		Response.Headers.ETag = eTag;
		return null;
	}
	
	/// <summary>
	/// Skips rendering the layout around the view if the request is part of a js fetch and is able to load a different base view and handover different
	/// properties to base- and target view so that the base view can be rendered first and can either integrate the target view or call it afterwards via ajax
	/// </summary>
	/// <param name="baseViewModel">The base view need some additional information to render properly?</param>
	/// <param name="viewDirectory">Path of the base view</param>
	/// <param name="baseView">Should a different view be rendered if this is no fetch request? (for example render list instead of create and integrate create into list view)</param>
	/// <returns></returns>
	public IActionResult RenderPartialFromDirectory([AspMvcModelType] object? baseViewModel, string viewDirectory,
													string? baseView = null)
	{
		// if CacheControl is not set until this point, explicitly forbid caching
		if (Response.Headers.CacheControl.IsNullOrEmpty())
			Response.Headers.CacheControl = "no-cache, no-store, must-revalidate";
		
		var view = baseView ?? ControllerContext.ActionDescriptor.ActionName;
		
		string currentMenu = "";
		if (Request.RouteValues.TryGetValue("menu", out var menuRoutValue))
			currentMenu = menuRoutValue != null ? (string)menuRoutValue : "";
		
		if (!string.IsNullOrEmpty(currentMenu))
			ViewData["targetMenu"] = currentMenu;
		
		// check if we are allowed to do a partial rendering (check if the current request is a fetch request)
		if (IsPartialRequest())
			return PartialView($"{viewDirectory}{view}", baseViewModel);
		
		// is this an inline menu request?
		if (IsMenuRequest() && !string.IsNullOrEmpty(currentMenu))
			return PartialView($"{viewDirectory}/_{currentMenu}", baseViewModel);
		
		return View($"{viewDirectory}{view}", baseViewModel);
	}
	
	#endregion
	
	#region Filter handling

	/// <summary>
	/// parse frontend filter conditions and take multiple filters based on the same field into account
	/// </summary>
	/// <param name="dataSource">datasource the filters are based on</param>
	/// <param name="filters">list of filter conditions</param>
	/// <returns></returns>
	protected QueryFilterGroup? ParseFrontendFilters(DataSourceEntity dataSource, IList<QueryParamFilterDto> filters)
	{
		if (filters.Count == 0)
			return null;
		
		// group by filter field
		var andFilterGroup = new QueryFilterGroup();
		filters.GroupBy(filter => filter.FilterColumn).ForEach(byField =>
		{
			// try to find a matching field
			var filterColumn = !string.IsNullOrEmpty(byField.Key) ? dataSource.Fields.FirstOrDefault(field => field.Name == byField.Key.Split(".")[0]) : null;

			// inside one field, group by link type of filter operators
			byField.GroupBy(filter => filter.Operator.GetLinkType()).ForEach(byLinkType =>
			{
				var filterGroup = string.IsNullOrEmpty(byField.Key) || byLinkType.Key == QueryParamFilterLinkType.And ? new QueryFilterGroup() : new QueryFilterGroup(QueryFilterLinkType.Or);
				byLinkType.ForEach(queryFilter =>
				{
					// fulltext filter is the only one which does not require a valid filter column
					if (filterColumn == null && queryFilter.Operator != QueryParamFilterOperator.FulltextSearch && queryFilter.Operator != QueryParamFilterOperator.Favorite  && queryFilter.Operator != QueryParamFilterOperator.Inactive)
					{
						Logger.Warning("skipped parsing filter with unknown filter field {FilterFieldName} for definition {DefinitionName}", byField.Key, dataSource.Name);
						return;
					}
					
					// parse compare value to correct filter column type
					var compareType = filterColumn?.Type ?? DataType.String;
					if (queryFilter.CompareList != null)
						queryFilter.CompareValue = DataStoreUtils.ParseMultiDataValues(queryFilter.CompareList, compareType, DataStoreUtils.ParseRequestElementDataValue);
					else
						queryFilter.CompareValue = DataStoreUtils.ParseRequestElementDataValue(queryFilter.CompareValue, compareType);

					// add wildcard for like/notLike operations
					if (queryFilter.Operator is QueryParamFilterOperator.Like or QueryParamFilterOperator.NotLike && queryFilter.CompareValue != null && queryFilter.CompareValue.ToString() != "")
						queryFilter.CompareValue = $"%{queryFilter.CompareValue}%";
					
					queryFilter.AddToQueryFilterGroup(filterGroup, filterColumn?.Type);
				});
				andFilterGroup.AddFilterGroup(filterGroup);
			});
		});

		return andFilterGroup;
	}
	
	#endregion
	
	#region Helpers
	
	/// <summary>
	/// Checks for uniqueness of the given combination of values for the given property in the given dbSet
	/// </summary>
	/// <param name="dbSet">Entity set inside which the element can be found</param>
	/// <param name="propertyNames">Array of strings describing the property which should be checked</param>
	/// <param name="entityDto">the dto filled with data which has to be written into the entity</param>
	/// <param name="id">Id of the element which should be deleted</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns></returns>
	/// <exception cref="Exception"></exception>
	protected bool IsUniquePropertyValues<TEntity, TEntityDto>(DbSet<TEntity> dbSet, string[] propertyNames, TEntityDto entityDto, Guid? id = null)
		where TEntity : class, IPersistentEntity
		where TEntityDto : EntityDto
	{
		Expression? body = null;
		var entity = Expression.Parameter(typeof(TEntity));
		foreach (var propertyName in propertyNames)
		{
			var propertyValue = typeof(TEntityDto).GetProperty(propertyName)?.GetValue(entityDto, null);
			
			// does the entity have a property with matching name?
			var propertyInfo = typeof(TEntity).GetProperty(propertyName);
			if (propertyInfo == null)
				throw new Exception($"property {propertyName} not found inside type {typeof(TEntity).Name}");
			
			// build expression
			var propertyFieldExpression = Expression.Property(entity, propertyName);
			var propertyValueExpression = Expression.Convert(Expression.Constant(propertyValue), propertyFieldExpression.Type);
			Expression propertyCheck;
			
			// add case-insensitive if it's a string
			if (propertyFieldExpression.Type == typeof(string))
			{
				var nullCheckProp = Expression.Equal(propertyFieldExpression, Expression.Constant(null, propertyFieldExpression.Type)); 
				var nullCheckVal = Expression.Equal(propertyValueExpression, Expression.Constant(null, propertyValueExpression.Type)); 
				var nullCheckBoth = Expression.OrElse(nullCheckProp,nullCheckVal);

				var leftExpression = Expression.Call(propertyFieldExpression, typeof(string).GetMethod("ToLower", Type.EmptyTypes)!);
				var rightExpression = Expression.Call(propertyValueExpression, typeof(string).GetMethod("ToLower", Type.EmptyTypes)!);
				var originalExpression = Expression.Equal(leftExpression, rightExpression);
				
				// check on null -> if one is null: no comparison
				propertyCheck = Expression.Condition(nullCheckBoth, Expression.Constant(false), originalExpression);
			}
			else
			{
				propertyCheck = Expression.Equal(propertyFieldExpression, propertyValueExpression);
			}
			
			// create expression and combine property + id check if needed
			body = body != null ? Expression.AndAlso(body, propertyCheck) : propertyCheck;
		}
		
		// if id is set (aka we perform an update), we need to exclude the current elements id from the results
		var idField = Expression.Property(entity, "Id");
		var idCheck = id.HasValue ? Expression.NotEqual(idField, Expression.Constant(id.Value)) : null;
		
		body = idCheck != null
				   ? (
						 body != null ? Expression.AndAlso(body, idCheck) : idCheck
					 )
				   : body;
		
		var expression = Expression.Lambda<Func<TEntity, bool>>(body!, entity);
		
		// execute expression and return
		return !dbSet.Any(expression);
	}
	
	/// <summary>
	/// Checks for uniqueness of the given value for the given property in the given dbSet
	/// </summary>
	/// <param name="dbSet">Entity set inside which the element can be found</param>
	/// <param name="propertyName">String describing the property which should be checked</param>
	/// <param name="propertyValue">value of the property which is to be checked for uniqueness</param>
	/// <param name="id">Id of the element which should be deleted</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <returns></returns>
	/// <exception cref="Exception"></exception>
	protected bool IsUniquePropertyValue<TEntity>(DbSet<TEntity> dbSet, string propertyName, object? propertyValue,
												  Guid? id = null)
		where TEntity : class, IPersistentEntity
	{
		// does the entity have a property with matching name?
		var propertyInfo = typeof(TEntity).GetProperty(propertyName);
		if (propertyInfo == null)
			throw new Exception($"property {propertyName} not found inside type {typeof(TEntity).Name}");
		
		// is the property a GUID it will always be unique and we don't need to test
		if (propertyInfo.PropertyType == typeof(Guid))
			return true;
		
		// build expression
		var entity = Expression.Parameter(typeof(TEntity));
		
		var propertyField = Expression.Property(entity, propertyName);
		var propertyCheck = Expression.Equal(propertyField, Expression.Constant(propertyValue));
		
		// if id is set (aka we perform an update), we need to exclude the current elements id from the results
		var idField = Expression.Property(entity, "Id");
		var idCheck = id.HasValue ? Expression.NotEqual(idField, Expression.Constant(id.Value)) : null;
		
		// create expression and combine property + id check if needed
		var body = idCheck != null ? Expression.AndAlso(propertyCheck, idCheck) : propertyCheck;
		var expression = Expression.Lambda<Func<TEntity, bool>>(body, entity);
		
		// execute expression and return
		return !dbSet.Any(expression);
	}
	
	/// <summary>
	/// Checks if all required fields are set.
	/// </summary>
	/// <param name="entity"></param>
	/// <typeparam name="TEntity"></typeparam>
	/// <returns></returns>
	protected static PropertyInfo? CheckRequiredFields<TEntity>(TEntity entity)
		where TEntity : class, IPersistentEntity
	{
		// get all required fields and check if we have a value to work with
		var requiredFields = typeof(TEntity).GetProperties().Where(p => p.HasAttribute<RequiredAttribute>());
		foreach (PropertyInfo field in requiredFields)
		{
			var value = field.GetValue(entity);
			if (value == null || value.ToString().IsNullOrEmpty() || value.ToString() == Guid.Empty.ToString())
				return field;
		}
		
		return null;
	}
	
	/// <summary>
	/// Checks if a partial view is requested.
	/// </summary>
	/// <returns></returns>
	protected bool IsPartialRequest()
	{
		// browser navigations (new tab, tab refresh etc.) are detected by the Sec-Fetch-Mode: navigate header
		// (X-Requested-With would be another option, but setting this to XMLHttpRequest feels a bit weird)
		// Request.Headers.TryGetValue("Sec-Fetch-Mode", out var fetchMode);
		// return !string.IsNullOrEmpty(fetchMode) && fetchMode != "navigate" &&
		
		// custom url property is needed for the browser to distinguish between inline and full pages and be able to cache only inline ones 
		return Request.Query.ContainsKey("inline");
	}
	
	// ReSharper disable once MemberCanBePrivate.Global
	/// <summary>
	/// Checks if a partial menu view is requested.
	/// </summary>
	/// <returns></returns>
	protected bool IsMenuRequest()
	{
		// custom url property is needed for the browser to distinguish between inline within element (side-nav used) and normal inline requests
		return Request.Query.ContainsKey("menuInline");
	}
	
	/// <summary>
	/// Retrieves all columns of an entity which are transferred to the list frontend
	/// </summary>
	/// <returns></returns>
	protected PropertyPathList<TEntity> GetFilterableHeaderColumns<TEntity, TEntityDto>() where TEntity : class
	{
		PropertyPathList<TEntity> columns = new PropertyPathList<TEntity>();
		var entityType = typeof(TEntity);
		typeof(TEntityDto).GetProperties()
			.Where(property => Attribute.IsDefined(property, typeof(HeaderValueAttribute)))
			.ForEach(property =>
			{
				if (entityType.GetProperty(property.Name) != null)
					columns.Add(PropertyPath.BuildFromString(property.Name, typeof(TEntity)));
			});
		return columns;
	}
	
	#endregion
}