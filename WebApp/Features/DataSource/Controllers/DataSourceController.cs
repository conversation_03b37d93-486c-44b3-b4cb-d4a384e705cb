using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.DataStoreConfig;
using Levelbuild.Entities.Features.Module;
using Levelbuild.Entities.Helpers.DataSource;
using Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Module.ViewModels;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Reflection;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using SharpCompress;

namespace Levelbuild.Frontend.WebApp.Features.DataSource.Controllers;

/// <summary>
/// Controller for the configuration view of data sources
/// </summary>
[Route("[controller]")]
public class DataSourceController : AdminController<DataSourceDto>
{
	private IStringLocalizer? _localizer;

	//Todo: @TWE
	private readonly ActivitySource _activitySource;

	/// <inheritdoc />
	public DataSourceController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
								IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader, ActivitySource activitySource) :
		base(logManager, logManager.GetLoggerForClass<DataSourceController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		_activitySource = activitySource;
	}

	#region Views

	/// <summary>
	/// Renders the list view with help of the list dto
	/// </summary>
	/// <returns>rendered list view</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/DataSources/")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/DataSources/")]
	public IActionResult List(string? dataStoreSlug)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity("Admin - DataSource List");

		return CachedPartial() ?? RenderPartial(new DataSourceList()
		{
			DataStores = DatabaseContext.DataStoreConfigs.AsQueryable().OrderBy(store => store.Name).ToDtoList()
		});
	}

	/// <summary>
	/// Renders the detail view with help of the data source dto
	/// </summary>
	/// <param name="dataStoreSlug">readable identifier for a specific data store</param>
	/// <param name="moduleSlug">readable identifier for a specific module</param>
	/// <param name="slug">readable identifier for a specific data source</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/DataSources/Edit/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/DataSources/{slug}/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/Modules/{moduleSlug?}/DataSources/{slug}/{menu?}")]
	public IActionResult Detail(string? dataStoreSlug, string? moduleSlug, string? slug)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity();

		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(slug))
		{
			return CachedPartial() ?? RenderPartial(new DataSourceForm(ViewType.Edit));
		}

		DataStoreConfigEntity? dataStore = DatabaseContext.DataStoreConfigs
			.FirstOrDefault(dataStore => dataStore.Slug == dataStoreSlug.ToLower());

		if (dataStore == null)
			throw new ElementNotFoundException($"DataStore configuration with slug: {dataStoreSlug} could not be found");

		DataSourceEntity? dataSource = DatabaseContext.DataSources
			.Include(config => config.DataStore)
			.Include(config => config.Module)
			.Include(config => config.AnnotationSource)
			.Include(config => config.AnnotationKeyField)
			.Include(config => config.AnnotationGroupByField)
			.Include(config => config.AnnotationCreatePage)
			.Include(config => config.AnnotationDetailPage)
			.Include(config => config.DefaultDetailPage)
			.FirstOrDefault(config => config.DataStoreId == dataStore.Id && config.Slug == slug.ToLower());

		if (dataSource == null)
			throw new ElementNotFoundException($"DataSource configuration with slug: {slug} could not be found");

		if (moduleSlug != null)
		{
			ModuleEntity? module =
				DatabaseContext.Modules.FirstOrDefault(module => module.Slug == moduleSlug.ToLower() && module.DataStoreId == dataStore.Id &&
																 dataSource.ModuleId == module.Id);

			if (module == null)
				throw new ElementNotFoundException($"Module configuration with slug: {moduleSlug} could not be found");
		}

		return RenderPartial(new DataSourceForm(ViewType.Edit)
		{
			DataSource = dataSource.ToDto(),
			NavigateOverModule = moduleSlug != null
		});
	}

	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/DataSources/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/DataSources/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/Modules/{moduleSlug?}/{menu}/Create")]
	public IActionResult Create(string? dataStoreSlug, string? moduleSlug, [FromQuery(Name = "moduleId")] Guid? moduleId,
								[FromQuery(Name = "moduleName")] string? moduleName, [FromQuery(Name = "dataStoreId")] Guid? dataStoreId)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity();

		if (string.IsNullOrEmpty(dataStoreSlug))
		{
			return CachedPartial() ?? RenderPartial(new DataSourceForm()
			{
				DataSource = new DataSourceDto(), ModuleId = moduleId, ModuleName = moduleName, DataStoreId = dataStoreId
			});
		}

		dataStoreId = DatabaseContext.DataStoreConfigs.FirstOrDefault(config => config.Slug == dataStoreSlug)?.Id;
		if (!dataStoreId.HasValue)
			throw new ElementNotFoundException($"DataStore configuration with slug: {dataStoreSlug} could not be found");

		var module = string.IsNullOrEmpty(moduleSlug)
						 ? null
						 : DatabaseContext.Modules.Include(module => module.Responsible).FirstOrDefault(config => config.Slug == moduleSlug);

		if (module != null)
		{
			return CachedPartial() ?? RenderPartial(
					   new DataSourceForm(ViewType.Edit)
					   {
						   DataSource = new DataSourceDto()
							   { DataStoreId = dataStoreId.Value, ModuleId = module.Id, Module = module?.ToDto(), ModuleName = module?.Name }
					   }, "~/Features/Module/Views/Detail.cshtml",
					   new ModuleForm(ViewType.Edit)
					   {
						   Module = module!.ToDto()
					   });
		}

		return CachedPartial() ?? RenderPartial(new DataSourceForm() { DataSource = new DataSourceDto() { DataStoreId = dataStoreId.Value } }, "List",
												new DataSourceList()
												{
													DataStores = DatabaseContext.DataStoreConfigs.AsQueryable().ToDtoList().ToList()
												});
	}

	#endregion

	#region Actions

	/// <inheritdoc />
	[HttpGet("/Api/DataSources/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity();
		
		var sources = DatabaseContext.DataSources.Include(source => source.Module);
		return HandleQueryRequest<DataSourceEntity, DataSourceDto>(sources, parameters);
	}

	/// <summary>
	/// select data sources via autocomplete field
	/// </summary>
	/// <param name="parameters">query parameters</param>
	/// <param name="type">filter by DataSourceType</param>
	/// <returns></returns>
	[HttpGet("/Api/DataSources/Autocomplete")]
	public ActionResult<FrontendResponse> Autocomplete(QueryParamsDto parameters, [FromQuery(Name = "type")] DataSourceType? type)	{
		var query = DatabaseContext.DataSources.Include(dataSource => dataSource.DataStore).AsQueryable();
		
		// filter by type
		if (type.HasValue)
			query = query.Where(dataSource => dataSource.Type == type.Value);
		
		return HandleAutocompleteRequest(query, parameters,
										 nameof(DataSourceEntity.Id),
										 nameof(DataSourceEntity.Name),
										 new PropertyPathList<DataSourceEntity>()
										 {
											 nameof(DataSourceEntity.Slug),
											 (DataSourceEntity dataSource) => dataSource.DataStore.Slug
										 });
	}

	/// <summary>
	/// Returns a mutated list of data fields with field type DataField as JSON.
	/// </summary>
	/// <param name="dataSourceId">identifier for a specific data source</param>
	/// <param name="parameters">an object to query a Subset of data fields</param>
	/// <param name="fieldType">only return a specific type of fields?</param>
	/// <param name="targetSourceType">if the fields are of type LookupField, should the lookupSource have a special type as well?</param>
	/// <param name="excludeSystemFields">should system fields be excluded?</param>
	[HttpGet("/Api/DataSources/{dataSourceId}/DataFields")]
	public ActionResult<FrontendResponse> DataFields(Guid dataSourceId, QueryParamsDto parameters, [FromQuery(Name = "type")] DataFieldType? fieldType,
													 [FromQuery(Name = "targetSourceType")] DataSourceType? targetSourceType,
													 [FromQuery(Name = "excludeSystemFields")]
													 bool? excludeSystemFields)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity("Api - DataSource List DataFields");
		
		// filter by datasource
		var query = DatabaseContext.DataFields.Include(field => field.DataSource).ThenInclude(source => source!.DataStore)
			.Where(dataField => dataField.DataSourceId == dataSourceId);

		// filter by type
		if (fieldType.HasValue)
			query = query.Where(dataField => dataField.FieldType == fieldType.Value);
		
		// filter by target Source type if type = LookupField
		if (targetSourceType.HasValue && fieldType is DataFieldType.LookupField)
			query = query.Include(field => field.LookupSource)
				.Where(dataField => dataField.LookupSource != null && dataField.LookupSource.Type == targetSourceType);
		
		// exclude system fields
		if (excludeSystemFields is true)
			query = query.Where(dataField => !dataField.SystemField);

		return HandleAutocompleteRequest(query, parameters,
										 nameof(DataFieldEntity.Id),
										 nameof(DataFieldEntity.Name),
										 new PropertyPathList<DataFieldEntity>()
										 {
											 nameof(DataFieldEntity.Name),
											 nameof(DataFieldEntity.Type),
											 nameof(DataFieldEntity.Slug),
											 (DataFieldEntity field) => field.DataSource!.Slug,
											 (DataFieldEntity field) => field.DataSource!.DataStore.Slug,
										 });
	}
	
	/// <summary>
	/// returns a list of possible annotation key fields (filds used by the annotation to determine its parent blueprint)
	/// </summary>
	/// <param name="dataSourceId">annotation datasource id</param>
	/// <param name="parameters">filter parameters</param>
	/// <param name="parentSourceId">blueprint datasource id</param>
	/// <returns></returns>
	[HttpGet("/Api/DataSources/{dataSourceId:guid}/AnnotationKeyFields")]
	public ActionResult<FrontendResponse> AnnotationKeyFields(Guid dataSourceId, QueryParamsDto parameters, [FromQuery(Name = "parentSourceId")] Guid? parentSourceId)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity("Api - DataSource List AnnotationKeyFields");
		
		// filter by datasource
		var query = DatabaseContext.DataFields
			.Include(field => field.DataSource)
				.ThenInclude(source => source!.DataStore)
			.Where(dataField => dataField.DataSourceId == dataSourceId && (dataField.Type == DataType.Guid || dataField.Type == DataType.String) &&
								(dataField.LookupSourceId == null || dataField.LookupSourceId == parentSourceId) && !dataField.SystemField);

		return HandleAutocompleteRequest(query, parameters,
										 nameof(DataFieldEntity.Id),
										 nameof(DataFieldEntity.Name),
										 new PropertyPathList<DataFieldEntity>()
										 {
											 nameof(DataFieldEntity.Name),
											 nameof(DataFieldEntity.Type),
											 nameof(DataFieldEntity.Slug),
											 (DataFieldEntity field) => field.DataSource!.Slug,
											 (DataFieldEntity field) => field.DataSource!.DataStore.Slug
										 });
	}

	/// <summary>
	/// Returns a mutated list of lookup fields from a specific data source.
	/// </summary>
	/// <param name="dataSourceId">identifier of the data source the fields should belong to</param>
	/// <param name="parameters">an object to query a Subset of field configurations</param>
	[HttpGet("/Api/DataSources/{dataSourceId:guid}/LookupFields")]
	public ActionResult<FrontendResponse> LookupFields(Guid dataSourceId, QueryParamsDto parameters)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity("Api - DataSource List LookupFields");
		
		var query = DatabaseContext.DataFields.Include(field => field.DataSource).ThenInclude(source => source!.DataStore)
			.Where(dataField => dataField.DataSourceId == dataSourceId && dataField.FieldType == DataFieldType.LookupField);
		return HandleAutocompleteRequest(query, parameters,
										 nameof(DataSourceEntity.Id),
										 nameof(DataSourceEntity.Name),
										 new PropertyPathList<DataFieldEntity>()
										 {
											 nameof(DataFieldEntity.Name),
											 nameof(DataFieldEntity.Slug),
											 (DataFieldEntity field) => field.DataSource!.Name,
											 (DataFieldEntity field) => field.DataSource!.Comment,
											 (DataFieldEntity field) => field.DataSource!.Slug,
											 (DataFieldEntity field) => field.DataSource!.DataStore.Slug,
										 });
	}

	/// <inheritdoc />
	[HttpGet("/Api/DataSources/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		using var activity = _activitySource.StartActivity();
		
		var dataSources = DatabaseContext.DataSources
			.Include(source => source.DataStore)
			.Include(source => source.Module)
			.Include(source => source.AnnotationSource)
			.Include(source => source.AnnotationKeyField)
			.Include(source => source.AnnotationGroupByField)
			.Include(source => source.AnnotationCreatePage)
			.Include(source => source.AnnotationDetailPage)
			.Include(source => source.DefaultDetailPage);
		return HandleGetRequest<DataSourceEntity, DataSourceDto>(dataSources, id);
	}

	/// <inheritdoc />
	[HttpPost("/Api/DataSources/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] DataSourceDto configurationDto)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity();
		
		return await HandleCreateRequestAsync(DatabaseContext.DataSources, configurationDto, BeforeSaveAction, AfterSaveAction);

		void BeforeSaveAction(DataSourceEntity entity)
		{
			if (entity.DataStore.Type != DataStoreType.Storage)
				return;

			entity.SetConnection(new StorageDataSourceConnectionHelper(entity));

			entity.CreateDataSource();
		}

		void AfterSaveAction(DataSourceEntity entity)
		{
			entity.SyncStorageFields(DatabaseContext);
			PrepareDefaultFields(entity);
		}
	}

	/// <inheritdoc />
	[HttpPatch("/Api/DataSources/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] DataSourceDto configurationDto)
	{
		using var activity = _activitySource.StartActivity();
		
		// load current type for later comparison
		var entityInstance = await DatabaseContext.DataSources.FindAsync(id);
		var oldType = entityInstance?.Type;
		
		return await HandleUpdateRequestAsync(DatabaseContext.DataSources, id, configurationDto, AfterSaveAction);
		
		void AfterSaveAction(DataSourceEntity entity)
		{
			entity.UpdateDataSource();
			if (oldType != entity.Type)
				PrepareDefaultFields(entity);
		}
	}

	/// <inheritdoc />
	[HttpDelete("/Api/DataSources/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity();

		return HandleDeleteRequest(DatabaseContext.DataSources, id, entity =>
		{
			entity.RemoveDataSource();
			foreach (var dataField in entity.Fields)
			{
				if (dataField.FieldType == DataFieldType.VirtualField)
				{
					dataField.LoadVirtualData(DatabaseContext);
					dataField.RemoveReferencingVirtualIds();
					dataField.SetVirtualData(null);
					dataField.ForwardVirtualDataFieldChanges(DatabaseContext);
				}

				if (dataField.FieldType is DataFieldType.DataField or DataFieldType.LookupField)
				{
					var referencingFields =
						DatabaseContext.DataFields.Where(field => field.VirtualDataFieldId == entity.Id || field.LookupDisplayFieldId == entity.Id);
					referencingFields.ForEach(referencingField =>
					{
						if (referencingField.FieldType == DataFieldType.LookupField)
							referencingField.LookupDisplayFieldId = null;

						referencingField.SetVirtualData(null);
						referencingField.ForwardVirtualDataFieldChanges(DatabaseContext);
					});
				}
			}
			
			// @TODO: Refactor it so that manuel delete statements are not needed anymore - same in DataStore Delete
			// Without this, Backends with a GalleryView and (Sub)TitleField cannot be deleted 
			DatabaseContext.DataFields
				.Where(dataField => dataField.DataSource != null && dataField.DataSourceId == id)
				.ExecuteDelete();
		});
	}

	#endregion

	#region Sync

	/// <summary>
	/// Adds all fields that are present in the Storage but not in the WebApp to the WebApp. 
	/// </summary>
	/// <param name="id">Guid which identifies the data source</param>
	/// <returns>OK Response for successful request and BAD Response when an error occurred.</returns>
	[HttpGet("/Api/DataSources/{id:guid}/SyncMissingFields")]
	public ActionResult<FrontendResponse> SyncFields(Guid id)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity();

		try
		{
			var dataSource = DatabaseContext.DataSources.Find(id);

			if (dataSource == null)
				return GetNotFoundResponse($"No dataSource found for id: {id}");

			var response = new BatchActionResultDto<string>()
			{
				ErrorList = new()
			};

			try
			{
				var syncedFields = dataSource.SyncStorageFields(DatabaseContext);
				response.SuccessCount = syncedFields.Count;
			}
			catch (Exception e)
			{
				Logger.Error(e, $"DataSources could not be synced.");
				response.ErrorCount++;
				response.ErrorList.Add(e.Message);
			}

			response.TotalCount = response.SuccessCount + response.ErrorCount;
			_localizer ??= StringLocalizerFactory.Create("DataSource", "");
			response.Heading = _localizer["syncDialog/notification/addedFields/success", response.SuccessCount, response.TotalCount];
			if (response.ErrorCount > 0)
				response.Message = _localizer["syncDialog/notification/addedFields/error"];

			return GetOkResponse(response);
		}
		catch (Exception e)
		{
			Logger.Error(e, $"DataSources could not be synced.");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Returns a list of fields that are present in the WebApp but not in the Storage.
	/// </summary>
	/// <param name="id">Guid which identifies the data source</param>
	/// <returns>OK Response for successful request and BAD Response when an error occurred.</returns>
	[HttpGet("/Api/DataSources/{id:guid}/Sync")]
	public ActionResult<FrontendResponse> GetFieldsToRemove(Guid id)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity();

		try
		{
			using var activityFindDatasource = _activitySource.StartActivity("Find DataSource by Id");
			var dataSource = DatabaseContext.DataSources.Find(id);
			activityFindDatasource?.Stop();

			if (dataSource == null)
			{
				activity?.SetStatus(ActivityStatusCode.Error, "DataSource not found");
				return GetNotFoundResponse($"No DataSource found for id: {id}");
			}

			var activityGetFieldToSync = _activitySource.StartActivity("Get Fields to Sync");
			var fieldsToSync = dataSource.GetFieldsToSync(DatabaseContext);
			activityGetFieldToSync?.Stop();

			var activityFieldsToSyncDto = _activitySource.StartActivity("Get Fields to Sync DTO");
			FieldsToSyncDto responseFields = new()
			{
				Fields = fieldsToSync.Select(fieldEntity => new FieldToSyncDto()
				{
					Id = fieldEntity.Id,
					Name = fieldEntity.Name,
					Type = fieldEntity.Type.ToString()
				}).ToList()
			};
			activityFieldsToSyncDto?.Stop();

			activity?.SetStatus(ActivityStatusCode.Ok, "Syncing Fields");
			return GetOkResponse(responseFields);
		}
		catch (Exception e)
		{
			activity?.SetStatus(ActivityStatusCode.Error, "There was an error retrieving fields to remove. Please see logs for more details.");
			Logger.Error(e, $"Fields to sync could not be loaded.");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Removes fields from the WebApp.
	/// </summary>
	/// <param name="id">Guid which identifies the data source</param>
	/// <param name="fields">fields that should be removed</param>
	/// <returns>OK Response for successful removal and BAD Response when an error occurred.</returns>
	[HttpDelete("/Api/DataSources/{id:guid}/Sync")]
	public ActionResult<FrontendResponse> RemoveFields(Guid id, [FromBody] FieldsToSyncDto fields)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity();

		try
		{
			var fieldIds = fields.Fields.Select(field => field.Id).ToList();

			var dataSource = DatabaseContext.DataSources.Find(id);
			if (dataSource == null)
				return GetNotFoundResponse($"No DataSource found for id: {id}");

			var response = new BatchActionResultDto<string>
			{
				ErrorList = new(),
			};

			DataFieldEntity? fieldEntity = null;
			foreach (var fieldId in fieldIds)
			{
				try
				{
					fieldEntity = DatabaseContext.DataFields.Find(fieldId);
					if (fieldEntity == null)
					{
						response.ErrorCount++;
						response.ErrorList.Add(fieldEntity?.Name ?? fieldId.ToString());
						continue;
					}

					DatabaseContext.DataFields.Remove(fieldEntity);
					DatabaseContext.SaveChanges();
					response.SuccessCount++;
				}
				catch (Exception e)
				{
					response.ErrorCount++;
					response.ErrorList.Add(fieldEntity?.Name ?? fieldId.ToString());
					Logger.Error(e, $"Failed to remove field.");
				}
			}

			response.TotalCount = response.SuccessCount + response.ErrorCount;
			_localizer ??= StringLocalizerFactory.Create("DataSource", "");
			response.Heading = _localizer["syncDialog/notification/fields/success", response.SuccessCount, response.TotalCount];
			if (response.ErrorCount > 0)
				response.Message = _localizer["syncDialog/notification/fields/error"];

			return GetOkResponse(response);
		}
		catch (Exception e)
		{
			Logger.Error(e, $"Failed to remove fields.");
			return GetBadRequestResponse(e.Message);
		}
	}

	#endregion
	
	#region Helpers

	private void PrepareDefaultFields(DataSourceEntity dataSource)
	{
		switch (dataSource.Type)
		{
			case DataSourceType.Annotation:
				CreateFieldIfNotExists(dataSource, "AnnotationX", DataType.Integer);
				CreateFieldIfNotExists(dataSource, "AnnotationY", DataType.Integer);
				CreateFieldIfNotExists(dataSource, "AnnotationZ", DataType.Integer);
				CreateFieldIfNotExists(dataSource, "AnnotationWidth", DataType.Integer);
				CreateFieldIfNotExists(dataSource, "AnnotationHeight", DataType.Integer);
				CreateFieldIfNotExists(dataSource, "AnnotationType", DataType.Integer);
				break;
			case DataSourceType.ElementType:
				CreateFieldIfNotExists(dataSource, "ElementTypeIcon", DataType.String, InputDataType.Icon);
				break;
		}
	}

	private void CreateFieldIfNotExists(DataSourceEntity dataSource, string fieldName, DataType fieldType, InputDataType? inputDataType = null)
	{
		// Load FieldEntities if not already present
		if (dataSource.Fields.Count == 0)
			DatabaseContext.DataFields.Where(field => field.DataSourceId == dataSource.Id).Load();
		
		var fieldEntity = dataSource.Fields.FirstOrDefault(field => field.Name == fieldName);
		if (fieldEntity != null)
			return;

		fieldEntity = new DataFieldEntity()
		{
			Name = fieldName,
			Type = fieldType,
			InputDataType = inputDataType,
			AutoGenerated = true
		};
		dataSource.Fields.Add(fieldEntity);
		DatabaseContext.SaveChanges();
		
		fieldEntity.CreateField();
		dataSource.Touch();
	}
	
	#endregion
}