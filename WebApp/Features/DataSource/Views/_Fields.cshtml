@inject IExtendedStringLocalizerFactory LocalizerFactory
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.DataField.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.EditPanel
@model Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels.DataSourceForm
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("DataField", "");
	var sortings = new List<QueryParamSortingDto>()
	{
		new()
		{
			OrderColumn = "Name",
			Direction = SortDirection.Asc
		}
	};
	var fieldName = "";
	if (ViewData["targetViewModel"] != null && ViewData["targetAction"]?.ToString() == "Detail")
	{
		fieldName = (ViewData["targetViewModel"] as DataFieldForm)?.DataField?.Name;
	}
}
<script type="module" defer>
	// disable save button
	Page.buttonConfig.saveButton.disabled = true

	// reactivate save button when content changes
	Page.getContentChangeSignal().addEventListener("abort", () => {
		Page.buttonConfig.saveButton.disabled = false
	})
</script>
<style>
	lvl-data-list {
		width: clamp(300px, 80%, 1400px);
		background-color: var(--clr-background-lvl-0);
		padding: 0 var(--size-spacing-m);
		border-radius: var(--size-radius-m);
		box-shadow: 0 0 0.2rem 0 var(--clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--clr-shadow-weak);
	}
</style>
@* @TODO: create address book component *@
<div class="list-view">
	@* ReSharper disable CSharpWarnings::CS0618 *@
	<data-list-component id="data-field-list" label-column="icon" group-by="name" sortings="@sortings" responsive searchbar identity-column="id">
		<data-column-component name="slug" hidden></data-column-component>
		<data-column-component name="used" hidden></data-column-component>
		<data-column-component name="icon" hidden></data-column-component>
		<data-column-component name="name" min-width="300"></data-column-component>
		<data-column-component name="type" text-align="Alignment.Right"></data-column-component>
	</data-list-component>
</div>
<vc:admin-list-page entity="DataField" route-name="DataFields" localizer="@localizer" menu-entry="Fields" parent-property-name="dataSourceId" use-custom-list="true"></vc:admin-list-page>
<vc:create-panel entity="DataField" route-name="DataFields" localizer="@localizer" menu-entry="Fields" parent-property-name="dataSourceId"></vc:create-panel>
<vc:edit-panel entity="DataField" route-name="DataFields" route-params="{fieldType:'fieldType', dataType:'type', systemField:'systemField', autoGenerated:'autoGenerated', multi:'multi'}"
               localizer="@localizer" menu-entry="Fields" heading="@fieldName" skeleton="@(Model.DataSource == null)" ignore-overflow="true"></vc:edit-panel>
