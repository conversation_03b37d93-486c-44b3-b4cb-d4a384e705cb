@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels.DataSourceForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("DataSource", "");
	var typeLocalizer = LocalizerFactory.Create("DataSourceType", "");
}

<script type="module" defer>
	const form = document.getElementById('data-source-form')
	await Component.waitForComponentInitialization(form)
	const storagePathInput = form.querySelector('[name="storagePath"]')
	
	// use the input value of data source name as placeholder text in storage path
	form.querySelector('[name="name"]').addEventListener('input', (event) => {
		storagePathInput.placeholder = event.detail
	})
	
	// initialize Autocompletes with real data url
	const module = document.getElementById("data-source-module")
	await Component.waitForComponentInitialization(module)
	const dataStoreId = Page.getFormData()["id"]
	module.url = `/Api/DataStores/${dataStoreId}/Modules`
</script>
<form-component id="data-source-form" class="form">
	<config-section label="@localizer["sectionInfo"]">
		<div class="form__item">
			<input type="hidden" class="item__value" name="dataStoreId" value="@(Model.DataStoreId ?? Model.DataSource?.DataStoreId)"/>
		</div>
		<div class="form__item">
			<config-label target="data-source-name" label="@localizer["name"]"></config-label>
			<input-component id="data-source-name" name="name" class="item__value" required></input-component>
		</div>
		<div class="form__item">
			<config-label target="data-source-module" label="@localizer["module"]"></config-label>
			@{
				var moduleColumns = new List<AutocompleteColumnDefinition>
				{
					new("name", localizer["name"], DataType.String),
					new("description", localizer["description"], DataType.String, false, true)
				};
				<autocomplete-component type="InputDataType.Guid" id="data-source-module" nullable="true" 
			                        url="/Api/DataStores/@(Model.DataSource?.DataStoreId)/Modules"
				                        columns="moduleColumns" name="module" output-name="moduleId"
				                        value="@(Model.ModuleId ?? Model.DataSource?.ModuleId)" display-value="@(Model.ModuleName ?? Model.DataSource?.ModuleName)" class="item__value"
				                        placeholder="@localizer["pleaseChoose"]" readonly="@(Model.ModuleId != null || Model.DataSource?.ModuleId != null)"
			                        edit-link-url="/Admin/DataStores/{dataStore.Slug}/Modules/{slug}">
				</autocomplete-component>
			}
		</div>
		<div class="form__item">
			@{
				var columns = new List<AutocompleteColumnDefinition>
				{
					new ("LABEL", "label", DataType.String),
					new ("DESCRIPTION", "description", DataType.String)
				};
				var dataSourceTypes = Enum.GetValues(typeof(DataSourceType)).Cast<DataSourceType>().Select(
					level => new AutocompleteOptionDefinition(level.ToString(), [typeLocalizer[level.ToString()], typeLocalizer[$"{level}.Description"]])
				).ToList();
			}
			<config-label target="data-source-type" label="@localizer["type"]"></config-label>
			<autocomplete-component type="InputDataType.String" id="data-source-type" options="dataSourceTypes" name="type"
			                        value="@(DataSourceType.Default)" class="item__value"
			                        placeholder="@localizer["pleaseChoose"]" required="true" columns="@columns">
			</autocomplete-component>
		</div>
		<div class="form__item">
			<config-label target="data-source-comment" label="@localizer["comment"]"></config-label>
			<textarea-component id="data-source-comment" name="comment" class="item__value" placeholder="@localizer["commentPlaceholder"]"></textarea-component>
		</div>
		<div class="form__item">
			<config-label target="data-source-store-revision" label="@localizer["storeRevision"]"></config-label>
			<toggle-component id="data-source-store-revision" name="storeRevision" class="item__value" value="true"></toggle-component>
		</div>
		<div class="form__item">
			<config-label target="data-source-full-text-search" label="@localizer["fulltextSearch"]"></config-label>
			<toggle-component id="data-source-full-text-search" name="fulltextSearch" class="item__value" value="true"></toggle-component>
		</div>
		<div class="form__item">
			<config-label target="data-source-storage-path" label="@localizer["storagePath"]" description="@localizer["storagePathDescription"]"></config-label>
			<input-component id="data-source-storage-path" name="storagePath" class="item__value"></input-component>
		</div>
		<div class="form__item">
			<config-label target="data-source-full-text-search" label="@localizer["encryption"]"></config-label>
			<toggle-component id="data-source-encryption" name="encryption" class="item__value" value="true"></toggle-component>
		</div>
	</config-section>
</form-component>
