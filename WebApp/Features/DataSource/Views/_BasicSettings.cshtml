@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels.DataSourceForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("DataSource", "");
	var typeLocalizer = LocalizerFactory.Create("DataSourceType", "");
}

<script type="module" defer>
	const form = document.getElementById("data-source-form")
	const sourceId = Page.getFormData()["id"]
	await window.Sync.syncDataSource(sourceId)

	//set urls of autocomplete components
	await Component.waitForComponentInitialization(form)
	const module = form.querySelector('[name="module"]')
	await Component.waitForComponentInitialization('lvl-autocomplete')
	const dataStoreId = Page.getFormData()["dataStoreId"]
	module.url = `/Api/DataStores/${dataStoreId}/Modules`
	const annotationGroupByField = form.querySelector('[name="annotationGroupByField"]')
	annotationGroupByField.url = `/Api/DataSources/${sourceId}/DataFields?type=lookupField&targetSourceType=elementType`
	
	const annotationKeyField = form.querySelector('[name="annotationKeyField"]')
	form.querySelector('[name="annotationSource"]').addEventListener('change', event => {
		if (annotationKeyField.url)
			annotationKeyField.clear()
		annotationKeyField.url = `/Api/DataSources/${event.target.value}/AnnotationKeyFields?parentSourceId=${sourceId}`
	})
	
	const showIfMapping = new Map()
	showIfMapping.set("annotationSource", [[{option: "type", compareValue: "Blueprint"}]]);
	showIfMapping.set("annotationKeyField", [[{option: "type", compareValue: "Blueprint"}]]);
	showIfMapping.set("annotationGroupByField", [[{option: "type", compareValue: "Annotation"}]]);
	showIfMapping.set("annotationLabel", [[{option: "type", compareValue: "Annotation"}]]);
	showIfMapping.set("annotationCreatePage", [[{option: "type", compareValue: "Annotation"}]]);
	showIfMapping.set("annotationDetailPage", [[{option: "type", compareValue: "Annotation"}]]);
	Page.handleConditionalVisibility(form, showIfMapping)
</script>
<div class="grid--centered @(Model.ViewType == ViewType.Edit ? "vanishing-scrollbar static-scrollbar" : "")">
	<form-component id="data-source-form" skeleton="@(Model is { ViewType: ViewType.Edit, DataSource: null })">
		<div class="form__item">
			<input type="hidden" class="item__value" name="id" value="@Model.DataSource?.Id"/>
			<input type="hidden" class="item__value" name="name" value="@Model.DataSource?.Name"/>
		</div>
		<config-section label="@localizer["sectionInfo"]">
			<div class="form__item">
				<config-label target="icon" label="@localizer["icon"]"></config-label>
				<input-component name="icon" class="item__value" value="@Model.DataSource?.Icon" type="InputDataType.Icon"></input-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-comment" label="@localizer["comment"]"></config-label>
				<textarea-component id="data-source-comment" name="comment" class="item__value" placeholder="@localizer["commentPlaceholder"]" value="@Model.DataSource?.Comment"></textarea-component>
			</div>
		</config-section>
		<config-section label="@localizer["sectionSettings"]">
			<div class="form__item">
				<config-label target="data-source-module" label="@localizer["module"]"></config-label>
				@{
					var moduleColumns = new List<AutocompleteColumnDefinition>
					{
						new("name", localizer["name"], DataType.String),
						new("description", localizer["description"], DataType.String, false, true)
					};
					<autocomplete-component type="InputDataType.Guid" id="data-source-module" url="/Api/DataStores/@(Model.DataSource?.DataStoreId)/Modules"
					                        columns="moduleColumns" name="module" output-name="moduleId"
					                        value="@(Model.DataSource?.ModuleId)" display-value="@(Model.DataSource?.Module?.Name)" class="item__value"
					                        placeholder="@localizer["pleaseChoose"]" edit-link-url="/Admin/DataStores/{dataStore.Slug}/Modules/{slug}">
					</autocomplete-component>
				}
			</div>
			<div class="form__item">
				@{
					var columns = new List<AutocompleteColumnDefinition>
					{
						new ("LABEL", "label", DataType.String),
						new ("DESCRIPTION", "description", DataType.String)
					};
					var dataSourceTypes = Enum.GetValues(typeof(DataSourceType)).Cast<DataSourceType>().Select(
						level => new AutocompleteOptionDefinition(level.ToString(), [typeLocalizer[level.ToString()], typeLocalizer[$"{level}.Description"]])
					).ToList();
				}
				<config-label target="data-source-type" label="@localizer["type"]"></config-label>
				<autocomplete-component type="InputDataType.String" id="data-source-type" options="dataSourceTypes" name="type"
				                        value="@(Model.DataSource?.Type ?? DataSourceType.Default)" class="item__value"
				                        placeholder="@localizer["pleaseChoose"]" required="true" columns="@columns">
				</autocomplete-component>
			</div>
			<div class="form__item hide">
				<config-label target="data-source-annotation-source" label="@localizer["annotationSource"]"
				              description="@localizer["annotationSourceDescription"]"></config-label>
				<autocomplete-component type="InputDataType.Guid" id="data-source-annotation-source" name="annotationSource" output-name="annotationSourceId"
				                        url="/Api/DataSources/Autocomplete?type=annotation"
				                        edit-link-url="/Admin/DataStores/{dataStore.Slug}/DataSources/{slug}"
				                        value="@(Model.DataSource?.AnnotationSourceId)" display-value="@(Model.DataSource?.AnnotationSource?.Name)"
				                        class="item__value" placeholder="@localizer["pleaseChoose"]" required="true">
				</autocomplete-component>
			</div>
			<div class="form__item hide">
				<config-label target="data-source-annotation-key-field" label="@localizer["annotationKeyField"]"
				              description="@localizer["annotationKeyFieldDescription"]"></config-label>
				<autocomplete-component type="InputDataType.Guid" id="data-source-annotation-key-field" name="annotationKeyField" output-name="annotationKeyFieldId"
				                        url="@(Model.DataSource != null ? $"/Api/DataSources/{Model.DataSource.AnnotationSourceId}/AnnotationKeyFields?parentSourceId={Model.DataSource.Id}" : "")"
				                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}"
				                        value="@(Model.DataSource?.AnnotationKeyFieldId)" display-value="@(Model.DataSource?.AnnotationKeyField?.Name)"
				                        class="item__value" placeholder="@localizer["pleaseChoose"]" required="true">
				</autocomplete-component>
			</div>
			<div class="form__item hide">
				<config-label target="data-source-annotation-group-by-field" label="@localizer["annotationGroupByField"]"
				              description="@localizer["annotationGroupByFieldDescription"]"></config-label>
				<autocomplete-component type="InputDataType.Guid" id="data-source-annotation-group-by-field" name="annotationGroupByField" output-name="annotationGroupByFieldId"
				                        url="@(Model.DataSource != null ? $"/Api/DataSources/{Model.DataSource.Id}/DataFields?type=lookupField&targetSourceType=elementType" : "")"
				                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}"
				                        value="@(Model.DataSource?.AnnotationGroupByFieldId)" display-value="@(Model.DataSource?.AnnotationGroupByField?.Name)"
				                        class="item__value" placeholder="@localizer["pleaseChoose"]">
				</autocomplete-component>
			</div>
			<div class="form__item hide">
				<config-label target="data-source-annotation-label" label="@localizer["annotationLabel"]"
				              description="@localizer["annotationLabelDescription"]"></config-label>
				<input-component id="data-source-annotation-label" name="annotationLabel" class="item__value" required
				                 value="@Model.DataSource?.AnnotationLabel"></input-component>
			</div>
			<div class="form__item hide">
				<config-label target="data-source-annotation-create-page" label="@localizer["annotationCreatePage"]"
				              description="@localizer["annotationCreatePageDescription"]"></config-label>
				<autocomplete-component type="InputDataType.Guid" id="data-source-annotation-create-page" name="annotationCreatePage" output-name="annotationCreatePageId"
				                        url="@(Model.DataSource != null ? $"/Api/DataSources/{Model.DataSource.Id}/CreatePages" : "")"
				                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/Pages/{slug}"
				                        value="@(Model.DataSource?.AnnotationCreatePageId)" display-value="@(Model.DataSource?.AnnotationCreatePage?.Name)"
				                        class="item__value" placeholder="@localizer["pleaseChoose"]">
				</autocomplete-component>
			</div>
			<div class="form__item hide">
				<config-label target="data-source-annotation-detail-page" label="@localizer["annotationDetailPage"]"
				              description="@localizer["annotationDetailPageDescription"]"></config-label>
				<autocomplete-component type="InputDataType.Guid" id="data-source-annotation-detail-page" name="annotationDetailPage" output-name="annotationDetailPageId"
				                        url="@(Model.DataSource != null ? $"/Api/DataSources/{Model.DataSource.Id}/DetailPages" : "")"
				                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/Pages/{slug}"
				                        value="@(Model.DataSource?.AnnotationDetailPageId)" display-value="@(Model.DataSource?.AnnotationDetailPage?.Name)"
				                        class="item__value" placeholder="@localizer["pleaseChoose"]">
				</autocomplete-component>
			</div>
			<div class="form__item hide">
				<config-label target="data-source-annotation-group-by-field" label="@localizer["annotationGroupByField"]"
				              description="@localizer["annotationGroupByFieldDescription"]"></config-label>
				
			</div>
			<div class="form__item">
				<config-label target="data-source-store-revision" label="@localizer["storeRevision"]"></config-label>
				<toggle-component id="data-source-store-revision" name="storeRevision" class="item__value" value="@(Model.DataSource != null ? Model.DataSource.StoreRevision.ToString() : "true")"></toggle-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-full-text-search" label="@localizer["fulltextSearch"]"></config-label>
				<toggle-component id="data-source-full-text-search" name="fulltextSearch" class="item__value" value="@(Model.DataSource != null ? Model.DataSource.FulltextSearch.ToString() : "true")"></toggle-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-storage-path" label="@localizer["storagePath"]" description="@localizer["storagePathDescription"]"></config-label>
				<input-component id="data-source-storage-path" name="storagePath" class="item__value" required value="@Model.DataSource?.StoragePath" placeholder="@Model.DataSource?.Name"></input-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-full-text-search" label="@localizer["encryption"]"></config-label>
				<toggle-component id="data-source-encryption" name="encryption" class="item__value" value="@(Model.DataSource != null ? Model.DataSource.Encryption.ToString() : "true")"></toggle-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-allow-file" label="@localizer["allowFile"]"></config-label>
				<toggle-component id="data-source-allow-file" name="allowFile" class="item__value" value="@(Model.DataSource?.AllowFile == true)"></toggle-component>
			</div>
		</config-section>
		<config-section label="@localizer["sectionPageSettings"]">
			<div class="form__item">
				<config-label target="data-source-default-list-page" label="@localizer["defaultListPage"]" description="@localizer["defaultListPageDescription"]"></config-label>
				<autocomplete-component id="data-source-default-list-page" name="defaultListPage" class="item__value" placeholder="@localizer["pleaseChoose"]" value="" readonly="true"></autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-default-detail-page" label="@localizer["defaultDetailPage"]" description="@localizer["defaultDetailPageDescription"]"></config-label>
				<autocomplete-component type="InputDataType.Guid" id="data-source-default-detail-page" name="defaultDetailPage" output-name="defaultDetailPageId"
				                        url="@(Model.DataSource != null ? $"/Api/DataSources/{Model.DataSource.Id}/DetailPages" : "")"
				                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/Pages/{slug}"
				                        value="@(Model.DataSource?.DefaultDetailPageId)" display-value="@(Model.DataSource?.DefaultDetailPage?.Name)"
				                        class="item__value" placeholder="@localizer["pleaseChoose"]"></autocomplete-component>
			</div>
		</config-section>
		@{
			// TODO: App setting fields are not persistent yet (Prototype/Mock). Discuss which config fields we want to use and refactor this section
		}
		<config-section label="@localizer["sectionAppSettings"]" style="display:none;">
			<div class="form__item">
				<config-label target="data-source-default-mask" label="@localizer["defaultMask"]" description="@localizer["defaultMaskDescription"]"></config-label>
				<autocomplete-component id="data-source-default-mask" name="defaultMask" class="item__value" placeholder="@localizer["pleaseChoose"]" value=""></autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-default-mask-offline" label="@localizer["defaultMaskOffline"]" description="@localizer["defaultMaskOfflineDescription"]"></config-label>
				<autocomplete-component id="data-source-default-mask-offline" name="defaultMaskOffline" class="item__value" placeholder="@localizer["pleaseChoose"]" value=""></autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-barcode-field" label="@localizer["barcodeField"]" description="@localizer["barcodeFieldDescription"]"></config-label>
				<autocomplete-component id="data-source-barcode-field" name="barcodeField" class="item__value" placeholder="@localizer["pleaseChoose"]" value=""></autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-nfc-field" label="@localizer["NfcField"]"></config-label>
				<autocomplete-component id="data-source-nfc-field" name="NfcField" class="item__value" placeholder="@localizer["pleaseChoose"]" value=""></autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-extended-web-access" label="@localizer["extendedWebAccess"]" description="@localizer["extendedWebAccessDescription"]"></config-label>
				<toggle-component id="data-source-extended-web-access" name="extendedWebAccess" class="item__value"></toggle-component>
			</div>
			<div class="form__item">
				<config-label target="data-source-trigger" label="@localizer["trigger"]" description="@localizer["triggerDescription"]"></config-label>
				<toggle-component id="data-source-trigger" name="trigger" class="item__value"></toggle-component>
			</div>
		</config-section>
	</form-component>
</div>
@await Html.PartialAsync("_SyncDialog")