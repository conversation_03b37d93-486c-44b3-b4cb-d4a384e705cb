@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.FrontendDtos.PageView
@using Levelbuild.Frontend.WebApp.Features.ListViewColumn.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.EditPanel
@model Levelbuild.Frontend.WebApp.Features.PageView.ViewModels.PageViewForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("PageView", "viewDesigner");
	var columnLocalizer = LocalizerFactory.Create("ListViewColumn", "");
	
	var columnName = "";
	if (ViewData["targetViewModel"] != null && ViewData["targetAction"]?.ToString() == "Detail")
	{
		columnName = (ViewData["targetViewModel"] as ListViewColumnForm)?.ListViewColumn?.FieldName;
	}
}
<style>
	#data-field-list {
		height:  100%;
	}
</style>
<script type="module" defer>
	const form = document.getElementById('page-view-form')
	const showIfMapping = new Map()
	showIfMapping.set("stickyColumnCount", [[{option: "expertMode", compareValue: 'true' }]]);
	Page.handleConditionalVisibility(form, showIfMapping)
	
	const expertMode = form.querySelector('[name=expertMode]').value === 'true'
	if (expertMode) {
		const displayColumn = form.querySelector('#list-view-column-list [name="display"]')
		displayColumn.hidden = false
		const allowEditColumn = form.querySelector('#list-view-column-list [name="allowEdit"]')
		allowEditColumn.hidden = false
	}
</script>
<div class="grid--centered vanishing-scrollbar static-scrollbar">
	<form-component id="page-view-form" handle-save-button="true" skeleton="@(Model.PageView == null)">
		<input type="hidden" name="dtoType" value="@Model.PageViewType"/>
		<input type="hidden" name="expertMode" disabled value="@(Model.PageView?.ExpertMode.ToString()?.ToLower())"/>
		<config-section label="@localizer["sectionSpecialColumns"]" description="@localizer["sectionSpecialColumnsDescription"]">
			<list-component class="section-span-all">
				<list-line-component size="LineSize.Small">
					<list-line-item-component type="LineItemType.Flag">
						<button-component icon="circle-check"></button-component>
					</list-line-item-component>
					<list-line-item-component type="LineItemType.Primary">@localizer["batchProcessing"]</list-line-item-component>
					<list-line-item-component></list-line-item-component>
				</list-line-component>
				@if (Model.PageView?.Page?.DataSource?.Workflows?.Count > 0)
				{
					<list-line-component size="LineSize.Small">
						<list-line-item-component type="LineItemType.Flag">
							<button-component icon="arrow-progress"></button-component>
						</list-line-item-component>
						<list-line-item-component type="LineItemType.Primary">@localizer["workflowState"]</list-line-item-component>
						<list-line-item-component></list-line-item-component>
					</list-line-component>	
				}
				<list-line-component size="LineSize.Small" disabled>
					<list-line-item-component type="LineItemType.Flag">
						<button-component icon="icons"></button-component>
					</list-line-item-component>
					<list-line-item-component type="LineItemType.Primary">@localizer["informationIcon"]</list-line-item-component>
					<list-line-item-component align="Alignment.Right">
						<toggle-component></toggle-component>
					</list-line-item-component>
				</list-line-component>
			</list-component>
		</config-section>
		<config-section label="@localizer["sectionColumns"]">
			<div class="form__item">
				<config-label label="@localizer["stickyColumnCount"]" description="@localizer["stickyColumnCountDescription"]"></config-label>
				<button-group-component name="stickyColumnCount" value="@(Model.PageView != null ? ((ListViewDto)Model.PageView).StickyColumnCount : "1")">
					<button-component label="1" value="1"></button-component>
					<button-component label="2" value="2"></button-component>
					<button-component label="3" value="3"></button-component>
				</button-group-component>
			</div>
			@{
				var columnSorting = new List<QueryParamSortingDto>() { new() { OrderColumn = "Position", Direction = SortDirection.Asc } };
			}
			<enumeration-component id="list-view-column-list" class="section-span-all" sorting="@columnSorting">
				<list-component size="LineSize.Small">
					<list-column-component name="type" type="ListColumnType.Flag" with-converter></list-column-component>
					<list-data-column-component name="fieldName"></list-data-column-component>
					<list-data-column-component name="allowEdit" with-converter width="30" text-align="Alignment.Center" hidden="true"></list-data-column-component>
					<list-data-column-component name="display" with-converter width="30" text-align="Alignment.Center" hidden="true"></list-data-column-component>
					<list-column-component name="delete" with-converter width="30"></list-column-component>
				</list-component>
			</enumeration-component>
		</config-section>
	</form-component>
</div>
<div class="fixed-panel">
	<div class="panel__header flex--centered"><i class="fal fa-circle-plus"></i>@localizer["detail/addColumns"]</div>
	<div class="panel__content">
		<list-component id="data-field-list" size="LineSize.Small" rows="new List<Dictionary<string, object>?>() { null, null, null, null, null }">
			<list-column-component name="fieldType" type="ListColumnType.Flag" with-converter></list-column-component>
			<list-data-column-component name="name"></list-data-column-component>
			<list-data-column-component name="type" with-converter text-align="Alignment.Right"></list-data-column-component>
		</list-component>
	</div>
</div>
<script type="module" defer>
	const fieldList = document.getElementById('data-field-list')
	const columnEnumeration = document.getElementById('list-view-column-list')
	const columnList = columnEnumeration.querySelector('lvl-list')
	
	const fieldSorting = JSON.stringify([{ orderColumn: 'name', direction: 'asc' }])
	columnEnumeration.addEventListener('query-view:changed', () => loadFields())
	
	columnEnumeration.querySelector('lvl-list-column[name="type"]').converter = (data, index) => `<i class="fa-light fa-${index === 0 ? 'paperclip' : 'grip-dots-vertical'}"></i>`
	columnEnumeration.querySelector('lvl-list-data-column[name="allowEdit"]').converter = (data) => `<i class="fa-${data.allowEdit ? 'solid' : 'light'} fa-pen" style="${data.allowEdit ? 'color: var(--clr-state-active)' : ''}"></i>`
	columnEnumeration.querySelector('lvl-list-data-column[name="display"]').converter = (data) => `<i class="fa-${data.display ? 'solid' : 'light'} fa-eye" style="${data.display ? 'color: var(--clr-state-active)' : ''}"></i>`
	columnEnumeration.querySelector('lvl-list-column[name="delete"]').converter = () => `<i class="fa-light fa-trash clickable" style="color: var(--clr-signal-error)" onclick="removeColumn(event)"></i>`
	columnEnumeration.url = '/Api/ListViewColumns'
	
	fieldList.onRowClick = (row, index) => addColumn(row.data, index)
	fieldList.querySelector('lvl-list-column[name="fieldType"]').converter = (data) => data.type !== 'false' ? '<i class="fa-light fa-grip-dots-vertical"></i>' : ''
	fieldList.querySelector('lvl-list-data-column[name="type"]').converter = (data) => getTypeLabel(data)
	
	function getTypeLabel(data) {
		if (data.type == null)
			return ''
	
		const labelParts = [data.type.toUpperCase()]
		if (data.type.toLowerCase() === 'string')
			labelParts.push(` (${data.length})`)
		if (data.multi)
			labelParts.push('[+]')
		return labelParts.join('')
	}
	
	async function loadFields() {
		@* @TODO Exchange single != conditions with 1 NOT IN condition if its working in QueryableExtensions *@
		const excludeFilters = columnList.rows.map(row => ({
			filterColumn: 'name',
			operator: 'NotEquals',
			compareValue: row.fieldName
		}))
		const fieldFilters = JSON.stringify([{
			filterColumn: 'dataSourceId',
			operator: 'Equals',
			compareValue: Page.getFormData().page['dataSourceId'] ?? ''
		}, ...excludeFilters])

		const response = await fetch(Page.buildUrl('/Api/DataFields', { sortings: fieldSorting, filters: fieldFilters }))
		if (!response.ok){
			fieldList.rows = []
			return
		}

		const jsonData = await response.json()
		if (jsonData.error != null){
			fieldList.rows = []
			console.error(jsonData.error)
			return
		}

		fieldList.rows = jsonData.data.rows
	}
	
	async function addColumn(fieldData, fieldIndex) {
		const response = await fetch('/Api/ListViewColumns', {
			body: JSON.stringify({
				'listViewId': Page.getFormData().id,
				'fieldId': fieldData.id
			}),
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
		})
		
		if (!response.ok)
			return
			
		const jsonData = await response.json()
		if (jsonData.error != null)
			return
			
		fieldList.rows = fieldList.rows.filter((_, index) => index !== fieldIndex)
			
		// reload columns
		columnEnumeration.reload()
	}
</script>
@* normal script for event handlers directly at the dom element (onclick,...) *@
<script>
	async function removeColumn(event) {
		event.stopPropagation()
		const columnEnumeration = document.getElementById('list-view-column-list')
		const columnList = columnEnumeration.querySelector('lvl-list')
		const listLine = event.target.parentElement.parentElement
		if (!listLine || listLine.tagName !== 'LVL-LIST-LINE'){
			console.error('List line not found. Column cannot be removed')
			return
		}
		
		const position = Number(listLine.dataset['position'])
		const currentRow = columnList.rows[position]
		const response = await fetch(`/Api/ListViewColumns/${currentRow.id}`, { method: 'DELETE' })
		if (response.ok){
			columnEnumeration.reload()
			
		}
	}
</script>
<vc:admin-list-page entity="ListViewColumn" route-name="ListViewColumns" localizer="@columnLocalizer" menu-entry="ViewDesigner" parent-property-name="listViewId" use-custom-list="true"></vc:admin-list-page>
<vc:edit-panel entity="ListViewColumn" route-name="ListViewColumns" localizer="@columnLocalizer" menu-entry="ViewDesigner" heading="@columnName" display-property-name="fieldName" width="484" skeleton="@(Model.PageView == null)"></vc:edit-panel>