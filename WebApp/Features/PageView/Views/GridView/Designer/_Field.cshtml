@using Levelbuild.Core.FrontendDtos.Enums
@using Microsoft.IdentityModel.Tokens
@model Levelbuild.Core.FrontendDtos.PageView.GridViewFieldDto

@{
	var label = !Model.Label.IsNullOrEmpty() ? Model.LabelTranslated : Model.DataField?.Name ?? Model.DataTypeTranslated;
	var configName = Model.DataType is InputDataType.Text ? "TextInput" : "Input";
}
<lvl-grid-element id="@(Model.Id)" row-start="@(Model.RowStart)" row-end="@(Model.RowEnd)" col-start="@(Model.ColStart)" col-end="@(Model.ColEnd)"
                  flag="@(Model.DataField?.Name ?? Model.DataTypeTranslated)" config="@(configName)">
	<span class="inputPreview">
		@switch (Model.DataType)
		{
			case InputDataType.Boolean:
				<text>
					<toggle-component label="@label" preview disconnected="@(Model.DataFieldId.IsNullOrEmpty())"></toggle-component>
				</text>
				break;
			case InputDataType.Text:
				<text>
					<textarea-component label="@label" preview disconnected="@(Model.DataFieldId.IsNullOrEmpty())"></textarea-component>
				</text>
				break;
			case InputDataType.Guid:
				<text>
					<autocomplete-component preview
					                        label="@label"
					                        type="@(Model.DataType ?? InputDataType.String)"
					                        disconnected="@(Model.DataFieldId.IsNullOrEmpty())"
					                        required="@(Model.Required == true)"
					                        readonly="@(Model.Readonly == true)"
					                        placeholder="@(Model.PlaceholderTranslated)"
					                        tooltip="@(Model.HelpTextTranslated)"
					                        text-align="@(Model.TextAlign ?? Alignment.Left)">
					</autocomplete-component>
				</text>
				break;
			default:
				<text>
					<input-component preview
					                 label="@label"
					                 type="@(Model.DataType ?? InputDataType.String)"
					                 disconnected="@(Model.DataFieldId.IsNullOrEmpty())"
					                 required="@(Model.Required == true)"
					                 readonly="@(Model.Readonly == true)"
					                 placeholder="@(Model.PlaceholderTranslated)"
					                 tooltip="@(Model.HelpTextTranslated)"
					                 text-align="@(Model.TextAlign ?? Alignment.Left)">
					</input-component>
				</text>
				break;
		}
	</span>
</lvl-grid-element>