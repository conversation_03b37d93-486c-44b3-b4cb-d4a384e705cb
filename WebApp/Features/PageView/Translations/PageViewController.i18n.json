{"detail": {"de": {"menuTitle": "Einstellungen", "basicSettings": "Grundkonfiguration", "addColumns": "Spalten hinzufügen"}, "en": {"menuTitle": "Settings", "basicSettings": "Basic Settings", "addColumns": "Add columns"}}, "basicSettings": {"de": {"display": "Anzeigen", "displayDescription": "Wenn Anzeigen nicht aktiv gesetzt ist, wird die View beim Zugriff auf die Seiten durch den Nutzer nicht angezeigt. Sie kann nur in anderen Seiten eingebettet werden.", "sectionDisplay": "Darstellung", "showPreview": "Dokumentenvorschau", "showPreviewDescription": "In einer Vorschauspalte werden Dokumente am Datensatz in einer minimierten Version angezeigt", "allowDisplayColumns": "<PERSON>utzer können Spalten ein und ausblenden", "columnCount": "Spaltenanzahl", "columnRatio": "Spaltenverhältnis", "columnMinWidth": "Mindestbreite", "showViewer": "<PERSON>er anzeigen", "viewerRatio": "Spaltenverhältnis", "Columns.One": "1 Spalte", "Columns.Two": "2 Spalten", "Columns.Three": "3 Spalten", "readonly": "Schreibgeschützt", "readonlyDescription": "Feldwerte als Text statt als Eingabefelder darstellen"}, "en": {"display": "Display", "displayDescription": "If Display is not set to active, the view is not displayed when the user accesses the pages. It can only be embedded in other pages.", "sectionDisplay": "Display", "showPreview": "Document preview", "showPreviewDescription": "Documents on the data record are displayed in a minimized version in a preview column", "allowDisplayColumns": "Users can show and hide columns", "columnCount": "Number of columns", "columnRatio": "Column ratio", "columnMinWidth": "Minimum width", "showViewer": "Show viewer", "viewerRatio": "Column ratio", "Columns.One": "1 Column", "Columns.Two": "2 Columns", "Columns.Three": "3 Columns", "readonly": "Read-only", "readonlyDescription": "Display field values as text instead of input fields"}}, "viewDesigner": {"de": {"sectionSpecialColumns": "Sonderspalten", "sectionSpecialColumnsDescription": "Die hier genannten Sonderspalten werden vor den Inhaltsspalten angezeigt.", "batchProcessing": "Stapelverarbeitung", "workflowState": "Workflow Status", "informationIcon": "Informationsicon", "sectionColumns": "Spalten"}, "en": {"sectionSpecialColumns": "Special columns", "sectionSpecialColumnsDescription": "The special columns mentioned here are displayed before the content columns.", "batchProcessing": "Batch processing", "workflowState": "Workflow state", "informationIcon": "Information icon", "sectionColumns": "Columns"}}, "designer": {"de": {"newSection": "<PERSON><PERSON><PERSON>", "editSection": "<PERSON><PERSON>chnitt bearbeiten", "newEmbeddedPage": "Neue eingebettete Seite", "editEmbeddedPage": "Eingebettete Seite bearbeiten", "editEmbeddedTile": "Eingebettete Kachel bear<PERSON>ten", "editHeadline": "Überschrift bearbeiten", "editField": "<PERSON><PERSON> bear<PERSON>ten", "structure": "Struktur", "layout": "Layout", "formFields": "Formular-<PERSON><PERSON>", "column": "<PERSON>lt<PERSON>", "columnsAll": "Alle", "titleField": "Zeile 1 (Titel)", "titleFieldDescription": "Diese Information dient zur Identifikation des Datensatzes. Falls auch eine Tabellenansicht genutzt wurde, bitte in Zeile 1 und Spalte 1 das gleiche Feld wählen.", "subtitleField": "Zeile 2", "subtitleFieldDescription": "<PERSON><PERSON> Z<PERSON>e kann genutzt werden, um direkt weitere Details zum Datensatz zu teilen."}, "en": {"newSection": "New Section", "editSection": "Edit Section", "newEmbeddedPage": "New Embedded Page", "editEmbeddedPage": "Edit Embedded Page", "editEmbeddedTile": "Edit Embedded Tile", "editHeadline": "Edit Heading", "editField": "Edit Field", "structure": "Structure", "layout": "Layout", "formFields": "Form fields", "column": "Column", "columnsAll": "All", "titleField": "Line 1 (Title)", "titleFieldDescription": "This information is used to identify the data record. If a table view was also used, please select the same field in row 1 and column 1.", "subtitleField": "Line 2", "subtitleFieldDescription": "This line can be used to share further details about the data record directly."}}, "menu": {"de": {"item/basicSettings": "Grundkonfiguration", "item/listView/columns": "Designer", "item/gridView/designer": "Designer", "item/galleryView/designer": "Designer"}, "en": {"item/basicSettings": "Basic Settings", "item/listView/columns": "Designer", "item/gridView/designer": "Designer", "item/galleryView/designer": "Designer"}}, "*": {"de": {"newItem": "Neue Ansicht", "name": "Name", "nameDescription": "Den Namen mit eindeutigen Bezeichnungen übersetzen, da die Übersetzungen als Klarnamen in der der Anzeigeliste genutzt werden.", "type": "<PERSON><PERSON>", "responsible": "Verantwortlicher", "description": "Beschreibung", "hideInSelection": "In der Ansichtenauswahl ausblenden", "displayInSelection": "In der Ansichtenauswahl anzeigen", "expertMode": "Expertenmodus", "expertModeDescription": "Im Expertenmodus gibt es eine horizontale Scrollbar, es werden keine Spalten automatisch ausgeblendet, es gibt Sticky-Spalten und Nutzer können die Spaltengrößen anpassen", "stickyColumnCount": "<PERSON><PERSON><PERSON>-Spalten", "stickyColumnCountDescription": "Sticky-Spalten verschwinden nicht beim horizontalen Scrollen."}, "en": {"newItem": "New View", "name": "Name", "nameDescription": "Translate the name with clear designations, as the translations are used as clear names in the display list.", "type": "Type", "responsible": "Responsible", "description": "Description", "hideInSelection": "Hide in view selection", "displayInSelection": "Display in view selection", "expertMode": "Expert mode", "expertModeDescription": "There is a horizontal scrollbar in expert mode, no columns are automatically hidden, there are sticky columns and users can adjust the column sizes", "stickyColumnCount": "Number of sticky columns", "stickyColumnCountDescription": "Sticky columns do not disappear when scrolling horizontally."}}}