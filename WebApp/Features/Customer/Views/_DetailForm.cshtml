@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.Customer.ViewModels.CustomerForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("Customer", "");
}

<div class="grid--centered @(Model.ViewType == ViewType.Edit ? "vanishing-scrollbar static-scrollbar" : "")">
	<form-component id="customer-form" skeleton="@(Model is {ViewType: ViewType.Edit, Customer: null})">
		<config-section label="@localizer["sectionInfo"]">
			<div class="form__item">
				<input type="hidden" class="item__value" name="id" value="@Model.Customer?.Id"/>
			</div>
			<div class="form__item">
				<config-label target="customer-display-name" label="@localizer["name"]"></config-label>
				<input-component id="customer-display-name" name="displayName" class="item__value" required value="@Model.Customer?.DisplayName"></input-component>
			</div>
		</config-section>
	</form-component>
</div>