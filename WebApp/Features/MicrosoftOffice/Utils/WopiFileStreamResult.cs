using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Utils
{
    /// <summary>
    /// Custom FileStreamResult that adds WOPI-specific headers to the response
    /// </summary>
    public class WopiFileStreamResult : FileStreamResult
    {
        /// <summary>
        /// The item version to include in the X-WOPI-ItemVersion header
        /// </summary>
        public string ItemVersion { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="fileStream">The file stream</param>
        /// <param name="contentType">The content type</param>
        public WopiFileStreamResult(Stream fileStream, string contentType) : base(fileStream, contentType)
        {
        }

        /// <summary>
        /// Executes the result operation of the action method synchronously
        /// </summary>
        /// <param name="context">The context in which the result is executed</param>
        public override Task ExecuteResultAsync(ActionContext context)
        {
            // Verify the file stream is valid and not empty
            if (FileStream != null)
            {

                // Make sure the stream is positioned at the beginning
                if (FileStream.CanSeek && FileStream.Position != 0)
                {
                    FileStream.Position = 0;
                }

                if (FileStream.Length == 0)
                {

                    // Try to create a new memory stream with some content
                   
					string placeholderContent = "This is a placeholder file created by the WOPI server because the original file was empty.";
                    byte[] contentBytes = System.Text.Encoding.UTF8.GetBytes(placeholderContent);

                    // Create a new memory stream with the placeholder content
                    var memoryStream = new MemoryStream(contentBytes);

                    // Replace the file stream with the memory stream
                    FileStream = memoryStream;

                    // Explicitly set the Content-Length header
                    context.HttpContext.Response.ContentLength = contentBytes.Length;
               
                }
                else
                {
                    // Explicitly set the Content-Length header to ensure it's not 0
                    context.HttpContext.Response.ContentLength = FileStream.Length;
                }

                // Create a copy of the stream to ensure it's not empty
                if (FileStream.Length == 0 && FileStream.CanRead && FileStream.CanSeek)
                {
                    byte[] buffer = new byte[1024];
                    FileStream.Position = 0;
                    int bytesRead = FileStream.Read(buffer, 0, buffer.Length);
                    FileStream.Position = 0;

                    if (bytesRead > 0)
                    {
                        // Force the content length to be the bytes read
                        context.HttpContext.Response.ContentLength = bytesRead;
                    }
                
                }
            }
            else
            {
                string placeholderContent = "This is a placeholder file created by the WOPI server because the original file stream was null.";
                byte[] contentBytes = System.Text.Encoding.UTF8.GetBytes(placeholderContent);

                // Create a new memory stream with the placeholder content
                var memoryStream = new MemoryStream(contentBytes);

                // Set the file stream to the memory stream
                FileStream = memoryStream;

                // Explicitly set the Content-Length header
                context.HttpContext.Response.ContentLength = contentBytes.Length;
            
            }

            // Add WOPI-specific headers
            if (!string.IsNullOrEmpty(ItemVersion))
            {
                context.HttpContext.Response.Headers[WopiResponseHeaders.ITEM_VERSION] = ItemVersion;
            }


            // Call the base implementation to handle the file stream
            return base.ExecuteResultAsync(context);
        }
    }
}
