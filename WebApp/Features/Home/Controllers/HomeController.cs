using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.ViewModels;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Frontend.WebApp.Features.Home.Controllers;

/// <summary>
/// Controller for home page (Dashboard)
/// </summary>
public class HomeController : FrontendController
{
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="versionReader">injected VersionReader</param>
	[ExcludeFromCodeCoverage]
	public HomeController(ILogManager logManager, IVersionReader versionReader) : base(logManager, logManager.GetLoggerForClass<HomeController>(), versionReader)
	{
		// nothing...
	}

	/// <summary>
	/// Index page.
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	public IActionResult Index()
	{
		// TODO: use this whenever the user changes his local via our configuration to remove all translated elements from the browser cache
		Response.Headers["Clear-Site-Data"] = "\"cache\"";
		
		return CachedPartial() ?? RenderPartial();
	}

	/// <summary>
	/// Privacy page.
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	public IActionResult Privacy()
	{
		foreach (var header in Request.Headers)
		{
			Logger.Information("{HeaderKey}: {HeaderValue}", header.Key, header.Value);
		}
		
		return View();
	}

	/// <summary>
	/// Error page.
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
	public IActionResult Error()
	{
		return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
	}
}