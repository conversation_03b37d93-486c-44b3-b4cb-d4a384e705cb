using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.Page;
using Levelbuild.Entities.Features.Page.Create;
using Levelbuild.Entities.Features.Page.MultiData;
using Levelbuild.Entities.Features.Page.SingleData;
using Levelbuild.Entities.Features.PageView;
using Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.Page.ViewModels;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Reflection;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.Page.Controllers;

/// <summary>
/// Controller for the configuration view of pages
/// </summary>
public class AdminPageController : AdminController<PageDto>
{
	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">logging</param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="localizerFactory">injected StringLocalizerFactory</param>
	/// <param name="contextFactory">database context</param>
	/// <param name="versionReader">injected VersionReader</param>
	public AdminPageController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
							   IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : base(
		logManager, logManager.GetLoggerForClass<AdminPageController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
	}

	#region Views

	/// <summary>
	/// Renders the list view with help of the list dto
	/// </summary>
	/// <returns>rendered list view</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Pages/")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Pages/")]
	public IActionResult List()
	{
		// no caching here, because list of available dataStores may change with every new call
		return RenderPartial(new PageList()
		{
			DataStores = DatabaseContext.DataStoreConfigs.AsQueryable().OrderBy(dataStore => dataStore.Name).ToDtoList().ToList()
		});
	}

	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Pages/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Pages/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/DataSources/{dataSourceSlug}/Pages/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Modules/{moduleSlug}/DataSources/{dataSourceSlug}/Pages/Create")]
	public IActionResult Create(string? dataStoreSlug, string? dataSourceSlug, [FromQuery(Name = "dataSourceId")] Guid? dataSourceId)
	{
		if (string.IsNullOrEmpty(dataStoreSlug))
			return CachedPartial() ?? RenderPartial(new PageForm() {DataSourceId = dataSourceId});

		// convert dataStoreSlug into id
		var dataStoreId = DatabaseContext.DataStoreConfigs.FirstOrDefault(dataStore => dataStore.Slug == dataStoreSlug)?.Id;
		if (dataStoreId == null)
			throw new ElementNotFoundException($"DataStore with slug: {dataStoreSlug} could not be found");
		
		if (dataSourceSlug != null)
		{
			var dataSource = DatabaseContext.DataSources.FirstOrDefault(source => source.Slug == dataSourceSlug);
			
			if (dataSource == null)
				throw new ElementNotFoundException($"DataSource configuration with slug '{dataSourceSlug}' could not be found");
			
			Request.RouteValues.Add("menu", "Pages");
			return CachedPartial() ?? RenderPartial(
		   new PageForm()
		   {
			   Page = new PageDto() { DataSourceId = dataSource.Id },
			   DataSourceId = dataSource.Id
		   }, "~/Features/DataSource/Views/Detail.cshtml", new DataSourceForm(ViewType.Edit)
		   {
			   DataSource = dataSource.ToDto()
		   });
		}

		// ReSharper disable once Mvc.ViewNotResolved
		return CachedPartial() ?? RenderPartial(new PageForm() { DataStoreId = dataStoreId.Value, DataSourceId = dataSourceId}, "List",
												new PageList()
												{
													DataStores = DatabaseContext.DataStoreConfigs.AsQueryable().ToDtoList().ToList()
												});
	}

	/// <summary>
	/// Renders the detail view with help of the data store dto
	/// </summary>
	/// <param name="dataStoreSlug">readable identifier for a datastore</param>
	/// <param name="pageSlug">readable identifier for a specific page</param>
	/// <param name="menu">optional menu entry which needs to be loaded</param>
	/// <param name="pageType">type of the page to render (Create, SingleData, MultiData)</param>
	/// <param name="pageId">id of the page</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Pages/Edit/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Pages/{pageSlug}/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/DataSources/{dataSourceSlug}/Pages/{pageSlug}/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Modules/{moduleSlug}/DataSources/{dataSourceSlug}/Pages/{pageSlug}/{menu?}")]
	public IActionResult Detail(string? dataStoreSlug, string? pageSlug, string? menu, [FromQuery(Name = "type")] PageType? pageType,
								[FromQuery(Name = "id")] Guid? pageId)
	{
		// "Views" is part of the BasicSettings Tab and therefore needs to be redirected if called directly via url manipulation
		if (menu == "Views")
			return RedirectToAction("Detail", new RouteValueDictionary(new { dataStoreSlug, pageSlug, menu = "BasicSettings" }));

		if (string.IsNullOrEmpty(pageSlug) && !pageId.HasValue)
		{
			if (pageType == null)
				throw new Exception("pageType is required in order to render empty PageDetails view");

			return CachedPartial(pageType.ToString()) ?? this.RenderPartial(GetPageFormByType(pageType.Value));
		}

		PageEntity? entity;
		if (pageId.HasValue)
		{
			entity = DatabaseContext.Pages.Find(pageId.Value);
		}
		else
		{
			if (dataStoreSlug == null)
				throw new ElementNotFoundException("DataStoreSlug is null");
			if (pageSlug == null)
				throw new ElementNotFoundException("PageSlug is null");
			var dataStore = DatabaseContext.DataStoreConfigs.FirstOrDefault(dataStore => dataStore.Slug == dataStoreSlug.ToLower());
			if (dataStore == null)
				throw new ElementNotFoundException($"DataStore with slug: {dataStoreSlug} could not be found");

			entity = DatabaseContext.Pages.FirstOrDefault(page => page.DataStoreId == dataStore.Id && page.Slug == pageSlug.ToLower());
		}


		if (entity == null)
			throw new ElementNotFoundException($"Page with slug: {pageSlug} could not be found");

		return RenderPartial(GetPageFormByType(entity.Type, entity.Id));
	}

	private PageForm GetPageFormByType(PageType type, Guid? id = null)
	{
		return GetPageFormByType(DatabaseContext, type, id);
	}

	/// <summary>
	/// loads the page entity by id and creates the correct PageForm dto depending on the type of the given page
	/// </summary>
	/// <param name="dbContext">database context needs to be passed because method is static</param>
	/// <param name="type">type of the page (for better performance, so we don't need to select it from the base page entity)</param>
	/// <param name="id">ID of the page</param>
	/// <returns></returns>
	/// <exception cref="ElementNotFoundException"></exception>
	/// <exception cref="Exception"></exception>
	public static PageForm GetPageFormByType(CoreDatabaseContext dbContext, PageType type, Guid? id = null)
	{
		switch (type)
		{
			case PageType.Create:
				if (id == null)
					return new CreateForm(ViewType.Edit);

				var createEntity = dbContext.CreatePages
					.Include(page => page.DataSource)
					.Include(page => page.DataSource.DataStore)
					.FirstOrDefault(page => page.Id == id);

				var gridViewEntity = createEntity?.DefaultViewId != null
										 ? dbContext.GridViews
											 .Include(gridView => gridView.Sections)
											 .Include(gridView => gridView.Pages).ThenInclude(page => page.EmbeddedPage)
											 .FirstOrDefault(view => view.Id == createEntity.DefaultViewId)
										 : null;

				if (createEntity == null)
					throw new ElementNotFoundException($"CreatePage with id: {id} could not be found");

				return new CreateForm(ViewType.Edit, createEntity.ToDto()) { PageType = PageType.Create, GridView = gridViewEntity?.ToDto() };

			case PageType.SingleData:
				if (id == null)
					return new SingleDataForm(ViewType.Edit);

				var singleDataEntity = dbContext.SingleDataPages
					.Include(page => page.DataSource)
					.Include(page => page.DataSource.DataStore)
					.FirstOrDefault(page => page.Id == id);

				if (singleDataEntity == null)
					throw new ElementNotFoundException($"SingleDataPage with id: {id} could not be found");

				return new SingleDataForm(ViewType.Edit, singleDataEntity.ToDto()) { PageType = PageType.SingleData };

			case PageType.MultiData:
				if (id == null)
					return new MultiDataForm(ViewType.Edit);

				var multiDataEntity = dbContext.MultiDataPages
					.Include(page => page.DataSource)
					.Include(page => page.DataSource.DataStore)
					.Include(page => page.CreatePage)
					.Include(page => page.DetailPage)
					.Include(page => page.DefaultView)
					.Include(page => page.Views)
					.FirstOrDefault(page => page.Id == id);

				if (multiDataEntity == null)
					throw new ElementNotFoundException($"MultiDataPage with id: {id} could not be found");

				return new MultiDataForm(ViewType.Edit, multiDataEntity.ToDto())
				{
					PageType = PageType.MultiData,
				};

			default:
				throw new Exception($"Editing Page of type  \"{type.ToString()}\" is not yet supported!");
		}
	}

	#endregion

	#region Actions

	/// <inheritdoc />
	[HttpGet("/Api/Pages/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		var pageContext = DatabaseContext.Pages
			.Include(page => page.DataSource)
			.ThenInclude(dataSource => dataSource.Module);
		return HandleQueryRequest<PageEntity, PageDto>(pageContext, parameters, new PropertyPathList<PageEntity>()
		{
			(PageEntity entity) => entity.DataSource.Name
		});
	}

	/// <inheritdoc />
	[HttpGet("/Api/Pages/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		var page = DatabaseContext.Pages
			.Include(page => page.DataSource).ThenInclude(source => source.DataStore)
			.FirstOrDefault(page => page.Id == id);
		if (page == null)
			return GetNotFoundResponse($"Page with id: {id} could not be found");

		switch (page.Type)
		{
			case PageType.Create:
				return HandleGetRequest<CreatePageEntity, CreatePageDto>(DatabaseContext.CreatePages, id);

			case PageType.SingleData:
				return HandleGetRequest<SingleDataPageEntity, SingleDataPageDto>(DatabaseContext.SingleDataPages, id);

			case PageType.MultiData:
				var pageQuery = DatabaseContext.MultiDataPages
					.Include(entity => entity.CreatePage)
					.Include(entity => entity.DetailPage)
					.Include(entity => entity.DefaultView)
					.Include(entity => entity.Views);
				return HandleGetRequest<MultiDataPageEntity, MultiDataPageDto>(pageQuery, id);
		}

		return GetUnprocessableEntityResponse($"Type \"{page.Type.ToString()}\" is not yet supported!");
	}

	/// <inheritdoc />
	[HttpPost("/Api/Pages/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] PageDto pageDto)
	{
		switch (pageDto.Type)
		{
			case PageType.Create:
				var result = await HandleCreateRequestAsync(DatabaseContext.CreatePages, (CreatePageDto)pageDto);
				if (result.Result is OkObjectResult objectResult)
				{
					var frontendResponse = objectResult.Value as FrontendResponse<CreatePageDto>;
					var pageEntityId = frontendResponse?.Data.Id;
					if (pageEntityId != null)
					{
						var pageEntity = await DatabaseContext.CreatePages.Include(page => page.Views).FirstOrDefaultAsync(page => page.Id == pageEntityId);
						if (pageEntity is { Views.Count: > 0 })
						{
							pageEntity.DefaultView = pageEntity.Views.FirstOrDefault();
							await DatabaseContext.SaveChangesAsync();
						}
					}
				}

				return result;
			case PageType.SingleData:
				return await HandleCreateRequestAsync(DatabaseContext.SingleDataPages, (SingleDataPageDto)pageDto);
			case PageType.MultiData:
				return await HandleCreateRequestAsync(DatabaseContext.MultiDataPages, (MultiDataPageDto)pageDto);
			default:
				return GetUnprocessableEntityResponse($"Type \"{pageDto.Type.ToString()}\" is not yet supported!");
		}
	}

	/// <inheritdoc />
	[HttpPatch("/Api/Pages/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] PageDto configurationDto)
	{
		// check if element exists
		var entity = await DatabaseContext.Pages.Include(page => page.DataSource).FirstOrDefaultAsync(element => element.Id == id);
		if (entity == null)
			return GetNotFoundResponse($"PageEntity#{id} does not exist");

		// parse JSON into specific DTO according to type
		return entity.Type switch
		{
			// TODO: include von create/detailPage in das result! (maybe auto-load on for these two?)
			PageType.Create => await HandleUpdateRequestAsync(DatabaseContext.CreatePages, entity.Id, (CreatePageDto)configurationDto),
			PageType.SingleData => await HandleUpdateRequestAsync(DatabaseContext.SingleDataPages, entity.Id, (SingleDataPageDto)configurationDto),
			PageType.MultiData => await HandleUpdateRequestAsync(DatabaseContext.MultiDataPages, entity.Id, (MultiDataPageDto)configurationDto),
			_ => GetUnprocessableEntityResponse($"PageType \"{entity.Type.ToString()}\" is not yet supported!")
		};
	}

	/// <inheritdoc />
	[HttpDelete("/Api/Pages/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		return HandleDeleteRequest(DatabaseContext.Pages, id);
	}

	/// <summary>
	/// get all views for the given page
	/// </summary>
	/// <param name="pageId">ID of the page</param>
	/// <param name="parameters">query parameters</param>
	/// <returns></returns>
	[HttpGet("/Api/Pages/{pageId}/PageViews")]
	public ActionResult<FrontendResponse> AutocompletePageViews(Guid pageId, QueryParamsDto parameters)
	{
		var pageEntity = DatabaseContext.Pages.Find(pageId);
		if (pageEntity == null)
			return GetBadRequestResponse($"PageEntity with id: {pageId} could not be found");

		var query = DatabaseContext.PageViews.Where(pageView => pageView.PageId == pageEntity.Id && pageView.Id != pageEntity.DefaultViewId);
		return HandleAutocompleteRequest(query, parameters, nameof(PageViewEntity.Id), nameof(PageViewEntity.Name));
	}

	/// <summary>
	/// gets all possible create pages for the given multi data page
	/// </summary>
	/// <param name="pageId">ID of the page</param>
	/// <param name="parameters">query parameters</param>
	/// <returns></returns>
	[HttpGet("/Api/Pages/{pageId}/CreatePages")]
	public ActionResult<FrontendResponse> AutocompleteCreatePages(Guid pageId, QueryParamsDto parameters)
	{
		PageEntity? pageEntity = DatabaseContext.Pages.Find(pageId);
		if (pageEntity == null)
			return GetBadRequestResponse($"PageEntity with id: {pageId} could not be found");

		var query = DatabaseContext.Pages.Include(page => page.DataSource).ThenInclude(source => source.DataStore)
			.Where(page => page.DataSourceId == pageEntity.DataSourceId && page.Type == PageType.Create);
		return HandleAutocompleteRequest(
			query,
			parameters,
			nameof(PageEntity.Id),
			nameof(PageEntity.Name),
			new()
			{
				nameof(PageEntity.Name),
				nameof(PageEntity.Slug),
				(PageEntity page) => page.DataSource.Name,
				(PageEntity page) => page.DataSource.DataStore.Slug
			}
		);
	}

	/// <summary>
	/// gets all possible detail pages for the given multi data page
	/// </summary>
	/// <param name="pageId">ID of the page</param>
	/// <param name="parameters">query parameters</param>
	/// <returns></returns>
	[HttpGet("/Api/Pages/{pageId}/DetailPages")]
	public ActionResult<FrontendResponse> AutocompleteDetailPages(Guid pageId, QueryParamsDto parameters)
	{
		PageEntity? pageEntity = DatabaseContext.Pages.Find(pageId);
		if (pageEntity == null)
			return GetBadRequestResponse($"PageEntity with id: {pageId} could not be found");

		var query = DatabaseContext.Pages.Include(page => page.DataSource).ThenInclude(source => source.DataStore)
			.Where(page => page.DataSourceId == pageEntity.DataSourceId && page.Type == PageType.SingleData);
		return HandleAutocompleteRequest(query, parameters, nameof(PageEntity.Id), nameof(PageEntity.Name),
										 new()
										 {
											 nameof(PageEntity.Name),
											 nameof(PageEntity.Slug),
											 (PageEntity page) => page.DataSource.DataStore.Slug
										 });
	}

	#endregion
}