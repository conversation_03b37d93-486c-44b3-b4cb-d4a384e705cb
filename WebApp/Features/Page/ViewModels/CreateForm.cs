using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ViewModels;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.Page.ViewModels;

[ExcludeFromCodeCoverage]
public class CreateForm : PageForm
{
	public CreatePageDto? CreatePage => (CreatePageDto?)Page;

	public GridViewDto? GridView { get; init; }
	
	public CreateForm(ViewType viewType = ViewType.Create, PageDto? page = null) : base(viewType, page)
	{
		PageType = PageType.Create;
		if (page != null)
		{
			DataStoreSlug = page.DataSource?.DataStore?.Slug ?? "";
			DataStoreId = page.DataSource?.DataStoreId;
		}
		
		MenuItems = new List<MenuItem>
		{
			new("basicSettings", "BasicSettings", "screwdriver-wrench"),
			new("create/designer", "Designer", "brush") { UrlParams = new() { { "id", page?.Id != null ? page.Id : "##id##" } } }
		};
		
		ViewDirectory = "Create/";
	}
}