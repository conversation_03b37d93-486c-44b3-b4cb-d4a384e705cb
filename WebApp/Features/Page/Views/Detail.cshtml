@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminDetailPage
@model Levelbuild.Frontend.WebApp.Features.Page.ViewModels.PageForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("Page", "detail");
	var listLocalizer = LocalizerFactory.Create("Page", "list");
	var currentMenu = ViewData["targetMenu"] == null || ViewData["targetMenu"]!.ToString() == "" ? "BasicSettings" : ViewData["targetMenu"]!.ToString()!;
	var menuRouteParams = $"{{type: '{Model.PageType}'}}";
}

<script type="module" defer>
	let pageSlug = Page.getFormData()?.slug
	@if (Model.Page != null)
	{
		<text>
    		Page.setMainPage(`/Admin/DataStores/@(Model.DataStoreSlug)/Pages/@(Model.Page.Slug)`, '@(currentMenu)')
    		Page.setBreadcrumbs([
    				{ label: '@listLocalizer["pageTitle"]', url: '/Admin/DataStores/@(Model.DataStoreSlug)/Pages' },
    				{ label: '@Model.Page!.Name', url: `/Admin/DataStores/@(Model.DataStoreSlug)/Pages/@Model.Page.Slug/@currentMenu` }
    		])
    		
    		// add currenMenu to path to make sure setting the view location via ../Views is working properly (Not when edit/create panel is rendered)
			@if (ViewData["targetAction"] == null)
			{
				@: Page.setInfo(Page.getMainPageUrl() + '/@currentMenu')
			}
			pageSlug = '@Model.Page.Slug'
    	</text>
	}
	
	@if (Model.PageType == PageType.MultiData)
    {
	    <text>
			Page.buttonConfig.preview = true
			Page.buttonConfig.previewButton.addEventListener('click', () => { window.open(`/Public/Pages/${pageSlug}`, '_blank') }, { signal: Page.getPageChangeSignal() })
			Page.buttonConfig.previewButton.skeleton = false
	    </text>	
    }
</script>

<input type="hidden" id="pageId" value="@Model.Page?.Id"/>

<vc:basic-menu type="@BasicMenuType.ViewSwitcher" entity="Page" title="@localizer["menuTitle"]" route-name="Pages" route-params="@menuRouteParams"
               menu-items="@Model.MenuItems" menu-infos="@Model.MenuInfos" width="250" skeleton="@(Model.Page == null)">
</vc:basic-menu>
<vc:admin-detail-page entity="Page" route-name="Pages" model="@Model" title="@Model.Page?.Name" menu-item="@currentMenu"
                      show-default-buttons="@(Model.Page != null)" view-name="@($"~/Features/Page/Views/{Model.PageType}/_{currentMenu}.cshtml")">
</vc:admin-detail-page>