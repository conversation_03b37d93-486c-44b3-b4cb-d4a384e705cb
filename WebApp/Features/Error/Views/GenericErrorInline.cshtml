@using Levelbuild.Frontend.WebApp.Features.User.Services
@model Levelbuild.Frontend.WebApp.Features.Error.ViewModels.ErrorModel
@inject UserManager UserManager

<style>
	.error {
		display: grid;
		text-align: center;
		align-content: center;
		justify-items: center;
		gap: var(--size-spacing-m);
    	height: 100%;
    	width: 100%;
		background-color: var(--clr-background-lvl-0);
    	color: var(--clr-text-secondary);
    	
    	& .icon {
    		color: var(--clr-state-inactive);
    		font-size: var(--size-text-xxxl);
    	}
	}
		
	.error__title {
		font-size: 1.5rem;
	}
	
	.error__message {
		width: min(80%, 1200px);
	}
</style>
<div class="error">
	<i class="fal fa-info-circle icon"></i>
	<h2 class="error__title">@(Model.ErrorTitle) - @(Model.ErrorSubTitle)</h2>
	@if (await UserManager.IsAdminAsync())
	{
		<span class="error__message">@Html.Raw(Model.ErrorMessage)</span>
		@if (!string.IsNullOrEmpty(Model.ErrorStacktrace))
		{
			<textarea-component class="error__message" readonly value="@(Model.ErrorStacktrace)"></textarea-component>
		}
	}
</div>