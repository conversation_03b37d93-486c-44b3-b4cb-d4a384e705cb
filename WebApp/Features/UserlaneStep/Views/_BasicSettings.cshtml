@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.UserlaneStep.ViewModels.UserlaneStepForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("UserlaneSteps", "");
	var targetOptions = Model.DataFields.Select(dataField =>
	{
		// Split the Label string by spaces
		var labelParts = (dataField.Label?.ToString()!).Split(' ');

		// Localize each part and join them with a space
		var localizedLabel = string.Join(" : ", labelParts.Select(part => localizer[part]));

		// Return the AutocompleteOptionDefinition with localized parts
		return new AutocompleteOptionDefinition(
			dataField.Value!, 
			localizedLabel
		);
	}).ToList();
	targetOptions.Insert(0, new AutocompleteOptionDefinition("createRecord", localizer["createButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("saveRecord", localizer["saveButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("cancelRecord", localizer["cancelButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("informationSection", localizer["informationSection"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-save", localizer["topBarSaveButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-delete", localizer["topBarDeleteButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-preview", localizer["topBarPreviewButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-edit", localizer["topBarEditButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-sync", localizer["topBarSyncButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-sync-all", localizer["topSyncAllSaveButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-import", localizer["topBarImportButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-impersonate", localizer["topBarImpersonateButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-deactivate", localizer["topBarDeactivateButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("content-button-reactivate", localizer["topBarReactivateButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("dialog-cancel-button", localizer["dialogCancelButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("dialog-save-button", localizer["dialogSaveButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("mask-edit-button", localizer["maskEditButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("createRecord", localizer["createButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("saveRecord", localizer["saveButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("cancelRecord", localizer["cancelButton"]));
	targetOptions.Insert(0, new AutocompleteOptionDefinition("null", localizer["floatingDialog"]));
}

<script type="module" defer>
	// enable save button
	Page.buttonConfig.saveButton.disabled = false
	// enable delete button on this panel
	Page.buttonConfig.deleteButton.disabled = false

</script>

<div class="grid--centered">
	<form-component id="userlanes-form" skeleton="@(Model is { ViewType: ViewType.Edit, UserlaneStep: null })">
		<div class="form__item">
			<input type="hidden" class="item__value" id="id" name="id" value="@Model.UserlaneStep?.Id"/>
			<input type="hidden" class="item__value" id="userlaneId" name="userlaneId" value="@(Model.UserlaneStep?.UserlaneId ?? Model.UserlaneId)"/>
		</div>
		
		<config-section label="Information">
			<div class="form__item">
				<config-label target="order" label="@localizer["Order"]"></config-label>
				<input-component type="InputDataType.Integer" min="0" step="1" name="order" id="order" required="true" placeholder="@localizer["Order"]" value="@Model.UserlaneStep?.Order" class="item__value"></input-component>
			</div>

			<div class="form__item">
				<config-label target="delay" label="@localizer["Delay"]"></config-label>
				<input-component type="InputDataType.Integer" min="0" step="1" name="delay" id="delay" placeholder="@localizer["Delay"]" value="@Model.UserlaneStep?.Delay" class="item__value"></input-component>
			</div>
			
			<div class="form__item">
				<config-label target="page" label="@localizer["TargetElement"]" description=""></config-label>
				<autocomplete-component
					type="InputDataType.String"
					id="targetElement"
					options="targetOptions"
					name="targetElement"
					value = "@(Model.UserlaneStep?.TargetElement ?? "null")"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]"
					required="false">
				</autocomplete-component>
			</div>
			
			<div class="form__item hiiden">
				<config-label target="title" label="@localizer["Title"]"></config-label>
				<input-component name="title" id="title" placeholder="@localizer["Title"]" value="@Model.UserlaneStep?.Title" class="item__value"></input-component>
			</div>
			<div class="form__item">
				<config-label target="description" label="@localizer["Description"]"></config-label>
				<input-component name="description" id="description" placeholder="@localizer["description"]" value="@Model.UserlaneStep?.Description" class="item__value"></input-component>
			</div>
		</config-section>
	</form-component>
</div>