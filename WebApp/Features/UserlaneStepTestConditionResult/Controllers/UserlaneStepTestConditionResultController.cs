using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.UserlaneStepTestConditionResult;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics.CodeAnalysis;

namespace Levelbuild.Frontend.WebApp.Features.UserlaneStepTestConditionResult.Controllers;

/// <inheritdoc />
public class UserlaneStepTestConditionResultController : AdminController<UserlaneStepTestConditionResultDto>
{
    /// <summary>
    /// Constructor.
    /// </summary>
    /// <param name="logManager"></param>
    /// <param name="localizerFactory"></param>
    /// <param name="versionReader">injected VersionReader</param>
    /// <param name="contextFactory"></param>
    /// <param name="userManager"></param>
    public UserlaneStepTestConditionResultController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
                                        IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : base(
        logManager, logManager.GetLoggerForClass<UserlaneStepTestConditionResultController>(), contextFactory, userManager, localizerFactory, versionReader)
    {
    }

    /// <inheritdoc />
    [HttpGet("/Api/UserlaneStepTestConditionResult")]
    public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
    {
        var query = DatabaseContext.UserlaneStepTestConditionResults;
        return HandleQueryRequest<UserlaneStepTestConditionResultEntity, UserlaneStepTestConditionResultDto>(query, parameters);
    }

    /// <inheritdoc />
    [HttpGet("/Api/UserlaneStepTestConditionResult/{userlaneStepTestConditionResultId}")]
    public override ActionResult<FrontendResponse> Get(Guid userlaneStepTestConditionResultId)
    {
        return HandleGetRequest<UserlaneStepTestConditionResultEntity, UserlaneStepTestConditionResultDto>(DatabaseContext.UserlaneStepTestConditionResults, userlaneStepTestConditionResultId);
    }

    /// <inheritdoc />
    [HttpPost("/Api/UserlaneStepTestConditionResult")]
    public override Task<ActionResult<FrontendResponse>> Create([FromBody] UserlaneStepTestConditionResultDto userlaneStepTestConditionResultDto)
    {
        return HandleCreateRequestAsync(DatabaseContext.UserlaneStepTestConditionResults, userlaneStepTestConditionResultDto);
    }

    /// <inheritdoc />
    [HttpPatch("/Api/UserlaneStepTestConditionResult/{userlaneStepTestConditionResultId:guid}")]
    public override Task<ActionResult<FrontendResponse>> Update(Guid userlaneStepTestConditionResultId, UserlaneStepTestConditionResultDto dto)
    {
        return HandleUpdateRequestAsync(DatabaseContext.UserlaneStepTestConditionResults, userlaneStepTestConditionResultId, dto);
    }

    /// <inheritdoc />
    [HttpDelete("/Api/UserlaneStepTestConditionResult/{userlaneStepTestConditionResultId:guid}")]
    public override ActionResult<FrontendResponse> Delete(Guid userlaneStepTestConditionResultId)
    {
        return HandleDeleteRequest(DatabaseContext.UserlaneStepTestConditionResults, userlaneStepTestConditionResultId);
    }
}