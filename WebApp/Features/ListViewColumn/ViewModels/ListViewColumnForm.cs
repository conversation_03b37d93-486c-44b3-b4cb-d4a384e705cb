using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.ListViewColumn.ViewModels;

[ExcludeFromCodeCoverage]
public class ListViewColumnForm
{
	public ViewType ViewType { get; init; }

	public ListViewColumnDto? ListViewColumn { get; init; }

	public Guid ListViewId { get; init; }

	//public IList<DataFieldDto> AvailableFields { get; set; }

	public ListViewColumnForm(ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
	}
}