using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;
using Levelbuild.Frontend.WebApp.Shared.Converters;
using Levelbuild.Frontend.WebApp.Shared.Extensions;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Converters;

/// <summary>
/// JSON converter that excludes all non excluded properties from query results.
/// </summary>
/// <typeparam name="TEntityDto">The DTO to be converted</typeparam>
public class PublicApiValueConverter<TEntityDto> : JsonConverter<TEntityDto> where TEntityDto : EntityDto
{
	/// <inheritdoc />
	public override TEntityDto Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
	{
		throw new NotImplementedException();
	}
	
	/// <inheritdoc />
	public override void Write(Utf8JsonWriter writer, TEntityDto dto, JsonSerializerOptions options)
	{
		// find all not blacklisted properties
		var includedProperties = dto
			.GetType()
			.GetProperties()
			.Where(property => !Attribute.IsDefined(property, typeof(PublicApiExcludeAttribute)))
			.OrderBy((Func<PropertyInfo, string>)OrderByFunc);
		
		// write properties to object
		writer.WriteStartObject();
		foreach (var property in includedProperties)
		{
			writer.WritePropertyName(property.Name.ToLowerFirstChar());
			
			// JS Clients can't handle 64-bit integer values, so we have to serialize them as strings
			var rawValue = property.GetValue(dto);
			if (rawValue != null &&
				(property.PropertyType == typeof(ulong) || property.PropertyType == typeof(long)))
			{
				rawValue = rawValue.ToString();
			}
			
			writer.WriteRawValue($"{JsonSerializer.Serialize(rawValue, options)}");
		}
		
		writer.WriteEndObject();
		return;

		// order properties by JsonPropertyOrder attribute, if present
		string OrderByFunc(PropertyInfo property)
		{
			var orderAttribute = (JsonPropertyOrderAttribute?)Attribute.GetCustomAttribute(property, typeof(JsonPropertyOrderAttribute));

			return orderAttribute == null ? property.Name : $"{orderAttribute.Order:D10}_{property.Name}";
		}
	}
}

/// <summary>
/// Factory pattern for the <see cref="EntityHeaderValueConverter{TEntityDto}"/> class.
/// </summary>
public class PublicApiValueConverterFactory : JsonConverterFactory
{
	/// <inheritdoc />
	public override bool CanConvert(Type typeToConvert)
	{
		return typeToConvert.IsSubclassOf(typeof(EntityDto));
	}
	
	/// <inheritdoc />
	public override JsonConverter? CreateConverter(Type typeToConvert, JsonSerializerOptions options)
	{
		var converterType = typeof(PublicApiValueConverter<>).MakeGenericType(typeToConvert);
		return (JsonConverter?)Activator.CreateInstance(converterType);
	}
}