namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Attributes;

/// <summary>
/// Attribute to handle endpoint category groupings in Swagger documentation.
/// </summary>
[AttributeUsage(AttributeTargets.Class)]
public class SwaggerGroupAttribute : Attribute
{
	/// <summary>
	/// Name of the group. Used as group key at the same time.
	/// </summary>
	public string GroupName { get; set; }
	
	/// <summary>
	/// Sorting order.
	/// Groups will be sorted alphabetically if order values are identical.
	/// </summary>
	public uint Order { get; set; }
	
	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="groupName"></param>
	/// <param name="order"></param>
	public SwaggerGroupAttribute(string groupName, uint order)
	{
		GroupName = groupName;
		Order = order;
	}
}