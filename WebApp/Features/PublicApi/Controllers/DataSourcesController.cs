using System.Text.Json;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.PublicApi;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Attributes;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SharpCompress;
using Swashbuckle.AspNetCore.Annotations;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Controllers;

/// <summary>
/// Public API controller for data source access
/// </summary>
[SwaggerGroup("App Configuration", 1)]
public class DataSourcesController : ConfigController
{
	/// <inheritdoc />
	public DataSourcesController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, IVersionReader versionReader) :
		base(logManager.GetLoggerForClass<DataSourcesController>(), contextFactory, versionReader)
	{
	}

	/// <summary>
	/// Returns a list of data source ids and revision guids to enable faster app sync.
	/// </summary>
	/// <returns></returns>
	[HttpGet("SyncPreview")]
	[SwaggerOperation("Get Ids & revision info of all allowed data sources")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<IList<IdRevisionTupleDto>>))]
	public ActionResult<PublicApiResponse> SyncPreview()
	{
		try
		{
			var result = DatabaseContext.DataSources
				.Select(dataSource => new IdRevisionTupleDto { Id = dataSource.Id, Revision = dataSource.Revision })
				.ToList();

			return GetOkResponse(JsonSerializer.SerializeToElement(result, SerializerOptions));
		}
		catch (Exception e)
		{
			Logger.Error(e, "Page sync preview could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <inheritdoc />
	[SwaggerOperation("Get all allowed data sources")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<IList<DataSourceDto>>))]
	public override async Task<ActionResult<PublicApiResponse>> List()
	{
		var query = DatabaseContext.DataSources
				.IgnoreAutoIncludes();
		
		return await HandleListRequestAsync<DataSourceEntity, DataSourceDto>(query, nameof(DataFieldEntity.LookupSource), nameof(DataFieldEntity.VirtualLookupField));
	}

	/// <inheritdoc />
	[SwaggerOperation("Get a specific data source")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DataSourceDto>))]
	public override async Task<ActionResult<PublicApiResponse>> Get(Guid id)
	{
		var query = DatabaseContext.DataSources
			.Include(dataSource => dataSource.Fields).ThenInclude(dataField => dataField.LookupDisplayField)
			.Include(dataSource => dataSource.Fields).ThenInclude(dataField => dataField.Columns)
			.Include(dataSource => dataSource.Fields).ThenInclude(dataField => dataField.Filters)
			.Include(dataSource => dataSource.Workflows).ThenInclude(workflow => workflow.Nodes)
			.AsSplitQuery();
		
		return await HandleGetRequestAsync<DataSourceEntity, DataSourceDto>(query, id);
	}
}