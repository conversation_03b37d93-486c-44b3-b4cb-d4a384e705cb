using System.Text.Json;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Customer;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.PublicApi;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Attributes;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Swashbuckle.AspNetCore.Annotations;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Controllers;

/// <summary>
/// Public API controller for customer configuration access
/// </summary>
[SwaggerGroup("Authorization", 0)]
public class CustomersController : ConfigController
{
	private readonly UserManager _userManager;
	
	/// <inheritdoc />
	public CustomersController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IVersionReader versionReader) : 
		base(logManager.GetLoggerForClass<CustomersController>(), contextFactory, versionReader)
	{
		_userManager = userManager;
	}
	
	/// <inheritdoc />
	[SwaggerOperation("Get all allowed customers")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<IList<CustomerDto>>))]
	public override async Task<ActionResult<PublicApiResponse>> List()
	{
		try
		{
			var zitadelOrgIds = await _userManager.GetAllowedCustomerRemoteIdsAsync();
			
			return await HandleListRequestAsync<CustomerEntity, CustomerDto>(
				DatabaseContext.Customers.Where(
					customer => zitadelOrgIds.Contains(customer.RemoteId)
				)
			);
		}
		catch (Exception e)
		{
			Logger.Error(e, "Customer list could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <inheritdoc />
	[SwaggerOperation("Get a specific customer")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<CustomerDto>))]
	public override async Task<ActionResult<PublicApiResponse>> Get(Guid id)
	{
		try
		{
			var customer = await DatabaseContext.Customers.FindAsync(id);
			
			if (customer == null)
				return GetNotFoundResponse($"Customer with id: {id} could not be found");
			
			var zitadelOrgIds = await _userManager.GetAllowedCustomerRemoteIdsAsync();
			if (!zitadelOrgIds.Contains(customer.RemoteId))
				return GetUnauthorizedResponse($"Missing authorization to get Customer with id: {id}");
			
			var json = JsonSerializer.SerializeToElement(customer.ToDto(), SerializerOptions);
			return GetOkResponse(json);
		}
		catch (Exception e)
		{
			Logger.Error(e, "Customer could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Sets the user's current customer.
	/// </summary>
	/// <param name="id">id of a specific customer</param>
	/// <returns></returns>
	[SwaggerOperation("Sets the user's current customer")]
	[HttpPost("{id:guid}")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<CustomerDto>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public async Task<ActionResult<PublicApiResponse>> SwitchCustomer(Guid id)
	{
		try
		{
			var switchResult = await _userManager.SwitchCustomerAsync(id);
			if (!switchResult.Success)
				return GetBadRequestResponse(switchResult.Message);
			
			var json = JsonSerializer.SerializeToElement(switchResult.CustomerInstance!.ToDto(), SerializerOptions);
			return GetOkResponse(json);
		}
		catch (Exception e)
		{
			Logger.Error(e, "Customer could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}
}