@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("LoggerConfig", "");
	List<BaseColumnComponentTagHelper> columns =
	[
		new ListDataColumnComponentTagHelper()
		{
			Name = "loggerSource"
		},

		new ListDataColumnComponentTagHelper()
		{
			Name = "levelName",
			Label = localizer["levelName"]
		},

		new ListDataColumnComponentTagHelper()
		{
			Name = "isActive",
			Label = localizer["isActive"],
			Type = DataColumnType.Boolean,
			LiveEditable = true
		},

		new ListDataColumnComponentTagHelper()
		{
			Name = "logToFile",
			Label = localizer["logToFile"],
			Type = DataColumnType.Boolean,
			LiveEditable = true
		}
	];
}
<script type="module" defer>
	Page.setMainPage('/Admin/LoggerConfigs')
	Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: Page.getMainPageUrl() }])
</script>
<vc:admin-list-page entity="LoggerConfig" route-name="LoggerConfigs" display-property-name="loggerSource" localizer="@localizer" columns="@columns" open-on-row-click="true"></vc:admin-list-page>
<vc:create-panel entity="LoggerConfig" route-name="LoggerConfigs" localizer="@localizer"></vc:create-panel>