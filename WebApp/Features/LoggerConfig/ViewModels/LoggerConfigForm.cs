using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Logging;
using Levelbuild.Entities.Features.LoggerConfig;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Serilog.Events;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.LoggerConfig.ViewModels;

/// <summary>
/// Basic View Model for Logger Configuration Form
/// </summary>
[ExcludeFromCodeCoverage]
public class LoggerConfigForm
{
	public ViewType ViewType { get; init; }
	public LoggerConfigEntity? LoggerConfig { get; init; }

	public LoggerConfigDto? LoggerConfigDto { get; init; }

	public IEnumerable<LogEventLevel> LogEventLevels { get; } = Enum.GetValues(typeof(LogEventLevel))
		.Cast<LogEventLevel>()
		.ToList();

	public LoggerConfigForm(ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
	}
}