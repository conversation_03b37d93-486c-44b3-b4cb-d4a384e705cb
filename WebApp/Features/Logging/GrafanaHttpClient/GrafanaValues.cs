using Newtonsoft.Json.Linq;

namespace Levelbuild.Frontend.WebApp.Features.Logging.GrafanaHttpClient;

/// <summary>
/// All the log information from Grafana
/// </summary>
public class GrafanaValues
{
	/// <summary>
	/// The json response as per grafana api
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	// ReSharper disable once CollectionNeverUpdated.Global
	public List<JToken>? Values { set; private get; }
	
	private IDictionary<string, string>[]? _grafanaValuesMetas;
	private long[]? _grafanaValuesTimestampLongs;
	private string[]? _grafanaValuesLogLines;
	private string[]? _grafanaValuesTimestampStrings;
	private string[]? _grafanaValuesLogIds;
	
	/// <summary>
	/// Dictionary of all the tags per log line
	/// </summary>
	public IDictionary<string, string>[] GrafanaValuesMetas {
		get
		{
			_grafanaValuesMetas ??= Values?[0].ToObject<IDictionary<string, string>[]>();
			return _grafanaValuesMetas!;
		}
	}
	
	/// <summary>
	/// Timestamp per log line
	/// </summary>
	public long[] GrafanaValuesTimestampLongs {
		get
		{
			_grafanaValuesTimestampLongs ??= Values?[1].ToObject<long[]>();
			return _grafanaValuesTimestampLongs!;
		}
	}
	
	/// <summary>
	/// The log lines itself
	/// </summary>
	public string[] GrafanaValuesLogLines {
		get
		{
			_grafanaValuesLogLines ??= Values?[2].ToObject<string[]>();
			return _grafanaValuesLogLines!;
		}
	}
	
	/// <summary>
	/// Timestamp per log line as string
	/// </summary>
	public string[] GrafanaValuesTimestampStrings {
		get
		{
			_grafanaValuesTimestampStrings ??= Values?[3].ToObject<string[]>();
			return _grafanaValuesTimestampStrings!;
		}
	}
	
	/// <summary>
	/// The ids for each log line
	/// </summary>
	public string[] GrafanaValuesLogIds {
		get
		{
			_grafanaValuesLogIds ??= Values?[5].ToObject<string[]>();
			return _grafanaValuesLogIds!;
		}
	}
}