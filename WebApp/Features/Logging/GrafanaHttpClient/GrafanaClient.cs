using System.Net;
using System.Net.Http.Headers;
using IdentityModel.Client;
using Levelbuild.Frontend.WebApp.Shared;
using Newtonsoft.Json;

namespace Levelbuild.Frontend.WebApp.Features.Logging.GrafanaHttpClient;

/// <summary>
/// The client to query grafana for logs and configuration
/// </summary>
public class GrafanaClient: HttpClient
{
	private readonly string _dataSourceId;
	private readonly Func<string> _getDuration;


	/// <summary>
	/// Initializes a new instance of the <see cref="GrafanaClient"/> class.
	/// </summary>
	/// <param name="baseUrl">The base URL of the Grafana instance.</param>
	/// <param name="username">The username to use for basic authentication.</param>
	/// <param name="password">The password to use for basic authentication.</param>
	/// <param name="dataSourceId">The ID of the data source in Grafana to query.</param>
	/// <param name="metadataFrom">default history duration</param>
	public GrafanaClient(string baseUrl, string username, string password, string dataSourceId, TimeSpan metadataFrom)
	{
		BaseAddress = new Uri(baseUrl);
		this.SetBasicAuthentication(username, password);
		_dataSourceId = dataSourceId;
		_getDuration = () =>
			$"start={DateTimeOffset.UtcNow.Subtract(metadataFrom).ToUnixTimeMilliseconds() * 1000 * 1000}&end={DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() * 1000 * 1000}";
	}
	
	/// <summary>
	/// Queries the Grafana instance for labels for the specified data source within the last 24 hours.
	/// </summary>
	/// <returns>The list of labels as response from the Grafana API.</returns>
	public async Task<GetLabelsResponse?> GetLabels()
	{
		var message = new HttpRequestMessage(HttpMethod.Get, $"/api/datasources/uid/{_dataSourceId}/resources/labels?{_getDuration()}");
		return await GetResponse<GetLabelsResponse>(message);
	}
	
	/// <summary>
	/// Queries the Grafana instance for the distinct namespaces in the data source.
	/// </summary>
	/// <returns>The list of namespaces as response from the Grafana API.</returns>
	public async Task<GetNamespaceResponse?> GetNamespaces()
	{
		var message = new HttpRequestMessage(HttpMethod.Get, $"/api/datasources/uid/{_dataSourceId}/resources/label/namespace/values");
		return await GetResponse<GetNamespaceResponse>(message);
	}

	/// <summary>
	/// Queries the Grafana instance for the namespace configuration for the given namespace in the data source.
	/// </summary>
	/// <param name="namespaceName">The namespace to query the configuration for.</param>
	/// <returns>The configuration for the namespace as response from the Grafana API.</returns>
	public async Task<GetNamespaceConfigResponse?> GetNamespaceConfig(string namespaceName)
	{
		var message = new HttpRequestMessage(HttpMethod.Get, $"/api/datasources/uid/{_dataSourceId}/resources/series?match%5B%5D=%7Bnamespace%3D%22{namespaceName}%22%7D&{_getDuration()}");
		return await GetResponse<GetNamespaceConfigResponse>(message);
	}
	
	/// <summary>
	/// Queries the Grafana instance for the given query in the data source.
	/// </summary>
	/// <param name="query">The query to execute.</param>
	/// <returns>The response from the Grafana API.</returns>
	public async Task<QueryResponse?> Query(string query)
	{
		var message = new HttpRequestMessage(HttpMethod.Post, "/api/ds/query");
		message.Content = new StringContent(query, MediaTypeHeaderValue.Parse("application/json"));
		return await GetResponse<QueryResponse>(message);
	}
	
	/// <summary>
	/// Sends the given <paramref name="message"/> and interprets the response.
	/// </summary>
	/// <typeparam name="T">The type of the response.</typeparam>
	/// <param name="message">The message to send.</param>
	/// <returns>The deserialized response if the request was successful (HTTP status code 200), otherwise <see langword="null"/>.</returns>
	/// <exception cref="Exception">If the request was not successful.</exception>
	private async Task<T?> GetResponse<T>(HttpRequestMessage message)
	{
		HttpResponseMessage json = await SendAsync(message);
		
		if (json.StatusCode == HttpStatusCode.OK)
		{
			var responseContent = await json.Content.ReadAsStringAsync();
			return JsonConvert.DeserializeObject<T>(responseContent, ConfigHelper.JsonSettingsCamel);
		}
		
		var content = await json.Content.ReadAsStringAsync();
		throw new Exception($"Error while executing request {message.RequestUri}. Code: {json.StatusCode}. Response: {content}");
	}
}