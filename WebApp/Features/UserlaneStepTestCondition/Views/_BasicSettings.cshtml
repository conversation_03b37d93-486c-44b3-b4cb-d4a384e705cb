@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@inject IExtendedStringLocalizerFactory LocalizerFactory
@model Levelbuild.Frontend.WebApp.Features.UserlaneStepTestCondition.ViewModels.UserlaneStepTestConditionForm
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@{
	var localizer = LocalizerFactory.Create("UserlaneSteps", "");
	var conditionTypeOptions = Model.UserlaneStepTestConditionType.Select(type => new AutocompleteOptionDefinition(type.ToString(), type.GetLabel())).ToList();
	var compareOperatorOptions = Model.CompareOperators.Select(op => new AutocompleteOptionDefinition(op.ToString(), localizer[op.ToString()])).ToList();
	var targetOptions = Model.DataFields.Select(dataField =>
	{
		// Split the Label string by spaces
		var labelParts = (dataField.Label?.ToString()!).Split(' ');

		// Localize each part and join them with a space
		var localizedLabel = string.Join(" : ", labelParts.Select(part => localizer[part]));

		// Return the AutocompleteOptionDefinition with localized parts
		return new AutocompleteOptionDefinition(
			dataField.Value!, 
			localizedLabel
		);
	}).ToList();
	var logicalOperatorOptions = new List<AutocompleteOptionDefinition>
	{
		new("AND", "AND"),
		new("OR", "OR")
	};
}

<div class="grid--centered">
	<form-component id="userlanes-test-condition-form" skeleton="@(Model is { ViewType: ViewType.Edit, UserlaneStepTestCondition: null })">
		<div class="form__item">
			<input type="hidden" class="item__value" id="id" name="id" value="@Model.UserlaneStepTestCondition?.Id"/>
			<input type="hidden" class="item__value" id="userlaneStepId" name="userlaneStepId" value="@Model.UserlaneStepTestCondition?.UserlaneStepId"/>
		</div>
		<config-section label="Test Condition Configuration">
			<div class="form__item hidden">
				<config-label target="order" label="@localizer["Order"]"></config-label>
				<input-component type="InputDataType.Integer" min="0" step="1" name="order" id="order" required="true" placeholder="@localizer["Order"]" value="@Model.UserlaneStepTestCondition?.Order" class="item__value"></input-component>
			</div>

			<div class="form__item">
				<config-label target="conditionType" label="@localizer["ConditionType"]"></config-label>
				<autocomplete-component
					type="InputDataType.String"
					id="conditionType"
					options="conditionTypeOptions"
					name="conditionType"
					value = "@((object?)Model.UserlaneStepTestCondition?.ConditionType ?? "")"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]"
					required="true">
				</autocomplete-component>
			</div>

			<div class="form__item" id="fieldFormSection">
				<config-label target="field" label="@localizer["ConditionField"]"></config-label>
				<autocomplete-component
					type="InputDataType.String"
					id="field"
					options="targetOptions"
					name="field"
					value = "@(Model.UserlaneStepTestCondition?.Field ?? "")"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]"
					required="true">
				</autocomplete-component>
			</div>

			<!-- Combined Operator and Value Section -->
			<div class="form__item" id="operatorValueFormSection">
				<config-label target="operatorValue" label="@localizer["Operator"] & @localizer["Value"]"></config-label>
				<div class="operator-value-container">
					<!-- Merged display button -->
					<div class="operator-value-merged" id="operatorValueMerged" style="display: none;">
						<button type="button" class="operator-value-button" onclick="toggleOperatorValueEdit()">
							<span id="operatorValueDisplay">@localizer["pleaseChoose"]</span>
						</button>
					</div>

					<!-- Individual inputs (hidden when merged) -->
					<div class="operator-value-inputs" id="operatorValueInputs">
						<div class="operator-input">
							<autocomplete-component
								type="InputDataType.String"
								id="operator"
								options="compareOperatorOptions"
								name="operator"
								value = "@((object?)Model.UserlaneStepTestCondition?.Operator ?? "")"
								class="item__value"
								placeholder="@localizer["Operator"]"
								required="true">
							</autocomplete-component>
						</div>
						<div class="value-input">
							<input-component type="InputDataType.String" name="value" id="value" placeholder="@localizer["Value"]" value="@Model.UserlaneStepTestCondition?.Value" class="item__value"></input-component>
						</div>
					</div>
				</div>
			</div>

			<div class="form__item hidden" id="logicalOperatorFormSection">
				<config-label target="logicalOperator" label="@localizer["LogicalOperator"]"></config-label>
				<autocomplete-component
					type="InputDataType.String"
					id="logicalOperator"
					options="logicalOperatorOptions"
					name="logicalOperator"
					value = "@(Model.UserlaneStepTestCondition?.LogicalOperator ?? "")"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]"
					required="false">
				</autocomplete-component>
			</div>
			
		</config-section>
	</form-component>
</div>

<script>
	hasChanged = false;

	checkValues = () => {
		const conditionType = document.getElementById('conditionType').value;

		if (!hasChanged) {
			// Handle visibility on an initial load
			switch (conditionType) {
				case 'FieldComparison':
					document.getElementById('fieldFormSection').style.display = "";
					document.getElementById('operatorFormSection').style.display = "";
					document.getElementById('valueFormSection').style.display = "";
					break;
				case 'ElementExists':
				case 'ElementVisible':
					document.getElementById('fieldFormSection').style.display = "";
					document.getElementById('operatorFormSection').style.display = "none";
					document.getElementById('valueFormSection').style.display = "none";
					break;
				case 'TextContains':
				case 'ValueEquals':
					document.getElementById('fieldFormSection').style.display = "";
					document.getElementById('operatorFormSection').style.display = "none";
					document.getElementById('valueFormSection').style.display = "";
					break;
				default:
					document.getElementById('fieldFormSection').style.display = "";
					document.getElementById('operatorFormSection').style.display = "";
					document.getElementById('valueFormSection').style.display = "";
			}
			return;
		}

		// From this point, user has changed something, so clear fields as needed
		switch (conditionType) {
			case 'FieldComparison':
				document.getElementById('fieldFormSection').style.display = "";
				document.getElementById('operatorFormSection').style.display = "";
				document.getElementById('valueFormSection').style.display = "";
				break;
			case 'ElementExists':
			case 'ElementVisible':
				document.getElementById('fieldFormSection').style.display = "";
				document.getElementById('operatorFormSection').style.display = "none";
				document.getElementById('operator').value = null;
				document.getElementById('valueFormSection').style.display = "none";
				document.getElementById('value').value = null;
				break;
			case 'TextContains':
			case 'ValueEquals':
				document.getElementById('fieldFormSection').style.display = "";
				document.getElementById('operatorFormSection').style.display = "none";
				document.getElementById('operator').value = null;
				document.getElementById('valueFormSection').style.display = "";
				break;
			default:
				document.getElementById('fieldFormSection').style.display = "";
				document.getElementById('operatorFormSection').style.display = "";
				document.getElementById('valueFormSection').style.display = "";
		}
	};

	// Add a listener and set flag
	document.getElementById('conditionType').addEventListener('change', () => {
		hasChanged = true;
		checkValues();
	});

	// Initial call on a page load
	checkValues();
</script>