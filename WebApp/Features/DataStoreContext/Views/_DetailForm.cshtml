@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.DataStoreContext.ViewModels.DataStoreContextForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var viewType = Model.ViewType.ToString().ToLower();
	var localizer = LocalizerFactory.Create("DataStoreContext", "detail");
}

<form-component id="data-store-context-@viewType-form">
	<config-section label="@localizer["sectionInfo"]">
		<input type="hidden" class="item__value" name="id" value="@Model.DataStoreContext?.Id"/>
		<input type="hidden" class="item__value" id="data-store-id" name="dataStoreId" value="@Model.DataStoreId"/>
		<div class="form__item">
			<config-label target="context-name" label="@localizer["name"]"></config-label>
			<input-component type="InputDataType.String" id="context-name" name="name" value="@(Model.DataStoreContext?.Name ?? string.Empty)"
			                 class="item__value"
			                 required="true">
			</input-component>
		</div>
		<div class="form__item">
			<config-label target="context-customer-id" label="@localizer["customer"]"></config-label>
			@{
				var customerColumns = new List<AutocompleteColumnDefinition>
				{
					new("displayName", localizer["name"], DataType.String)
				};
			}
			@if (Model.ViewType == ViewType.Create)
			{
				<autocomplete-component type="InputDataType.String" id="context-customer-id" Url="/Api/Customers/autocomplete/@Model.DataStoreId"
				                        columns="customerColumns" name="customerId" value="@(Model.DataStoreContext?.CustomerId.ToString() ?? string.Empty)"
				                        class="item__value"
				                        placeholder="@localizer["pleaseChoose"]" required="true"
				                        edit-link-url="/Admin/Customers/{slug}">
				</autocomplete-component>
			}
			else
			{
				<input-component type="InputDataType.String" id="customer-display-name" name="customerName" value="@(Model.DataStoreContext?.Customer?.DisplayName)"
				                 readonly="true">
				</input-component>
			}
		</div>
		<div class="form__item">
			<config-label target="context-enabled" label="@localizer["enabled"]"></config-label>
			<toggle-component id="context-enabled" name="enabled" value="@(Model.DataStoreContext?.Enabled)" class="item__value"></toggle-component>
		</div>
	</config-section>
	<div id="data-store-context-options" class="form__options">
		<i class="icon loading fa-solid fa-gear fa-spin hide"></i>
	</div>
</form-component>

<template id="data-store-context-group-template">
	<config-section label="default"></config-section>
</template>
<template id="data-store-context-option-input-template">
	<div class="form__item">
		<config-label class="option__label" label="" target=""></config-label>
		<input-component class="option__input item__value"></input-component>
		<legend></legend>
	</div>
</template>
<template id="data-store-context-option-textarea-template">
	<div class="form__item">
		<config-label class="option__label" label="" target=""></config-label>
		<textarea-component class="option__input item__value"></textarea-component>
		<legend></legend>
	</div>
</template>
<template id="data-store-context-option-autocomplete-template">
	<div class="form__item">
		<config-label class="option__label" label="" target=""></config-label>
		<autocomplete-component class="option__input item__value"></autocomplete-component>
		<legend></legend>
	</div>
</template>
<template id="data-store-context-option-toggle-template">
	<div class="form__item">
		<config-label class="option__label" description="" label="" target=""></config-label>
		<toggle-component class="option__input item__value"></toggle-component>
	</div>
</template>
<script type="module" defer>
	let dataStoreConfigDetailContext = (() => {
		const groupTemplate = document.getElementById('data-store-context-group-template')
		const optionInputTemplate = document.getElementById('data-store-context-option-input-template')
		const optionTextAreaTemplate = document.getElementById('data-store-context-option-textarea-template')
		const optionAutocompleteTemplate = document.getElementById('data-store-context-option-autocomplete-template')
		const optionToggleTemplate = document.getElementById('data-store-context-option-toggle-template')
		const optionShowIfsMap = new Map()
		
		@if (Model.DataStoreInfo != null)
		{
			@:appendConfigurationOptionsToForm(@Json.Serialize(Model.DataStoreInfo), @Json.Serialize(Model.DataStoreContext?.Options))
		}

		// create input fields for options and append them to the form
		function appendConfigurationOptionsToForm(dataStoreInfo, optionValues) {
			const form = document.querySelector('#data-store-context-@viewType-form #data-store-context-options')
			dataStoreInfo.configurationGroups.forEach(groupConfig => {

				// create group html and fill it out
				const groupElement = groupTemplate.content.cloneNode(true)
				groupElement.querySelector('lvl-section').heading = groupConfig.name

				// create option html for each config option
				groupConfig.options.forEach(optionConfig => {
					const optionElement = createOptionElement(optionConfig, optionValues)
					groupElement.querySelector('.form__section').appendChild(optionElement)
				})

				// finally attach new group to the dom
				form.appendChild(groupElement)
				document.querySelector('#data-store-context-@viewType-form #data-store-context-options .loading').classList.add('hide')
			})

			Page.handleConditionalVisibility(form, optionShowIfsMap)
		}

		// uses an json config object to create a dom node
		function createOptionElement(optionConfig, optionValues) {
			const optionId = `data-store-context-${optionConfig.name.toLowerCase()}`
			const containsDescription = optionConfig.tooltip != null
			const optionType = optionConfig.type
			const hasOptions = optionConfig.options != null
			let optionElement
			if (hasOptions)
				optionElement = optionAutocompleteTemplate.content.cloneNode(true)
			else switch (optionType.toLowerCase()) {
				case 'boolean':
					optionElement = optionToggleTemplate.content.cloneNode(true)
					break
				case 'text':
					optionElement = optionTextAreaTemplate.content.cloneNode(true)
					break
				case 'password':
					// TODO: Extend, when Password is supported. For now fallback to default	
				default:
					optionElement = optionInputTemplate.content.cloneNode(true)
					break
			}
			if (containsDescription) {
				if (optionType.toLowerCase() === 'boolean')
					optionElement.querySelector('.option__description').innerText = optionConfig.tooltip
				else
					optionElement.querySelector('legend').innerText = optionConfig.tooltip
			}

			optionElement.querySelector('.option__label').innerText = optionConfig.label
			optionElement.querySelector('.option__label').htmlFor = optionId

			const input = optionElement.querySelector('.option__input')
			input.setAttribute('id', optionId)
			input.setAttribute('name', optionConfig.name)
			if (hasOptions) {
				let optionBody = document.createElement('lvl-body')
				optionConfig.options.forEach(option => {
					let optionNode = document.createElement('lvl-option')
					optionNode.setAttribute('value', option)
					optionNode.innerText = option
					optionBody.appendChild(optionNode)
				})
				input.appendChild(optionBody)
			}

			if (optionConfig.required && optionConfig.type.toString().toLowerCase() !== 'boolean')
				input.setAttribute('required', true)
			if (!optionValues && optionConfig.defaultValue)
				input.setAttribute('value', optionConfig.defaultValue)

			if (optionValues && optionValues[optionConfig.name])
				input.setAttribute('value', optionValues[optionConfig.name])
			else if (optionValues && optionValues[optionConfig.name] === undefined && optionConfig.defaultValue)
				input.setAttribute('value', optionConfig.defaultValue)

			let showIfGroups = []
			optionConfig.showIf?.forEach(optionShowIfs => {
				let showIfGroup = []
				optionShowIfs?.forEach(optionShowIf => {
					// add to showIfs Map
					showIfGroup.push({comparator: optionShowIf.type.toLowerCase(), option: optionShowIf.configOption, compareValue: optionShowIf.compareValue})
				})
				if (showIfGroup?.length)
					showIfGroups.push(showIfGroup)
			})
			if (showIfGroups?.length)
				optionShowIfsMap.set(optionConfig.name, showIfGroups)

			return optionElement
		}
		return {}
	})()
</script>