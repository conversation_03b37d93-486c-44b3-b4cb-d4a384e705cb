@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Features.User.Services
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@inject IExtendedStringLocalizerFactory LocalizerFactory
@inject UserManager UserManager
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("User", "");
	List<BaseColumnComponentTagHelper> columns =
	[
		new ListDataColumnComponentTagHelper()
		{
			Name = "username"
		},
		new ListDataColumnComponentTagHelper()
		{
			Name = "mainCustomerName",
			Label = localizer["mainCustomerName"]
		}
	];
}
<vc:admin-list-page list-id="machine-user-list" entity="User" route-name="MachineUsers" localizer="@localizer" display-property-name="username" columns="@columns"
                    open-on-row-click="true" menu-entry="Edit" parent-property-name="mainCustomerId" parent-property-value="@(await UserManager.GetCurrentCustomerAsync()).Id">
</vc:admin-list-page>