@using Levelbuild.Core.EntityInterface
@model Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels.UserlaneForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@{
    var localizer = LocalizerFactory.Create("Userlane", "");
}

<div class="activity-feed">
    <div class="feed-main" id="feedContainer">
        <!-- Feed content will be loaded here via JavaScript -->
        <div class="feed-loading">
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading activity feed...</p>
                <button class="btn btn-outline-primary btn-sm mt-2" onclick="debugFeedStatus(); loadFeedData();">
                    <i class="fas fa-refresh"></i> Refresh Feed
                </button>
            </div>
        </div>
    </div>

    <div class="feed-sidebar">
        <div class="sidebar-content">
            <div class="sidebar-icon">
                <i class="fas fa-search fa-3x"></i>
            </div>
            <h4>Further information</h4>
            <p>Please select an entry on the left for which you would like to see further information.</p>
        </div>
    </div>
</div>

<!-- Report Modal -->
<div id="reportModal" class="report-modal" style="display: none;">
    <div class="modal-backdrop" onclick="closeReportModal()"></div>
    <div class="modal-container">
        <div class="modal-header">
            <div class="modal-title">
                <i class="fas fa-chart-bar"></i>
                <span>Userlane Test Results</span>
            </div>
            <button class="modal-close" onclick="closeReportModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-content">
            <div class="results-section">
                <h3>Results</h3>
                
                <div class="results-table">
                    <div class="table-header">
                        <div class="col-step">Step/Action</div>
                        <div class="col-test">Test Condition</div>
                        <div class="col-expected">Expected</div>
                        <div class="col-actual">Actual</div>
                        <div class="col-duration">Duration</div>
                        <div class="col-status">Status</div>
                    </div>
                    
                    <div class="table-body">
                        <!-- Action 1 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>1. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-duration">30 secs</div>
                            <div class="col-status"><span class="status-badge success">Complete</span></div>
                        </div>
                        
                        <!-- Test Condition 1 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to Field
                            </div>
                            <div class="col-duration">10 secs</div>
                            <div class="col-status"><span class="status-badge success">Complete</span></div>
                        </div>
                        
                        <!-- Test Condition 2 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to ma...
                            </div>
                            <div class="col-duration">10 secs</div>
                            <div class="col-status"><span class="status-badge success">Complete</span></div>
                        </div>
                        
                        <!-- Test Condition 3 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to Field
                            </div>
                            <div class="col-duration">10 secs</div>
                            <div class="col-status"><span class="status-badge success">Complete</span></div>
                        </div>
                        
                        <!-- Action 2 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>2. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-duration">10 secs</div>
                            <div class="col-status"><span class="status-badge failed">Incomplete</span></div>
                        </div>
                        
                        <!-- Failed Test Condition -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to ma...
                            </div>
                            <div class="col-duration">10 secs</div>
                            <div class="col-status"><span class="status-badge failed">Incomplete</span></div>
                        </div>
                        
                        <!-- Action 3 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator success"></div>
                                <span>3. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-prev">15 secs</div>
                            <div class="col-present">10 secs</div>
                        </div>
                        
                        <!-- Action 4 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>4. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-duration">10 secs</div>
                            <div class="col-status"><span class="status-badge success">Complete</span></div>
                        </div>
                        
                        <!-- Failed Test Condition for Action 4 -->
                        <div class="table-row test-condition">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>-</span>
                            </div>
                            <div class="col-test">
                                <i class="fas fa-circle-check"></i>
                                Field comparison to Field
                            </div>
                            <div class="col-duration">10 secs</div>
                            <div class="col-status"><span class="status-badge success">Complete</span></div>
                        </div>
                        
                        <!-- Action 5 -->
                        <div class="table-row">
                            <div class="col-actions">
                                <div class="action-indicator failed"></div>
                                <span>5. Action</span>
                            </div>
                            <div class="col-test">-</div>
                            <div class="col-duration">10 secs</div>
                            <div class="col-status"><span class="status-badge success">Complete</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <button class="btn-cancel" onclick="closeReportModal()">Cancel</button>
            <button class="btn-download">Download Results</button>
            <button class="btn-try-again">Try again</button>
        </div>
    </div>
</div>

<style>
.activity-feed {
    display: flex;
    min-height: 100vh;
    width: 100%;
    background: #f8f9fa;
    margin: 0;
    padding: 0;
}

.feed-main {
    flex: 1;
    width: 50%;
    padding: 1.5rem;
    overflow-y: auto;
    background: white;
    border-right: 1px solid #e9ecef;
}

.feed-date-group {
    margin-bottom: 2rem;
}

.feed-date-label {
    width: 120px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    flex-shrink: 0;
    margin-top: 0.5rem;
}

.feed-date-label.empty {
    visibility: hidden;
}

.timeline-content {
    display: flex;
    gap: 1rem;
    flex: 1;
}

.feed-date {
    font-size: 1rem;
}

.feed-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 0;
    position: relative;
}

.timeline-connector {
    position: relative;
    width: 60px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-shrink: 0;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: -1.5rem;
    width: 2px;
    background: #e9ecef;
    transform: translateX(-50%);
}

.feed-item:last-child .timeline-line {
    display: none;
}

.feed-avatar {
    position: relative;
    z-index: 2;
    margin-top: 0.5rem;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
    color: white;
}

.avatar-circle.blue {
    background-color: #0066cc;
}

.avatar-circle.red {
    background-color: #dc3545;
}

.avatar-circle.green {
    background-color: #28a745;
}

.feed-content {
    flex: 1;
    padding-top: 0.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f1f3f4;
}

.feed-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.feed-user {
    font-weight: 600;
    color: #495057;
}

.feed-time {
    color: #6c757d;
}

.feed-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.feed-status.successful {
    background-color: #d4edda;
    color: #155724;
}

.feed-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

.feed-status.deleted {
    background-color: #f8d7da;
    color: #721c24;
}

.feed-status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.feed-status.running {
    background-color: #d1ecf1;
    color: #0c5460;
}

.feed-action-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: auto;
}

.feed-action-btn:hover {
    color: #495057;
}

.feed-description {
    color: #495057;
    margin-bottom: 0.75rem;
}

.feed-actions {
    margin-top: 0.75rem;
}

.feed-separator {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.feed-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
    z-index: 1;
}

.feed-separator span {
    background: white;
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.875rem;
    position: relative;
    z-index: 2;
}

.feed-sidebar {
    flex: 1;
    width: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-content {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.sidebar-icon {
    opacity: 0.3;
    margin-bottom: 1rem;
}

.sidebar-content h4 {
    color: #495057;
    margin-bottom: 1rem;
}

.sidebar-content p {
    font-size: 0.875rem;
    line-height: 1.5;
}

.btn-outline-primary {
    border: 1px solid #0066cc;
    color: #0066cc;
    background: transparent;
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    text-decoration: none;
    display: inline-block;
}

.btn-outline-primary:hover {
    background: #0066cc;
    color: white;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.8125rem;
}

/* Report Modal Styles */
.report-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-container {
    position: relative;
    background: white;
    border-radius: 8px;
    width: 95%;
    max-width: 1200px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1001;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.modal-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
}

.modal-close:hover {
    color: #495057;
}

.modal-content {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
    overflow-x: auto;
}

.results-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
    display: inline-block;
}

.results-table {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    min-width: 900px;
    overflow-x: auto;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 2.5fr 1.5fr 1.5fr 1fr 1fr;
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    min-width: 900px;
}

.table-header > div {
    padding: 0.75rem 0.5rem;
    border-right: 1px solid #e9ecef;
    font-size: 0.875rem;
}

.table-header > div:last-child {
    border-right: none;
}

.table-body {
    background: white;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 2.5fr 1.5fr 1.5fr 1fr 1fr;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
    min-width: 900px;
}

.table-row:last-child {
    border-bottom: none;
}

.table-row.test-condition {
    background: #f8f9fa;
}

.table-row > div {
    padding: 0.75rem 0.5rem;
    border-right: 1px solid #e9ecef;
    font-size: 0.875rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table-row > div:last-child {
    border-right: none;
}

.col-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-indicator {
    width: 4px;
    height: 20px;
    border-radius: 2px;
}

.action-indicator.success {
    background: #28a745;
}

.action-indicator.failed {
    background: #dc3545;
}

.col-test {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
}

.col-test i {
    color: #28a745;
    font-size: 0.875rem;
}

.col-duration, .col-status {
    font-size: 0.875rem;
    color: #495057;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.success {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.failed {
    background-color: #f8d7da;
    color: #721c24;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.btn-cancel {
    background: none;
    border: 1px solid #6c757d;
    color: #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-cancel:hover {
    background: #6c757d;
    color: white;
}

.btn-download {
    background: none;
    border: 1px solid #007bff;
    color: #007bff;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-download:hover {
    background: #007bff;
    color: white;
}

.btn-try-again {
    background: #007bff;
    border: 1px solid #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-try-again:hover {
    background: #0056b3;
}

/* Empty state styles */
.feed-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.empty-state {
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    opacity: 0.3;
    margin-bottom: 1rem;
}

.empty-state h4 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Feed details styles */
.feed-details {
    padding: 1rem;
}

.feed-details h4 {
    color: #495057;
    margin-bottom: 1rem;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
    display: inline-block;
}

.detail-item {
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
}

.detail-item strong {
    color: #495057;
    display: inline-block;
    min-width: 80px;
}

.detail-item pre {
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 200px;
    overflow-y: auto;
}

.detail-item code {
    background: #f8f9fa;
    padding: 0.125rem 0.25rem;
    border-radius: 3px;
    font-size: 0.75rem;
    color: #e83e8c;
}

.detail-item .feed-status {
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Loading state styles */
.feed-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #6c757d;
}

.feed-loading i {
    margin-bottom: 1rem;
}

.feed-loading p {
    font-size: 0.875rem;
}
</style>

<script>
// Wrap in namespace to prevent variable conflicts
(function() {
    'use strict';
    
console.log('🚀 Feed script loading...');

// Immediate execution to check if script is running
(function() {
    console.log('🚀 Feed script IIFE executing');
    console.log('🚀 Document ready state:', document.readyState);
    console.log('🚀 Feed container exists:', !!document.getElementById('feedContainer'));
    console.log('🚀 Current URL:', window.location.href);
    console.log('🚀 Userlane ID from model:', '@Model.Userlane?.Id');

    // Add a visual indicator that the script is running
    const feedContainer = document.getElementById('feedContainer');
    if (feedContainer) {
        const indicator = document.createElement('div');
        indicator.style.cssText = 'position: absolute; top: 10px; right: 10px; background: green; color: white; padding: 5px; border-radius: 3px; font-size: 12px; z-index: 1000;';
        indicator.textContent = 'JS LOADED';
        feedContainer.style.position = 'relative';
        feedContainer.appendChild(indicator);

        // Remove indicator after 3 seconds
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 3000);
    }
})();

function openReportModal() {
    document.getElementById('reportModal').style.display = 'flex';
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function closeReportModal() {
    document.getElementById('reportModal').style.display = 'none';
    document.body.style.overflow = 'auto'; // Restore scrolling
}

// Close modal when pressing Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeReportModal();
    }
});

// Debug function to check feed status
function debugFeedStatus() {
    console.log('=== Feed Debug Info ===');
    console.log('Current URL:', window.location.href);
    console.log('Userlane ID from model:', '@Model.Userlane?.Id');
    console.log('Feed container exists:', !!document.getElementById('feedContainer'));
    console.log('Feed container visible:', document.getElementById('feedContainer')?.offsetParent !== null);
    console.log('Feed container content:', document.getElementById('feedContainer')?.innerHTML.substring(0, 100));

    // Test the API endpoint directly
    const userlaneId = '@Model.Userlane?.Id';
    if (userlaneId && userlaneId !== '' && userlaneId !== 'null') {
        const testUrl = `/Api/UserlaneResultBatch?filters[0].FilterColumn=UserlaneId&filters[0].CompareValue=${userlaneId}&pageSize=10`;
        console.log('Testing API URL:', testUrl);

        fetch(testUrl)
            .then(response => {
                console.log('API Response Status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('API Response Data:', data);
                console.log('Data count:', data.data ? data.data.length : 'No data array');
            })
            .catch(error => {
                console.error('API Test Error:', error);
            });
    }
    console.log('========================');
}

// Load feed data when the feed tab is shown
function loadFeedData() {
    console.log('=== loadFeedData called ===');

    // Try to get Userlane ID from model first, then from URL
    let userlaneId = '@Model.Userlane?.Id';
    console.log('Userlane ID from model:', userlaneId);
    
    // If model ID is empty, extract from URL
    if (!userlaneId || userlaneId === '' || userlaneId === 'null' || userlaneId === 'undefined') {
        console.log('Model ID not available, extracting from URL...');
        const urlPath = window.location.pathname;
        const urlMatch = urlPath.match(/\/Admin\/Userlane\/([a-f0-9-]{36})/i);
        if (urlMatch && urlMatch[1]) {
            userlaneId = urlMatch[1];
            console.log('Extracted Userlane ID from URL:', userlaneId);
        }
    }
    
    console.log('Final Userlane ID:', userlaneId);
    console.log('Userlane ID type:', typeof userlaneId);
    console.log('Userlane ID length:', userlaneId ? userlaneId.length : 'null/undefined');

    if (!userlaneId || userlaneId === '' || userlaneId === 'null' || userlaneId === 'undefined') {
        console.error('❌ No valid Userlane ID available');
        console.log('Model.Userlane object:', '@Model.Userlane');
        console.log('Current URL:', window.location.href);
        showFeedError('No Userlane ID available. Please ensure you are viewing a specific Userlane.');
        return;
    }

    const apiUrl = `/Api/UserlaneResultBatch?filters[0].FilterColumn=UserlaneId&filters[0].CompareValue=${userlaneId}&pageSize=100&sortColumn=CreatedDateTime&sortDirection=desc`;
    console.log('🌐 API URL:', apiUrl);
    console.log('🔍 Making fetch request...');

    fetch(apiUrl)
        .then(response => {
            console.log('📡 Response received');
            console.log('📊 Response status:', response.status);
            console.log('📊 Response statusText:', response.statusText);
            console.log('📊 Response ok:', response.ok);
            console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            console.log('🔄 Parsing JSON...');
            return response.json();
        })
        .then(data => {
            console.log('✅ JSON parsed successfully');
            console.log('📦 Full API Response:', data);
            console.log('📦 Response structure:');
            console.log('  - error:', data.error);
            console.log('  - data type:', typeof data.data);
            console.log('  - data is array:', Array.isArray(data.data));
            console.log('  - data length:', data.data ? data.data.length : 'null/undefined');
            console.log('  - validationErrors:', data.validationErrors);
            console.log('  - All response keys:', Object.keys(data));

            // Check if this is a paginated response format
            if (data.data && typeof data.data === 'object' && !Array.isArray(data.data)) {
                console.log('📦 Data object keys:', Object.keys(data.data));
                console.log('📦 Data object:', data.data);
            }

            // Handle FrontendResponse format
            let feedItems = [];

            // Check if this is a valid FrontendResponse (no error)
            if (data.error) {
                console.error('❌ API returned error:', data.error);
                showFeedError(`API Error: ${data.error.errorMessage || 'Unknown error'}`);
                return;
            }

            // Check for validation errors
            if (data.validationErrors && data.validationErrors.length > 0) {
                console.error('❌ API returned validation errors:', data.validationErrors);
                showFeedError(`Validation Error: ${data.validationErrors[0].errorMessage || 'Validation failed'}`);
                return;
            }

            // Extract data from FrontendResponse structure
            if (data.data && data.data.rows && Array.isArray(data.data.rows)) {
                // Standard FrontendResponse with QueryResponseData
                feedItems = data.data.rows;
                console.log('📊 Total count from API:', data.data.countTotal);
            } else if (data.data && Array.isArray(data.data)) {
                // Direct data array (fallback)
                feedItems = data.data;
            } else if (Array.isArray(data)) {
                // Response is directly an array (fallback)
                feedItems = data;
            } else {
                console.log('🔍 Unexpected response structure, checking for arrays...');
                // Try to find any array in the response
                for (const key of Object.keys(data)) {
                    if (Array.isArray(data[key])) {
                        console.log(`📦 Found array in key "${key}":`, data[key]);
                        feedItems = data[key];
                        break;
                    }
                }
            }

            console.log('🎯 Final feed items to render:', feedItems.length);

            if (feedItems.length > 0) {
                console.log('📋 First item structure:', feedItems[0]);
                console.log('📋 First item keys:', Object.keys(feedItems[0]));
            } else {
                console.log('⚠️ No feed items found for Userlane ID:', userlaneId);
            }

            renderFeedData(feedItems);
        })
        .catch(error => {
            console.error('💥 Error in loadFeedData:', error);
            console.error('💥 Error stack:', error.stack);
            showFeedError(`Error loading feed data: ${error.message}`);
        });

    console.log('=== loadFeedData setup complete ===');
}

// Render feed data in the UI
function renderFeedData(batchItems) {
    console.log('=== renderFeedData called ===');
    console.log('📊 Batch items to render:', batchItems);
    console.log('📊 Batch items count:', batchItems ? batchItems.length : 'null/undefined');

    const container = document.getElementById('feedContainer');
    if (!container) {
        console.error('❌ Feed container not found');
        return;
    }

    if (!batchItems || batchItems.length === 0) {
        console.log('⚠️ No batch items to render - showing empty state');
        container.innerHTML = `
            <div class="feed-empty">
                <div class="empty-state">
                    <i class="fas fa-rss fa-3x"></i>
                    <h4>No Feed Data</h4>
                    <p>There are no test runs recorded for this Userlane yet.</p>
                </div>
            </div>
        `;
        return;
    }

    // Group items by date
    console.log('🗂️ Grouping batch items by date...');
    const groupedItems = groupBatchItemsByDate(batchItems);
    console.log('🗂️ Grouped items:', groupedItems);
    console.log('🗂️ Number of date groups:', Object.keys(groupedItems).length);

    let html = '';

    for (const [dateKey, items] of Object.entries(groupedItems)) {
        console.log(`📅 Processing date group "${dateKey}" with ${items.length} items`);
        html += `<div class="feed-date-group">`;

        items.forEach((batch, index) => {
            console.log(`📋 Processing batch ${index + 1}/${items.length}:`, batch);

            const isFirstInGroup = index === 0;
            const userInitials = getUserInitials(batch.userName);
            const avatarColor = getAvatarColor(batch.status, batch.userId);
            const duration = formatDuration(batch.runtime);

            console.log(`  - User: ${batch.userName} (${userInitials})`);
            console.log(`  - Duration: ${duration}`);
            console.log(`  - Status: ${batch.status}`);
            console.log(`  - Status Class: ${getStatusClass(batch.status)}`);
            console.log(`  - Avatar Color: ${avatarColor}`);
            console.log(`  - Created: ${batch.createdDateTime}`);

            html += `
                <div class="feed-item">
                    <div class="feed-date-label ${isFirstInGroup ? '' : 'empty'}">
                        ${isFirstInGroup ? `<span class="feed-date">${dateKey}</span><i class="fas fa-chevron-down"></i>` : ''}
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-connector">
                            <div class="timeline-line"></div>
                            <div class="feed-avatar">
                                <div class="avatar-circle ${avatarColor}">
                                    ${userInitials === '??' ? '<i class="fas fa-user"></i>' : userInitials}
                                </div>
                            </div>
                        </div>
                        <div class="feed-content">
                            <div class="feed-meta">
                                <span class="feed-user">${batch.userName || 'Unknown User'}</span>
                                <span class="feed-time">${formatTimestamp(batch.createdDateTime)}</span>
                                ${batch.status ? `<span class="feed-status ${getStatusClass(batch.status)}">${getStatusDisplayText(batch.status)}</span>` : ''}
                                <button class="feed-action-btn" onclick="showBatchDetails('${batch.id}')">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="feed-description">
                                "@Model.Userlane?.Name" was run (${duration})
                            </div>
                            <div class="feed-actions">
                                <button class="btn btn-outline-primary btn-sm" onclick="openReportModal('${batch.id}')">Open report</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `</div>`;
    }

    console.log('🎨 Generated HTML length:', html.length);
    console.log('🎨 Setting container innerHTML...');
    container.innerHTML = html;
    console.log('✅ Feed rendering complete');
    console.log('=== renderFeedData complete ===');
}

// Helper functions
function groupBatchItemsByDate(items) {
    console.log('🗂️ groupBatchItemsByDate called with', items.length, 'items');

    const grouped = {};
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

    console.log('📅 Today:', today);
    console.log('📅 Yesterday:', yesterday);

    items.forEach((item, index) => {
        console.log(`📋 Processing item ${index + 1}:`, item);

        if (!item.createdDateTime) {
            console.log(`⚠️ Item ${index + 1} has no createdDateTime, skipping`);
            return;
        }

        const itemDate = new Date(item.createdDateTime);
        console.log(`📅 Item ${index + 1} date:`, itemDate);

        if (isNaN(itemDate.getTime())) {
            console.log(`⚠️ Item ${index + 1} has invalid date, skipping`);
            return; // Skip invalid dates
        }

        const itemDateOnly = new Date(itemDate.getFullYear(), itemDate.getMonth(), itemDate.getDate());
        console.log(`📅 Item ${index + 1} date only:`, itemDateOnly);

        let dateKey;

        if (itemDateOnly.getTime() === today.getTime()) {
            dateKey = 'Today';
            console.log(`📅 Item ${index + 1} is today`);
        } else if (itemDateOnly.getTime() === yesterday.getTime()) {
            dateKey = 'Yesterday';
            console.log(`📅 Item ${index + 1} is yesterday`);
        } else {
            // Format as DD.MM.YYYY
            const day = itemDate.getDate().toString().padStart(2, '0');
            const month = (itemDate.getMonth() + 1).toString().padStart(2, '0');
            const year = itemDate.getFullYear();
            dateKey = `${day}.${month}.${year}`;
            console.log(`📅 Item ${index + 1} is specific date: ${dateKey}`);
        }

        if (!grouped[dateKey]) {
            grouped[dateKey] = [];
            console.log(`📅 Created new group for: ${dateKey}`);
        }
        grouped[dateKey].push(item);
        console.log(`📅 Added item to group "${dateKey}", now has ${grouped[dateKey].length} items`);
    });

    // Sort groups by date (most recent first)
    const sortedGrouped = {};
    const sortedKeys = Object.keys(grouped).sort((a, b) => {
        if (a === 'Today') return -1;
        if (b === 'Today') return 1;
        if (a === 'Yesterday') return -1;
        if (b === 'Yesterday') return 1;

        // Parse DD.MM.YYYY format for comparison
        const parseDate = (dateStr) => {
            const [day, month, year] = dateStr.split('.').map(Number);
            return new Date(year, month - 1, day);
        };

        return parseDate(b) - parseDate(a);
    });

    sortedKeys.forEach(key => {
        sortedGrouped[key] = grouped[key];
    });

    console.log('🗂️ Final grouped result:', sortedGrouped);
    console.log('🗂️ Final group keys:', Object.keys(sortedGrouped));
    console.log('🗂️ groupBatchItemsByDate complete');

    return sortedGrouped;
}

function getUserInitials(userName) {
    if (!userName || typeof userName !== 'string') return '??';

    // Remove extra whitespace and split by spaces
    const parts = userName.trim().split(/\s+/).filter(part => part.length > 0);

    if (parts.length >= 2) {
        // First and last name initials
        return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
    } else if (parts.length === 1 && parts[0].length >= 2) {
        // First two characters of single name
        return parts[0].substring(0, 2).toUpperCase();
    } else if (parts.length === 1 && parts[0].length === 1) {
        // Single character, duplicate it
        return (parts[0][0] + parts[0][0]).toUpperCase();
    }

    return '??';
}

function getAvatarColor(status, userId) {
    if (!status) {
        // Fallback to user-based hash if no status
        const hash = userId ? userId.split('').reduce((a, b) => { a = ((a << 5) - a) + b.charCodeAt(0); return a & a; }, 0) : 0;
        return ['blue', 'green', 'red'][Math.abs(hash) % 3];
    }
    
    switch (status.toLowerCase()) {
        case 'successful': 
        case 'pass': 
        case 'passed': 
            return 'green';
        case 'failed': 
        case 'fail': 
        case 'error':
            return 'red';
        case 'pending': 
        case 'waiting':
            return 'blue';
        case 'running': 
        case 'in-progress':
        case 'inprogress':
            return 'blue';
        case 'deleted': 
        case 'cancelled':
        case 'canceled':
            return 'red';
        default:
            // Fallback to user-based hash for unknown statuses
            const hash = userId ? userId.split('').reduce((a, b) => { a = ((a << 5) - a) + b.charCodeAt(0); return a & a; }, 0) : 0;
            return ['blue', 'green', 'red'][Math.abs(hash) % 3];
    }
}

function getStatusClass(status) {
    switch (status.toLowerCase()) {
        case 'successful': return 'successful';
        case 'pass': return 'successful';
        case 'failed': return 'failed';
        case 'fail': return 'failed';
        case 'deleted': return 'deleted';
        case 'pending': return 'pending';
        case 'running': return 'running';
        default: return '';
    }
}

function getStatusDisplayText(status) {
    switch (status.toLowerCase()) {
        case 'successful': return 'Successful';
        case 'pass': return 'Pass';
        case 'failed': return 'Failed';
        case 'fail': return 'Fail';
        case 'deleted': return 'Deleted';
        case 'pending': return 'Pending';
        case 'running': return 'Running';
        default: return status;
    }
}

function formatTimestamp(timestamp) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return 'Invalid Date';

    // Format as DD.MM.YYYY • HH:MM to match the expected format
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${day}.${month}.${year} • ${hours}:${minutes}`;
}

function formatDescription(description, userlaneName) {
    if (!description) return '';

    // Replace various placeholder patterns that might be in the description
    let formatted = description
        .replace('@Model.Userlane?.Name', `"${userlaneName || 'Unknown Userlane'}"`)
        .replace('{{userlaneName}}', `"${userlaneName || 'Unknown Userlane'}"`)
        .replace('{userlaneName}', `"${userlaneName || 'Unknown Userlane'}"`);

    return formatted;
}

function formatMetadata(metadata) {
    if (!metadata) return '';

    try {
        // Try to parse as JSON and format it nicely
        const parsed = JSON.parse(metadata);
        return JSON.stringify(parsed, null, 2);
    } catch (e) {
        // If it's not valid JSON, return as-is
        return metadata;
    }
}

function formatDuration(runtime) {
    console.log('🕒 formatDuration input:', runtime, typeof runtime);
    
    if (!runtime && runtime !== 0) return '0ms';
    
    let milliseconds = runtime;
    
    // Handle different input formats
    if (typeof runtime === 'string') {
        milliseconds = parseInt(runtime);
    }
    
    console.log('🕒 formatDuration parsed ms:', milliseconds);
    
    if (milliseconds === 0) return '0ms';

    // If less than 1 second, show raw milliseconds
    if (milliseconds < 1000) {
        return `${milliseconds}ms`;
    }

    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    console.log('🕒 Calculated - hours:', hours, 'minutes:', minutes, 'seconds:', seconds);

    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    } else {
        return `${seconds}s`;
    }
}

function showFeedError(message) {
    const container = document.getElementById('feedContainer');
    container.innerHTML = `
        <div class="feed-empty">
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle fa-3x"></i>
                <h4>Error</h4>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="loadFeedData()">Retry</button>
            </div>
        </div>
    `;
}

// Show batch details in sidebar
function showBatchDetails(batchId) {
    console.log('=== showBatchDetails called ===');
    console.log('🆔 Batch ID:', batchId);

    const apiUrl = `/Api/UserlaneResultBatch/${batchId}`;
    console.log('🌐 Batch details API URL:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('📡 Batch details response status:', response.status);
            console.log('📡 Batch details response ok:', response.ok);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response.json();
        })
        .then(result => {
            console.log('📦 Batch details response:', result);
            console.log('📦 Batch details error:', result.error);

            if (!result.error && result.data) {
                const batch = result.data;
                console.log('📋 Batch data:', batch);
                console.log('📋 Batch properties:', Object.keys(batch));

                const sidebar = document.querySelector('.sidebar-content');
                if (!sidebar) {
                    console.error('❌ Sidebar content not found');
                    return;
                }

                console.log('✅ Updating sidebar with batch details');
                sidebar.innerHTML = `
                    <div class="feed-details">
                        <h4>Test Run Details</h4>
                        <div class="detail-item">
                            <strong>User:</strong> ${batch.userName || 'Unknown User'}
                        </div>
                        <div class="detail-item">
                            <strong>Status:</strong> <span class="feed-status ${getStatusClass(batch.status)}">${getStatusDisplayText(batch.status)}</span>
                        </div>
                        <div class="detail-item">
                            <strong>Start Time:</strong> ${formatTimestamp(batch.startTime)}
                        </div>
                        <div class="detail-item">
                            <strong>End Time:</strong> ${formatTimestamp(batch.endTime)}
                        </div>
                        <div class="detail-item">
                            <strong>Runtime:</strong> ${formatDuration(batch.runtime)}
                        </div>
                        <div class="detail-item">
                            <strong>Created:</strong> ${formatTimestamp(batch.createdDateTime)}
                        </div>
                        <div class="detail-item">
                            <strong>Userlane:</strong> "@Model.Userlane?.Name"
                        </div>
                        <div class="detail-item">
                            <strong>Batch ID:</strong> <code>${batch.id}</code>
                        </div>
                        <div class="detail-item">
                            <button class="btn btn-primary btn-sm" onclick="openReportModal('${batch.id}')">
                                <i class="fas fa-chart-bar"></i> View Full Report
                            </button>
                        </div>
                    </div>
                `;
            } else {
                console.error('❌ Batch details API returned error or no data:', result);
                showSidebarError('Could not load batch details');
            }
        })
        .catch(error => {
            console.error('💥 Error loading batch details:', error);
            console.error('💥 Error stack:', error.stack);
            showSidebarError(`Error loading batch details: ${error.message}`);
        });
}

function showSidebarError(message) {
    const sidebar = document.querySelector('.sidebar-content');
    if (!sidebar) {
        console.error('Sidebar content not found');
        return;
    }
    sidebar.innerHTML = `
        <div class="sidebar-icon">
            <i class="fas fa-exclamation-triangle fa-3x"></i>
        </div>
        <h4>Error</h4>
        <p>${message}</p>
    `;
}

// Open report modal with specific batch data
function openReportModal(batchId) {
    console.log('=== openReportModal called ===');
    console.log('🆔 Batch ID for report:', batchId);

    if (!batchId) {
        console.error('❌ No batch ID provided for report modal');
        return;
    }

    const userlaneId = '@Model.Userlane?.Id';
    
    // Load both action results and test condition results
    const actionResultsUrl = `/Api/UserlaneResult?filters[0].FilterColumn=BatchId&filters[0].CompareValue=${batchId}&filters[1].FilterColumn=UserlaneId&filters[1].CompareValue=${userlaneId}&pageSize=1000&sortColumn=StartTime&sortDirection=asc`;
    const testConditionResultsUrl = `/Api/UserlaneStepTestConditionResult?filters[0].FilterColumn=BatchId&filters[0].CompareValue=${batchId}&pageSize=1000`;
    
    console.log('🌐 Action Results URL:', actionResultsUrl);
    console.log('🌐 Test Condition Results URL:', testConditionResultsUrl);

    // Fetch both action results and test condition results
    Promise.all([
        fetch(actionResultsUrl).then(response => {
            if (!response.ok) throw new Error(`Action results: HTTP ${response.status}`);
            return response.json();
        }),
        fetch(testConditionResultsUrl).then(response => {
            if (!response.ok) throw new Error(`Test condition results: HTTP ${response.status}`);
            return response.json();
        })
    ])
    .then(([actionResult, testConditionResult]) => {
        console.log('📦 Action Result:', actionResult);
        console.log('📦 Test Condition Result:', testConditionResult);

        // Extract action data
        let actionData = [];
        if (actionResult.data && actionResult.data.rows && Array.isArray(actionResult.data.rows)) {
            actionData = actionResult.data.rows;
        } else if (actionResult.data && Array.isArray(actionResult.data)) {
            actionData = actionResult.data;
        }

        // Extract test condition data
        let testConditionData = [];
        if (testConditionResult.data && testConditionResult.data.rows && Array.isArray(testConditionResult.data.rows)) {
            testConditionData = testConditionResult.data.rows;
        } else if (testConditionResult.data && Array.isArray(testConditionResult.data)) {
            testConditionData = testConditionResult.data;
        }

        console.log('📦 Action data length:', actionData.length);
        console.log('📦 Test condition data length:', testConditionData.length);

        if (actionData.length > 0 || testConditionData.length > 0) {
            console.log('✅ Populating report modal with action and test condition results');
            populateReportModalWithTestConditions(batchId, actionData, testConditionData);

            const modal = document.getElementById('reportModal');
            if (modal) {
                console.log('✅ Showing report modal');
                modal.style.display = 'flex';
            } else {
                console.error('❌ Report modal element not found');
            }
        } else {
            console.error('❌ No results found');
            alert('No test results found for this batch');
        }
    })
    .catch(error => {
        console.error('💥 Error loading test results:', error);
        console.error('💥 Error stack:', error.stack);
        alert(`Error loading test results: ${error.message}`);
    });
}

// Populate the report modal with test results
function populateReportModal(batchId, results) {
    const modal = document.getElementById('reportModal');
    if (!modal) {
        console.error('Report modal not found');
        return;
    }

    const resultsContainer = modal.querySelector('.results-table');
    if (!resultsContainer) {
        console.error('Results container not found in modal');
        return;
    }

    // Clear existing content except header
    const header = resultsContainer.querySelector('.table-header');
    resultsContainer.innerHTML = '';
    if (header) {
        resultsContainer.appendChild(header);
    }

    // Add results
    results.forEach((result, index) => {
        console.log(`📋 Processing result ${index + 1}:`, result);
        console.log(`📋 Duration value:`, result.duration, typeof result.duration);
        console.log(`📋 Formatted duration:`, formatDuration(result.duration));
        
        const row = document.createElement('div');
        row.className = 'table-row';

        const statusIcon = result.result === 'Pass' ?
            '<i class="fas fa-check-circle text-success"></i>' :
            '<i class="fas fa-times-circle text-danger"></i>';

        const foundIcon = result.found ?
            '<i class="fas fa-eye text-success"></i>' :
            '<i class="fas fa-eye-slash text-warning"></i>';

        const completeStatus = result.result === 'Pass' ? 
            '<span class="status-badge success">Pass</span>' : 
            '<span class="status-badge failed">Fail</span>';

        const durationFormatted = formatDuration(result.duration);
        console.log(`📋 Final duration for display:`, durationFormatted);

        row.innerHTML = `
            <div class="col-actions">
                ${statusIcon} ${foundIcon}
            </div>
            <div class="col-test">${result.title || 'Unknown Step'}</div>
            <div class="col-duration">${durationFormatted}</div>
            <div class="col-status">${completeStatus}</div>
        `;

        resultsContainer.appendChild(row);
    });

    // Update modal title with batch info
    const modalTitle = modal.querySelector('.modal-title span');
    if (modalTitle) {
        modalTitle.textContent = `Test Results - Batch ${batchId.substring(0, 8)}...`;
    }
}

// Populate the report modal with both action results and test condition results
function populateReportModalWithTestConditions(batchId, actionResults, testConditionResults) {
    console.log('=== populateReportModalWithTestConditions called ===');
    console.log('📦 Action results:', actionResults.length);
    console.log('📦 Test condition results:', testConditionResults.length);

    const modal = document.getElementById('reportModal');
    if (!modal) {
        console.error('Report modal not found');
        return;
    }

    const resultsContainer = modal.querySelector('.results-table');
    if (!resultsContainer) {
        console.error('Results container not found in modal');
        return;
    }

    // Clear existing content except header
    const header = resultsContainer.querySelector('.table-header');
    resultsContainer.innerHTML = '';
    if (header) {
        resultsContainer.appendChild(header);
    }

    const tableBody = document.createElement('div');
    tableBody.className = 'table-body';

    // Group test conditions by UserlaneStepTestConditionId to match with steps
    const testConditionsByStep = {};
    testConditionResults.forEach(tc => {
        if (!testConditionsByStep[tc.userlaneStepTestConditionId]) {
            testConditionsByStep[tc.userlaneStepTestConditionId] = [];
        }
        testConditionsByStep[tc.userlaneStepTestConditionId].push(tc);
    });

    console.log('📦 Test conditions grouped by step:', testConditionsByStep);

    // Process action results and their associated test conditions
    actionResults.forEach((action, index) => {
        console.log(`📋 Processing action ${index + 1}:`, action);

        // Add action row
        const actionRow = document.createElement('div');
        actionRow.className = 'table-row';

        const actionStatusIcon = action.result === 'Pass' ?
            '<i class="fas fa-check-circle" style="color: #28a745;"></i>' :
            '<i class="fas fa-times-circle" style="color: #dc3545;"></i>';

        const actionStatus = action.result === 'Pass' ? 
            '<span class="status-badge success">Pass</span>' : 
            '<span class="status-badge failed">Fail</span>';

        actionRow.innerHTML = `
            <div class="col-step">
                <div class="action-indicator ${action.result === 'Pass' ? 'success' : 'failed'}"></div>
                <span>${action.title || `Action ${index + 1}`}</span>
            </div>
            <div class="col-test">-</div>
            <div class="col-expected">-</div>
            <div class="col-actual">-</div>
            <div class="col-duration">${formatDuration(action.duration)}</div>
            <div class="col-status">${actionStatus}</div>
        `;

        tableBody.appendChild(actionRow);

        // Check if this action has associated test conditions
        // For now, we'll display all test conditions after actions
        // In a real implementation, you'd need to link test conditions to specific actions
    });

    // Add test condition results
    testConditionResults.forEach((testCondition, index) => {
        console.log(`🧪 Processing test condition ${index + 1}:`, testCondition);

        const tcRow = document.createElement('div');
        tcRow.className = 'table-row test-condition';

        const tcStatusIcon = testCondition.passed ?
            '<i class="fas fa-check-circle" style="color: #28a745;"></i>' :
            '<i class="fas fa-times-circle" style="color: #dc3545;"></i>';

        const tcStatus = testCondition.passed ? 
            '<span class="status-badge success">Pass</span>' : 
            '<span class="status-badge failed">Fail</span>';

        // Format test condition description
        let conditionDescription = testCondition.conditionType;
        if (testCondition.field) {
            conditionDescription += ` (${testCondition.field})`;
        }
        if (testCondition.operator) {
            conditionDescription += ` ${testCondition.operator}`;
        }

        tcRow.innerHTML = `
            <div class="col-step">
                <div class="action-indicator ${testCondition.passed ? 'success' : 'failed'}"></div>
                <span style="font-style: italic;">Test Condition</span>
            </div>
            <div class="col-test">
                ${tcStatusIcon}
                <span>${conditionDescription}</span>
            </div>
            <div class="col-expected">${testCondition.expectedValue || '-'}</div>
            <div class="col-actual">${testCondition.actualValue || '-'}</div>
            <div class="col-duration">-</div>
            <div class="col-status">${tcStatus}</div>
        `;

        // Add error information if available
        if (testCondition.error) {
            tcRow.title = `Error: ${testCondition.error}`;
            tcRow.style.backgroundColor = '#fff5f5';
        }

        tableBody.appendChild(tcRow);
    });

    resultsContainer.appendChild(tableBody);

    // Update modal title with batch info
    const modalTitle = modal.querySelector('.modal-title span');
    if (modalTitle) {
        modalTitle.textContent = `Test Results - Batch ${batchId.substring(0, 8)}...`;
    }

    console.log('✅ Modal populated with actions and test conditions');
}

// Always try to load feed data when script executes
console.log('🚀 Feed script executing, loading data immediately...');
setTimeout(function() {
    console.log('🚀 Attempting to load feed data...');
    const feedContainer = document.getElementById('feedContainer');
    if (feedContainer) {
        console.log('🚀 Feed container found, calling loadFeedData()');
        loadFeedData();
    } else {
        console.log('🚀 Feed container not found');
    }
}, 100);

// Also try after a longer delay as fallback
setTimeout(function() {
    console.log('🚀 Fallback feed data load attempt...');
    const feedContainer = document.getElementById('feedContainer');
    if (feedContainer && feedContainer.innerHTML.includes('Loading activity feed')) {
        console.log('🚀 Still showing loading, attempting to load data again');
        loadFeedData();
    }
}, 1000);

// Load when the feed menu item is clicked
document.addEventListener('click', function(e) {
    try {
        const feedMenuItem = e.target.closest('[data-menu="feed"]') ||
                            e.target.closest('a[href*="feed"]') ||
                            e.target.closest('.menu-item[data-target*="feed"]');

        if (feedMenuItem) {
            setTimeout(() => {
                try {
                    const feedContainer = document.getElementById('feedContainer');
                    if (feedContainer && feedContainer.offsetParent !== null) {
                        loadFeedData();
                    }
                } catch (error) {
                    console.error('Error in delayed feed load:', error);
                }
            }, 200); // Increased delay to ensure tab content is visible
        }
    } catch (error) {
        console.error('Error in click handler:', error);
    }
});

// Also try to load when the feed container becomes visible
if (typeof window.feedObserver === 'undefined') {
    window.feedObserver = null;
    if (typeof MutationObserver !== 'undefined') {
        window.feedObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const feedContainer = document.getElementById('feedContainer');
                    if (feedContainer && feedContainer.offsetParent !== null) {
                        // Container became visible, load data if it's still showing the loader
                        if (feedContainer.innerHTML.includes('Loading activity feed')) {
                            loadFeedData();
                        }
                    }
                }
            });
        });
    }

    // Start observing
    const feedContainer = document.getElementById('feedContainer');
    if (feedContainer && window.feedObserver) {
        const targetElement = feedContainer.parentElement || feedContainer;
        if (targetElement) {
            window.feedObserver.observe(targetElement, {
                attributes: true,
                subtree: true,
                attributeFilter: ['style', 'class']
            });
        }
    }
}

// Immediate attempt to load feed data
(function() {
    console.log('🚀 Immediate feed load attempt...');
    try {
        const feedContainer = document.getElementById('feedContainer');
        if (feedContainer) {
            console.log('🚀 Feed container found, attempting immediate load');
            loadFeedData();
        } else {
            console.log('🚀 Feed container not found, will try later');
        }
    } catch (error) {
        console.error('💥 Error in immediate feed load:', error);
    }
})();

// Additional immediate attempts with short delays
setTimeout(function() {
    console.log('🚀 Quick retry attempt...');
    try {
        const feedContainer = document.getElementById('feedContainer');
        if (feedContainer) {
            console.log('🚀 Quick retry: Loading feed data');
            loadFeedData();
        }
    } catch (error) {
        console.error('💥 Error in quick retry:', error);
    }
}, 50);

setTimeout(function() {
    console.log('🚀 Second retry attempt...');
    try {
        const feedContainer = document.getElementById('feedContainer');
        if (feedContainer) {
            console.log('🚀 Second retry: Loading feed data');
            loadFeedData();
        }
    } catch (error) {
        console.error('💥 Error in second retry:', error);
    }
}, 200);

// Fallback: Try to load feed data after a short delay regardless of other conditions
setTimeout(function() {
    try {
        const feedContainer = document.getElementById('feedContainer');
        if (feedContainer && feedContainer.innerHTML.includes('Loading activity feed')) {
            console.log('⏰ Fallback: Attempting to load feed data');
            loadFeedData();
        }
    } catch (error) {
        console.error('💥 Error in fallback feed load:', error);
    }
}, 1000);

// Additional fallback with longer delay
setTimeout(function() {
    try {
        const feedContainer = document.getElementById('feedContainer');
        if (feedContainer && feedContainer.innerHTML.includes('Loading activity feed')) {
            console.log('⏰ Extended fallback: Attempting to load feed data');
            loadFeedData();
        }
    } catch (error) {
        console.error('💥 Error in extended fallback feed load:', error);
    }
}, 3000);

// Window onload fallback
window.addEventListener('load', function() {
    console.log('🚀 Window load event fired');
    try {
        const feedContainer = document.getElementById('feedContainer');
        if (feedContainer) {
            console.log('🚀 Window load: Loading feed data');
            loadFeedData();
        }
    } catch (error) {
        console.error('💥 Error in window load feed attempt:', error);
    }
});

// Page visibility change fallback
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        console.log('🚀 Page became visible');
        try {
            const feedContainer = document.getElementById('feedContainer');
            if (feedContainer && feedContainer.innerHTML.includes('Loading activity feed')) {
                console.log('🚀 Visibility change: Attempting to load feed data');
                setTimeout(loadFeedData, 500);
            }
        } catch (error) {
            console.error('💥 Error in visibility change feed attempt:', error);
        }
    }
});

// Make functions globally accessible
window.openReportModal = openReportModal;
window.closeReportModal = closeReportModal;
window.debugFeedStatus = debugFeedStatus;
window.loadFeedData = loadFeedData;
window.showBatchDetails = showBatchDetails;

})(); // Close namespace wrapper
</script>