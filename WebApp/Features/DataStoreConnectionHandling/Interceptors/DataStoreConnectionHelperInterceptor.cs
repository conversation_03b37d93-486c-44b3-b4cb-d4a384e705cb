using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Domain.Storage;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.DataStoreConfig;
using Levelbuild.Entities.Features.DataStoreContext;
using Levelbuild.Entities.Helpers.DataSource;
using Levelbuild.Entities.Helpers.DataStoreConfig;
using Levelbuild.Entities.Providers;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.EntityFrameworkCore.Infrastructure;

namespace Levelbuild.Frontend.WebApp.Features.DataStoreConnectionHandling.Interceptors;

/// <summary>
/// A <see cref="IMaterializationInterceptor"/> that injects the various ConnectionHelper objects into <see cref="PersistentEntity{TEntity}"/>s that need to be able to connect to a DataStore.
/// </summary>
public class DataStoreConnectionHelperInterceptor : IMaterializationInterceptor
{
	#region Singleton

	private static DataStoreConnectionHelperInterceptor? _instance;

	/// <summary>
	/// The Singleton's instance.
	/// </summary>
	public static DataStoreConnectionHelperInterceptor Instance
	{
		get { return _instance ??= new DataStoreConnectionHelperInterceptor(); }
	}

	private DataStoreConnectionHelperInterceptor()
	{
		/* nothing... */
	}

	#endregion

	/// <inheritdoc />
	public object InitializedInstance(MaterializationInterceptionData materializationData, object instance)
	{
		switch (instance)
		{
			case DataStoreConfigEntity dataStoreConfig:
			{
				var logManager = materializationData.Context.GetService<ILogManager>();
				var userManager = materializationData.Context.GetService<UserManager>();
				dataStoreConfig.SetConnectionProvider(new DataStoreConnectionProvider<DataStoreConnectionHelper>(() =>
				{
					switch (dataStoreConfig.Type)
					{
						case DataStoreType.Storage:
							var logger = logManager.GetLoggerForDataStore<Storage>();

							return new DataStoreConnectionHelper(dataStoreConfig.Type, dataStoreConfig.Options!, logger,
																 userManager.GetCurrentUserDisplayNameAsync().ResultWithUnwrappedExceptions(),
																 userManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions().Id);
						case DataStoreType.Weather:
							// TODO: Nothing yet...
							break;
						default:
							throw new Exception("Unsupported DataStore type!");
					}

					throw new Exception("Unsupported DataStore type!");
				}));
				break;
			}
			case DataSourceEntity dataSource:
			{
				var userManager = materializationData.Context.GetService<UserManager>();
				var databaseContextFactory = materializationData.Context.GetService<IDbContextFactory<CoreDatabaseContext>>();
				dataSource.SetConnectionProvider(new DataStoreConnectionProvider<DataSourceConnectionHelper>(() =>
				{
					var currentCustomer = userManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
					var databaseContext = databaseContextFactory.CreateDbContext();

					// ReSharper disable once NullCoalescingConditionIsAlwaysNotNullAccordingToAPIContract
					dataSource.DataStore ??= databaseContext.DataStoreConfigs.Find(dataSource.DataStoreId)!;

					switch (dataSource.DataStore.Type)
					{
						case DataStoreType.Storage:
							DataStoreContextEntity? dataStoreContext = null;
							if (currentCustomer != null)
								dataStoreContext =
									databaseContext.DataStoreContexts.FirstOrDefault(
										context => context.DataStoreId == dataSource.DataStoreId && context.CustomerId == currentCustomer.Id);

							return new StorageDataSourceConnectionHelper(dataSource, dataStoreContext);
						case DataStoreType.Weather:
							// TODO: Nothing yet...
							break;
						default:
							throw new Exception("Unsupported DataStore type!");
					}

					throw new Exception("Unsupported DataStore type!");
				}));
				break;
			}
		}

		return instance;
	}
}