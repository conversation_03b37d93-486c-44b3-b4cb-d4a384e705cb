@using System.Text.Json
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.PageView
@using Levelbuild.Core.FrontendDtos.Shared
@using Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels
@using Levelbuild.Frontend.WebApp.Features.User.Services
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Microsoft.AspNetCore.Mvc.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels.GridViewModel
@inject UserManager UserManager
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("SinglePage", "");
	var showViewer = Model.View.ShowViewer != null && Model.View.ShowViewer.Value;
	var showHeader = Model.HeaderElements.Count > 0 || Model.Workflows.Count > 0;
	var user = await UserManager.GetCurrentUserAsync();
}

@functions{
	private string GetColFormat(int column, int? ratio = 1, int? minWidth = 0)
	{
		if (column > Model.View.ColumnCount)
			return "";

		if (minWidth > 0)
			return $"minmax({minWidth}px, {ratio}fr)";
		return $"{ratio}fr";
	}
}

@{
	var isEdit = Model.ViewType == ViewType.Edit;
	var formId = isEdit ? $"edit-form-{Model.View.Id}" : $"create-form-{Model.View.PageId}";
}
	<!--suppress CssUnusedSymbol -->
<style>
	.page-column-wrapper {
		display: grid;
		width: 100%;
		flex-grow: 1;
		overflow: hidden;
	}

	@('#'+formId) .page-column-wrapper {
		grid-template-columns: @GetColFormat(1, Model.View.ColumnOneRatio, Model.View.ColumnOneMinWidth) @GetColFormat(2, Model.View.ColumnTwoRatio, Model.View.ColumnTwoMinWidth) @GetColFormat(3, Model.View.ColumnThreeRatio, Model.View.ColumnThreeMinWidth) @(showViewer ? @GetColFormat(0, Model.View.ViewerRatio) : string.Empty);
		@if (isEdit)
		{
		@:padding-left: var(--size-spacing-s);
		}
	}

	.page-column {
		overflow-x: hidden;
		overflow-y: hidden;
		display: flex;
		flex-direction: column;
		row-gap: var(--size-spacing-l);
	}

	.page-column.vanishing-scrollbar {
		overflow-y: scroll;
		padding: var(--size-spacing-xs) var(--size-spacing-s) var(--size-spacing-l) var(--size-spacing-s);
	}

	lvl-section > .section-grid {
		display: grid;
		grid-template-columns: repeat(24, 1fr);
		grid-auto-rows: auto;
		grid-column-gap: var(--size-spacing-l);
		padding: 0 var(--size-spacing-m);
		margin-bottom: calc(-1 * var(--size-spacing-m)); /* correct for margin-bottom of grid-element-wrappers (we can't disable it for last-child because there can be multiple childs in the last row) */
		height: auto !important;
		overflow: hidden; /* otherwise it jumps while opening/closing a section */
	}

	.grid-element-wrapper {
		/* using grid-row-gap on the section-grid would lead to uneven spacing as soon as some rows elements get hidden (because the gap won't hide) */
		margin-bottom: var(--size-spacing-l);
	}

	.grid-element-wrapper.overview {
		display: flex;
		flex-direction: column;
	}

	.grid-element-wrapper.overview > label {
		display: block;
		font-size: var(--size-text-s);
		height: 1.2rem;
		color: var(--clr-text-secondary);
		margin-bottom: var(--size-spacing-s);
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.grid-element-wrapper.overview > lvl-value-formatter {
		line-height: 1.2;
	}

	.grid-element-wrapper > hr {
		margin: 7px 0 8px;
		border: none;
		border-top: 1px solid var(--clr-border-medium);
	}

	.grid-element-text {
		display: block;
		padding: 3px 7px;
		border-radius: var(--size-radius-m);
		cursor: pointer;
		position: relative;
		height: 100%;
		overflow: hidden;
		user-select: none;
	}

	.grid-element-text > label {
		white-space: nowrap;
		overflow: hidden;
		width: 100%;
	}

	.grid-element-text.Headline1 > label {
		font-size: var(--size-text-l);
		line-height: 4.0rem;
		border-bottom: 1px solid var(--clr-border-medium);
	}

	.grid-element-text.Headline2 > label {
		font-size: 1.4rem;
		font-weight: 700;
		line-height: 3.5rem;
	}

	.grid-element-text.Headline3 > label {
		font-size: var(--size-text-m);
		font-weight: 700;
		line-height: 3.0rem;
	}

	.page-view__header {
		height: 5.2rem;
		background-color: var(--clr-background-lvl-0);
		border-radius: var(--size-radius-m);
		margin: 0.2rem 1.9rem 1.4rem var(--size-spacing-m);
		flex-shrink: 0;
		display: flex;
		flex-direction: row;
		box-shadow: 0 0 0.2rem 0 var(--clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--clr-shadow-weak);
		align-items: center;
	}

	.page-view__header .header-favorites {
		width:  1.6rem;
		margin-left: 1.6rem;
		text-align: center;
		line-height: 5.2rem;
	}
	
	.page-view__header .header-values {
		margin: 1.0rem 1.6rem;
		display: grid;
		grid-auto-columns: minmax(0, auto);
		grid-auto-flow: column;
		column-gap: var(--size-spacing-l);
	}

	.page-view__header .header-value {
		height: 3rem;
		color: var(--clr-text-primary-positiv);
		display: flex;
		flex-direction: row;
		align-items: center;
		max-width: 45rem;

		& label {
			color: var(--clr-text-secondary);
			height: 1.4rem;
			line-height: 1.2rem;
			font-size: var(--size-text-s);
		}

		& span {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		& > div {
			display: flex;
			flex-direction: column;
			gap: var(--size-spacing-xs);
			height: 100%;
			overflow: hidden;
		}
	}

	.page-view__header .header-value > i {
		width: 2.0rem;
		margin-right: var(--size-spacing-m);
		font-size: var(--size-text-l);
		text-align: center;
		color: var(--clr-text-secondary);
	}

	.page-view__header .header-spacing {
		height: 100%;
		flex-grow: 1;
	}

	.page-view__header .header-status {
		margin:  0 1.6rem;
	}

	.notification-box {
		margin: 0 1.9rem 1.4rem var(--size-spacing-m);
	}

	.filePreview.detail-view {
		position: relative;
		display: block;
		overflow: hidden;
		background-color: var(--clr-background-lvl-0);
		margin: 2px 20px var(--size-spacing-l) var(--size-spacing-s);
		border-radius: var(--size-radius-m);
		box-shadow: 0 0 0.2rem 0 var(--clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--clr-shadow-weak);
		height: auto;
		width: auto;
		top: auto;
		left: auto;
		grid-template-rows: auto 1fr auto;
		z-index: auto;
	}

	.file-dropzone.hidden {
		display: none;
	}

	.dropzone-preview {
		position: absolute;
		display: block;
	}

	.navigation-button {
		width: 2.4rem;
		--height: 4.3rem;
		transition: border-color var(--animation-time-medium) ease;
	}

	.navigation-button[disabled] {
		cursor: help;
	}

	.navigation-button:not([disabled]):hover {
		--height: 5.2rem;
		--background-color: var(--clr-background-lvl-2);
		border-right-color: var(--clr-border-medium) !important;
	}

	#goto-previous {
		--border-radius-right: 0;
		border-right: 1px solid var(--clr-border-weak);
		margin-right: -0.4rem;
	}

	#goto-next {
		--border-radius-left: 0;
		border-left: 1px solid var(--clr-border-weak);
		margin-left: -0.4rem;
	}
</style>

<script type="module" defer>
	@if (showViewer)
	{
		<text>
		const dataSourceId = '@(Model.Page?.DataSourceId)'
		const elementId = Page.getFormData()["Id"] ?? '@(Model.Element?.Id)'
		
		const form = document.getElementById('page-detail').querySelector('lvl-form')
		const preview = document.querySelector("#detail-view-file-dropzone")
		const target = document.querySelector("#detail-view-file-preview")
		
		// load viewer incl. document
		const viewer = document.querySelector(".file-viewer")
		viewer.showPlaceholder = !Page.hasFile()
		viewer.allowFileOptions = true
		viewer.elementId = elementId
	
		if (Page.hasFile() && !viewer.fileId) {
			const fileInfo = Page.getFileInfo()
			const blueprintConfig = Page.getBlueprintConfig()
			if (blueprintConfig) {
				viewer.deepzoomImageInfo = blueprintConfig.imageInfo
				viewer.pinSource = blueprintConfig.pinSource
				if (blueprintConfig.pinSource?.createPageId) {
					viewer.annotationCreate = async (defaultValues) => {
						return await FileManager.showCreatePage({
							target: document.querySelector('body'),
							dataSourceId: blueprintConfig.pinSource.id,
							createPageId: blueprintConfig.pinSource.createPageId,
							defaultValues: defaultValues,
							embedded: true,
							allowFile: blueprintConfig.pinSource.allowFile === true,
							allowMultiFile: false
						})
					}
				}
			}
			viewer.classList.add('enabled')
			viewer.filename = fileInfo.fileName
			viewer.loadNewDocument('@(Model.Page?.DataSourceId)', fileInfo.fileId, fileInfo.fileName.toString().substring(fileInfo.fileName.toString().lastIndexOf('.') + 1))
			FileManager.showFileOptionsButton()
		
		} else if (Page.hasFormData())
			preview.classList.remove('hidden')

		FileManager.initDropzoneDetail(target, form, dataSourceId, elementId, [])
		FileManager.initFileOptionsButton(target, form, dataSourceId, elementId)
		
		</text>
	}

	@if (showHeader)
	{
		<text>
			async function handlePreviousButtonClick()
			{
				// define callbacks for resolve (and cancel)
				const callback = () => {
					handlePreviousButtonClick()
				}

				// define event and put callbacks in detail
				const pageLeaveEvent = new CustomEvent('initiate-page-leave', {
					cancelable: true,
					detail: {
						resolveCallback: callback
					}
				})

				// dispatch the page-leave-event and stop if it gets canceled
				if(!window.dispatchEvent(pageLeaveEvent))
					return
			
				const form  = document.getElementById('page-detail').querySelector('lvl-form')
				Page.goToPreviousElement(form, `/Api/DataSources/@(Model.Page?.DataSourceId)`)
			}

			async function handleNextButtonClick()
			{
				// define callbacks for resolve (and cancel)
				const callback = () => {
					handleNextButtonClick()
				}

				// define event and put callbacks in detail
				const pageLeaveEvent = new CustomEvent('initiate-page-leave', {
					cancelable: true,
					detail: {
						resolveCallback: callback
					}
				})

				// dispatch the page-leave-event and stop if it gets canceled
				if(!window.dispatchEvent(pageLeaveEvent))
					return
			
				const form  = document.getElementById('page-detail').querySelector('lvl-form')
				Page.goToNextElement(form, `/Api/DataSources/@(Model.Page?.DataSourceId)`)
			}

		document.querySelector('#goto-previous.navigation-button').onClick = handlePreviousButtonClick
		document.querySelector('#goto-next.navigation-button').onClick = handleNextButtonClick
		</text>
	}

	@if (Model.Page?.DataSource?.Inactive ?? false)
	{ 
		<text>
			if (Page.getFormDataOptions().inactive)
				document.querySelector('.notification-box[data-name="discarded-notification"]')?.classList.remove('hidden')
			else
				document.querySelector('.notification-box[data-name="discarded-notification"]')?.classList.add('hidden')
		</text>
	}

	// Set workflow items to their initial state
	@if (Model.Workflows.Count > 0)
	{
		@foreach (var workflow in Model.Workflows)
		{
			<text>
				setActiveWorkflowNodeInfo('@workflow.Id', '@workflow.StatusFieldName', JSON.parse('@JsonSerializer.Serialize(workflow.Nodes!)'.replace(/&quot;/ig, '"')))
			</text>
		}
		
		<text>
			function setActiveWorkflowNodeInfo(workflowId, statusFieldName, nodes) {
				const workflowItem = document.querySelector(`.header-status lvl-workflow-item[workflow-id="${workflowId}"]`)
				const statusValue = Page.formData[statusFieldName]
				
				const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$/i
				for (let node of nodes) {
					// No uuid? -> Set to start node!
					if (!uuidRegex.test(statusValue)) {
						if (node.State === "Start") {
							workflowItem.setAttribute('label', node.NameTranslated ?? node.Name)
							workflowItem.setAttribute('state-type', node.State.toLowerCase())
							workflowItem.setAttribute('icon', node.Icon)
							break
						}
					} else if (node.Id === statusValue) {
							workflowItem.setAttribute('label', node.NameTranslated ?? node.Name)
							workflowItem.setAttribute('state-type', node.State.toLowerCase())
							workflowItem.setAttribute('icon', node.Icon)
							break
					}
				}
			}
		</text>
	}
</script>

@if (showHeader)
{
	<div class="page-view__header">
		<button-component id="goto-previous" class="navigation-button" icon="chevron-left" data-tooltip="tooltip-previous" disabled="true" hidden="true"></button-component>
		<tooltip-component name="tooltip-previous" placement="PopupPlacement.TopStart" block-offset-y="4">@localizer["goToPreviousRecord"]</tooltip-component>
		@if (Model.Page?.DataSource?.Favor ?? false)
		{
			<lvl-fav-button class="header-favorites" data-source-id="@(Model.Page.DataSourceId)" enabled="@(Model.Element?.IsFavourite ?? false)"></lvl-fav-button>
		}
		<div class="header-values">
			@{
				var headerLocalizer = LocalizerFactory.Create("PageHeaderElement", Model.Page?.Id.ToString() ?? "", true);
			}
			@foreach (var element in Model.HeaderElements.OrderBy(element => element.Position))
			{
				<div class="header-value">
					<i class="fa fa-@(element.Icon)" data-tooltip="header-tooltip-@(element.Id)"></i>
					<div>
						<label>@headerLocalizer[element.Label ?? string.Empty]</label>
						<span class="header-value-content" value="@(element.Value)"></span>
					</div>
				</div>
			}
		</div>
		<div class="header-spacing"></div>
		@if (Model.Workflows.Count > 0)
		{
			@foreach (var workflow in Model.Workflows)
			{
				var workflowLocalizer = LocalizerFactory.Create("Workflow", workflow.Id.ToString() ?? string.Empty, true);
				<div class="header-status" data-tooltip="header-status-tooltip">
					<workflow-item
						workflow-id="@workflow.Id?.ToString()"
						workflow-name="@workflowLocalizer[workflow.Name ?? string.Empty]"
						nodes="workflow.Nodes" slot="@workflow.Slot">
					</workflow-item>
				</div>
			}
		}
		<button-component id="goto-next" class="navigation-button" icon="chevron-right" data-tooltip="tooltip-next" disabled="true" hidden="true"></button-component>
		<tooltip-component name="tooltip-next" placement="PopupPlacement.TopEnd" block-offset-y="4">@localizer["goToNextRecord"]</tooltip-component>
	</div>
}
<div class="notification-box hidden" data-signal="error" data-name="discarded-notification">
	<icon name="circle-minus"></icon>
	<span>@localizer["discardedNotification"]</span>
</div>
<form-component id="@formId" style="display: contents;" skeleton="true" handle-save-button="@isEdit"  api-url="/Api/DataSources/@(Model.Page?.DataSourceId)/Elements" data-source="@(Model.Page?.DataSourceId)" >
	<div class="page-column-wrapper" class="@(Model.View.Readonly ? "overview" : "")" data-embedded-count="@Model.View.Pages.Count" data-section-container>
		@for (var i = 0; i < Model.View?.ColumnCount; i++)
		{
			var sectionsInColumn = Model.View.Sections.Where(section => section.GridViewColumn == i).ToList();
			var embeddedPagesInColumn = Model.View.Pages.Where(embeddedPage => embeddedPage.GridViewColumn == i).ToList();

			List<ISortableDto> columnItems = [];
			columnItems.AddRange(sectionsInColumn);
			columnItems.AddRange(embeddedPagesInColumn);

			// bring both list in the right order
			columnItems = columnItems.OrderBy(section => section.Position).ToList();

			<text>
				<div class="page-column @(Model.ViewType == ViewType.Edit ? "vanishing-scrollbar static-scrollbar" : "")">
					@foreach (var item in columnItems)
					{
						switch (item)
						{
							case GridViewSectionDto sectionDto:
								<partial name="GridViewElements/_Section.cshtml"
								         model="@(new SectionModel(Model.ViewType, Model.View?.Readonly ?? false) { Page = Model.Page, Section = sectionDto })"/>
								break;
							case GridViewPageDto pageDto:
								<partial name="GridViewElements/_EmbeddedPage.cshtml" model="@(new EmbeddedPageModel(Model.View?.Readonly ?? false) { Page = pageDto })"/>
								break;
						}
					}
				</div>
			</text>
		}
		@if (showViewer)
		{
		<div id="detail-view-file-preview" class="filePreview detail-view" onmouseup="event.stopPropagation()">
			<main>
				<div id="detail-view-file-dropzone" class="file-dropzone empty hidden">
					<div class="file-dropzone__empty">
						<i class="fal fa-arrow-up-from-bracket"></i>
						<h2>@localizer["noPreviewAvailable"]</h2>
						<legend>@localizer["dragFileToUpload"]</legend>
						<button-component class="file-upload-start" label="@localizer["addEntry"]" icon="plus-circle" type="ButtonType.Secondary" color="ColorState.Info"></button-component>
					</div>
					<div class="file-dropzone__upload">
						<i class="fal fa-arrow-up-from-bracket"></i>
						<span class="file-upload-status">@localizer["uploading"] ...</span>
						<span class="file-upload-filename"></span>
						<span class="file-upload-progress"></span>
						<legend>
							<span class="file-size-uploaded">0</span>&nbsp;/&nbsp;<span class="file-size-total"></span>&nbsp;·&nbsp;<span class="file-type"></span>
							<span class="file-upload-percent">0</span>
						</legend>
						<button-component class="file-upload-abort" label="@localizer["abortButton"]" type="ButtonType.Secondary" color="ColorState.Info"></button-component>
					</div>
				</div>
				<viewer-component class="file-viewer" user="@(user.DisplayName)"></viewer-component>
			</main>
			<lvl-dialog id="file-action-dialog" icon="exclamation-triangle" icon-color="warning" icon-style="solid">
				<lvl-form></lvl-form>
				<button-component slot="button-left" type="@ButtonType.Tertiary" color="@ColorState.Info" data-action="cancel" label="@localizer["fileDialog/abort"]"></button-component>
				<button-component slot="button-right" type="@ButtonType.Primary" color="@ColorState.Error" icon="trash" data-action="start-cut" label="@localizer["fileDialog/startCut"]" hidden></button-component>
				<button-component slot="button-right" type="@ButtonType.Primary" data-action="start-update" label="@localizer["fileDialog/startUpdate"]" hidden></button-component>
			</lvl-dialog>
		</div>
		}
	</div>
</form-component>
@if (isEdit)
{
	<text>
		<script>
			Page.buttonConfig.saveButton.hidden = @(Model.View.Readonly ? "true" : "false")
			Page.buttonConfig.toggleNavigationButtons()

			@if (Model.Workflows.Count > 0)
			{
				<text>
					async function changeWorkflowStatus(event) {
						const workflowItem = this.closest('lvl-workflow-item')
						const workflowId = workflowItem.getAttribute('workflow-id')
						
						const workflowNode = this.closest('lvl-menu-item')
						const nodeId = workflowNode.getAttribute('data-id')
						
						const oldLabel = workflowItem.getAttribute('label')
						const oldState = workflowItem.getAttribute('state-type')
						const oldIcon = workflowItem.getAttribute('icon')

						workflowItem.setAttribute('label', workflowNode.getAttribute('data-name'))
						workflowItem.setAttribute('state-type', workflowNode.getAttribute('data-state').toLowerCase())
						workflowItem.setAttribute('icon', workflowNode.getAttribute('data-icon'))
						fetch(`/Api/Workflows/${workflowId}/Elements/${Page.formData.Id}/ChangeStatus/${nodeId}`, { method: 'POST' }).then(function (response) {
							if (!response.ok) {
								console.error(`HTTP: ${response.status} - failed to change status of workflow: '${workflowId}'`, response.statusText)
		
								workflowItem.setAttribute('label', oldLabel)
								workflowItem.setAttribute('state-type', oldState)
								workflowItem.setAttribute('icon', oldIcon)
								
								return null
							}
							
							return response.json()
						}).then(async function (json) {
							if (json == null)
								return null
							
							const form = document.getElementById(`page-detail`).querySelector('lvl-form')
							form.setAttribute('class', 'section-dialog')
							form.setAttribute('skeleton', '')
							
							// refresh form data
							Page.setFormData(form, json.data.values)
							
							// update blueprint info
							Page.parseBlueprintConfig(json.data)
		
							// disable skeleton
							form.removeAttribute('skeleton')
							overlay.open = false
						})
					}
				</text>
			}
		</script>
	</text>
}
