@using System.Text.Json
@using System.Text.RegularExpressions
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.DataField
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.FrontendDtos.PageView
@using Levelbuild.Frontend.WebApp.Features.User.Services
@using Levelbuild.Frontend.WebApp.Shared
@using Levelbuild.Frontend.WebApp.Shared.Services
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Utils
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Microsoft.IdentityModel.Tokens
@model Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels.SingleDataModel
@inject IExtendedStringLocalizerFactory LocalizerFactory
@inject UserManager UserManager
@inject IAssetService AssetService

@{
	var localizer = LocalizerFactory.Create("SinglePage", "");
	var pageLocalizer = LocalizerFactory.Create("Pages", "", true);
	var viewLocalizer = LocalizerFactory.Create("PageViews", "", true);
	var scriptLocalizer = LocalizerFactory.Create("ScriptMessages", "");
	var pageViews = Model.Page.Views?.OrderBy(view => view.Position) ?? Enumerable.Empty<PageViewDto>();

	// prepare breadcrumb labeling
	string label;
	var breadcrumbLabel = Model.Page.BreadcrumbLabel;
	if (Model.Element != null && !breadcrumbLabel.IsNullOrEmpty())
	{
		// replace placeholders inside breadcrumb label and set as our label immediately
		label = PageUtils.ReplacePlaceholders(breadcrumbLabel!, Model.Element.Values);
	}
	else
	{
		label = string.IsNullOrEmpty(Model.Page.Name) ? "" : pageLocalizer[Model.Page.Name];
	}
}

@if (Model.Element == null)
{
	<text>
		<script type="module" defer> 
			// restore stored breadcrumb
			@if (!string.IsNullOrEmpty(breadcrumbLabel))
			{
				@:Page.loadBreadcrumbs({ labelWithPlaceholders: '@breadcrumbLabel', url: Page.getMainPageUrl() }, true)
				@if (Model.Page.Name != null)
				{
					@:Page.setTitle('@pageLocalizer[Model.Page.Name]')	
				}
			}
			else
			{
				@:Page.loadBreadcrumbs({ label: '@label', url: Page.getMainPageUrl() }, true)
				@:Page.setTitle('@label')	
			}
		</script>
	</text>
}
else
{
	<text>
		<script type="module" defer>
			Page.saveFormData(@(Html.Raw(JsonSerializer.Serialize(Model.Element.Values, ConfigHelper.JsonOptionsCamel))), { 
				favorite: @(Model.Element.IsFavourite.ToString().ToLower()), 
				inactive: @(Model.Element.IsInactive.ToString().ToLower())
			})

			@if (Model.Element.FileInfo != null)
			{
				@: Page.saveFileInfo(@(Html.Raw(JsonSerializer.Serialize(Model.Element.FileInfo.ToDto(Model.Page.DataSourceId, Model.Element.Id), ConfigHelper.JsonOptionsCamel))))
			}

			// remember URL (for partial calls this is done from outside!)
			Page.setMainPage('/Public/Pages/@(Model.Page.Slug)/@(Model.Element.Id)')
			
			@if (Model.DeepZoomInfo != null && Model.DeepZoomInfo.State != CachedDeepZoomState.Failed)
			{
				var pinColor = "";
				if (Model.WorkflowInfos?.Count == 1) {
					var wfInfo = Model.WorkflowInfos[0];
					switch (wfInfo.State) {
						case WorkflowNodeState.Start:
						case WorkflowNodeState.InProgress:
							pinColor = "#FDBA74";
							break;
						case WorkflowNodeState.Positive:
							pinColor = "#22C55E";
							break;
						case WorkflowNodeState.Negative:
							pinColor = "#F87171";
							break;
					}
				}
				<text>
					Page.setBlueprintConfig({
						imageInfo: {
							width: @(Model.DeepZoomInfo.Width ?? 0),
							height: @(Model.DeepZoomInfo.Height ?? 0),
							tileSize: @(Model.DeepZoomInfo.TileSize ?? 0),
							fileId: '@Model.DeepZoomInfo.FileId',
							state: '@Model.DeepZoomInfo.State'
						},
						pinSource: @Json.Serialize(Model.AnnotationSource),
						pinColor: '@pinColor'
					})
				</text>
			}

			// restore stored breadcrumb
			// add new breadcrumb
			@if (!string.IsNullOrEmpty(breadcrumbLabel))
			{
				@:Page.loadBreadcrumbs({ labelWithPlaceholders: '@breadcrumbLabel', url: Page.getMainPageUrl() }, true)
			}
			else
			{
				@:Page.loadBreadcrumbs({ label: '@label', url: Page.getMainPageUrl() }, true)
			}
			Page.setTitle('@label')
		</script>
	</text>
}

<script type="module" defer>
	// which section should we load first?
	let currentViewId = @(Model.CurrentViewId != null ? Html.Raw($"'{Model.CurrentViewId}'") : Html.Raw($"localStorage.getItem('lvl:public:page:{Model.Page.Id}:current-view')"))
	if (!currentViewId)
		currentViewId = '@(Model.Page.DefaultViewId ?? Model.Page.Views?.First(view => view is { SystemView: true, Type: PageViewType.Grid }).Id)'

	// build field infos needed for header elements inside page
	let fieldDefinitions = []
	@if (Model.HeaderElements.Count > 0 && Model.Page.DataSource != null)
	{
		var fieldNames = new List<string>();
		var fieldDefinitions = new List<DataFieldFormatDto>();
		foreach (var element in Model.HeaderElements)
		{
			if (string.IsNullOrEmpty(element.Value))
				continue;

			var matches = Regex.Matches(element.Value, "##([a-zA-Z0-9_-]+)##");
			foreach (Match match in matches)
			{
				var fieldName = match.Groups[1].ToString();
				if (!fieldNames.Contains(fieldName))
					fieldNames.Add(fieldName);
			}
		}

		foreach (var fieldName in fieldNames)
		{
			var fieldDto = Model.Page?.DataSource?.Fields?.FirstOrDefault(field => field.Name == fieldName);
			if (fieldDto != null)
				fieldDefinitions.Add(new DataFieldFormatDto(fieldDto));
		}

		@:fieldDefinitions = @(Html.Raw(JsonSerializer.Serialize(fieldDefinitions, ConfigHelper.JsonOptionsCamel)))
	}
	
	const loadPageView = async (pageViewId, pageViewSlug) => {
		const pageDetail = document.getElementById('page-detail')
		await Page.load(`/Public/PageViews/${pageViewId}`, {}, { skipHistory: true, rootNode: pageDetail })

		if (pageViewSlug) {
			Page.pageMenu = pageViewSlug
			Page.setInfo(Page.getMainPageUrl() + '/' + pageViewSlug, {})
			await Page.updateCurrentBreadcrumb({ url: Page.getMainPageUrl() + '/' + pageViewSlug })
		}

		const form = pageDetail.querySelector('lvl-form')
		form?.setFieldDefinitions(fieldDefinitions)
		if (form != null && Page.hasFormData()) {
			await Page.setFormData(form)
			form.skeleton = false
		}

		// remember last viewed tab
		localStorage.setItem('lvl:public:page:@(Model.Page.Id):current-view', pageViewId)
	}

	const menu = document.getElementById('single-page-menu')
	menu.addEventListener('nav-item:click', async (event) => {
		// TODO: Loading indicator
		const menuItem = event.detail
		const viewId = menuItem.value
		const viewSlug = menuItem.data.slug

		if (viewId)
			await loadPageView(viewId, viewSlug)
	})

	// open our first page
	loadPageView(currentViewId, '@(Model.PageViewSlug ?? string.Empty)')

	const currentMenuEntry = menu.querySelector(`lvl-side-nav-item[value="${currentViewId}"]`)
	currentMenuEntry?.setAttribute('selected', '')

	// Enable edit button for admins
	@if (await UserManager.IsAdminAsync())
	{
		<text>
			Page.buttonConfig.edit = true
			Page.buttonConfig.editButton.addEventListener('click', () => {
				window.open(`/Admin/DataStores/@(Model.Page?.DataSource?.DataStore?.Slug)/Pages/@(Model.Page?.Slug)`, '_blank')
			}, { signal: Page.getPageChangeSignal() })
			Page.buttonConfig.editButton.skeleton = false
		</text>
	}

	Page.buttonConfig.save = false
	Page.buttonConfig.saveButton.disabled = true
	Page.buttonConfig.saveButton.addEventListener('click', async () => {
		if (Page.buttonConfig.saveButton.disabled)
			return
		
		Overlay.showWait("@scriptLocalizer["elementIsSaved"]")
		const form = document.getElementById('page-detail').querySelector('lvl-form')
		const formData = Page.getFormData()
		const result = await Form.storeData(form, `/Api/DataSources/@(Model.Page?.DataSourceId)/Elements/${formData.Id}`, 'PATCH')

		// update form data (formData is overwritten inside form-service but not from serverData which leads to destroyed lookup fields)
		if (result != null && !result.error) {
			Page.saveFormData(result.data.values)
			Page.setFormData(form, result.data)
		}

		Overlay.hideWait()
	}, { signal: Page.getPageChangeSignal() })
	Page.buttonConfig.saveButton.skeleton = false
	
	// enable active/reactive button
	@if (Model.Element != null && (Model.Page?.DataSource?.Inactive ?? false))
	{
		@: Page.buttonConfig.displayDiscardToggle(@(Model.Element.IsInactive.ToString().ToLower()), '@Model.Page.DataSourceId')
	}
	
	document.querySelector('.single_page_content')?.setAttribute('data-name', '@(Model.Page?.Name)')

	window.addEventListener('initiate-page-leave', (event) => {
		const pageDetail = document.getElementById('page-detail')
		const form = pageDetail.querySelector('lvl-form')
		if (!(event.cancelable && form != null && form.hasChanges()))
			return
		
		event.preventDefault();
		if (event.detail.skipDialog === true)
			return
				
		const executeCallback = (callback) => {
			if (callback != null && typeof callback == 'function')
				callback()
		}
		
		const unsavedChangesDialog = Page.getUnsavedChangesDialog()
		unsavedChangesDialog.showOnce() // showOnce = dialog gets appended automatically to the DOM and removes itself after any of the preset buttons was clicked
		
		// Add EventListeners to the dialog button ('dialog-cancel' does nothing so we don't need a listener here)
		unsavedChangesDialog.addEventListener('dialog-discard', () => {
			// discard all changes, then return true
			form.updateResetMarkers()
			executeCallback(event.detail.resolveCallback)
		})
		unsavedChangesDialog.addEventListener('dialog-save', async () => {
			// do the save action, then return true if successful
			const formData = Page.getFormData()
			const url = form.apiUrl + '/'  + formData.Id
			const result = await window.Form.storeData(form, url, 'PATCH')

			if (result != null && !result.error)
				executeCallback(event.detail.resolveCallback)
		})
		unsavedChangesDialog.addEventListener('dialog-abort', async () => {
			executeCallback(event.detail.cancelCallback)
		})
	}, { signal: Page.getPageChangeSignal() })
</script>

<style>
	#page-detail {
		width: 100%;
		display: flex;
		flex-direction: column;
		background-color: var(--clr-background-lvl-1);
	}

	.grid-element-wrapper > lvl-textarea, lvl-rich-text {
		height: 100%;
	}

	.grid-element-wrapper:has(lvl-value-formatter[skeleton]) {
		position: relative;
		
		& > * {
			opacity: 0;
		}
	}

	.grid-element-wrapper:has(lvl-value-formatter[skeleton]):before {
		content: '';
		position: absolute;
		inset: 0;
		background-color: var(--clr-background-lvl-1);
		border-radius: var(--size-radius-s);
		animation: skeleton-fade 3s linear;
		animation-iteration-count: infinite;
		cursor: default;
		z-index: 9999;
	}
	
	.grid-element-wrapper.hide-empty:has(lvl-value-formatter[empty]:not([skeleton])) {
		display:  none;
	}
</style>

<div class="single_page_content" style="width:100%;display:flex;flex-direction: row;flex-grow:1;overflow:hidden;">
	<side-nav-component id="single-page-menu" width="250" heading="Tab-Navigation">
		@foreach (var view in pageViews)
		{
			var data = "{\"slug\":\"" + view.Slug + "\"}";
			<side-nav-item-component icon="@(!view.Icon.IsNullOrEmpty() ? view.Icon : "fa-objects-column")" label="@viewLocalizer[view.Name ?? ""]"
			                         value="@view.Id" data="@Html.Raw(data)"></side-nav-item-component>
		}
		<side-nav-item-component icon="clipboard-list-check" label="@localizer["tasks"]" disabled="true" data-tooltip="tasks-tooltip"></side-nav-item-component>
		<tooltip-component name="tasks-tooltip">@localizer["mvpTooltip"]</tooltip-component>
		<side-nav-item-component icon="plus" label="@localizer["furtherViews"]" disabled="true" data-tooltip="furtherViews-tooltip"></side-nav-item-component>
		<tooltip-component name="furtherViews-tooltip">@localizer["mvpTooltip"]</tooltip-component>
		<side-nav-item-component icon="list-timeline" label="@localizer["feed"]" disabled="true" slot="bottom"
		                         data-tooltip="feed-tooltip"></side-nav-item-component>
		<tooltip-component name="feed-tooltip">@localizer["mvpTooltip"]</tooltip-component>
	</side-nav-component>
	<div id="page-detail"></div>
</div>
@await Html.PartialAsync("CreatePage/SlideOut", new CreatePageModel())