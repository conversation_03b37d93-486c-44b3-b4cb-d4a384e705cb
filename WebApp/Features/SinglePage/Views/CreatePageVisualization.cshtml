@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@model Levelbuild.Core.FrontendDtos.Page.CreatePageDto
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("SinglePage", "");
}
<lvl-tab-bar>
	<lvl-tab name="form" label="@localizer["sectionInfo"]">
		@await Html.PartialAsync("_GridView.cshtml", new GridViewModel { View = Model.CreateForm!, ViewType = ViewType.Create})
	</lvl-tab>
	<lvl-tab name="actions" label="@localizer["sectionActions"]" under-construction></lvl-tab>
</lvl-tab-bar>