using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;

#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels;

/// <summary>
/// Dto for multi data page 
/// </summary>
public class SectionModel(ViewType viewType, bool textView = false, bool setReadOnly = false)
{
	/// <summary>
	/// Dto which holds the Page config
	/// </summary>
	public PageDto? Page { get; init; }
	
	/// <summary>
	/// Dto of the page configuration
	/// </summary>
	public required GridViewSectionDto Section { get; init; }
	
	/// <summary>
	/// should the elements inside this section be displayed as text or as form elements?
	/// </summary>
	public bool? TextView { get; init; } = textView;

	/// <summary>
	/// is this a create or a edit view?
	/// </summary>
	public ViewType ViewType { get; init; } = viewType;

	/// <summary>
	/// Render field as disabled/readonly
	/// </summary>
	public bool Readonly { get; set; } = setReadOnly;
}