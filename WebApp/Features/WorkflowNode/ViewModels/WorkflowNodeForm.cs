using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;

namespace Levelbuild.Frontend.WebApp.Features.WorkflowNode.ViewModels;

public class WorkflowNodeForm
{
	public ViewType ViewType { get; init; }
	
	public Guid? WorkflowId { get; init; }
	
	public WorkflowNodeDto? WorkflowNode { get; init; }
	
	public List<WorkflowNodeState> States { get; } = Enum.GetValues(typeof(WorkflowNodeState))
		.Cast<WorkflowNodeState>()
		.ToList();
	
	public IList<CustomerDto>? Customers { get; init; }
	
	public WorkflowNodeForm(ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
	}
}