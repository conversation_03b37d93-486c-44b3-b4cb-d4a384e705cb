using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using Levelbuild.Entities.Features.User;
using Levelbuild.Frontend.WebApp.Features.Auth.Utils;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using ILogger = Serilog.ILogger;

namespace Levelbuild.Frontend.WebApp.Features.Auth.Services;

/// <summary>
/// Handles creation and validation of signed JWTs for our microservices
/// </summary>
public class JwtService
{
	private readonly string _zitadelUrl;
	private readonly IConfigurationSection _jwtSecret;
	private readonly TokenValidationParameters _tokenValidationParameters;

	private readonly ILogger _logger;

	/// <summary>
	/// A Constructor.
	/// </summary>
	/// <param name="configuration">The app's config.</param>
	/// <param name="logManager">log manage instance to create a logger</param>
	public JwtService(IConfiguration configuration, ILogManager logManager)
	{
		var zitadelConfig = configuration.GetSection("Zitadel");
		_zitadelUrl = zitadelConfig["Url"]!.TrimEnd('/');
		_jwtSecret = zitadelConfig.GetSection("JwtSecret");

		_tokenValidationParameters = new()
		{
			ValidateAudience = true,
			ValidAudiences = [_zitadelUrl, _jwtSecret["clientId"]],
			ValidateIssuer = true,
			ValidIssuers = [_zitadelUrl, _jwtSecret["clientId"]],
			ValidateIssuerSigningKey = true,
			ValidateTokenReplay = false,
			ValidateLifetime = true,
		};

		_logger = logManager.GetLoggerForClass<JwtService>();
	}

	/// <summary>
	/// Creates a signed JWT using the private key provided by Zitadel.
	/// </summary>
	/// <param name="userEntity">The user to issue the JWT for.</param>
	/// <returns>The signed JWT as string.</returns>
	public string CreateSignedJwt(UserEntity userEntity)
	{
		using var rsa = RSA.Create();
		var privateKey = AuthUtils.DecodeRsaPrivateKey(
			_jwtSecret.GetValue<string>("key")!
		);
		
		rsa.ImportFromPem(privateKey);

		var securityKey = new RsaSecurityKey(rsa);
		var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.RsaSha256)
		{
			CryptoProviderFactory = new CryptoProviderFactory()
			{
				CacheSignatureProviders = false
			}
		};

		var token = new JwtSecurityToken(
			issuer: _jwtSecret["clientId"],
			audience: _zitadelUrl,
			expires: DateTime.Now.AddMinutes(30), // TODO: Necessary for microservices?
			signingCredentials: credentials
		);

		// TODO: Necessary for microservices?
		// Add necessary info for Zitadel
		token.Header.Add("kid", _jwtSecret["keyId"]);
		token.Payload.Add("sub", userEntity.RemoteId);
		token.Payload.Add("iat", DateTimeOffset.Now.ToUnixTimeSeconds());

		// Add user name & id
		token.Payload.Add("name", userEntity.DisplayName);
		token.Payload.Add("identifier", userEntity.Id.ToString());

		return new JwtSecurityTokenHandler().WriteToken(token);
	}

	/// <summary>
	/// JWT verification of tokens issued by this application.
	/// </summary>
	/// <param name="jwt">The JWT to verify.</param>
	/// <param name="claims">The resulting claims.</param>
	/// <returns>True, if the jwt is valid.</returns>
	public bool VerifySignedJwt(string jwt, out ClaimsPrincipal? claims)
	{
		try
		{
			using var rsa = RSA.Create();
			rsa.ImportFromPem(_jwtSecret["key"]);

			var securityKey = new RsaSecurityKey(rsa);
			_tokenValidationParameters.IssuerSigningKey = securityKey;

			claims = new JwtSecurityTokenHandler().ValidateToken(jwt, _tokenValidationParameters, out _);
			if (claims != null)
				return true;

			return false;
		}
		catch (Exception e)
		{
			_logger.Warning(e, "Incoming JWT could not be verified");

			claims = null;
			return false;
		}
	}
	
	/// <summary>
	/// Verifies JWT issued by Zitadel (ID tokens for example).
	/// </summary>
	/// <param name="jwt">The JWT to verify.</param>
	/// <returns></returns>
	public async Task<(bool Success, ClaimsPrincipal? ClaimsPrincipal)> VerifySignedZitadelJwt(string jwt)
	{
		try
		{
			// Get Zitadel's config to retrieve signing keys from
			var metaDataUrl = $"{_zitadelUrl}/.well-known/openid-configuration";
			var oidcConfigurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(metaDataUrl, new OpenIdConnectConfigurationRetriever(), new HttpDocumentRetriever());
			var metadataDocument = await oidcConfigurationManager.GetConfigurationAsync(CancellationToken.None);

			_tokenValidationParameters.IssuerSigningKeys = metadataDocument.SigningKeys;
			
			var claims = new JwtSecurityTokenHandler().ValidateToken(jwt, _tokenValidationParameters, out _);
			return claims != null ? (true, claims) : (false, null);
		}
		catch (Exception e)
		{
			_logger.Warning(e, "Incoming JWT could not be verified");
			
			return (false, null);
		}
	}
}