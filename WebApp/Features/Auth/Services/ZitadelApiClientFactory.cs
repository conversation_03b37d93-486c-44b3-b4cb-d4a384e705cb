using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.ZitadelApiInterface;
using Levelbuild.Domain.ZitadelApi;
using Levelbuild.Frontend.WebApp.Features.Auth.Utils;
using Zitadel.Credentials;

namespace Levelbuild.Frontend.WebApp.Features.Auth.Services;

/// <inheritdoc />
[ExcludeFromCodeCoverage]
public class ZitadelApiClientFactory : IZitadelApiClientFactory
{
	private readonly string _apiUrl;
	private readonly ServiceAccount _serviceAccount;
	private readonly IList<string> _protectedOrgIds;
	private readonly string _projectId;

	/// <summary>
	/// Don't call manually! This factory is provided as Singleton by the Services.
	/// </summary>
	/// <param name="configuration">The WebApp's configuration (i.e. appsettings.json)</param>
	public ZitadelApiClientFactory(IConfiguration configuration)
	{
		_apiUrl = configuration.GetSection("Zitadel")["Url"]!;

		var configSection = configuration.GetSection("Zitadel");
		var serviceAccountSection = configSection.GetSection("ServiceAccount");
		var privateKey = AuthUtils.DecodeRsaPrivateKey(
			serviceAccountSection.GetValue<string>("key")!
		);
		
		_serviceAccount = ServiceAccount.LoadFromJsonString(
			$@"{{""type"": ""{serviceAccountSection["type"]}"", ""keyId"": ""{serviceAccountSection["keyId"]}"", ""key"": ""{privateKey}"",""userId"": ""{serviceAccountSection["userId"]}""}}"
		);

		_protectedOrgIds = configSection.GetSection("ProtectedOrgs").Get<List<string>>()!;
		_projectId = configSection["ProjectId"]!;
	}
	
	/// <inheritdoc />
	public IZitadelAuthApiClient GetAuthClient(string accessToken)
	{
		return ZitadelAuthApiClient.GetInstance(_apiUrl, accessToken);
	}
	
	/// <inheritdoc />
	public IZitadelAdminApiClient GetAdminClient()
	{
		return ZitadelAdminApiClient.GetInstance(_apiUrl, _serviceAccount);
	}

	/// <inheritdoc />
	public IZitadelManagementApiClient GetManagementClient()
	{
		return ZitadelManagementApiClient.GetInstance(_apiUrl, _serviceAccount, _protectedOrgIds, _projectId);
	}

	/// <inheritdoc />
	public IZitadelUserServiceApiClient GetUserServiceClient()
	{
		return ZitadelUserServiceApiClient.GetInstance(_apiUrl, _serviceAccount);
	}
}