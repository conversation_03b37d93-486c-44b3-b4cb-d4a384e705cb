@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.Auth.ViewModels.PasskeyRegistrationForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	// TODO: Just for testing/debugging. Can be removed once <PERSON><PERSON> has implemented Passkey registration
	
	//Layout = "";
	var localizer = LocalizerFactory.Create("Passkey", "");
}

<script>
	const authCode = '@Model.AuthCode'
	
	async function fetchPasskeyCredentials() {
		const response = await fetch(`/PublicApi/Passkeys/GetCredentials/${authCode}`)
		if (!response.ok) {
			console.error(`${response.status} - unexpected server error received: ${response.statusText}`)
			return null
		}
		
		return (await response.json()).data;
	}
	
	async function startPasskeyRegistration() {
		const registrationCredentials = await fetchPasskeyCredentials();
		
		const jsonOptions = JSON.parse(registrationCredentials.credentials.replaceAll('&quot;', '"')).publicKey
		const encoder = new TextEncoder('utf-8')
		const publicKeyCredentialCreationOptions = {
			challenge: encoder.encode(jsonOptions.challenge),
			rp: jsonOptions.rp,
			user: {
				id: encoder.encode(jsonOptions.user.id),
				name: jsonOptions.user.name,
				displayName: jsonOptions.user.displayName
			},
			pubKeyCredParams: jsonOptions.pubKeyCredParams,
			authenticatorSelection: jsonOptions.authenticatorSelection,
			timeout: jsonOptions.timeout,
			attestation: jsonOptions.attestation
		}
		console.log(publicKeyCredentialCreationOptions)
		const credentials = await navigator.credentials.create({
			publicKey: publicKeyCredentialCreationOptions
		})
		console.log(credentials)
		console.log(JSON.stringify(credentials))
		
		const passkeyName = document.getElementById('passkey-name').value
		await verifyPasskeyRegistration(registrationCredentials.passkeyId, passkeyName, credentials)
	}
	
	async function verifyPasskeyRegistration(passkeyId, passkeyName, credentials) {
		// Move data into Arrays in case it is super long
		let attestationObject = new Uint8Array(credentials.response.attestationObject);
		let clientDataJSON = new Uint8Array(credentials.response.clientDataJSON);
		let rawId = new Uint8Array(credentials.rawId);

		const data = {
			id: credentials.id,
			rawId: coerceToBase64Url(rawId),
			type: credentials.type,
			extensions: credentials.getClientExtensionResults(),
			response: {
				attestationObject: coerceToBase64Url(attestationObject),
				clientDataJSON: coerceToBase64Url(clientDataJSON),
				transports: credentials.response.getTransports()
			}
		};
		console.log(data)
		console.log(JSON.stringify(data))
		
		const response = await fetch(`/PublicApi/Passkeys/VerifyRegistration/${authCode}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json;charset=UTF-8',
			},
			body: JSON.stringify({
				PasskeyId: passkeyId,
				Name: passkeyName,
				Credentials: JSON.stringify(data)
			})
		})
		if (!response.ok) {
			console.error(`${response.status} - unexpected server error received: ${response.statusText}`)
			return null
		}
	}

	function coerceToBase64Url(thing) {
		// Array or ArrayBuffer to Uint8Array
		if (Array.isArray(thing)) {
			thing = Uint8Array.from(thing);
		}

		if (thing instanceof ArrayBuffer) {
			thing = new Uint8Array(thing);
		}

		// Uint8Array to base64
		if (thing instanceof Uint8Array) {
			let str = "";
			const len = thing.byteLength;

			for (let i = 0; i < len; i++) {
				str += String.fromCharCode(thing[i]);
			}
			thing = window.btoa(str);
		}

		if (typeof thing !== "string") {
			throw new Error("could not coerce to string");
		}

		// base64 to base64url
		// NOTE: "=" at the end of challenge is optional, strip it off here
		thing = thing.replace(/\+/g, "-").replace(/\//g, "_").replace(/=*$/g, "");

		return thing;
	}
</script>
<div>
	<form-component id="passkey-form">
		<input-component required="true" id="passkey-name" class="item__value" name="name"></input-component>
		<button-component slot="button-right" id="passkey-register-button" label="@localizer["registerButton"]" type="ButtonType.Primary"></button-component>
	</form-component>
</div>
<script type="module" defer>
	document.getElementById('passkey-register-button').addEventListener('click', async () => {
		await startPasskeyRegistration();
	})
</script>