using System.Diagnostics.CodeAnalysis;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// Small TagHelper to render "filter-config"/>
/// </summary>
[ExcludeFromCodeCoverage]
public class FilterConfigComponentTagHelper : TagHelper
{
	/// <summary>
	/// base url used to select existing filters and create/update/delete elements 
	/// </summary>
	public string? Url { get; set; }
	
	/// <summary>
	/// optional create url used for create/update/delete if differing from base url
	/// </summary>
	public string? CreateUrl { get; set; }
	
	/// <summary>
	/// url used to select the correct filter fields
	/// </summary>
	public string? FieldUrl { get; set; }
	
	/// <summary>
	/// filter key used to only select filters matching the right parent element
	/// </summary>
	public string? ParentElementKey { get; set; }
	
	/// <summary>
	/// filter value used to only select filters matching the right parent element
	/// </summary>
	public string? ParentElementValue { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-filter-config";
		
		if (Url != null)
			output.Attributes.SetAttribute("url", Url);
		
		if (CreateUrl != null)
			output.Attributes.SetAttribute("create-url", CreateUrl);
		
		if (FieldUrl != null)
			output.Attributes.SetAttribute("field-url", FieldUrl);
		
		if (ParentElementKey != null)
			output.Attributes.SetAttribute("parent-element-key", ParentElementKey);
		
		if (ParentElementValue != null)
			output.Attributes.SetAttribute("parent-element-value", ParentElementValue);
		
		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}