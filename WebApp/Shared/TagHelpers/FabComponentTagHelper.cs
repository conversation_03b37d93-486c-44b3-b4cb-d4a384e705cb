using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-fab
/// </summary>
public class FabComponentTagHelper : TagHelper
{
	/// <summary>
	/// Icon in the middle of the floating action button
	/// </summary>
	public string? Icon { get; set; }

	/// <summary>
	/// Font size of the button
	/// </summary>
	public FontSize? Size { get; set; }

	/// <summary>
	/// Mark item as loading
	/// </summary>
	public bool Skeleton { get; set; } = false;
	
	/// <summary>
	/// Is the button hidden?
	/// </summary>
	public bool Hidden { get; set; } = false;

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-fab";

		if (Icon != null)
			output.Attributes.SetAttribute("icon", Icon);
		if (Size != null)
			output.Attributes.SetAttribute("size", ((FontSize)Size).GetFontSizeAsString());
		if (Skeleton)
			output.Attributes.SetAttribute("skeleton", "");
		if(Hidden)
			output.Attributes.SetAttribute("hidden", "");

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}