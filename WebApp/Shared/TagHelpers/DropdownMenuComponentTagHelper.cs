using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-menu
/// </summary>
public class DropdownMenuComponentTagHelper : TagHelper
{
	/// <summary>
	/// Minimum display width
	/// </summary>
	public string? MinWidth { get; set; }

	/// <summary>
	/// Maximum height (enables overflow: auto and is therefore currently not usable with submenus)
	/// </summary>
	public string? MaxWidth { get; set; }

	/// <summary>
	/// Text color
	/// </summary>
	public string? Color { get; set; }

	/// <summary>
	/// Background color
	/// </summary>
	public string? BackgroundColor { get; set; }

	/// <summary>
	/// Border color
	/// </summary>
	public string? BorderColor { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-menu";

		if (MinWidth != null)
			output.Attributes.SetAttribute("min-width", MinWidth);
		if (MaxWidth != null)
			output.Attributes.SetAttribute("max-width", MaxWidth);
		if (Color != null)
			output.Attributes.SetAttribute("color", Color);
		if (BackgroundColor != null)
			output.Attributes.SetAttribute("background-color", BackgroundColor);
		if (BorderColor != null)
			output.Attributes.SetAttribute("border-color", BorderColor);

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}