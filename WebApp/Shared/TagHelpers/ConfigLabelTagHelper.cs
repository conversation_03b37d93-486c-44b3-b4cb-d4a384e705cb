using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for config labels
/// </summary>
[ExcludeFromCodeCoverage]
public class ConfigLabelTagHelper : TagHelper
{
	/// <summary>
	/// the text of the label
	/// </summary>
	public string? Label { get; set; }

	/// <summary>
	/// the description next to the label
	/// </summary>
	public string? Description { get; set; }

	/// <summary>
	/// html classes to be added to the label
	/// </summary>
	public string? Class { get; set; }

	/// <summary>
	/// target of the label
	/// </summary>
	public string? Target { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		var wrapperRequired = Description != null;

		if (wrapperRequired)
		{
			var target = !Target.IsNullOrEmpty() ? $"for=\"{Target}\"" : "";
			var description = $"<span class=\"option__description\">{Description}</span>";
			var clazz = Class.IsNullOrEmpty() ? "" : $"class=\"{Class}\"";

			output.TagName = "div";
			output.Attributes.SetAttribute("class", "item__description");
			output.Content.AppendHtml($"<label {target} {clazz}>{Label}</label>{description}");
		}
		else
		{
			output.TagName = "label";
			if (!Target.IsNullOrEmpty())
				output.Attributes.SetAttribute("for", Target);
			if (!Class.IsNullOrEmpty())
				output.Attributes.SetAttribute("class", Class);
			output.Content.SetContent(Label);
		}
	}
}