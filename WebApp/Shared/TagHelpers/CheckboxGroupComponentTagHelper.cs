using System.Text;
using System.Text.Json;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-checkbox-group
/// </summary>
public class CheckboxGroupComponentTagHelper : TagHelper
{
	/// <summary>
	/// Name of the buttongroup (used to grab the value of this group)
	/// </summary>
	public string? Name { get; set; }

	/// <summary>
	/// Currently selected value(s) (if any)
	/// </summary>
	public string[]? Value { get; set; }

	/// <summary>
	/// If set to true, the group is grayed out and can not be toggled
	/// </summary>
	public bool Readonly { get; set; } = false;
	
	/// <summary>
	/// If set to true, at least one checkbox inside needs to be checked before submitting the form
	/// </summary>
	public bool Required { get; set; } = false;
	
	/// <summary>
	/// displays a skeleton loading animation instead of the contained checkboxes
	/// </summary>
	public bool Skeleton { get; set; } = false;
	
	/// <summary>
	/// limits the maximum number of cols the contained checkboxes are grouped into 
	/// </summary>
	public int? MaxColumns { get; set; }
	
	/// <summary>
	/// keys:values of available options (aka checkboxes) if not manually put into the DOM as children of the checkbox-group
	/// </summary>
	public Dictionary<string, string>? Options { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-checkbox-group";

		if (Name != null)
			output.Attributes.SetAttribute("name", Name);
		if (Value is { Length: > 0 })
			output.Attributes.SetAttribute("value", JsonSerializer.Serialize(Value));
		if (Readonly)
			output.Attributes.SetAttribute("readonly", "");
		if (Required)
			output.Attributes.SetAttribute("required", "");
		if (Skeleton)
			output.Attributes.SetAttribute("skeleton", "");
		if (MaxColumns is > 0)
			output.Attributes.SetAttribute("max-columns", MaxColumns);
		
		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
		
		if (Options == null || Options.Count == 0)
			return;
		
		// build the options for the autocomplete, with single or multiple display values
		var stringBuilder = new StringBuilder();
		foreach (var option in Options)
		{
			stringBuilder.Append($"<lvl-checkbox value=\"{option.Key}\">{option.Value}</lvl-checkbox>");
		}
		
		output.Content.AppendHtml(stringBuilder.ToString());
	}
}