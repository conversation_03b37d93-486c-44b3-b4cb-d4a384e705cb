using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for an usual icon
/// </summary>
public class IconTagHelper : TagHelper
{
	/// <summary>
	/// Icon which is located to the left of the button
	/// </summary>
	public required string Name { get; set; }
	
	/// <summary>
	/// Optional icon style (default = light)
	/// </summary>
	public IconStyle? Style { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "i";
		output.Attributes.SetAttribute("class", $"fa-{(Style == null ? "light" : Style.ToString()!.ToLower())} fa-{Name}");
	}
}