using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Newtonsoft.Json;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-list
/// </summary>
public class ListComponentTagHelper : TagHelper
{
	/// <summary>
	/// This list will be displayed in the enumeration view as alternative to work without data fetching
	/// </summary>
	public List<Dictionary<string, object>?>? Rows { get; set; }
	
	/// <summary>
	/// Width of the enumeration wrapper with units
	/// </summary>
	public string? Width { get; set; }

	/// <summary>
	/// Height of the enumeration wrapper with units
	/// </summary>
	public string? Height { get; set; }

	/// <summary>
	/// Used to update single data
	/// </summary>
	public string? IdentityColumn { get; set; }
	
	/// <summary>
	/// Used to highlight specific lines as active
	/// </summary>
	public string? ActiveColumn { get; set; }

	/// <summary>
	/// Rows can be displayed next to each other if space is available
	/// </summary>
	public bool Responsive { get; set; } = false;

	/// <summary>
	/// Size of each line
	/// </summary>
	public LineSize? Size { get; set; }
	
	/// <summary>
	/// Mark item as loading
	/// </summary>
	public bool Skeleton { get; set; } = false;

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-list";

		if (Responsive)
			output.Attributes.SetAttribute("responsive", "");

		if (Skeleton)
			output.Attributes.SetAttribute("skeleton", "");
		
		if (IdentityColumn != null)
			output.Attributes.SetAttribute("identity-column", IdentityColumn);
		
		if (ActiveColumn != null)
			output.Attributes.SetAttribute("active-column", ActiveColumn);
		
		if (Size != null)
			output.Attributes.SetAttribute("size", Size.Value.GetLineSizeAsString());
		
		if (Rows?.Count > 0)
		{
			var rowString = JsonConvert.SerializeObject(Rows);
			output.Attributes.SetAttribute("rows", rowString);
		}
		
		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}

		// Set Width and Height as style
		var styleList = new List<string>();
		if (!string.IsNullOrEmpty(Width))
		{
			styleList.Add($"width: {Width};");
		}

		if (!string.IsNullOrEmpty(Height))
		{
			styleList.Add($"height: {Height};");
		}

		if (styleList.Count > 0)
		{
			output.Attributes.SetAttribute("style", string.Join(" ", styleList));
		}
	}
}