using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable PropertyCanBeMadeInitOnly.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-data-column
/// </summary>
public class BaseColumnComponentTagHelper : TagHelper
{
	/// <summary>
	/// Unique name of the column
	/// </summary>
	public string? Name { get; set; }

	/// <summary>
	/// Displayed label of the column
	/// </summary>
	public string? Label { get; set; }

	/// <summary>
	/// Minimum width of the column
	/// </summary>
	public int? MinWidth { get; set; }
	
	/// <summary>
	/// Minimum width unit of the column
	/// </summary>
	public DimensionUnit? MinWidthUnit { get; set; }
	
	/// <summary>
	/// Width of the column
	/// </summary>
	public int? Width { get; set; }
	
	/// <summary>
	/// Width unit of the column
	/// </summary>
	public DimensionUnit? WidthUnit { get; set; }

	/// <summary>
	/// Maximum width of the column
	/// </summary>
	public int? MaxWidth { get; set; }
	
	/// <summary>
	/// Maximum width unit of the column
	/// </summary>
	public DimensionUnit? MaxWidthUnit { get; set; }
	
	/// <summary>
	/// Tooltip for the Icon within a column, if value is true
	/// </summary>
	public string? IconTooltipYes { get; set; }
	
	/// <summary>
	/// Tooltip for the Icon within a column, if value is false
	/// </summary>
	public string? IconTooltipNo { get; set; }

	/// <summary>
	/// Take use of a callback function which formats the data input.
	/// Hint: Add a converter -> document.querySelector('lvl-list-data-column').converter = (data: Record) => { console.log('Hello World') } 
	/// </summary>
	public bool WithConverter { get; set; } = false;
	
	
	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	protected void ProcessBaseOutput(TagHelperOutput output, TagHelperContext context)
	{
		if (Name != null)
			output.Attributes.SetAttribute("name", Name);
		if (Label != null)
			output.Attributes.SetAttribute("label", Label);
		if (MinWidth != null)
		{
			output.Attributes.SetAttribute("min-width", MinWidth);
			if (MinWidthUnit != null)
				output.Attributes.SetAttribute("min-width-unit", MinWidthUnit.Value.GetSign());
		}

		if (Width != null)
		{
			output.Attributes.SetAttribute("width", Width);
			if (WidthUnit != null)
				output.Attributes.SetAttribute("width-unit", WidthUnit.Value.GetSign());
		}

		if (MaxWidth != null)
		{
			output.Attributes.SetAttribute("max-width", MaxWidth);
			if (MaxWidthUnit != null)
				output.Attributes.SetAttribute("max-width-unit", MaxWidthUnit.Value.GetSign());
		}

		if (WithConverter)
			output.Attributes.SetAttribute("with-converter", "");
		
		if(IconTooltipYes != null)
			output.Attributes.SetAttribute("icon-tooltip-yes", IconTooltipYes);
		
		if(IconTooltipNo != null)
			output.Attributes.SetAttribute("icon-tooltip-no", IconTooltipNo);
		
		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}