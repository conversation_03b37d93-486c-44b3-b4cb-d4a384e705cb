using System.Text.Json;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-table-data-column
/// </summary>
public class TableDataColumnComponentTagHelper : BaseColumnComponentTagHelper
{
	/// <summary>
	/// Number of decimal places for numbers displayed in the column
	/// </summary>
	public int? DecimalPlaces { get; set; }

	/// <summary>
	/// Optional unit symbol
	/// </summary>
	public string? Sign { get; set; }

	/// <summary>
	/// Should the column be hidden?
	/// </summary>
	public bool Hidden { get; set; } = false;
	
	/// <summary>
	/// Should the label be hidden?
	/// </summary>
	public bool HideLabel { get; set; } = false;

	/// <summary>
	/// Is the content of column editable?
	/// </summary>
	public bool LiveEditable { get; set; } = false;

	/// <summary>
	/// Display a thousand separator?
	/// </summary>
	public bool WithThousandSeparators { get; set; } = false;

	/// <summary>
	/// Render this column and all before as sticky?
	/// </summary>
	public bool Sticky { get; set; } = false;
	
	/// <summary>
	/// Display as HTML?
	/// </summary>
	public bool RichText { get; set; } = false;

	/// <summary>
	/// Data type of the columns content
	/// </summary>
	public InputDataType? Type { get; set; }

	/// <summary>
	/// Alignment of the content within the column
	/// </summary>
	public Alignment? TextAlign { get; set; }
	
	/// <summary>
	/// is this column based on a multi value field?
	/// </summary>
	public bool MultiValue { get; set; } = false;

	/// <summary>
	/// Is this column only a representation column for the real data? What is the real data column name?
	/// </summary>
	public string? ReferenceField { get; set; }

	/// <summary>
	/// List of values which are passed to the column as possible static options 
	/// </summary>
	public List<DataColumnValueDefinition>? Values { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-table-data-column";
		
		if (Type != null)
			output.Attributes.SetAttribute("type", ((InputDataType)Type).GetTypeAsString());
		if (TextAlign != null)
			output.Attributes.SetAttribute("text-align", ((Alignment)TextAlign).GetAlignmentAsString());
		if (DecimalPlaces != null)
			output.Attributes.SetAttribute("decimal-places", DecimalPlaces);
		if (Sign != null)
			output.Attributes.SetAttribute("sign", Sign);
		if (Hidden)
			output.Attributes.SetAttribute("hidden", "");
		if (HideLabel)
			output.Attributes.SetAttribute("hide-label", "");
		if (LiveEditable)
			output.Attributes.SetAttribute("live-editable", "");
		if (WithThousandSeparators)
			output.Attributes.SetAttribute("with-thousand-separators", "");
		if (RichText)
			output.Attributes.SetAttribute("rich-text", "");
		if (Sticky)
			output.Attributes.SetAttribute("sticky", "");
		if (ReferenceField != null)
			output.Attributes.SetAttribute("ref", ReferenceField);
		if (MultiValue)
			output.Attributes.SetAttribute("multi-value", "");
		if (Values is { Count: > 0 })
		{
			var filterString = JsonSerializer.Serialize(Values, ConfigHelper.JsonOptionsCamel);
			output.Attributes.SetAttribute("values", filterString);
		}

		ProcessBaseOutput(output, context);
	}
}