using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// Tag Helper lvl-side-nav
/// </summary>
public class SideNavComponentTagHelper : TagHelper
{
	/// <summary>
	/// Optional Heading of the SideNav
	/// </summary>
	public string? Heading { get; set; }

	/// <summary>
	/// Do you want to display an icon which displays the state of the nav item?
	/// </summary>
	public bool ShowState { get; set; } = false;

	/// <summary>
	/// Width of the menu
	/// </summary>
	public int? Width { get; set; }

	/// <summary>
	/// Fetch nav items from url to generate them dynamically
	/// </summary>
	public string? Url { get; set; }

	/// <summary>
	/// Name of the column to get the item state from when data was fetched from url
	/// </summary>
	public string? StateColumn { get; set; }

	/// <summary>
	/// should the menu elements be displayed as pulsating bars?
	/// </summary>
	public bool Skeleton { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-side-nav";

		if (Heading != null)
			output.Attributes.SetAttribute("heading", Heading);
		if (ShowState)
			output.Attributes.SetAttribute("show-state", "");
		if (Skeleton)
			output.Attributes.SetAttribute("skeleton", "");
		if (Width != null)
			output.Attributes.SetAttribute("width", Width);
		if (Url != null)
		{
			output.Attributes.SetAttribute("url", Url);
			if (StateColumn != null)
				output.Attributes.SetAttribute("state-column", StateColumn);
		}

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}