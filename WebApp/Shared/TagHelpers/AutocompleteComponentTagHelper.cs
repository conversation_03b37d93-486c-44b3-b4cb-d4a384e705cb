using System.Globalization;
using System.Text;
using System.Text.Json;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Microsoft.IdentityModel.Tokens;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-autocomplete
/// </summary>
public class AutocompleteComponentTagHelper : TagHelper
{
	/// <summary>
	/// Unique name of the field
	/// </summary>
	public string? Name { get; set; }
	
	/// <summary>
	/// Label displayed above the input
	/// </summary>
	public string? Label { get; set; }
	
	/// <summary>
	/// Value which gets parsed to the configured data type
	/// </summary>
	public string? Value { get; set; }
	
	/// <summary>
	/// Default Value displayed on load
	/// </summary>
	public string? Default { get; set; }
	
	/// <summary>
	/// if set, values get send to the server with a different name than this.Name (needed because we usually use EntityName as Name but need EntityNameId as form value)
	/// </summary>
	public string? OutputName { get; set; }
	
	/// <summary>
	/// if this.Value is an object, which property contains the key? (defaults to "id" inside the component)
	/// </summary>
	public string? KeyValue { get; set; }
	
	/// <summary>
	/// if this.Value is an object, which property contains the display value? (defaults to "name" inside the component)
	/// </summary>
	public string? DisplayValue { get; set; }
	
	/// <summary>
	/// Display field of the referenced dataset
	/// </summary>
	public string? DisplayField { get; set; }
	
	/// <summary>
	/// Data type of the field
	/// </summary>
	public InputDataType? Type { get; set; }
	
	/// <summary>
	/// Should text inside the input be aligned to the left or the right
	/// </summary>
	public Alignment? TextAlign { get; set; }

	/// <summary>
	/// Optional unit symbol
	/// </summary>
	public string? Sign { get; set; }
	
	/// <summary>
	/// Is this a mandatory field?
	/// </summary>
	public bool Required { get; set; } = false;
	
	/// <summary>
	/// If set to true, it is not possible to change the input value
	/// </summary>
	public bool Readonly { get; set; } = false;
	
	/// <summary>
	/// Placeholder displayed if field is empty
	/// </summary>
	public string? Placeholder { get; set; }
	
	/// <summary>
	/// Tooltip displayed when hovering the help icon next to the toggle switch
	/// </summary>
	public string? Tooltip { get; set; }
	
	/// <summary>
	/// Makes it possible to set an error from outside the control (for example via rule action)
	/// </summary>
	public string? Error { get; set; }

	/// <summary>
	/// Is it possible to create new entries within the lookup table?
	/// </summary>
	public bool AllowCreate { get; set; } = false;
	
	/// <summary>
	/// Which create mask should be called for creating a new lookup entry
	/// </summary>
	public int? CreateMaskId { get; set; }
	
	/// <summary>
	/// Endpoint url for fetching autocomplete data
	/// </summary>
	public string? Url { get; set; }
	
	/// <summary>
	/// A list of filter conditions
	/// </summary>
	public IList<QueryParamFilterDto>? Filters { get; set; }
	
	/// <summary>
	/// Endpoint url for fetching autocomplete data
	/// </summary>
	public string? Legend { get; set; }
	
	/// <summary>
	/// List of column definitions, used for autocompletes with multiple columns
	/// </summary>
	public List<AutocompleteColumnDefinition>? Columns { get; set; }
	
	/// <summary>
	/// Static content of the autocomplete if autocomplete is not filled by url
	/// </summary>
	public List<AutocompleteOptionDefinition>? Options { get; set; }
	
	/// <summary>
	/// Is the input part of the page designer and displayed without being connected to a DataField
	/// </summary>
	public bool Disconnected { get; set; } = false;
	
	/// <summary>
	/// should the values be displayed in columns instead of multiple rows per entry
	/// </summary>
	public bool ColumnView { get; set; } = false;
	
	/// <summary>
	/// link with placeholders to navigate to the selected element
	/// </summary>
	public string? EditLinkUrl { get; set; }
	
	/// <summary>
	/// Should an autocomplete return null or empty when it is emptied?
	/// </summary>
	public bool Nullable { get; set; } = false;
	
	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-autocomplete";
		
		if (!string.IsNullOrEmpty(Value))
			output.Attributes.SetAttribute("value", Value);
		
		if (!string.IsNullOrEmpty(Default))
			output.Attributes.SetAttribute("default", Default);
		
		if (!string.IsNullOrEmpty(OutputName))
			output.Attributes.SetAttribute("output-name", OutputName);
		
		if (!string.IsNullOrEmpty(KeyValue))
			output.Attributes.SetAttribute("key-value", KeyValue);
		
		if (!string.IsNullOrEmpty(DisplayValue))
			output.Attributes.SetAttribute("display-value", DisplayValue);
		
		if (!string.IsNullOrEmpty(DisplayField))
			output.Attributes.SetAttribute("display-field", DisplayField);
		
		if (!string.IsNullOrEmpty(EditLinkUrl))
			output.Attributes.SetAttribute("edit-link-url", EditLinkUrl);
		
		if (Name != null)
			output.Attributes.SetAttribute("name", Name);
		if (Label != null)
			output.Attributes.SetAttribute("label", Label);
		if (Type != null)
			output.Attributes.SetAttribute("type", ((InputDataType)Type).GetTypeAsString());
		if (Sign != null)
			output.Attributes.SetAttribute("sign", Sign);
		if (TextAlign != null)
			output.Attributes.SetAttribute("text-align", TextAlign.Value.GetAlignmentAsString());
		if (Placeholder != null)
			output.Attributes.SetAttribute("placeholder", Placeholder);
		if (Tooltip != null)
			output.Attributes.SetAttribute("tooltip", Tooltip);
		if (Error != null)
			output.Attributes.SetAttribute("error", Error);
		if (Url != null)
			output.Attributes.SetAttribute("url", Url);
		if (Legend != null)
			output.Attributes.SetAttribute("legend", Legend);
		if (Required)
			output.Attributes.SetAttribute("required", "");
		if (Readonly)
			output.Attributes.SetAttribute("readonly", "");
		if (Nullable)
			output.Attributes.SetAttribute("nullable", "");
		if (Disconnected)
			output.Attributes.SetAttribute("disconnected", "");
		if (ColumnView)
			output.Attributes.SetAttribute("column-view", "");
		if (AllowCreate)
			output.Attributes.SetAttribute("allow-create", "");
		if (CreateMaskId != null)
			output.Attributes.SetAttribute("create-mask-id", CreateMaskId);
		if (Filters != null)
			output.Attributes.SetAttribute("filters", JsonSerializer.Serialize(Filters, ConfigHelper.JsonOptionsCamel));
		
		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
		
		// build the table header for the autocomplete
		if (Columns != null)
		{
			var head = new StringBuilder("<lvl-head>");
			head.Append(Columns.Aggregate(
							"",
							(current, column) => current + $"<lvl-th name=\"{column.Name}\" type=\"{column.Type.GetTypeAsString()}\"{(column.Hidden ? " hidden" : "")}" +
												 $"{(column.HideIfEmpty ? " hide-if-empty" : "")}" + $"{(!string.IsNullOrEmpty(column.Sign) ? $" sign=\"{column.Sign}\"" : "")}" +
												 $"{(column.Type == DataType.Double ? $" decimal-places=\"{column.DecimalPlaces ?? 0}\"" : "")}>{column.Label}</lvl-th>"));
			head.Append("</lvl-head>");
			output.Content.AppendHtml(head.ToString());
		}
		
		if (Options == null)
			return;
		
		// build the options for the autocomplete, with single or multiple display values
		var stringBuilder = new StringBuilder("<lvl-body>");
		foreach (var option in Options)
		{
			var columns = "";
			if (option.Columns != null)
			{
				columns = option.Columns.Aggregate(columns, (current, columnValue) => current + $"<lvl-td>{ParseValue(columnValue)}</lvl-td>");
			}
			
			stringBuilder.Append(
				$"<lvl-option value=\"{option.Value}\"{(option.Readonly ? " readonly" : "")}>{(option.Columns.IsNullOrEmpty() ? ParseValue(option.Label) : columns)}</lvl-option>");
		}
		
		stringBuilder.Append("</lvl-body>");
		output.Content.AppendHtml(stringBuilder.ToString());
	}
	
	// Parsing values to display - needs to be centralized later?
	private static string ParseValue(object? valueToParse)
	{
		string resultString;
		if (valueToParse is DateTime dateTime)
			resultString = dateTime.ToString(CultureInfo.CurrentCulture);
		else if (valueToParse is Double doubleValue)
			resultString = doubleValue.ToString(CultureInfo.CurrentCulture);
		else
			resultString = valueToParse?.ToString() ?? string.Empty;
		
		return resultString;
	}
}