using System.Diagnostics.CodeAnalysis;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// Vertical line separator that has the current text height or a horizontal line that goes across the whole wrapper
/// </summary>
[ExcludeFromCodeCoverage]
public class SeparatorTagHelper : TagHelper
{
	/// <summary>
	/// Direction of the line
	/// Default is Vertical.
	/// </summary>
	public DirectionType Direction { get; set; } = DirectionType.Vertical;
	
	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "span";
		
		List<string> classes = ["separator", $"separator--{Direction.GetDirectionTypeAsString()}"];
		output.Attributes.SetAttribute("class", string.Join(" ", classes));
	}
}