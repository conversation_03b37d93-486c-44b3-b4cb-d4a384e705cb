using System.Diagnostics.CodeAnalysis;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-viewer
/// </summary>
[ExcludeFromCodeCoverage]
public class ViewerComponentTagHelper : TagHelper
{
	/// <summary>
	/// Do the viewer non-editable
	/// </summary>
	public bool Readonly { get; set; } = false;
	
	/// <summary>
	/// Its used as author label for new comments
	/// </summary>
	public string? User { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-viewer";

		if (Readonly)
			output.Attributes.SetAttribute("readonly", "");
		
		if (!string.IsNullOrEmpty(User))
			output.Attributes.SetAttribute("user", User);

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}