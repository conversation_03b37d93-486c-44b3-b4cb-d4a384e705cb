using System.Text.Json;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-list-data-column
/// </summary>
public class ListDataColumnComponentTagHelper : BaseColumnComponentTagHelper
{
	/// <summary>
	/// Number of decimal places for numbers displayed in the column
	/// </summary>
	public int? DecimalPlaces { get; set; }

	/// <summary>
	/// Optional unit symbol
	/// </summary>
	public string? Sign { get; set; }

	/// <summary>
	/// Should the column be hidden?
	/// </summary>
	public bool Hidden { get; set; } = false;
	
	/// <summary>
	/// Should the label be hidden?
	/// </summary>
	public bool HideLabel { get; set; } = false;

	/// <summary>
	/// Is the content of column editable?
	/// </summary>
	public bool LiveEditable { get; set; }

	/// <summary>
	/// Display a thousand separator?
	/// </summary>
	public bool UseThousandSeparator { get; set; } = false;
	
	/// <summary>
	/// Display as HTML?
	/// </summary>
	public bool RichText { get; set; } = false;

	/// <summary>
	/// Data type of the columns content
	/// </summary>
	public DataColumnType? Type { get; set; }

	/// <summary>
	/// Alignment of the content within the column
	/// </summary>
	public Alignment? TextAlign { get; set; }

	/// <summary>
	/// Is this column only a representation column for the real data? What is the real data column name?
	/// </summary>
	public string? ReferenceField { get; set; }

	/// <summary>
	/// List of values which are passed to the column as possible static options 
	/// </summary>
	public List<DataColumnValueDefinition>? Values { get; set; }
	
	/// <summary>
	/// If the columnType is Icon than this icon will be rendered. Add LiveEditable = true to enable the toggling function
	/// </summary>
	public string? Icon { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-list-data-column";
		
		if (Type != null)
			output.Attributes.SetAttribute("type", ((DataColumnType)Type).GetTypeAsString());
		if (TextAlign != null)
			output.Attributes.SetAttribute("text-align", ((Alignment)TextAlign).GetAlignmentAsString());
		if (DecimalPlaces != null)
			output.Attributes.SetAttribute("decimal-places", DecimalPlaces);
		if (Sign != null)
			output.Attributes.SetAttribute("sign", Sign);
		if (Hidden)
			output.Attributes.SetAttribute("hidden", "");
		if (HideLabel)
			output.Attributes.SetAttribute("hide-label", "");
		if (LiveEditable)
			output.Attributes.SetAttribute("live-editable", "");
		if (UseThousandSeparator)
			output.Attributes.SetAttribute("use-thousand-separator", "");
		if (RichText)
			output.Attributes.SetAttribute("rich-text", "");
		if (ReferenceField != null)
			output.Attributes.SetAttribute("ref", ReferenceField);
		if (!string.IsNullOrEmpty(Icon))
			output.Attributes.SetAttribute("icon", Icon);
		
		if (Values is { Count: > 0 })
		{
			var filterString = JsonSerializer.Serialize(Values, ConfigHelper.JsonOptionsCamel);
			output.Attributes.SetAttribute("values", filterString);
		}

		ProcessBaseOutput(output, context);
	}
}