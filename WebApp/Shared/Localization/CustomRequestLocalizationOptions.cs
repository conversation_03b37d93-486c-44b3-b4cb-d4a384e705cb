using Microsoft.Extensions.Options;

namespace Levelbuild.Frontend.WebApp.Shared.Localization;

/// <summary>
/// Used to register CustomCultureProvider and inject RequestLocalizationOptions
/// </summary>
public class CustomRequestLocalizationOptions : IConfigureOptions<RequestLocalizationOptions>
{
	/// <summary>
	/// Register CustomCultureProvider as RequestCultureProvider
	/// </summary>
	/// <param name="options">contains supported request cultures (and gets extended inside CustomCultureProvider)</param>
	public void Configure(RequestLocalizationOptions options)
	{
		// Add custom culture provider
		options.RequestCultureProviders.Insert(0, new CustomCultureProvider(options));
	}
}