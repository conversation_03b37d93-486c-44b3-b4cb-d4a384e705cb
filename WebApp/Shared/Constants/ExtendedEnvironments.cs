namespace Levelbuild.Frontend.WebApp.Shared.Constants;

/// <summary>
/// Extended environment names.
/// </summary>
public static class ExtendedEnvironments
{
	/// <inheritdoc cref="Environments.Development"/>>
	public static readonly string Development = Environments.Development;
	
	/// <inheritdoc cref="Environments.Staging"/>>
	public static readonly string Staging = Environments.Staging;
	
	/// <inheritdoc cref="Environments.Production"/>>
	public static readonly string Production = Environments.Production;
	
	/// <summary>
	/// Specifies the Testing environment.
	/// </summary>
	/// <remarks>The testing environment should use non-persistent database to enable clean testing without leaving garbage behind.</remarks>
	public static readonly string Testing = "Testing";
}