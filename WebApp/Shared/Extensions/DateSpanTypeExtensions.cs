using System.Linq.Expressions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared.Enums;

namespace Levelbuild.Frontend.WebApp.Shared.Extensions;

/// <summary>
/// DateSpan Extension for more functionality 
/// </summary>
public static class DateSpanTypeExtensions
{
	// TODO: we need to evaluate the user timezone (at least for DateTime columns)
	/// <summary>
	/// Transforms DateSpan compareValue to a real date filter
	/// </summary>
	/// <param name="dateSpanType"></param>
	/// <param name="filterField"></param>
	/// <returns></returns>
	public static QueryFilterGroup GetQueryFilterGroup(this DateSpanType dateSpanType, QueryFilterField filterField)
	{
		var today = DateTime.UtcNow.Date;
		var dayOfWeekIndex = today.DayOfWeek == DayOfWeek.Sunday ? 6 : (int)today.DayOfWeek - 1;
		switch (dateSpanType)
		{
			case DateSpanType.Today:
				return new QueryFilterGroup([
					new GreaterThanEqualsFilter(filterField, today),
					new LessThanFilter(filterField, today.AddDays(1))
				]);
			case DateSpanType.Yesterday:
				return new QueryFilterGroup([
					new GreaterThanEqualsFilter(filterField, today.AddDays(-1)),
					new LessThanFilter(filterField, today)
				]);
			case DateSpanType.CurrentWeek:
				return new QueryFilterGroup([
					new GreaterThanEqualsFilter(filterField, today.AddDays(-dayOfWeekIndex)), 
					new LessThanFilter(filterField, today.AddDays(7 - dayOfWeekIndex))
				]);
			case DateSpanType.LastWeek:
				return new QueryFilterGroup([
					new GreaterThanEqualsFilter(filterField, today.AddDays(-dayOfWeekIndex - 7)),
					new LessThanFilter(filterField, today.AddDays(-dayOfWeekIndex))
				]);
			case DateSpanType.CurrentMonth:
				var nextMonth = today.AddMonths(1);
				return new QueryFilterGroup([
					new GreaterThanEqualsFilter(filterField, GetDate(today.Year, today.Month, 1)),
					new LessThanFilter(filterField, GetDate(nextMonth.Year, nextMonth.Month, 1))
				]);
			case DateSpanType.LastMonth:
				var lastMonth = today.AddMonths(-1);
				return new QueryFilterGroup([
					new GreaterThanEqualsFilter(filterField, GetDate(lastMonth.Year, lastMonth.Month, 1)),
					new LessThanFilter(filterField, GetDate(today.Year, today.Month, 1))
				]);
			case DateSpanType.CurrentYear:
				return new QueryFilterGroup([
					new GreaterThanEqualsFilter(filterField, GetDate(today.Year, 1, 1)),
					new LessThanFilter(filterField, GetDate(today.AddYears(1).Year, 1, 1))
				]);
			case DateSpanType.LastYear:
				return new QueryFilterGroup([
					new GreaterThanEqualsFilter(filterField, GetDate(today.AddYears(-1).Year, 1, 1)),
					new LessThanFilter(filterField, GetDate(today.Year, 1, 1))
				]);
			case DateSpanType.Older:
				return new QueryFilterGroup([
					new LessThanFilter(filterField, GetDate(today.AddYears(-1).Year, 1, 1)),
					new IsNullFilter(filterField)
				], QueryFilterLinkType.Or);
			default:
				throw new ArgumentNullException();
		}
	}

	/// <summary>
	/// Build expressions according to a data span
	/// </summary>
	/// <param name="dateSpanType"></param>
	/// <param name="filterField"></param>
	/// <typeparam name="TEntity"></typeparam>
	/// <returns></returns>
	/// <exception cref="ArgumentNullException"></exception>
	public static Expression GetExpression<TEntity>(this DateSpanType dateSpanType, string filterField) where TEntity : class
	{
		var today = DateTime.UtcNow.Date;
		var dayOfWeekIndex = today.DayOfWeek == DayOfWeek.Sunday ? 6 : (int)today.DayOfWeek - 1;
		Expression left, right;
		switch (dateSpanType)
		{
			case DateSpanType.Today:
				left = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.GreaterThanEquals, today);
				right = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.LessThan, today.AddDays(1));
				return ExpressionExtension.AndAlso<TEntity>(left, right);
			case DateSpanType.Yesterday:
				left = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.GreaterThanEquals, today.AddDays(-1));
				right = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.LessThan, today);
				return ExpressionExtension.AndAlso<TEntity>(left, right);
			case DateSpanType.CurrentWeek:
				left = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.GreaterThanEquals, today.AddDays(-dayOfWeekIndex));
				right = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.LessThan, today.AddDays(7 - dayOfWeekIndex));
				return ExpressionExtension.AndAlso<TEntity>(left, right);
			case DateSpanType.LastWeek:
				left = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.GreaterThanEquals, today.AddDays(-dayOfWeekIndex - 7));
				right = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.LessThan, today.AddDays(-dayOfWeekIndex));
				return ExpressionExtension.AndAlso<TEntity>(left, right);
			case DateSpanType.CurrentMonth:
				var nextMonth = today.AddMonths(1);
				left = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.GreaterThanEquals, GetDate(today.Year, today.Month, 1));
				right = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.LessThan, GetDate(nextMonth.Year, nextMonth.Month, 1));
				return ExpressionExtension.AndAlso<TEntity>(left, right);
			case DateSpanType.LastMonth:
				var lastMonth = today.AddMonths(-1);
				left = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.GreaterThanEquals, GetDate(lastMonth.Year, lastMonth.Month, 1));
				right = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.LessThan, GetDate(today.Year, today.Month, 1));
				return ExpressionExtension.AndAlso<TEntity>(left, right);
			case DateSpanType.CurrentYear:
				left = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.GreaterThanEquals, GetDate(today.Year, 1, 1));
				right = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.LessThan, GetDate(today.AddYears(1).Year, 1, 1));
				return ExpressionExtension.AndAlso<TEntity>(left, right);
			case DateSpanType.LastYear:
				left = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.GreaterThanEquals, GetDate(today.AddYears(-1).Year, 1, 1));
				right = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.LessThan, GetDate(today.Year, 1, 1));
				return ExpressionExtension.AndAlso<TEntity>(left, right);
			case DateSpanType.Older:
				left = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.GreaterThanEquals, GetDate(today.AddYears(-1).Year, 1, 1));
				right = ExpressionExtension.BuildFilterExpression<TEntity>(filterField, QueryParamFilterOperator.IsNull, null);
				return ExpressionExtension.OrElse<TEntity>(left, right);
			default:
				throw new ArgumentNullException();
		}
	}

	private static DateTime GetDate(int year, int month, int day)
	{
		return DateTime.SpecifyKind(new DateTime(year, month, day), DateTimeKind.Utc);
	}
}