using System.Text.Json;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using ILogger = Serilog.ILogger;

namespace Levelbuild.Frontend.WebApp.Shared.Extensions;

internal class JsonQueryBinder : IModelBinder
{
	private readonly ILogger _logger;

	public JsonQueryBinder(ILogManager logManager)
	{
		_logger = logManager.GetLoggerForClass<JsonQueryBinder>();
	}

	public Task BindModelAsync(ModelBindingContext bindingContext)
	{
		var value = bindingContext.ValueProvider.GetValue(bindingContext.FieldName).FirstValue;
		if (value == null)
		{
			return Task.CompletedTask;
		}

		try
		{
			var parsedJson = JsonSerializer.Deserialize(value, bindingContext.ModelType, new JsonSerializerOptions(JsonSerializerDefaults.Web));
			bindingContext.Result = ModelBindingResult.Success(parsedJson);
		}
		catch (Exception e)
		{
			_logger.Error(e, "Failed to bind parameter '{FieldName} = {Value}'", bindingContext.FieldName, value);
			bindingContext.Result = ModelBindingResult.Failed();
		}

		return Task.CompletedTask;
	}
}

internal class FromJsonQueryAttribute : ModelBinderAttribute
{
	public FromJsonQueryAttribute()
	{
		BinderType = typeof(JsonQueryBinder);
	}
}