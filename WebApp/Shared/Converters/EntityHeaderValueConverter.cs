using System.Text.Json;
using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Frontend.WebApp.Shared.Extensions;

namespace Levelbuild.Frontend.WebApp.Shared.Converters;

/// <summary>
/// JSON converter that excludes all non-HeaderValues from query results.
/// </summary>
/// <typeparam name="TEntityDto">The DTO to be converted</typeparam>
public class EntityHeaderValueConverter<TEntityDto> : JsonConverter<TEntityDto> where TEntityDto : EntityDto
{
	/// <inheritdoc />
	public override TEntityDto Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
	{
		throw new NotImplementedException();
	}
	
	/// <inheritdoc />
	public override void Write(Utf8JsonWriter writer, TEntityDto entity, JsonSerializerOptions options)
	{
		// find header properties
		var headerProperties = entity.GetType().GetProperties().Where(property => Attribute.IsDefined(property, typeof(HeaderValueAttribute)));

		// write properties to object
		writer.WriteStartObject();
		foreach (var property in headerProperties)
		{
			writer.WritePropertyName(property.Name.ToLowerFirstChar());
			writer.WriteRawValue($"{JsonSerializer.Serialize(property.GetValue(entity))}");
		}

		writer.WriteEndObject();
	}
}

/// <summary>
/// Factory pattern for the <see cref="EntityHeaderValueConverter{TEntityDto}"/> class.
/// </summary>
public class EntityHeaderValueConverterFactory : JsonConverterFactory
{
	/// <inheritdoc />
	public override bool CanConvert(Type typeToConvert)
	{
		return typeToConvert.IsSubclassOf(typeof(EntityDto));
	}
	
	/// <inheritdoc />
	public override JsonConverter? CreateConverter(Type typeToConvert, JsonSerializerOptions options)
	{
		var converterType = typeof(EntityHeaderValueConverter<>).MakeGenericType(typeToConvert);
		return (JsonConverter?)Activator.CreateInstance(converterType);
	}
}