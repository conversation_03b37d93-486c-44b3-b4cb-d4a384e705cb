@using System.Text.Json
@using Humanizer
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@using Microsoft.AspNetCore.Html
@using Microsoft.IdentityModel.Tokens
@model Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu.BasicMenuModel
@inject IExtendedStringLocalizerFactory LocalizerFactory
@{
	var adminDefaultUrl = $"/Admin/{Model.RouteName}";
	var kebabEntity = Model.EntityName.Kebaberize();
	var localizer = LocalizerFactory.Create(Model.EntityName, "menu");
	var infoLocalizer = LocalizerFactory.Create(Model.EntityName, "menu/info");
	var itemLocalizer = LocalizerFactory.Create(Model.EntityName, "menu/item");
}

@{
	void GetMenuItemComponent(MenuItem item, int? index = null)
	{
		var selected = item.Selected || (Model.Type == BasicMenuType.ViewSwitcher && (ViewData["targetMenu"]?.ToString() == item.Value || (ViewData["targetMenu"] == null && index == 0)));
		var data = item.Data != null ? JsonSerializer.Serialize(item.Data, ConfigHelper.DefaultJsonOptions) : null;
		var urlParams = item.UrlParams != null ? JsonSerializer.Serialize(item.UrlParams, ConfigHelper.DefaultJsonOptions) : null;
		var subItems = item.Items ?? new List<MenuItem>();
		
		<lvl-side-nav-item
			icon="@(item.Icon ?? "")"
			label="@(Model.Type == BasicMenuType.ViewSwitcher ? itemLocalizer[item.Label].Value : item.Label)"
			value="@(item.Value)"
			state="@(Model.ShowState ? (item.Enabled ? ColorState.Success : ColorState.Inactive) : null)"
			selected="@(selected)"
			data="@(data ?? "")"
			skeleton="@(Model.Skeleton)"
			url-params="@(urlParams ?? "")">
			@foreach (var subItem in subItems)
			{
				GetMenuItemComponent(subItem);
			}
		</lvl-side-nav-item>
	}
}

<style>
	/* prevents flicker of page content */
	@($"#{kebabEntity}-menu"):not(:defined) {
		width: @(Model.Width ?? 220)px
	}

	.menu__no-data {
		display:flex;
		flex-direction: column;
		align-items: center;
		gap:var(--size-spacing-l);
		text-align: center;
		color: var(--clr-text-secondary);

		& .no-data__description {
			font-size: var(--size-text-s);
		}
	}
	
	.no-info {
		display: none;
	}
</style>
<side-nav-component id="@(kebabEntity)-menu" heading="@(Model.Title ?? localizer["title"])" width="@(Model.Width ?? 220)" show-state="@(Model.ShowState)" skeleton="@(Model.Skeleton)">
	@{ int index = 0; }
	@foreach (var item in Model.MenuItems)
	{
		GetMenuItemComponent(item, index);
		index++;
	}
	@foreach (var info in Model.MenuInfos)
	{
		<side-nav-info-component id="@(info.Identifier)" label="@(infoLocalizer[info.Label])" skeleton="@(Model.Skeleton)" class="@(String.IsNullOrEmpty(info.TextLeft) ? "no-info" : "")">
			<span>
				<value-formatter name="@(info.Identifier)-left" value="@info.TextLeft" type="@info.ValueLeftType"></value-formatter>
			</span>
			@if (info.Icon != null)
			{
				<button-component slot="right" name="@(info.Identifier)-right" class="@(info.IconLink != null ? "menu-info__link" : "")" icon="@(info.Icon)" data-href="@(info.IconLink ?? "")"></button-component>
			}
			else
			{
				<span slot="right">
					<value-formatter name="@(info.Identifier)-right" value="@info.TextRight" type="@info.ValueRightType"></value-formatter>
				</span>
			}
		</side-nav-info-component>
	}
	@if (Model.Type == BasicMenuType.ListFilter)
	{
		<div slot="no-data" class="menu__no-data">
			<span class="no-data__heading">@localizer["menu/noDataHeading"]</span>
			<span class="no-data__description">@localizer["menu/noDataDescription"]</span>
			<lvl-button type="secondary" label="@localizer["menu/noDataButton"]" trailing-icon="arrow-up-right-from-square" color="info"></lvl-button>
		</div>
	}
</side-nav-component>
<script type="module" defer>
	@if (Model.Type == BasicMenuType.ListFilter)
	{
		<text>
			const menu = document.getElementById('@(kebabEntity)-menu') ?? document.querySelector('lvl-side-nav')
			let selectItem
			if (Page.getFormData()?.id)
				selectItem = menu.querySelector(`lvl-side-nav-item[value="${Page.getFormData().id}"]`)
			
			if (selectItem == null) {
				// set data store in menu if not set via url
				const localStoreId = localStorage.getItem('lvl:user:config:@(kebabEntity):parent-id')
				
				//check if saved store id is still existing
				selectItem = menu.querySelector(`lvl-side-nav-item[value="${localStoreId}"]`) ?? menu.querySelector('lvl-side-nav-item:first-child')
				if (selectItem) {
					localStorage.setItem('lvl:user:config:@(kebabEntity):parent-id', selectItem.value)
					const data = JSON.parse(selectItem.getAttribute('data'))
					Page.saveFormData(data) 
				}
			}
			
			if (selectItem != null && !selectItem.hasAttribute('selected')){
				selectItem.setAttribute('selected', '')
				updateUrlBySlug(Page.getFormData().slug)
			}
			
			menu.addEventListener('nav-item:click', async (mouseEvent) => {
				const item = mouseEvent.target
				if (item == null)
					return
				
				@{
					//TODO: Load data afterwards via url but no url available currently
					// if (item.data == null)
					//	item.data = await Page.getJSON(`apiUrl/${item.value}`)
				}       								
				Page.saveFormData(item.data ?? {})
				localStorage.setItem('lvl:user:config:@(kebabEntity):parent-id', item.value)
				
				updateUrlBySlug(Page.getFormData().slug)
				// trigger a change in the filtering
				mouseEvent.target.dispatchEvent(new MouseEvent('parent-data:changed', { bubbles: true }));
			}, {capture: true})
			
			// needed for cypress to know when it can start using the menu
			menu.setAttribute('initDone', '');
			
			function getBaseRouteWithParentSlug(slug){
				if (!slug)
					return 
				return '@(Model.BaseRoute)'.endsWith('-') ? '@(Model.BaseRoute)'.replace(/\/-$/g, `/${slug}`) : '@(Model.BaseRoute)'.replace(/\/-\//g, `/${slug}/`);
			}
			
			// exchange the parent slug in the browser url with the new selected menu item value
			function updateUrlBySlug(slug) {
				const url = getBaseRouteWithParentSlug(slug)
				Page.setInfo(url)
				Page.setMainPage(url)
				Page.updateCurrentBreadcrumb({ url: url })
			}
		</text>
	}
	else if (Model.Type == BasicMenuType.ViewSwitcher)
	{
		<text>
		    let menuParams
			@if (!Model.RouteParams.IsNullOrEmpty())
		    {
			    <text>
                	menuParams = @(new HtmlString(Model.RouteParams))
                </text>
		    }
			Page.registerMenu(document.getElementById('@(kebabEntity)-menu'), '@(Model.ViewType == ViewType.Edit ? $"{adminDefaultUrl}/Edit" : adminDefaultUrl)', menuParams)
	    </text>
	}
    	
	// link button in the info part of the menu has to open the selected data store config
	const handleMenuInfoLinkClick = (event) => window.open(event.target.dataset['href'], '_blank')
	
	document.querySelectorAll('#@(kebabEntity)-menu .menu-info__link')?.forEach(infoLink => {
		infoLink.addEventListener('click', handleMenuInfoLinkClick, { signal: Page.getPageChangeSignal() })
	})
</script>