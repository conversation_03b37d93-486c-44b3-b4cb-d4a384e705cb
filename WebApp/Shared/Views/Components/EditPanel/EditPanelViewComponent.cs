using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace Levelbuild.Frontend.WebApp.Shared.Views.Components.EditPanel;

/// <summary>
/// Component for the edit panel template
/// </summary>
public class EditPanelViewComponent : ViewComponent
{
	/// <summary>
	/// Default method to render a view component
	/// </summary>
	/// <param name="entity">Name of the WebAppEntity in PascalCase</param>
	/// <param name="routeName">Entity name which is used in url routes</param>
	/// <param name="heading">Heading for the panel</param>
	/// <param name="localizer">Used to translate labels</param>
	/// <param name="menuEntry">Is the access of the create-panel part of a menu view</param>
	/// <param name="displayPropertyName">Where can the display value be found?</param>
	/// <param name="width">Width of the edit panel (default = 370)</param>
	/// <param name="skeleton">show skeleton loading?</param>
	/// <param name="routeParams">Optional Get Params for the URL</param>
	/// <param name="ignoreOverflow">content handles scrolling?</param>
	/// <returns></returns>
	public IViewComponentResult Invoke(string entity, string routeName, string heading, IStringLocalizer localizer, string? menuEntry = null,
									   string? displayPropertyName = null, int? width = null, bool skeleton = false, string? routeParams = null,
									   bool ignoreOverflow = false)
	{
		EditPanelModel model = new()
		{
			EntityName = entity,
			RouteName = routeName,
			RouteParams = routeParams,
			Heading = heading,
			Localizer = localizer,
			MenuEntry = menuEntry,
			DisplayPropertyName = displayPropertyName,
			Width = width ?? 550,
			Skeleton = skeleton,
			IgnoreOverflow = ignoreOverflow
		};

		return View(model);
	}
}

/// <summary>
/// Model for the edit panel template
/// </summary>
public class EditPanelModel
{
	/// <summary>
	/// Name of the WebAppEntity in PascalCase
	/// </summary>
	public required string EntityName { get; init; }

	/// <summary>
	/// Entity name which is used in url routes
	/// </summary>
	public required string RouteName { get; init; }

	/// <summary>
	/// Heading for the panel
	/// </summary>
	public required string Heading { get; init; }

	/// <summary>
	/// Used to translate labels
	/// </summary>
	public required IStringLocalizer Localizer { get; init; }

	/// <summary>
	/// Is the access of the create-panel part of a menu view
	/// </summary>
	public string? MenuEntry { get; init; }

	/// <summary>
	/// Where can the display value be found?
	/// </summary>
	public string? DisplayPropertyName { get; init; }
	
	/// <summary>
	/// Width of the edit panel (default = 370)
	/// </summary>
	public int Width { get; set; }

	/// <summary>
	/// show skeleton loading?
	/// </summary>
	public bool Skeleton { get; init; }
	
	/// <summary>
	/// Optional Get Params for the URL
	/// </summary>
	public string? RouteParams { get; init; }
	
	/// <summary>
	/// Content handles scrolling
	/// </summary>
	public bool IgnoreOverflow { get; init; }
}