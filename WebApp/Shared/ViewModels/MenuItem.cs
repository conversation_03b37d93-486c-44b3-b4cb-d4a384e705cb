namespace Levelbuild.Frontend.WebApp.Shared.ViewModels;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

/// <summary>
/// Dto to build a SideNavItem Component
/// </summary>
public class MenuItem
{
	/// <summary>
	/// Label key for translation
	/// </summary>
	public string Label { get; private init; }
	
	/// <summary>
	/// Value attribute
	/// </summary>
	public string Value { get; private init; }
	
	/// <summary>
	/// Is the menu item currently activated
	/// </summary>
	public bool Selected { get; private init; }
	
	/// <summary>
	/// Optional icon which is displayed first 
	/// </summary>
	public string? Icon { get; private init; }
	
	/// <summary>
	/// Used to show the state of the item
	/// </summary>
	public bool Enabled { get; private init; }
	
	/// <summary>
	/// Serializable dto of the item
	/// </summary>
	public object? Data { get; private init; }
	
	/// <summary>
	/// optional url parameters which get appended to the auto-generated url when requesting the content of this menu-entry from the server
	/// </summary>
	public Dictionary<string, object>? UrlParams { get; init; }
	
	/// <summary>
	/// Menu items can be nested
	/// </summary>
	public IList<MenuItem>? Items { get; init; }
	
	/// <summary></summary>
	/// <param name="label">Label key for translation</param>
	/// <param name="value">Value attribute</param>
	/// <param name="selected">Is the menu item currently activated</param>
	/// <param name="icon">Optional icon which is displayed first </param>
	/// <param name="enabled">Used to show the state of the item</param>
	/// <param name="data">Serializable dto of the item</param>
	public MenuItem(string label, string value, string? icon = null, bool? selected = false, bool? enabled = false, object? data = null)
	{
		Label = label;
		Value = value;
		Selected = selected == true;
		Icon = icon;
		Enabled = enabled == true;
		Data = data;
	}
}