using Levelbuild.Core.FrontendDtos.Enums;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Shared.ViewModels;

/// <summary>
/// Dto to build a SideNavInfo Component
/// </summary>
public class MenuInfo
{
	/// <summary>
	/// Id attribute to identify the info 
	/// </summary>
	public string? Identifier { get; private init; }

	/// <summary>
	/// Text over the single info 
	/// </summary>
	public string Label { get; private init; } = "";

	/// <summary>
	/// Text on the infos left side
	/// </summary>
	public string TextLeft { get; private init; } = "";

	/// <summary>
	/// DataType of the left text value
	/// </summary>
	public InputDataType ValueLeftType { get; private init; } = InputDataType.String;

	/// <summary>
	/// Text on the infos right side
	/// </summary>
	public string? TextRight { get; private init; }

	/// <summary>
	/// DataType of the right text value
	/// </summary>
	public InputDataType ValueRightType { get; private init; } = InputDataType.String;

	/// <summary>
	/// Icon on the infos right side
	/// </summary>
	public string? Icon { get; private init; }

	/// <summary>
	/// Href of the icon
	/// </summary>
	public string? IconLink { get; private init; }

	/// <summary>
	/// Creates a menu info with text on the left and on the right side 
	/// </summary>
	/// <param name="identifier"></param>
	/// <param name="label"></param>
	/// <param name="left"></param>
	/// <param name="right"></param>
	/// <returns></returns>
	public static MenuInfo GetTextInstance(string? identifier, string label, string left, string? right = null)
	{
		return new MenuInfo()
		{
			Identifier = identifier,
			Label = label,
			TextLeft = left,
			TextRight = right
		};
	}

	/// <summary>
	/// Creates a menu info with text on the left and on the right side 
	/// </summary>
	/// <param name="identifier"></param>
	/// <param name="label"></param>
	/// <param name="left"></param>
	/// <param name="leftType"></param>
	/// <param name="right"></param>
	/// <param name="rightType"></param>
	/// <returns></returns>
	public static MenuInfo GetTextInstance(string? identifier, string label, string left, InputDataType leftType, string? right = null,
										   InputDataType rightType = InputDataType.String)
	{
		return new MenuInfo()
		{
			Identifier = identifier,
			Label = label,
			TextLeft = left,
			ValueLeftType = leftType,
			TextRight = right,
			ValueRightType = rightType
		};
	}

	/// <summary>
	/// Creates a menu info with text on the left and a go-to page icon on the right side including href 
	/// </summary>
	/// <param name="identifier"></param>
	/// <param name="label"></param>
	/// <param name="text"></param>
	/// <param name="link"></param>
	/// <returns></returns>
	public static MenuInfo GetLinkInstance(string? identifier, string label, string text, string link)
	{
		return new MenuInfo()
		{
			Identifier = identifier,
			Label = label,
			TextLeft = text,
			Icon = "arrow-up-right-from-square",
			IconLink = link
		};
	}

	/// <summary>
	/// Creates a menu info with text on the left and a custom icon on the right side 
	/// </summary>
	/// <param name="identifier"></param>
	/// <param name="label"></param>
	/// <param name="text"></param>
	/// <param name="icon"></param>
	/// <returns></returns>
	public static MenuInfo GetIconInstance(string? identifier, string label, string text, string icon)
	{
		return new MenuInfo()
		{
			Identifier = identifier,
			Label = label,
			TextLeft = text,
			Icon = icon
		};
	}
}