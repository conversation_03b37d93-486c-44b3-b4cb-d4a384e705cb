using System.Text.RegularExpressions;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Frontend.WebApp.Shared.Utils;

/// <summary>
/// Utility class for pages
/// </summary>
public static class PageUtils
{
	/// <summary>
	/// Try to get a property value.
	/// </summary>
	/// <param name="source"></param>
	/// <param name="propertyName"></param>
	/// <param name="propertyValue"></param>
	/// <returns>True if value was found</returns>
	public static bool TryGetPropertyValue(object source, string propertyName, out object? propertyValue)
	{
		var propertyInfo = source.GetType().GetProperty(propertyName);
		if (propertyInfo == null)
		{
			propertyValue = default;
			return false;
		}

		propertyValue = propertyInfo.GetValue(source);
		return propertyValue != null;
	}
	
	/// <summary>
	/// Get a property value.
	/// </summary>
	/// <param name="source"></param>
	/// <param name="propertyName"></param>
	/// <returns>The value.</returns>
	public static object? GetPropertyValue(object source, string propertyName)
	{
		var propertyInfo = source.GetType().GetProperty(propertyName);
		if (propertyInfo == null)
		{
			return null;
		}

		return propertyInfo.GetValue(source);
	}

	/// <summary>
	/// Replace placeholder strings with the wanted value of a given element.
	/// </summary>
	/// <param name="input"></param>
	/// <param name="values"></param>
	/// <returns></returns>
	public static string ReplacePlaceholders(string input, IDictionary<string, object?> values)
	{
		if (input.IsNullOrEmpty())
			return "";
		
		if (!input.Contains("##"))
			return input;

		return Regex.Replace(input, "##([a-zA-Z0-9_-]+)##", match => 
		{
			string fieldName = match.Groups[1].ToString();
			
			if (values.TryGetValue(fieldName, out var value))
				return (value != null ? value.ToString() : "") ?? string.Empty;
			
			return match.Value;
		});
	}
}