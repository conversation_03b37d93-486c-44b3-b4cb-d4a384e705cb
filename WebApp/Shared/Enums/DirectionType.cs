using Levelbuild.Core.SharedUtilities;
using Microsoft.OpenApi.Extensions;

using System.Text.Json.Serialization;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Shared.Enums;

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum DirectionType
{
	Horizontal,

	Vertical,
}

public static class DirectionTypeExtensions
{
	public static string GetDirectionTypeAsString(this DirectionType enumValue)
	{
		return enumValue.GetDisplayName().ToLower();
	}

	public static string GetString(this DirectionType enumValue)
	{
		return EnumUtils<DirectionType>.GetTranslatableString(enumValue);
	}
}