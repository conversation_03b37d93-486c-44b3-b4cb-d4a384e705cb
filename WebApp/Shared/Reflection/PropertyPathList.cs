using System.Linq.Expressions;

namespace Levelbuild.Frontend.WebApp.Shared.Reflection;

/// <summary>
/// A List of class property paths that offers various methods to work with said properties.
/// </summary>
public class PropertyPathList<TEntity> : List<PropertyPath> where TEntity : class
{
	/// <summary>
	/// Constructor.
	/// </summary>
	public PropertyPathList() { }
	
	/// <summary>
	/// Constructor.
	/// </summary>
	public PropertyPathList(IEnumerable<PropertyPath> source) : base(source) { }
	
	/// <summary>
	/// Add a path using its path's string representation.
	/// </summary>
	/// <param name="stringPath"></param>
	public void Add(string stringPath)
	{
		var propertyPath = PropertyPath.BuildFromString(stringPath, typeof(TEntity));
		Add(propertyPath);
	}
	
	/// <summary>
	/// Add a path using an expression that defines it.
	/// </summary>
	/// <param name="expression"></param>
	public void Add<T, TProperty>(Expression<Func<T, TProperty>> expression) where T : TEntity
	{
		var propertyPath = PropertyPath.BuildFromExpression(expression);
		Add(propertyPath);
	}
}