using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;

namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// Sorting dto for frontend requests 
/// </summary>
public class QueryParamSortingDto : IResponseObject
{
	/// <summary>
	/// field name of the column to be sorted by 
	/// </summary>
	public string OrderColumn { get; set; } = "";

	/// <summary>
	/// order a list ascending or descending
	/// </summary>
	public SortDirection Direction { get; init; }
	
	/// <summary>
	/// default constructor
	/// </summary>
	public QueryParamSortingDto(){}
	
	/// <summary>
	/// Sorting constructor
	/// </summary>
	/// <param name="orderColumn"></param>
	/// <param name="direction"></param>
	public QueryParamSortingDto(string orderColumn, SortDirection direction)
	{
		OrderColumn = orderColumn;
		Direction = direction;
	}

	/// <summary>
	/// Used to print out this dto in logs
	/// </summary>
	/// <returns></returns>
	public override string ToString()
	{
		return $"[{OrderColumn} {Direction}]";
	}
}