using System.Diagnostics;
using System.IO.Compression;
using System.Net;
using System.Xml;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Domain.Storage.Helper;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.DeepZoom;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Shared.Services.Files;

/// <inheritdoc />
public class DeepZoomHelperService : IDeepZoomHelperService
{
	private readonly IDeepZoomMicroservice _deepZoomMicroservice;

	private readonly IFileCachingService _fileCachingService;

	private readonly Serilog.ILogger _logger;

	//Todo: @TWE
	private readonly ActivitySource _activitySource;

	/// <summary>
	/// constructor
	/// </summary>
	public DeepZoomHelperService(IFileCachingService fileCachingService, IDeepZoomMicroservice deepZoomMicroservice, ILogManager logManager,
								 ActivitySource activitySource)
	{
		_deepZoomMicroservice = deepZoomMicroservice;
		_fileCachingService = fileCachingService;
		_logger = logManager.GetLoggerForClass<DeepZoomHelperService>();
		_activitySource = activitySource;
	}

	/// <inheritdoc />
	public async Task<CachedDeepZoomEntity?> GetAndCacheDeepZoomImageAsync(CoreDatabaseContext databaseContext, string fileId, DataSourceEntity dataSource, int dpi = 300)
	{
		try
		{
			var deepZoomImageId = $"{fileId}_{dpi}";

			await using var fileStream = await dataSource.GetFileAsync(fileId);
			if (fileStream == null)
				return null;
			
			var cachedDeepZoomEntity = await databaseContext.DeepZoomImages.FirstOrDefaultAsync(entity => entity.CachedFileId == deepZoomImageId);
			cachedDeepZoomEntity ??= databaseContext.DeepZoomImages.Add(new CachedDeepZoomEntity{CachedFileId = deepZoomImageId, FileId = fileId}).Entity;
			cachedDeepZoomEntity.State = CachedDeepZoomState.Caching;
			await databaseContext.SaveChangesAsync();

			using var cancellationTokenSource = new CancellationTokenSource();
			var cancellationToken = cancellationTokenSource.Token;
			using var touchingTask = TouchUntilTimeoutCancelable(30, 10, cachedDeepZoomEntity, databaseContext, cancellationToken);
			using var mainTask = CacheDeepZoomCancelable(fileId, dpi, fileStream, cachedDeepZoomEntity, databaseContext, cancellationToken);

			if (await Task.WhenAny(touchingTask, mainTask) == mainTask)
			{
				// Main task completed first
				cachedDeepZoomEntity = await mainTask;
				await cancellationTokenSource.CancelAsync();
			}
			else
			{
				// continuous touching task finished first
				await touchingTask;
				// cancel main task wait for it so the OperationCanceledException is handled internally
				await cancellationTokenSource.CancelAsync();
				cachedDeepZoomEntity = await mainTask;
			}

			return cachedDeepZoomEntity;
		}
		catch (Exception e)
		{
			_logger.Error(e, "Deep zoom image generation failed!");
		}

		return null;
	}

	private async Task<CachedDeepZoomEntity?> CacheDeepZoomCancelable(string fileId, int dpi, DataStoreFileStream fileStream,
																	  CachedDeepZoomEntity cachedDeepZoomEntity, CoreDatabaseContext databaseContext,
																	  CancellationToken cancellationToken)
	{
		var deepZoomImageId = $"{fileId}_{dpi}";


		var composedStream = new MemoryStream();

		try
		{
			var response = await _deepZoomMicroservice.GetDeepZoomFile(fileStream, fileStream.Name, dpi, cancellationToken);

			if (response.StatusCode != HttpStatusCode.OK)
				return null;

			var overlap = 0;
			var height = 0;
			var width = 0;
			var tileSize = 0;

			await using var responseStream = await response.Content.ReadAsStreamAsync(cancellationToken);
			var filePositions = new Dictionary<string, (long, long)>();

			using (var archive = new ZipArchive(responseStream, ZipArchiveMode.Read))
			{
				foreach (var entry in archive.Entries)
				{
					cancellationToken.ThrowIfCancellationRequested();

					var extension = Path.GetExtension(entry.Name);
					if (extension == ".dzi")
					{
						var xmlDocument = new XmlDocument();
						xmlDocument.Load(entry.Open());
						overlap = Int32.Parse(xmlDocument.DocumentElement!.Attributes["Overlap"]!.Value);
						tileSize = Int32.Parse(xmlDocument.DocumentElement!.Attributes["TileSize"]!.Value);
						height = Int32.Parse(xmlDocument.DocumentElement!["Size"]!.Attributes["Height"]!.Value);
						width = Int32.Parse(xmlDocument.DocumentElement!["Size"]!.Attributes["Width"]!.Value);
					}

					if (extension != ".webp")
						continue;

					var splitFullName = entry.FullName.Split('/').ToList();
					splitFullName.RemoveAt(0);
					var fileName = String.Join("/", splitFullName);

					// encrypt the sub file
					await using var entryStream = entry.Open();
					await using var encryptedStream = new MemoryStream((int)((DeflateStream)entryStream).BaseStream.Length);
					_fileCachingService.EncryptFile(entryStream, encryptedStream, $"{cachedDeepZoomEntity.GetDirectoryName()}/{fileId}/{deepZoomImageId}");

					// append it to the composed stream and update the metadata
					var begin = composedStream.Length == 0 ? 0 : composedStream.Length;
					await encryptedStream.CopyToAsync(composedStream, CancellationToken.None);
					var length = composedStream.Length - begin;

					filePositions.Add(fileName, (begin, length));
				}
			}

			// save the whole composed stream
			composedStream.Seek(0, SeekOrigin.Begin);
			await _fileCachingService.UploadFileAsync(composedStream, "DeepZoomImages", fileId, deepZoomImageId, false);
			await composedStream.DisposeAsync();

			cachedDeepZoomEntity.SetMetaData(overlap, tileSize, dpi, width, height, filePositions);
			cachedDeepZoomEntity.State = CachedDeepZoomState.Ready;
			cachedDeepZoomEntity.ErrorMessage = null;
			cachedDeepZoomEntity.ErrorCount = 0;
			await databaseContext.SaveChangesAsync(CancellationToken.None);

			return cachedDeepZoomEntity;
		}
		catch (OperationCanceledException e)
		{
			_logger.Error(e, "Deep zoom image caching Timed Out!");
			cachedDeepZoomEntity.State = CachedDeepZoomState.Failed;
			cachedDeepZoomEntity.ErrorMessage = "Deep Zoom Caching Timed Out!";
			cachedDeepZoomEntity.ErrorCount++;
			await databaseContext.SaveChangesAsync(CancellationToken.None);
			return cachedDeepZoomEntity;
		}
		catch (Exception e)
		{
			_logger.Error(e, "Deep zoom image caching failed!");
			cachedDeepZoomEntity.State = CachedDeepZoomState.Failed;
			cachedDeepZoomEntity.ErrorMessage = e.Message;
			cachedDeepZoomEntity.ErrorCount++;
			await databaseContext.SaveChangesAsync(CancellationToken.None);
			return cachedDeepZoomEntity;
		}
		finally
		{
			await composedStream.DisposeAsync();
		}
	}

	/// <inheritdoc />
	/// TODO: make endpoint cancelable (request may get aborted if user leaves dataset before all request are finished) 
	public async Task<Stream?> GetDeepZoomImageFileAsync(CoreDatabaseContext databaseContext, string fileId, string subFileFolder, string subFileName, int dpi = 300)
	{
		//Todo: @TWE
		using var activity = _activitySource.StartActivity();

		Stream? cachedDeepZoomFile = null;
		try
		{
			using var activityGetDeepZoomEntityFromDb = _activitySource.StartActivity("Get Deep Zoom Entity from DB");
			var cachedDeepZoomEntity = await databaseContext.DeepZoomImages.FirstOrDefaultAsync(entity => entity.FileId == fileId && entity.Dpi == dpi);
			if (cachedDeepZoomEntity == null)
			{
				activityGetDeepZoomEntityFromDb?.SetStatus(ActivityStatusCode.Error);
				return null;
			}

			var fileName = subFileFolder + "/" + subFileName;
			if (cachedDeepZoomEntity.FilePositions?.TryGetValue(fileName, out var startLengthTuple) != true)
			{
				activityGetDeepZoomEntityFromDb?.SetStatus(ActivityStatusCode.Error);
				return null;
			}

			
			activityGetDeepZoomEntityFromDb?.Stop();

			using var activityGetDeepZoomImage = _activitySource.StartActivity("Get Deep Zoom Image from File Caching Service");

			cachedDeepZoomFile = _fileCachingService.GetFile(cachedDeepZoomEntity.GetDirectoryName(), fileId, cachedDeepZoomEntity.CachedFileId, true, startLengthTuple.Item1,
															 startLengthTuple.Item2);
			if (cachedDeepZoomFile == null)
			{
				activityGetDeepZoomImage?.SetStatus(ActivityStatusCode.Error);
				return null;
			}

			activityGetDeepZoomImage?.Stop();

			// touch once per day
			using var activitySetTouchOnDeepZoomEntity = _activitySource.StartActivity("Set Touch on Deep Zoom Entity");
			if (cachedDeepZoomEntity.LastTouched.Date.ToUniversalTime() < DateTime.Today.AddDays(-1).ToUniversalTime())
			{
				activitySetTouchOnDeepZoomEntity?.SetTag("touched_at", DateTime.Today.ToUniversalTime().ToString());
				cachedDeepZoomEntity.Touch(databaseContext);
			}

			activitySetTouchOnDeepZoomEntity?.Stop();

			return cachedDeepZoomFile;
		}
		catch (Exception e)
		{
			_logger.Error(e, "Deep zoom image fetching failed!");
			
			if(cachedDeepZoomFile != null)
				await cachedDeepZoomFile.DisposeAsync();
			
			activity?.SetStatus(ActivityStatusCode.Error, "Deep zoom image fetching failed!");
			return null;
		}
	}

	private async Task TouchUntilTimeoutCancelable(int timeout, int maxIterations, CachedDeepZoomEntity cachedDeepZoomEntity,
												   CoreDatabaseContext databaseContext,
												   CancellationToken cancellationToken)
	{
		using var periodicTimer = new PeriodicTimer(TimeSpan.FromSeconds(timeout));

		for (var i = 0; i < maxIterations; i++)
		{
			if (cancellationToken.IsCancellationRequested)
				return;

			try
			{
				await periodicTimer.WaitForNextTickAsync(cancellationToken);
			}
			catch (OperationCanceledException)
			{
				return;
			}

			cachedDeepZoomEntity.Touch(databaseContext);
		}
	}
	
	/// <inheritdoc />
	public async Task DeleteDeepZoomImagesAsync(CoreDatabaseContext databaseContext, string fileId, CancellationToken cancellationToken = default)
	{
		var deepZoomImages = databaseContext.DeepZoomImages.Where(deepZoom => deepZoom.FileId == fileId).ToList();
		await DeleteDeepZoomImagesAsync(databaseContext, deepZoomImages, cancellationToken);
	}
	
	/// <inheritdoc />
	public async Task DeleteDeepZoomImagesAsync(CoreDatabaseContext databaseContext, IList<CachedDeepZoomEntity> cachedDeepZoomEntities, CancellationToken cancellationToken = default)
	{
		foreach (var deepZoomEntity in cachedDeepZoomEntities.TakeWhile(_ => !cancellationToken.IsCancellationRequested))
		{
			try
			{
				_fileCachingService.DeleteFile(deepZoomEntity.GetDirectoryName(), deepZoomEntity.FileId, deepZoomEntity.CachedFileId);
				databaseContext.DeepZoomImages.Remove(deepZoomEntity);
			}
			catch (FileNotFoundException)
			{
				databaseContext.DeepZoomImages.Remove(deepZoomEntity);
			}
			catch (Exception e)
			{
				_logger.Error($"Deep Zoom Image with id {deepZoomEntity.Id} could not be deleted", e);
			}
		}
		await databaseContext.SaveChangesAsync(CancellationToken.None);
	}
}