namespace Levelbuild.Frontend.WebApp.Shared.Services;

/// <summary>
/// Service to distinguish between different js import module paths, depending on the environment
/// </summary>
public interface IAssetService
{
	/// <summary>
	/// Returns the relative path to the asset depending on build mode (dev / prod).
	/// In DEV mode all ts files have to be provided by the vite server, not by the .Net server
	/// </summary>
	string SolvePath(string path);
}