using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
	/// <inheritdoc />
	public partial class ChangeStorageSchemaChangeErrorIdentifier : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				name: "StorageContextId",
				table: "StorageSchemaChangeError");

			migrationBuilder.AddColumn<string>(
				name: "StorageContextIdentifier",
				table: "StorageSchemaChangeError",
				type: "text",
				nullable: false,
				defaultValue: "");
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				name: "StorageContextIdentifier",
				table: "StorageSchemaChangeError");

			migrationBuilder.AddColumn<long>(
				name: "StorageContextId",
				table: "StorageSchemaChangeError",
				type: "bigint",
				nullable: false,
				defaultValue: 0L);
		}
	}
}