using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
	/// <inheritdoc />
	public partial class ChangeStorageSchemaChangeError : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				name: "StorageSchemaChangeError",
				columns: table => new
				{
					Id = table.Column<long>(type: "bigint", nullable: false)
						.Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
					StorageSchemaChangeHash = table.Column<string>(type: "text", nullable: false),
					StorageContextId = table.Column<long>(type: "bigint", nullable: false),
					DateTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
				},
				constraints: table => { table.PrimaryKey("PK_StorageSchemaChangeError", x => x.Id); });
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				name: "StorageSchemaChangeError");
		}
	}
}