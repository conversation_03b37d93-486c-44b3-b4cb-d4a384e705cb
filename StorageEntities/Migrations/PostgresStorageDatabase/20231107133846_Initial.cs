using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
	/// <inheritdoc />
	public partial class Initial : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterDatabase()
				.Annotation("Npgsql:PostgresExtension:uuid-ossp", ",,");

			migrationBuilder.CreateTable(
				name: "StorageIndexDefinition",
				columns: table => new
				{
					Id = table.Column<long>(type: "bigint", nullable: false)
						.Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
					Name = table.Column<string>(type: "text", nullable: false),
					StoreRevisions = table.Column<bool>(type: "boolean", nullable: false),
					StoreFileContent = table.Column<bool>(type: "boolean", nullable: false)
				},
				constraints: table => { table.PrimaryKey("PK_StorageIndexDefinition", x => x.Id); });

			migrationBuilder.CreateTable(
				name: "StorageLink",
				columns: table => new
				{
					Id = table.Column<string>(type: "text", nullable: false),
					StorageIndexDefinitionId = table.Column<long>(type: "bigint", nullable: false),
					ElementId = table.Column<string>(type: "text", nullable: false),
					LinkedIndexDefinitionId = table.Column<long>(type: "bigint", nullable: false),
					LinkedElementId = table.Column<string>(type: "text", nullable: false),
					LinkedRevisionNumber = table.Column<long>(type: "bigint", nullable: true)
				},
				constraints: table => { table.PrimaryKey("PK_StorageLink", x => x.Id); });

			migrationBuilder.CreateTable(
				name: "StorageFieldDefinition",
				columns: table => new
				{
					Id = table.Column<long>(type: "bigint", nullable: false)
						.Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
					Type = table.Column<int>(type: "integer", nullable: false),
					PrimaryKey = table.Column<bool>(type: "boolean", nullable: false),
					MultiValue = table.Column<bool>(type: "boolean", nullable: false),
					DecimalPlaces = table.Column<int>(type: "integer", nullable: false),
					Unique = table.Column<bool>(type: "boolean", nullable: false),
					Readonly = table.Column<bool>(type: "boolean", nullable: false),
					Name = table.Column<string>(type: "text", nullable: false),
					Nullable = table.Column<bool>(type: "boolean", nullable: false),
					Length = table.Column<int>(type: "integer", nullable: false),
					LookupCheck = table.Column<bool>(type: "boolean", nullable: false),
					Encrypted = table.Column<bool>(type: "boolean", nullable: false),
					SystemField = table.Column<bool>(type: "boolean", nullable: false),
					LookupSourceField = table.Column<string>(type: "text", nullable: true),
					LookupSource = table.Column<string>(type: "text", nullable: true),
					StorageIndexDefinitionId = table.Column<long>(type: "bigint", nullable: false),
					DefaultValueJson = table.Column<string>(type: "text", nullable: true)
				},
				constraints: table =>
				{
					table.PrimaryKey("PK_StorageFieldDefinition", x => x.Id);
					table.ForeignKey(
						name: "FK_StorageFieldDefinition_StorageIndexDefinition_StorageIndexD~",
						column: x => x.StorageIndexDefinitionId,
						principalTable: "StorageIndexDefinition",
						principalColumn: "Id",
						onDelete: ReferentialAction.Cascade);
				});

			migrationBuilder.CreateTable(
				name: "StoragePath",
				columns: table => new
				{
					Id = table.Column<long>(type: "bigint", nullable: false)
						.Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
					Path = table.Column<string>(type: "text", nullable: false),
					Type = table.Column<int>(type: "integer", nullable: false),
					StorageIndexDefinitionId = table.Column<long>(type: "bigint", nullable: false),
					CreateDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
				},
				constraints: table =>
				{
					table.PrimaryKey("PK_StoragePath", x => x.Id);
					table.ForeignKey(
						name: "FK_StoragePath_StorageIndexDefinition_StorageIndexDefinitionId",
						column: x => x.StorageIndexDefinitionId,
						principalTable: "StorageIndexDefinition",
						principalColumn: "Id",
						onDelete: ReferentialAction.Cascade);
				});

			migrationBuilder.CreateTable(
				name: "StorageTempFile",
				columns: table => new
				{
					Id = table.Column<long>(type: "bigint", nullable: false)
						.Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
					FileId = table.Column<long>(type: "bigint", nullable: false),
					FileDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
					FileName = table.Column<string>(type: "text", nullable: false),
					FileSize = table.Column<long>(type: "bigint", nullable: false),
					FileHash = table.Column<string>(type: "text", nullable: false),
					FileType = table.Column<string>(type: "text", nullable: false),
					StorageIndexDefinitionId = table.Column<long>(type: "bigint", nullable: true),
					RevisionNumber = table.Column<long>(type: "bigint", nullable: true),
					ElementId = table.Column<string>(type: "text", nullable: true),
					CreateDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
				},
				constraints: table =>
				{
					table.PrimaryKey("PK_StorageTempFile", x => x.Id);
					table.ForeignKey(
						name: "FK_StorageTempFile_StorageIndexDefinition_StorageIndexDefiniti~",
						column: x => x.StorageIndexDefinitionId,
						principalTable: "StorageIndexDefinition",
						principalColumn: "Id");
				});

			migrationBuilder.CreateIndex(
				name: "IX_StorageFieldDefinition_StorageIndexDefinitionId_Name",
				table: "StorageFieldDefinition",
				columns: new[] { "StorageIndexDefinitionId", "Name" });

			migrationBuilder.CreateIndex(
				name: "IX_StorageIndexDefinition_Name",
				table: "StorageIndexDefinition",
				column: "Name",
				unique: true);

			migrationBuilder.CreateIndex(
				name: "IX_StoragePath_StorageIndexDefinitionId_Path",
				table: "StoragePath",
				columns: new[] { "StorageIndexDefinitionId", "Path" });

			migrationBuilder.CreateIndex(
				name: "IX_StorageTempFile_StorageIndexDefinitionId",
				table: "StorageTempFile",
				column: "StorageIndexDefinitionId");
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				name: "StorageFieldDefinition");

			migrationBuilder.DropTable(
				name: "StorageLink");

			migrationBuilder.DropTable(
				name: "StoragePath");

			migrationBuilder.DropTable(
				name: "StorageTempFile");

			migrationBuilder.DropTable(
				name: "StorageIndexDefinition");
		}
	}
}