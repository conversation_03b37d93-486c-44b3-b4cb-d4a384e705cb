using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
    /// <inheritdoc />
    public partial class FulltextSearch : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "FulltextSearch",
                table: "StorageIndexDefinition",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "ExcludeFromFulltext",
                table: "StorageFieldDefinition",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "FulltextIndexingType",
                table: "StorageFieldDefinition",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "ElasticConnectionString",
                table: "StorageContext",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Language",
                table: "StorageContext",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FulltextSearch",
                table: "StorageIndexDefinition");

            migrationBuilder.DropColumn(
                name: "ExcludeFromFulltext",
                table: "StorageFieldDefinition");

            migrationBuilder.DropColumn(
                name: "FulltextIndexingType",
                table: "StorageFieldDefinition");

            migrationBuilder.DropColumn(
                name: "ElasticConnectionString",
                table: "StorageContext");

            migrationBuilder.DropColumn(
                name: "Language",
                table: "StorageContext");
        }
    }
}
