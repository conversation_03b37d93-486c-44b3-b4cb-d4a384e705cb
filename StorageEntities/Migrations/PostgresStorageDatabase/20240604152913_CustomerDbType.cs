using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
    /// <inheritdoc />
    public partial class CustomerDbType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DefaultLanguage",
                table: "StorageFieldDefinition");

            migrationBuilder.AddColumn<int>(
                name: "DatabaseType",
                table: "StorageContext",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DatabaseType",
                table: "StorageContext");

            migrationBuilder.AddColumn<string>(
                name: "DefaultLanguage",
                table: "StorageFieldDefinition",
                type: "text",
                nullable: false,
                defaultValue: "");
        }
    }
}
