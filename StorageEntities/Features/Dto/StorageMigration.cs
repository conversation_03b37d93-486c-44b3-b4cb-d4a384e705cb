using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using FluentMigrator;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Domain.StorageEntities.Features.Dto;

[Index(nameof(MigrationKey), IsUnique = true)]
public class StorageMigration
{
	[ExcludeFromCodeCoverage]
	[Key]
	[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
	public int Id { get; set; }

	[ExcludeFromCodeCoverage]
	public string MigrationKey { get; init; }

	[ExcludeFromCodeCoverage]
	public string MigrationDesc { get; init; }

	[ExcludeFromCodeCoverage]
	public bool MigrationDone { get; set; } = false;

	[ExcludeFromCodeCoverage]
	public DateTime MigrationDoneTime { get; set; }

	public StorageMigration(string migrationKey, string migrationDesc)
	{
		MigrationKey = migrationKey;
		MigrationDesc = migrationDesc;
	}
}