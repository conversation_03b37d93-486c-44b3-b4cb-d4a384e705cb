using System.Data;
using Levelbuild.Core.DataStoreInterface.Enum;

namespace Levelbuild.Domain.StorageEntities.Features.Dto;

public class RevisionDefinition : StorageIndexDefinition
{
	public new const string Id = "Id";
	public const string Revision = "Revision";
	public const string ElementId = "RevElementId";
	public const string DateTime = "RevDateTime";
	public const string UserName = "RevUserName";
	public const string Comment = "RevComment";
	public const string ActionId = "RevActionId";
	public const string ActionName = "RevActionName";
	public const string InitiatorId = "RevInitiatorId";
	public const string InitiatorName = "RevInitiatorName";
	public const string HasFile = "RevHasFile";
	public const string PathId = "RevPathId";
	public const string ExtendedPath = "RevExtendedPath";
	public const string OperationType = "RevOperationType";
	public const string OperationOriginType = "RevOriginOperationType";
	public const string ChangedFields = "ChangedFields";

	/// <summary>
	/// constructor using name of the original table, fieldList just to initialize, will be overwritten internally
	/// </summary>
	/// <param name="tableName"></param>
	/// <param name="fieldList"></param>
	public RevisionDefinition(string tableName, List<StorageFieldDefinitionOrm> fieldList) : base(tableName,
																								  fieldList)
	{
		fieldList = new List<StorageFieldDefinitionOrm>()
		{
			new StorageFieldDefinitionOrm(RevisionDefinition.Id)
			{
				Type = DataStoreFieldType.Guid,
				PrimaryKey = true,
				Unique = true
			},
			new StorageFieldDefinitionOrm(RevisionDefinition.Revision)
			{
				Type = DataStoreFieldType.Long
			},
			new StorageFieldDefinitionOrm(RevisionDefinition.ElementId)
			{
				Type = DataStoreFieldType.Guid
			},
			new StorageFieldDefinitionOrm(RevisionDefinition.DateTime, DataStoreFieldType.DateTime),
			new StorageFieldDefinitionOrm(RevisionDefinition.UserName),
			new StorageFieldDefinitionOrm(RevisionDefinition.Comment)
			{
				Nullable = true
			},
			new StorageFieldDefinitionOrm(RevisionDefinition.ActionId, DataStoreFieldType.Integer)
			{
				Nullable = true
			},
			new StorageFieldDefinitionOrm(RevisionDefinition.ActionName)
			{
				Nullable = true
			},
			new StorageFieldDefinitionOrm(RevisionDefinition.InitiatorId),
			new StorageFieldDefinitionOrm(RevisionDefinition.InitiatorName),
			new StorageFieldDefinitionOrm(RevisionDefinition.HasFile, DataStoreFieldType.Boolean)
			{
				DefaultValue = false
			},
			new StorageFieldDefinitionOrm(RevisionDefinition.PathId, DataStoreFieldType.Long),
			new StorageFieldDefinitionOrm(RevisionDefinition.ExtendedPath)
			{
				Nullable = true
			},
			new StorageFieldDefinitionOrm(RevisionDefinition.OperationType, DataStoreFieldType.Integer)
			{
				Nullable = true
			},
			new StorageFieldDefinitionOrm(RevisionDefinition.OperationOriginType, DataStoreFieldType.Integer)
			{
				Nullable = true
			},
			new StorageFieldDefinitionOrm(RevisionDefinition.ChangedFields, DataStoreFieldType.Integer)
			{
				MultiValue = true
			}
		};
		fieldList.ForEach(it => it.SystemField = true);
		Fields = fieldList;
		StoreRevisions = false;
	}

	/// <summary>
	/// Defines the foreign key constraint to the main table conteining rule for deletion
	/// </summary>
	/// <param name="primaryTableName">Name of main table</param>
	/// <param name="primaryTableColumnName">name of primary key in main table</param>
	/// <param name="deleteRule">rule for deletion of data set main table (cascade, none, set default, set null)</param>
	public void DefineForeignKey(string primaryTableName, string primaryTableColumnName, Rule deleteRule)
	{
		StorageFieldDefinitionOrm foreignKeyField = Fields.Where(it => it.Name == RevisionDefinition.ElementId).First();
		StorageForeignKey foreignKey = new StorageForeignKey(primaryTableName, primaryTableColumnName, deleteRule);
		foreignKeyField.ForeignKey = foreignKey;
	}
}