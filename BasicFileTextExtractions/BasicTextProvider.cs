using Levelbuild.Core.FileTextInterface;

namespace Levelbuild.Domain.BasicFileTextExtractions;

/// <summary>
/// return text from file
/// </summary>
public class BasicTextProvider
{
	/// <inheritdoc />
	public class CustomBufferedStream : Stream
	{
		private readonly Stream _internal;
		private readonly byte[] _bufferedPrefix;
		private readonly int _bufferedLength;
		private long _counter;

		/// <inheritdoc />
		public CustomBufferedStream(byte[] bufferedPrefix, int bufferedLength, Stream stream)
		{
			_internal = stream;
			_bufferedPrefix = bufferedPrefix;
			_bufferedLength = bufferedLength;
			_counter = 0;
		}

		/// <inheritdoc />
		public override void Flush()
		{
			_internal.Flush();
		}

		/// <inheritdoc />
		public override int Read(byte[] buffer, int offset, int count)
		{
			int copied = 0;
			if (_counter < _bufferedLength)
			{
				var toCopy = Math.Min(Math.Min(_bufferedLength - _counter, count), _bufferedLength);
				Array.Copy(_bufferedPrefix, _counter, buffer, offset, toCopy);
				_counter += toCopy;
				copied = (int)toCopy;
				if (copied == count)
					return copied;
			}

			if (_counter >= _bufferedLength)
			{
				var read = copied + _internal.Read(buffer, offset + copied, count - copied);
				_counter = _internal.Position;
				return read;
			}

			return copied;
		}

		/// <inheritdoc />
		public override long Seek(long offset, SeekOrigin origin)
		{
			_counter = _internal.Seek(offset, origin);
			if (_counter < _bufferedLength)
				_internal.Position = _bufferedLength;
			return _counter;
		}

		/// <inheritdoc />
		public override void SetLength(long value)
		{
			_internal.SetLength(value);
		}

		/// <inheritdoc />
		public override void Write(byte[] buffer, int offset, int count)
		{
			_internal.Write(buffer, offset, count);
		}

		/// <inheritdoc />
		public override bool CanRead => _internal.CanRead;

		/// <inheritdoc />
		public override bool CanSeek => _internal.CanSeek;

		/// <inheritdoc />
		public override bool CanWrite => _internal.CanWrite;

		/// <inheritdoc />
		public override long Length => _internal.Length;

		/// <inheritdoc />
		public override long Position
		{
			get => _counter;
			set
			{
				_counter = value;
				if (_bufferedLength < value)
					_internal.Position = value;
				else
					_internal.Position = _bufferedLength;
			}
		}
	}

	private static IFileTextProvider _pdfProvider = new PdfFileTextProvider();
	private static IFileTextProvider _textProvider = new TxtFileTextProvider();

	/// <summary>
	/// get text from file
	/// </summary>
	/// <param name="fullFileName"></param>
	/// <param name="fileExtension"></param>
	/// <param name="fileStream"></param>
	/// <param name="contentLengthLimitHint"></param>
	/// <returns></returns>
	public static string? GetText(string fullFileName, string fileExtension, Stream fileStream, int contentLengthLimitHint)
	{
		byte[] first256Bytes = new byte[256];
		int read = fileStream.Read(first256Bytes);

		if (read <= 0)
			return null;

		var bufferedStream = new CustomBufferedStream(first256Bytes, read, fileStream);

		IFileTextProvider? provider = null;

		if (_pdfProvider.CanExtractContent(fullFileName, fileExtension, first256Bytes, read))
			provider = _pdfProvider;
		else if (_textProvider.CanExtractContent(fullFileName, fileExtension, first256Bytes, read))
			provider = _textProvider;

		if (provider != null)
		{
			var str = provider.GetText(bufferedStream, contentLengthLimitHint);

			return str;
		}

		return null;
	}
}